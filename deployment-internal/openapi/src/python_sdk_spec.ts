import { OpenApiGeneratorV3 } from "@asteasolutions/zod-to-openapi";
import { extendZodWithOpenApi } from "@asteasolutions/zod-to-openapi";
import { z } from "zod";
extendZodWithOpenApi(z);

import { cleanupV3Spec } from "./cleanup_openapi_spec";

import {
  spanTypeSchema,
  datasetEventSchema,
  experimentEventSchema,
  spanAttributesSchema,
  modelParamsSchema,
  promptOptionsSchema,
  attachmentReferenceSchema,
  attachmentStatusSchema,
  ifExistsEnum,
  savedFunctionIdSchema,
  toolFunctionDefinitionSchema,
  chatCompletionMessageParamSchema,
  promptDataSchema,
} from "@braintrust/core/typespecs";

/**
 * Generates a subset of OpenAPI spec to be converted to Python TypedDict
 * definitions.
 */
export function generatePythonSdkOpenAPISpec(): unknown {
  // Order matters to the OpenAPI generator. In the case of a nested schema:
  //
  //   const childSchema = z.something(...).openapi("Child");
  //   const parentSchema = z.object({
  //     child: childSchema.nullish().describe("Description."),
  //   }).openapi("Parent");
  //
  // If the `childSchema` is not listed first, the generated output will wrap
  // the child with a Python Optional type. Also, the description will not be
  // emitted. I am not sure why this happens.
  const schemas: z.ZodType[] = [
    spanTypeSchema, // Child of `spanAttributesSchema`. Must appear first.
    z
      .object({
        ...datasetEventSchema.shape,
        output: z.unknown().describe("Deprecated."),
      })
      .openapi("DatasetEvent"),
    experimentEventSchema,
    spanAttributesSchema,
    modelParamsSchema,
    promptOptionsSchema,
    attachmentReferenceSchema,
    attachmentStatusSchema,
    ifExistsEnum.openapi("IfExists"),
    savedFunctionIdSchema,
    toolFunctionDefinitionSchema.openapi("ToolFunctionDefinition"),
    chatCompletionMessageParamSchema,
    promptDataSchema,
  ];

  const generator = new OpenApiGeneratorV3(schemas);
  return cleanupV3Spec(
    generator.generateDocument({
      openapi: "3.0.3",
      info: {
        version: "1.0.0",
        title: "Braintrust API Python SDK types",
        description: "Subset of Braintrust API specs used in Python SDK.",
        license: {
          name: "Apache 2.0",
        },
      },
    }),
  );
}
