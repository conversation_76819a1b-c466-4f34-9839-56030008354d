import {
  OpenApiGeneratorV3,
  OpenAPIRegistry,
} from "@asteasolutions/zod-to-openapi";
import { extendZodWithOpenApi } from "@asteasolutions/zod-to-openapi";
import { z } from "zod";
extendZodWithOpenApi(z);

import {
  registerBearerAuth,
  registerCORSPath,
  CORSPath,
  errorResponses,
  schemaHasRequired,
} from "./common_spec";

import {
  aclBatchUpdateRequestSchema,
  aclBatchUpdateResponseSchema,
  createApiKeyOutputSchema,
  crossObjectInsertRequestSchema,
  crossObjectInsertResponseSchema,
  apiSpecEventObjectSchemas,
  feedbackResponseSchema,
  fetchEventsRequestSchema,
  insertEventsResponseSchema,
  fetchLimitParamSchema,
  maxXactIdSchema,
  maxRootSpanIdSchema,
  versionSchema,
  aclSchema,
  appLimitParamSchema,
  startingAfterSchema,
  endingBeforeSchema,
  makeObjectIdsFilterSchema,
  apiSpecObjectSchemas,
  getObjectArticle,
  getEventObjectType,
  getEventObjectDescription,
  objectTypes,
  objectTypesWithEvent,
  aiSecretSchema,
  envVarSchema,
  summarizeScoresParamSchema,
  comparisonExperimentIdParamSchema,
  summarizeDataParamSchema,
  objectTypeSummarizeResponseSchemas,
  viewTypeEnum,
  invokeApiSchema,
  runEvalSchema,
  patchOrganizationMembersSchema,
  patchOrganizationMembersOutputSchema,
  projectScoreTypeEnum,
} from "@braintrust/core/typespecs";

// NOTE: Only add type imports to this library. For value imports, use the import above against `= typespecs`.
import { ObjectType } from "@braintrust/core/typespecs";

import { capitalize, snakeToCamelCase } from "@braintrust/core";
import { registerProxyEndpoints } from "./proxy_spec";
import { cleanupV3Spec } from "./cleanup_openapi_spec";

export function generateApiOpenAPISpec(): unknown {
  const registry = new OpenAPIRegistry();

  // Common security settings.

  const bearerAuth = registerBearerAuth(registry);
  const bearerSecurity: { [name: string]: string[] }[] = [
    { [bearerAuth.name]: [] },
    {},
  ];

  // Common path params.

  function registerObjectIdParam(objectType: string) {
    const objectTypeCap = snakeToCamelCase(objectType);
    return registry.registerParameter(
      `${objectTypeCap}IdParam`,
      z
        .string()
        .uuid()
        .describe(`${objectTypeCap} id`)
        .openapi({
          param: {
            name: `${objectType}_id`,
            in: "path",
          },
        }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const idParams = Object.fromEntries(
    objectTypes.options.map((x): [ObjectType, z.ZodString] => [
      x,
      registerObjectIdParam(x),
    ]),
  ) as Record<ObjectType, z.ZodString>;

  const projectIdQueryParam = registry.registerParameter(
    `ProjectIdQuery`,
    z
      .string()
      .uuid()
      .optional()
      .describe(`Project id`)
      .openapi({
        param: {
          name: `project_id`,
          in: "query",
        },
      }),
  );

  function registerObjectNameParam(objectType: string) {
    const objectTypeCap = snakeToCamelCase(objectType);
    return registry.registerParameter(
      `${objectTypeCap}Name`,
      z
        .string()
        .optional()
        .describe(`Name of the ${objectType} to search for`)
        .openapi({
          param: {
            name: `${objectType}_name`,
            in: "query",
            allowReserved: true,
          },
        }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const nameParams = Object.fromEntries(
    objectTypes.options.map(
      (x): [ObjectType, z.ZodOptional<z.ZodString> | undefined] => [
        x,
        ["acl", "organization", "user"].includes(x)
          ? undefined
          : registerObjectNameParam(x),
      ],
    ),
  ) as Record<ObjectType, z.ZodOptional<z.ZodString> | undefined>;

  const orgNameParam = registry.registerParameter(
    "OrgName",
    z
      .string()
      .optional()
      .describe("Filter search results to within a particular organization")
      .openapi({
        param: {
          name: "org_name",
          in: "query",
          allowReserved: true,
        },
      }),
  );

  const idsParam = registry.registerParameter(
    "Ids",
    makeObjectIdsFilterSchema("object")
      .optional()
      .openapi({
        param: {
          name: "ids",
          in: "query",
        },
      }),
  );

  const appLimitParam = registry.registerParameter(
    "AppLimitParam",
    appLimitParamSchema.optional().openapi({
      param: {
        name: "limit",
        in: "query",
      },
    }),
  );

  const fetchLimitParam = registry.registerParameter(
    "FetchLimitParam",
    fetchLimitParamSchema.optional().openapi({
      param: {
        name: "limit",
        in: "query",
      },
    }),
  );

  const startingAfterParam = registry.registerParameter(
    "StartingAfter",
    startingAfterSchema.optional().openapi({
      param: {
        name: "starting_after",
        in: "query",
      },
    }),
  );

  const endingBeforeParam = registry.registerParameter(
    "EndingBefore",
    endingBeforeSchema.optional().openapi({
      param: {
        name: "ending_before",
        in: "query",
      },
    }),
  );

  const maxXactIdParam = registry.registerParameter(
    "MaxXactId",
    maxXactIdSchema.optional().openapi({
      param: {
        name: "max_xact_id",
        in: "query",
      },
    }),
  );

  const maxRootSpanIdParam = registry.registerParameter(
    "MaxRootSpanId",
    maxRootSpanIdSchema.optional().openapi({
      param: {
        name: "max_root_span_id",
        in: "query",
      },
    }),
  );

  const versionParam = registry.registerParameter(
    "Version",
    versionSchema.optional().openapi({
      param: {
        name: "version",
        in: "query",
      },
    }),
  );

  const promptVersionParam = registry.registerParameter(
    "PromptVersion",
    versionParam.describe(
      [
        "Retrieve prompt at a specific version.",
        "The version id can either be a transaction id (e.g. '1000192656880881099') or a version identifier (e.g. '81cd05ee665fdfb3').",
      ].join("\n\n"),
    ),
  );

  const summarizeScoresParam = registry.registerParameter(
    "SummarizeScores",
    summarizeScoresParamSchema.optional().openapi({
      param: {
        name: "summarize_scores",
        in: "query",
      },
    }),
  );

  const comparisonExperimentIdParam = registry.registerParameter(
    "ComparisonExperimentId",
    comparisonExperimentIdParamSchema.optional().openapi({
      param: {
        name: "comparison_experiment_id",
        in: "query",
      },
    }),
  );

  const summarizeDataParam = registry.registerParameter(
    "SummarizeData",
    summarizeDataParamSchema.optional().openapi({
      param: {
        name: "summarize_data",
        in: "query",
      },
    }),
  );

  const slugParam = registry.registerParameter(
    "Slug",
    z
      .string()
      .optional()
      .describe("Retrieve prompt with a specific slug")
      .openapi({
        param: {
          name: "slug",
          in: "query",
          allowReserved: true,
        },
      }),
  );

  const viewTypeParam = registry.registerParameter(
    "ViewType",
    viewTypeEnum.optional().openapi({
      param: {
        name: "view_type",
        in: "query",
      },
    }),
  );

  function registerUserFilterParam(colname: string) {
    const colNameCap = capitalize(colname).replace("_", " ");
    const colNameLowercase = colname.replace("_", " ");
    const colCamelCase = colname
      .split("_")
      .map((s) => capitalize(s))
      .join("");
    return registry.registerParameter(
      `User${colCamelCase}`,
      z
        .union([z.string(), z.string().array()])
        .optional()
        .describe(
          `${colNameCap} of the user to search for. You may pass the param multiple times to filter for more than one ${colNameLowercase}`,
        )
        .openapi({
          param: {
            name: colname,
            in: "query",
            allowReserved: true,
          },
        }),
    );
  }

  const userGivenNameParam = registerUserFilterParam("given_name");
  const userFamilyNameParam = registerUserFilterParam("family_name");
  const userEmailParam = registerUserFilterParam("email");

  const objectTypeSummarizeQueryParams: { [K in ObjectType]?: z.AnyZodObject } =
    {
      experiment: z.object({
        summarize_scores: summarizeScoresParam,
        comparison_experiment_id: comparisonExperimentIdParam,
      }),
      dataset: z.object({
        summarize_data: summarizeDataParam,
      }),
    };

  const aclObjectTypeParam = registry.registerParameter(
    "AclObjectType",
    aclSchema.shape.object_type.openapi({
      param: {
        name: "object_type",
        in: "query",
      },
    }),
  );

  const aclObjectIdParam = registry.registerParameter(
    "AclObjectId",
    aclSchema.shape.object_id.openapi({
      param: {
        name: "object_id",
        in: "query",
      },
    }),
  );

  const aclListOrgObjectTypeParam = registry.registerParameter(
    "AclListOrgObjectType",
    aclSchema.shape.object_type.openapi({
      param: {
        name: "object_type",
        in: "query",
        required: false,
      },
    }),
  );

  const aclListOrgObjectIdParam = registry.registerParameter(
    "AclListOrgObjectId",
    aclSchema.shape.object_id.openapi({
      param: {
        name: "object_id",
        in: "query",
        required: false,
      },
    }),
  );

  const aclListUserIdParam = registry.registerParameter(
    "AclListUserId",
    aclSchema.shape.user_id
      .unwrap()
      .unwrap()
      .describe(aclSchema.shape.user_id.description ?? "")
      .openapi({
        param: {
          name: "user_id",
          in: "query",
          required: false,
        },
      }),
  );

  const aclListGroupIdParam = registry.registerParameter(
    "AclListGroupId",
    aclSchema.shape.group_id
      .unwrap()
      .unwrap()
      .describe(aclSchema.shape.group_id.description ?? "")
      .openapi({
        param: {
          name: "group_id",
          in: "query",
          required: false,
        },
      }),
  );

  const aclListPermissionParam = registry.registerParameter(
    "AclListPermission",
    aclSchema.shape.permission
      .unwrap()
      .unwrap()
      .openapi({
        param: {
          name: "permission",
          in: "query",
          required: false,
        },
      }),
  );

  const aclListRestrictObjectTypeParam = registry.registerParameter(
    "AclListRestrictObjectType",
    aclSchema.shape.restrict_object_type
      .unwrap()
      .unwrap()
      .openapi({
        param: {
          name: "restrict_object_type",
          in: "query",
          required: false,
        },
      }),
  );

  const aclListRoleIdParam = registry.registerParameter(
    "AclListRoleId",
    aclSchema.shape.role_id
      .unwrap()
      .unwrap()
      .describe(aclSchema.shape.role_id.description ?? "")
      .openapi({
        param: {
          name: "role_id",
          in: "query",
          required: false,
        },
      }),
  );

  const projectScoreTypeParam = registry.registerParameter(
    "ProjectScoreType",
    z
      .union([
        projectScoreTypeEnum.openapi({ title: "project_score_type_single" }),
        projectScoreTypeEnum.openapi({ title: "project_score_type" }).array(),
      ])
      .optional()
      .openapi({
        param: {
          name: "score_type",
          in: "query",
        },
      }),
  );

  const aiSecretTypeSchema = aiSecretSchema.shape.type.unwrap().unwrap();
  const aiSecretTypeParam = registry.registerParameter(
    "AISecretType",
    z
      .union([aiSecretTypeSchema, aiSecretTypeSchema.array()])
      .optional()
      .openapi({
        param: {
          name: "ai_secret_type",
          in: "query",
        },
      }),
  );

  const envVarObjectTypeParam = registry.registerParameter(
    "EnvVarObjectType",
    envVarSchema.shape.object_type.optional().openapi({
      param: {
        name: "object_type",
        in: "query",
      },
    }),
  );

  const envVarObjectIdParam = registry.registerParameter(
    "EnvVarObjectId",
    envVarSchema.shape.object_id.optional().openapi({
      param: {
        name: "object_id",
        in: "query",
      },
    }),
  );

  // Endpoints.

  function registerObjectPaths(objectType: ObjectType) {
    const objectTypeWithEventParseReturn =
      objectTypesWithEvent.safeParse(objectType);
    const objectTypeWithEvent = objectTypeWithEventParseReturn.success
      ? objectTypeWithEventParseReturn.data
      : undefined;
    const objectTypeCap = snakeToCamelCase(objectType);
    const article = getObjectArticle(objectType);
    const isUnderProject = [
      "experiment",
      "dataset",
      "prompt",
      "function",
      "project_score",
      "project_tag",
      "custom_viewer",
    ].includes(objectType);
    const isAcl = objectType === "acl";
    const isPrompt = objectType === "prompt" || objectType === "function";
    const isUser = objectType === "user";
    const isView = objectType === "view";
    const isApiKey = objectType === "api_key";
    const isOrganization = objectType === "organization";
    const isProjectScore = objectType === "project_score";
    const isAISecret = objectType === "ai_secret";
    const isEnvVar = objectType === "env_var";

    const tags: string[] = [objectTypeCap + "s"];

    const idParamSchema = z.object({
      [`${objectType}_id`]: idParams[objectType],
    });
    const schemas = apiSpecObjectSchemas[objectType];
    const allPaths: CORSPath[] = [];

    const isDuplicateNoun = !nameParams[objectType]
      ? "contents"
      : isPrompt
        ? "slug"
        : "name";
    const ifDuplicateClause = `If there is an existing ${objectType}${
      isUnderProject ? " in the project" : ""
    } with the same ${isDuplicateNoun} as the one specified in the request`;
    const ifDuplicateReturnUnmodified = `${ifDuplicateClause}, will return the existing ${objectType} unmodified`;
    const duplicatesAllowedClause = `It is possible to have multiple API keys with the same name. There is no de-duplication`;

    if (schemas.create !== undefined && schemas.object !== undefined) {
      const createObjectConfigBase = {
        path: `/v1/${objectType}`,
        tags,
        request: {
          body: {
            description: `Any desired information about the new ${objectType} object`,
            content: {
              "application/json": { schema: schemas.create },
            },
            required: schemaHasRequired(schemas.create),
          },
        },
        responses: {
          200: {
            description: isApiKey
              ? `Returns an object containing the raw API key. This is the only time the raw API key will be exposed`
              : `Returns the new ${objectType} object`,
            content: {
              "application/json": {
                schema: isApiKey ? createApiKeyOutputSchema : schemas.object,
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      } as const;

      registry.registerPath({
        ...createObjectConfigBase,
        operationId: `post${objectTypeCap}`,
        method: "post",
        description: `Create a new ${objectType}. ${
          isApiKey ? duplicatesAllowedClause : ifDuplicateReturnUnmodified
        }`,
        summary: `Create ${objectType}`,
      });

      // These endpoints either don't support an 'update' argument in their
      // register UDF or we don't want to expose it.
      if (
        !["project", "experiment", "dataset", "acl", "api_key"].includes(
          objectType,
        )
      ) {
        registry.registerPath({
          ...createObjectConfigBase,
          operationId: `put${objectTypeCap}`,
          method: "put",
          description: `Create or replace ${objectType}. ${ifDuplicateClause}, will replace the existing ${objectType} with the provided fields`,
          summary: `Create or replace ${objectType}`,
        });
      }
    }

    if (schemas.delete !== undefined && schemas.object !== undefined) {
      registry.registerPath({
        operationId: `delete${objectTypeCap}`,
        method: "delete",
        path: `/v1/${objectType}`,
        tags,
        description: `Delete a single ${objectType}`,
        summary: `Delete single ${objectType}`,
        request: {
          body: {
            description: `Parameters which uniquely specify the ${objectType} to delete`,
            content: {
              "application/json": { schema: schemas.delete },
            },
            required: schemaHasRequired(schemas.delete),
          },
        },
        responses: {
          200: {
            description: `Returns the deleted ${objectType} object`,
            content: {
              "application/json": {
                schema: schemas.object,
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });
    }

    const nameParam = nameParams[objectType];
    const listObjectsQuerySchema = z.object({
      limit: appLimitParam,
      ...(!isEnvVar ? { starting_after: startingAfterParam } : {}),
      ...(!isEnvVar ? { ending_before: endingBeforeParam } : {}),
      ids: idsParam,
      ...(nameParam ? { [`${objectType}_name`]: nameParam } : {}),
      ...(isUnderProject
        ? { project_name: nameParams.project, project_id: projectIdQueryParam }
        : {}),
      ...(isPrompt
        ? {
            slug: slugParam,
            version: promptVersionParam,
          }
        : {}),
      ...(isView
        ? {
            view_type: viewTypeParam,
          }
        : {}),
      ...(isUser
        ? {
            given_name: userGivenNameParam,
            family_name: userFamilyNameParam,
            email: userEmailParam,
          }
        : {}),
      ...(isAcl || isView
        ? {
            object_type: aclObjectTypeParam,
            object_id: aclObjectIdParam,
          }
        : {}),
      ...(isAcl
        ? {
            user_id: aclListUserIdParam,
            group_id: aclListGroupIdParam,
            permission: aclListPermissionParam,
            restrict_object_type: aclListRestrictObjectTypeParam,
            role_id: aclListRoleIdParam,
          }
        : {}),
      ...(!(isAcl || isView || isEnvVar) ? { org_name: orgNameParam } : {}),
      // Acl and view queries must specify a particular object, so filtering by
      // org name is irrelevant.
      ...(isProjectScore
        ? {
            score_type: projectScoreTypeParam,
          }
        : {}),
      ...(isAISecret
        ? {
            ai_secret_type: aiSecretTypeParam,
          }
        : {}),
      ...(isEnvVar
        ? {
            object_type: envVarObjectTypeParam,
            object_id: envVarObjectIdParam,
          }
        : {}),
    });

    if (schemas.object !== undefined) {
      registry.registerPath({
        operationId: `get${objectTypeCap}`,
        method: "get",
        path: `/v1/${objectType}`,
        tags,
        description: `List out all ${objectType}s. The ${objectType}s are sorted by creation date, with the most recently-created ${objectType}s coming first`,
        summary: `List ${objectType}s`,
        request: {
          query: listObjectsQuerySchema,
        },
        responses: {
          200: {
            description: `Returns a list of ${objectType} objects`,
            content: {
              "application/json": {
                schema: z
                  .object({
                    objects: schemas.object.array().openapi({
                      description: `A list of ${objectType} objects`,
                    }),
                  })
                  .strict(),
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });

      allPaths.push({
        path: `/v1/${objectType}`,
        operationId: `options${objectTypeCap}`,
      });
    }

    const getObjectQuerySchema = z.object({
      ...(isView
        ? {
            object_type: aclObjectTypeParam,
            object_id: aclObjectIdParam,
          }
        : {}),
      ...(isPrompt
        ? {
            version: promptVersionParam,
          }
        : {}),
    });

    let registeredIdPath = false;

    if (schemas.object !== undefined) {
      registry.registerPath({
        operationId: `get${objectTypeCap}Id`,
        method: "get",
        path: `/v1/${objectType}/{${objectType}_id}`,
        tags,
        description: `Get ${article} ${objectType} object by its id`,
        summary: `Get ${objectType}`,
        request: {
          params: idParamSchema,
          query: getObjectQuerySchema,
        },
        responses: {
          200: {
            description: `Returns the ${objectType} object`,
            content: {
              "application/json": { schema: schemas.object },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });
      registeredIdPath = true;
    }

    if (schemas.patch_id !== undefined && schemas.object !== undefined) {
      registry.registerPath({
        operationId: `patch${objectTypeCap}Id`,
        method: "patch",
        path: `/v1/${objectType}/{${objectType}_id}`,
        tags,
        description: `Partially update ${article} ${objectType} object. Specify the fields to update in the payload. Any object-type fields will be deep-merged with existing content. Currently we do not support removing fields or setting them to null.`,
        summary: `Partially update ${objectType}`,
        request: {
          params: idParamSchema,
          body: {
            description: "Fields to update",
            content: {
              "application/json": { schema: schemas.patch_id },
            },
            required: schemaHasRequired(schemas.patch_id),
          },
        },
        responses: {
          200: {
            description: `Returns the ${objectType} object`,
            content: {
              "application/json": { schema: schemas.object },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });
      registeredIdPath = true;
    }

    if (schemas.object !== undefined && !isUser && !isOrganization) {
      registry.registerPath({
        operationId: `delete${objectTypeCap}Id`,
        method: "delete",
        path: `/v1/${objectType}/{${objectType}_id}`,
        tags,
        description: `Delete ${article} ${objectType} object by its id`,
        summary: `Delete ${objectType}`,
        request: {
          ...(schemas.delete_id
            ? {
                body: {
                  description: "Additional parameters for the delete operation",
                  content: {
                    "application/json": { schema: schemas.delete_id },
                  },
                  required: schemaHasRequired(schemas.delete_id),
                },
              }
            : {}),
          params: idParamSchema,
        },
        responses: {
          200: {
            description: `Returns the deleted ${objectType} object`,
            content: {
              "application/json": { schema: schemas.object },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });
      registeredIdPath = true;
    }

    if (registeredIdPath) {
      allPaths.push({
        path: `/v1/${objectType}/{${objectType}_id}`,
        operationId: `options${objectTypeCap}Id`,
        requestParams: idParamSchema,
      });
    }

    if (objectTypeWithEvent) {
      const eventObjectType = getEventObjectType(objectTypeWithEvent);

      // We want logs to appear as a top-level node in the docs, so we manually manipulate the tags
      const tagsOverride = eventObjectType === "project_logs" ? ["Logs"] : null;

      const eventSchemaName = capitalize(eventObjectType, "_").replace("_", "");
      const eventDescriptionName =
        getEventObjectDescription(objectTypeWithEvent);
      const eventSchemas = apiSpecEventObjectSchemas[eventObjectType];

      if (eventSchemas.insertRequest !== undefined) {
        registry.registerPath({
          operationId: `post${eventSchemaName}IdInsert`,
          method: "post",
          path: `/v1/${eventObjectType}/{${objectType}_id}/insert`,
          tags: tagsOverride ?? tags,
          description: `Insert a set of events into the ${eventDescriptionName}`,
          summary: `Insert ${eventDescriptionName} events`,
          request: {
            params: idParamSchema,
            body: {
              description: `An array of ${eventDescriptionName} events to insert`,
              content: {
                "application/json": { schema: eventSchemas.insertRequest },
              },
              required: schemaHasRequired(eventSchemas.insertRequest),
            },
          },
          responses: {
            200: {
              description: "Returns the inserted row ids",
              content: {
                "application/json": {
                  schema: insertEventsResponseSchema,
                },
              },
            },
            ...errorResponses,
          },
          security: bearerSecurity,
        });

        allPaths.push({
          path: `/v1/${eventObjectType}/{${objectType}_id}/insert`,
          operationId: `options${eventSchemaName}IdInsert`,
          requestParams: idParamSchema,
        });
      }

      if (eventSchemas.fetchResponse !== undefined) {
        registry.registerPath({
          operationId: `post${eventSchemaName}IdFetch`,
          method: "post",
          path: `/v1/${eventObjectType}/{${objectType}_id}/fetch`,
          tags: tagsOverride ?? tags,

          description: `Fetch the events in ${article} ${eventDescriptionName}. Equivalent to the GET form of the same path, but with the parameters in the request body rather than in the URL query. For more complex queries, use the \`POST /btql\` endpoint.`,
          summary: `Fetch ${eventDescriptionName} (POST form)`,
          request: {
            params: idParamSchema,
            body: {
              description: "Filters for the fetch query",
              content: {
                "application/json": { schema: fetchEventsRequestSchema },
              },
              required: schemaHasRequired(fetchEventsRequestSchema),
            },
            ...errorResponses,
          },
          responses: {
            200: {
              description: "Returns the fetched rows",
              content: {
                "application/json": {
                  schema: eventSchemas.fetchResponse,
                },
              },
            },
            ...errorResponses,
          },
          security: bearerSecurity,
        });

        registry.registerPath({
          operationId: `get${eventSchemaName}IdFetch`,
          method: "get",
          path: `/v1/${eventObjectType}/{${objectType}_id}/fetch`,
          tags: tagsOverride ?? tags,

          description: `Fetch the events in ${article} ${eventDescriptionName}. Equivalent to the POST form of the same path, but with the parameters in the URL query rather than in the request body. For more complex queries, use the \`POST /btql\` endpoint.`,
          summary: `Fetch ${eventDescriptionName} (GET form)`,
          request: {
            params: idParamSchema,
            query: z.object({
              limit: fetchLimitParam,
              max_xact_id: maxXactIdParam,
              max_root_span_id: maxRootSpanIdParam,
              // We skip the filter param in the get request. For some reason, I
              // couldn't find a simple way to pass a list of filters in the GET
              // request. They can always use the POST form if they want to add
              // filters.
              version: versionParam,
            }),
          },
          responses: {
            200: {
              description: "Returns the fetched rows",
              content: {
                "application/json": {
                  schema: eventSchemas.fetchResponse,
                },
              },
            },
            ...errorResponses,
          },
          security: bearerSecurity,
        });

        allPaths.push({
          path: `/v1/${eventObjectType}/{${objectType}_id}/fetch`,
          operationId: `options${eventSchemaName}IdFetch`,
          requestParams: idParamSchema,
        });
      }

      if (eventSchemas.feedbackRequest !== undefined) {
        registry.registerPath({
          operationId: `post${eventSchemaName}IdFeedback`,
          method: "post",
          path: `/v1/${eventObjectType}/{${objectType}_id}/feedback`,
          tags: tagsOverride ?? tags,

          description: `Log feedback for a set of ${eventDescriptionName} events`,
          summary: `Feedback for ${eventDescriptionName} events`,
          request: {
            params: idParamSchema,
            body: {
              description: `An array of feedback objects`,
              content: {
                "application/json": { schema: eventSchemas.feedbackRequest },
              },
              required: schemaHasRequired(eventSchemas.feedbackRequest),
            },
          },
          responses: {
            200: {
              description: "Returns a success status",
              content: {
                "application/json": { schema: feedbackResponseSchema },
              },
            },
            ...errorResponses,
          },
          security: bearerSecurity,
        });

        allPaths.push({
          path: `/v1/${eventObjectType}/{${objectType}_id}/feedback`,
          operationId: `options${eventSchemaName}IdFeedback`,
          requestParams: idParamSchema,
        });
      }
    }

    if (objectType === "function") {
      registry.registerPath({
        operationId: `post${objectTypeCap}IdInvoke`,
        method: "post",
        path: `/v1/${objectType}/{${objectType}_id}/invoke`,
        tags,
        description: `Invoke a ${objectType}.`,
        summary: `Invoke ${objectType}`,
        request: {
          params: idParamSchema,
          body: {
            description: "Function invocation parameters",
            content: {
              "application/json": {
                schema: invokeApiSchema,
              },
            },
          },
        },
        responses: {
          200: {
            description: "Function invocation response",
            content: {
              "application/json": { schema: z.unknown() },
            },
          },
        },
        security: bearerSecurity,
      });

      allPaths.push({
        path: `/v1/${objectType}/{${objectType}_id}/invoke`,
        operationId: `options${objectTypeCap}IdInvoke`,
        requestParams: idParamSchema,
      });
    }

    const summarizeResponseSchema =
      objectTypeSummarizeResponseSchemas[objectType];
    if (summarizeResponseSchema !== undefined) {
      registry.registerPath({
        operationId: `get${objectTypeCap}IdSummarize`,
        method: "get",
        path: `/v1/${objectType}/{${objectType}_id}/summarize`,
        tags,
        description: `Summarize ${objectType}`,
        summary: `Summarize ${objectType}`,
        request: {
          params: idParamSchema,
          query: objectTypeSummarizeQueryParams[objectType],
        },
        responses: {
          200: {
            description: `${objectTypeCap} summary`,
            content: {
              "application/json": { schema: summarizeResponseSchema },
            },
          },
          ...errorResponses,
        },
        security: [{ [bearerAuth.name]: [] }],
      });

      allPaths.push({
        path: `/v1/${objectType}/{${objectType}_id}/summarize`,
        operationId: `options${objectTypeCap}IdSummarize`,
        requestParams: idParamSchema,
      });
    }

    if (isOrganization) {
      registry.registerPath({
        operationId: `patchOrganizationMembers`,
        method: "patch",
        path: `/v1/${objectType}/members`,
        tags,
        description: `Modify ${objectType} membership`,
        summary: `Modify ${objectType} membership`,
        request: {
          body: {
            description: "Members to add/remove",
            content: {
              "application/json": { schema: patchOrganizationMembersSchema },
            },
            required: schemaHasRequired(patchOrganizationMembersSchema),
          },
        },
        responses: {
          200: {
            description: `A success status`,
            content: {
              "application/json": {
                schema: patchOrganizationMembersOutputSchema,
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });

      allPaths.push({
        path: `/v1/${objectType}/members`,
        operationId: `options${objectTypeCap}Members`,
      });
    }

    if (isAcl) {
      registry.registerPath({
        operationId: `aclBatchUpdate`,
        method: "post",
        path: `/v1/${objectType}/batch_update`,
        tags,
        description: `Batch update ${objectType}s. This operation is idempotent, so adding acls which already exist will have no effect, and removing acls which do not exist will have no effect.`,
        summary: `Batch update ${objectType}s`,
        request: {
          body: {
            description: `${objectTypeCap}s to add/remove.`,
            content: {
              "application/json": { schema: aclBatchUpdateRequestSchema },
            },
            required: schemaHasRequired(aclBatchUpdateRequestSchema),
          },
        },
        responses: {
          200: {
            description: `A success status`,
            content: {
              "application/json": {
                schema: aclBatchUpdateResponseSchema,
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });

      const aclListOrgQuerySchema = z.object({
        limit: appLimitParam,
        ids: idsParam,
        starting_after: startingAfterParam,
        ending_before: endingBeforeParam,
        object_type: aclListOrgObjectTypeParam,
        object_id: aclListOrgObjectIdParam,
        user_id: aclListUserIdParam,
        group_id: aclListGroupIdParam,
        permission: aclListPermissionParam,
        restrict_object_type: aclListRestrictObjectTypeParam,
        role_id: aclListRoleIdParam,
        org_name: orgNameParam,
      });

      registry.registerPath({
        operationId: `aclListOrg`,
        method: "get",
        path: `/v1/${objectType}/list_org`,
        tags,
        description: `List all ${objectType}s in the org. This query requires the caller to have \`read_acls\` permission at the organization level`,
        summary: `List org ${objectType}s`,
        request: {
          query: aclListOrgQuerySchema,
        },
        responses: {
          200: {
            description: `A list of ${objectType}s`,
            content: {
              "application/json": {
                schema: aclSchema.array(),
              },
            },
          },
          ...errorResponses,
        },
        security: bearerSecurity,
      });

      allPaths.push({
        path: `/v1/${objectType}/acl/batch_update`,
        operationId: `options${objectTypeCap}BatchUpdate`,
      });

      allPaths.push({
        path: `/v1/${objectType}/list_org`,
        operationId: `options${objectTypeCap}ListOrg`,
      });
    }

    for (const path of allPaths) {
      registerCORSPath(registry, path);
    }
  }

  for (const objectType of objectTypes.options) {
    registerObjectPaths(objectType);
  }

  registry.registerPath({
    operationId: "getIndex",
    method: "get",
    path: "/v1",
    tags: ["Other"],
    description:
      "Default endpoint. Simply replies with 'Hello, World!'. Authorization is not required",
    summary: "Hello world endpoint",
    security: [],
    responses: {
      200: {
        description: "Hello world string",
        content: {
          "text/plain": { schema: z.string() },
        },
      },
      400: errorResponses[400],
      500: errorResponses[500],
    },
  });
  registerCORSPath(registry, { path: "/v1", operationId: "optionsIndex" });

  registry.registerPath({
    operationId: `postCrossObjectInsert`,
    method: "post",
    path: `/v1/insert`,
    tags: ["CrossObject"],
    description: `Insert events and feedback across object types`,
    summary: `Cross-object insert`,
    request: {
      body: {
        description: `A mapping from event object type -> object id -> events to insert`,
        content: {
          "application/json": { schema: crossObjectInsertRequestSchema },
        },
        required: schemaHasRequired(crossObjectInsertRequestSchema),
      },
    },
    responses: {
      200: {
        description:
          "Returns the inserted row ids for the events on each individual object",
        content: {
          "application/json": { schema: crossObjectInsertResponseSchema },
        },
      },
      ...errorResponses,
    },
    security: [{ [bearerAuth.name]: [] }],
  });
  registerCORSPath(registry, {
    path: "/v1/insert",
    operationId: "optionsCrossObjectInsert",
  });

  registerProxyEndpoints({ registry, security: bearerSecurity });

  registry.registerPath({
    operationId: "evalLaunch",
    method: "post",
    path: `/v1/eval`,
    tags: ["Evals"],
    description:
      "Launch an evaluation. This is the API-equivalent of the `Eval` function that is built into " +
      "the Braintrust SDK. In the Eval API, you provide pointers to a dataset, task function, and " +
      "scoring functions. The API will then run the evaluation, create an experiment, and return the " +
      "results along with a link to the experiment. To learn more about evals, see the " +
      "[Evals guide](https://www.braintrust.dev/docs/guides/evals).",
    summary: "Launch an eval",
    request: {
      body: {
        description: "Eval launch parameters",
        content: {
          "application/json": { schema: runEvalSchema },
        },
        required: true,
      },
    },
    responses: {
      200: {
        description: "Eval launch response",
        content: {
          // TODO
          "application/json": {
            schema: objectTypeSummarizeResponseSchemas.experiment!,
          },
        },
      },
    },
    security: bearerSecurity,
  });

  const generator = new OpenApiGeneratorV3(registry.definitions);
  return cleanupV3Spec(
    generator.generateDocument({
      openapi: "3.0.3",
      info: {
        version: "1.0.0",
        title: "Braintrust API",
        description: `API specification for the backend data server. The API is hosted globally at
https://api.braintrust.dev or in your own environment.

You can access the OpenAPI spec for this API at https://github.com/braintrustdata/braintrust-openapi.`,
        license: {
          name: "Apache 2.0",
        },
      },
      servers: [{ url: "https://api.braintrust.dev" }],
      security: bearerSecurity,
    }),
  );
}
