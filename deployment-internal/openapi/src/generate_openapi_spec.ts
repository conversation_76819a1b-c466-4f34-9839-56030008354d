import { ArgumentParser, ArgumentDefaultsHelpFormatter } from "argparse";
import { z } from "zod";
import * as yaml from "yaml";

import { generateApiOpenAPISpec } from "./api_spec";
import { generatePythonSdkOpenAPISpec } from "./python_sdk_spec";

const allSpecsKeysSchema = z.enum(["api", "py_sdk"]);
type AllSpecsKeys = z.infer<typeof allSpecsKeysSchema>;
const AllSpecs: Record<AllSpecsKeys, () => unknown> = {
  api: generateApiOpenAPISpec,
  py_sdk: generatePythonSdkOpenAPISpec,
};

const formatSchema = z.enum(["json", "yaml"]);

const argsSchema = z.object({
  spec: allSpecsKeysSchema,
  format: formatSchema,
});

function main() {
  const parser = new ArgumentParser({
    description:
      "Generate an OpenAPI specification from the Zod definitions. Prints the spec to stdout",
    formatter_class: ArgumentDefaultsHelpFormatter,
  });
  parser.add_argument("spec", {
    help: "Which spec to generate",
    choices: allSpecsKeysSchema.options,
  });
  parser.add_argument("--format", {
    help: "Output format of spec",
    choices: formatSchema.options,
    default: "json",
  });

  const rawArgs = parser.parse_args();
  const args = argsSchema.parse(rawArgs);

  const schema = AllSpecs[args.spec]();
  if (args.format === "json") {
    console.log(JSON.stringify(schema, null, 2));
  } else if (args.format === "yaml") {
    console.log(yaml.stringify(schema));
  }
}

main();
