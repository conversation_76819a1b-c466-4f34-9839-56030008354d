# OpenAPI Spec

This package defines the REST API specification used across the codebase. It
utilizes types defined in `@braintrust/core/typespecs`.

## REST API Design

The API will be accessible at `{braintrust_url}/v1`. The blank endpoint `/` will
just return "hello world", to prove the server is up and running. No auth
required.

The design for the other endpoints is similar to the stripe object
[endpoints](https://stripe.com/docs/api/customers/create). Common points of
consideration:

- All of these endpoints require bearer authentication with API key or JWT
- The `object_type` param refers to one of `[experiment, dataset, project]`.
- The `event_object_type` param refers to one of `[experiment, dataset,
project_logs]`.
- The `object_type_id` param in the URL refers to the `[experiment, dataset,
project]_id` of the data object. Not the row ID of an individual event.

Here is the API:

- `POST /{object_type}`: Create a new instance of the object type. [Stripe
  analogue](https://stripe.com/docs/api/customers/create). Braintrust analogue:
  `init`/`initDataset`/`initLogger` (no init project though).

  - The payload can contain the arguments to the init method.
    - We can also allow specifying the org name in case their API key belongs to
      multiple orgs. But this should not be the norm.
  - For experiments and datasets, instead of providing a project_name, they
    should provide a project_id, which is more stable and regular.
  - In the case of experiments, if a duplicate is encountered, we create a new
    experiment with a perturbed name (as we do in `register_experiment`). For
    datasets/projects, we just return the existing one unmodified.
  - Returns the created/found data object, including the object ID.

- `PUT /{object_type}`: Create or replace a new instance of the object type. No
  stripe analogue. Braintrust analogue: `init` with `update=True`. Basically the
  same as `POST`, but when an existing object is found with the same name, we
  replace its contents with the provided ones.

- `GET /{object_type}`: List all data objects of the given type. [Stripe
  analogue](https://stripe.com/docs/api/customers/list). Braintrust analogue:
  `GET /crud/{table}`.

  - We can allow filtering by `name`.
  - We could support pagination. They can specify a `limit` and a
    `starting_after`/`ending_before` object ID in the query params. Similar to
    stripe, we can sort by `(created DESC, id ASC)`, but still use ID as the
    pagination cursor.

- `GET /{object_type}/{object_type_id}`: Get object metadata. [Stripe
  analogue](https://stripe.com/docs/api/customers/retrieve). Braintrust
  analogue: Running `init` for an existing object and then inspecting the
  resulting object.

- `PATCH /{object_type}/{object_type_id}`: Partially update object metadata. [Stripe
  analogue](https://stripe.com/docs/api/customers/update). Braintrust analogue:
  none.

  - The payload is a dictionary of new metadata information, for stuff we
    allow editing.
  - The response is similar to the response of `GET /{object_type}/{object_type_id}`.

- `DELETE /{object_type}/{object_type_id}`: Delete a data object. [Stripe
  analogue](https://stripe.com/docs/api/customers/delete). Braintrust analogue:
  `DELETE /crud/{table}`.

- `POST /{event_object_type}/{object_type_id}/insert`: Write a set of events to the data
  object. Not sure there's a good stripe analogue. Braintrust analogue: the
  `log` method of the various data objects.

  - The payload is a JSON list of rows.
    - The data backend fills in all the other object IDs based on the provided
      leaf ID.
    - We can support certain system fields like `IS_MERGE`, `MERGE_PATHS`,
      `OBJECT_DELETE_FIELD`, etc.
    - If they want to do tracing, they can just specify "parent_id" for creating
      a child span (assuming they control the ID of the parent). They are
      responsible for filling in the metrics start/end times themselves.
  - The response can be something lightweight like `{"row_ids": [row_id]}`,
    where the list of row IDs lines up with the input list they provided. This
    should be something we can provide before hitting the DB.
    - Note that providing the transaction ID will be difficult before we reach
      the DB, because of this potential [race
      condition](https://github.com/braintrustdata/braintrust/blob/563096178149b012c37154c670c1fd4b85bf35ab/api-ts/src/run_log_data.ts#L747).
    - Separately (in a later iteration probably), we can expose a "sync"
      interface which returns after the write hits the DB, and this can return a
      transaction ID.

- `GET|POST /{event_object_type}/{object_type_id}/fetch`: Retrieves the rows of the data
  object with the given ID. [Stripe analogue
  (sort-of)](https://stripe.com/docs/api/customers/search). Braintrust analogue:
  `GET|POST /object/{object_type}`.

  - For search queries, they should use the `/btql` endpoint.
  - They can also specify a `version` transaction ID.

- `GET|POST /{event_object_type}/{object_type_id}/fetch/audit`: Retrieves the audit log of
  the data object with the given ID. No stripe analogue. Braintrust analogue:
  `GET|POST /object/{object_type}/audit`.

  - Args are identical to `GET|POST /{event_object_type}/{object_type_id}/fetch`.

- `POST /{event_object_type}/{object_type_id}/feedback`: Send feedback for a particular set
  of events. No stripe analogue. Braintrust analogue: `logFeedback`.

  - API is similar to `POST /{event_object_type}/{object_type_id}/insert`, but scoped to the
    feedback fields.

- `POST /insert`: Cross-object insert method. Useful for batching operations
  across many objects. Braintrust analogue: the `log` methods, which batch
  everything behind the scenes.

  - The only difference from the `POST
/{event_object_type}/{object_type_id}/insert` method is that the input is a
    dictionary `{object_type: {object_id: {events, [rows], feedback: [rows]}}}`,
    so basically a grouping of the more-specific endpoints.

- `GET|POST /fetch`: Cross-object search method.

  - Here's a rough sketch of the object specification syntax (definitely open to
    revising):
    - They must specify a set of object types they want to search across, out of
      `[experiment, dataset, project]`. We could also support an identifier
      `'all'` to let them search across all object types in the org.
    - They can optionally specify either an `include` or `exclude` clause (not
      both) of the form: `{[experiment|dataset|project] -> [ids]}`. Note that
      this permits some sort of hierarchy, where they can filter objects at the
      project level AND the experiment/dataset level.

- `GET /{object_type}/{object_type_id}/summary`: Get a summary of the data object. Not sure
  there's a good stripe analogue. Braintrust analogue: the `summarize` family of
  methods:
  - We can support this for just experiments and datasets for now.

Possible extensions for the future (inspired by Stripe's API):

- [Expand](https://stripe.com/docs/api/expanding_objects): selectively expand
  subfields of returned responses.
- [Idempotency](https://stripe.com/docs/api/idempotent_requests): users can
  pass an "Idempotency key" to make their POST requests safely retryable.
- [Request IDs](https://stripe.com/docs/api/request_ids): Return a unique ID
  for every rest request which is logged and referenceable in the future.
- [Dated versioning](https://stripe.com/docs/api/versioning): Seems like they
  allow controlling the API version by date. But then what is the relationship
  with `/v1` in the URL string?
- [Webhooks](https://stripe.com/docs/api/webhook_endpoints): Seems useful for
  subscribing to events (sort-of like hooking into our realtime?)
