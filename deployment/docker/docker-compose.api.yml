# Each docker container can be configured with environment variables. Many of
# these variables point the container to the other services, which may be other
# containers you have deployed or centrally-hosted Braintrust services.
#
# Although we include containers here for Redis and Postgres, you should use
# cloud-native offerings (like RDS, Google Cloud SQL, Azure Managed Postgres, etc.)
# for production environments.
#
# - REDIS_URL: Connection URI for the redis instance for internal container use.
# The general form of the URI is
# `redis://[user[:password]@][host][:port][/db-number][?param1=value1&param2=value2...]`.
# Defaults to the URI for the `braintrust-redis` docker service.
#
# - PG_URL: Connection URI for the postgres instance for internal container use.
# The general form of the URI is
# `postgresql://[user[:password]@][host][:port][/dbname][?param1=value1&param2=value2...]`.
# Defaults to the URI for the 'braintrust-postgres' docker service. See
# https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING-URIS
# for full details on the URI spec.
#
# - PG_POOL_CONFIG_MAX_NUM_CLIENTS: Configure the maximum number of postgres
# clients allowed in use by the API server. Defaults to 10.
#
# - RESPONSE_BUCKET_NAME: The S3 (or protocol-compatible) bucket to use for storing API responses
# that exceed a certain threshold (RESPONSE_BUCKET_OVERFLOW_THRESHOLD).
#
# - RESPONSE_BUCKET_OVERFLOW_THRESHOLD: The threshold (in bytes) that an API response must exceed
# in order to be stored in the RESPONSE_BUCKET. This is useful for serverless environments where the
# responses have a maximum payload size.
#
# - RESPONSE_BUCKET_PREFIX: The prefix to use when storing responses in the RESPONSE_BUCKET.
#
# - RESPONSE_BUCKET_ACCESS_KEY_ID: Together with RESPONSE_BUCKET_SECRET_ACCESS_KEY, the credentials
# to use when uploading to the RESPONSE_BUCKET. If unspecified, uses the AWS credentials from the
# container's environment.
#
# - RESPONSE_BUCKET_SECRET_ACCESS_KEY: Together with RESPONSE_BUCKET_ACCESS_KEY_ID, the credentials
# to use when uploading to the RESPONSE_BUCKET. If unspecified, uses the AWS credentials from the
# container's environment.
#
# - RESPONSE_BUCKET_S3_ENDPOINT: The S3 (or protocol-compatible) endpoint to use when uploading to
# the RESPONSE_BUCKET (defaults to AWS at RESPONSE_BUCKET_REGION).
#
# - RESPONSE_BUCKET_REGION: The region of the RESPONSE_BUCKET.
#
# - ALLOW_CODE_FUNCTION_EXECUTION: Whether to allow custom code (TypeScript and Python)
# to execute. This is safe to enable as long as you are running code that you trust to not
# be malicious (i.e. do not expose this to the public). If you would like to support running
# untrusted code, then contact <NAME_EMAIL> so we can ensure you've configured
# the appropriate security controls.
#
# - CODE_BUNDLE_BUCKET: The S3 (or protocol-compatible) bucket to use for storing code bundles. Uses
# the same region and credentials as the RESPONSE_BUCKET. By default, the code bundle bucket is also
# used to store attachments under the `attachments/` prefix.
#
# - ATTACHMENT_BUCKET: The S3 (or protocol-compatible) bucket to use for storing attachments. Uses
# the same region and credentials as the RESPONSE_BUCKET. This overrides the default behavior of
# storing attachments in the code bundle bucket.
#
# - FUNCTION_SECRET_KEY: The key to use for encrypting function env secrets. Function environment
# secrets can be specified per org, project, or function and are exposed to functions as environment
# variables. This key is used to symmetrically encrypt (via AES-GCM 256) function secrets before storing
# them in the database. Once this key is set, if you change it, you will no longer be able to access
# any saved function secrets.
#
# - ORG_NAME: A specific org name to use for this deployment or * to allow any org to access it. If unspecified,
# `ORG_NAME` defaults to `*`. If specified, then certain operations, like AI secrets, will be
# constrained to only the specified org.
#

# Brainstore
#
# Brainstore is an optional service that enables very fast real-time search.
# To use Brainstore, you must have a dedicated S3 bucket for Brainstore data, as well as
# a license key provided by Braintrust. Brainstore will need to access the Postgres database
# and Redis instance.
#
# To enable Brainstore, uncomment the service definition below, as well as the environment variables
# and dependency in the standalone-api service definition.
# - BRAINSTORE_METADATA_URI:
#     This should point to the existing Braintrust Postgres database.
# - BRAINSTORE_WAL_URI:
#     This should point to the existing Braintrust Postgres database.
# - BRAINSTORE_LOCKS_URI:
#     This should point to the existing Braintrust Redis instance.
# - BRAINSTORE_INDEX_URI:
#     You should change <YOUR_S3_BUCKET_NAME> to the name of your Brainstore S3 bucket. Leave the path as is.
# - BRAINSTORE_REALTIME_WAL_URI:
#     You should change <YOUR_S3_BUCKET_NAME> to the name of your Brainstore S3 bucket. Leave the path as is.
# - BRAINSTORE_LICENSE_KEY:
#     This should be the license key provided by Braintrust. Brainstore will not start without this.
#

services:
  braintrust-redis:
    image: public.ecr.aws/braintrust/redis:latest
    ports:
      - 6479:6379
    extra_hosts:
      - "host.docker.internal:host-gateway"
  braintrust-postgres:
    image: public.ecr.aws/braintrust/postgres:latest
    command: postgres -c config_file=/etc/postgresql.conf
    environment:
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
    ports:
      - 5532:5432
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - api_pg_volume:/var/lib/postgresql/data
  # Uncomment this to enable Brainstore.
#  braintrust-brainstore:
#    image: public.ecr.aws/braintrust/brainstore:latest
#    environment:
#      BRAINSTORE_VERBOSE: 1
#      BRAINSTORE_PORT: 4000
#      BRAINSTORE_METADATA_URI: postgres://postgres:<EMAIL>:5532/postgres
#      BRAINSTORE_WAL_URI: postgres://postgres:<EMAIL>:5532/postgres
#      BRAINSTORE_LOCKS_URI: redis://host.docker.internal:6479/0
#      BRAINSTORE_INDEX_URI: s3://<YOUR_S3_BUCKET_NAME>/index
#      BRAINSTORE_REALTIME_WAL_URI: s3://<YOUR_S3_BUCKET_NAME>/wal
#      # You must set this environment variable in your local environment for
#      # the Brainstore container to start.
#      BRAINSTORE_LICENSE_KEY: ${BRAINSTORE_LICENSE_KEY}
#      NO_COLOR: 1
#    ports:
#      - 4000:4000
#    extra_hosts:
#      - "host.docker.internal:host-gateway"
  braintrust-standalone-api:
    image: public.ecr.aws/braintrust/standalone-api:latest
    environment:
      PG_URL: postgres://postgres:<EMAIL>:5532/postgres
      REDIS_URL: redis://host.docker.internal:6479/0
      ALLOW_CODE_FUNCTION_EXECUTION: true
      # Set this to 80% for production deployments that use a dedicated node for the API
      # NODE_MEMORY_PERCENT: 80
      # Any requests made to the container from within itself will use the
      # loopback URL rather than whatever externally-visible URL is set in the
      # org configuration.
      BRAINTRUST_API_URL: http://127.0.0.1:8000
      # If you are deploying any other services yourself, such as
      # realtime, you may override their URLs here as well.
      # REALTIME_URL: http://host.docker.internal:8788
      # Uncomment this to enable Brainstore.
      # BRAINSTORE_ENABLED: true
      # BRAINSTORE_URL: http://braintrust-brainstore:4000
    ports:
      - 8000:8000
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      braintrust-redis:
        condition: service_healthy
      braintrust-postgres:
        condition: service_healthy
      # Uncomment this to enable Brainstore.
#      braintrust-brainstore:
#        condition: service_healthy
  # To enable the realtime service, un-comment the service definition below. The realtime service
  # is optional.
#  braintrust-standalone-realtime:
#    image: public.ecr.aws/braintrust/standalone-realtime:latest
#    ports:
#      - 8788:8788
#    extra_hosts:
#      - "host.docker.internal:host-gateway"
volumes:
  api_pg_volume: null
