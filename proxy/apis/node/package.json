{"name": "ai-proxy-lambda", "version": "1.0.0", "description": "", "main": "./dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "run-p build:*", "build:typecheck": "tsc --noEmit", "build:local": "esbuild --platform=node --bundle src/local.ts --outfile=dist/local.js --minify --sourcemap --target=es2020", "build:lambda": "esbuild --platform=node --external:@aws-sdk --bundle src/index.js --outfile=dist/index.js --minify --sourcemap --target=es2020", "watch:local": "esbuild --platform=node --bundle src/local.ts --outfile=dist/local.js --sourcemap --target=es2020 --watch", "watch:lambda": "esbuild --platform=node --external:@aws-sdk --bundle src/index.js --outfile=dist/index.js --sourcemap --target=es2020 --watch", "dev": "run-p dev:serve watch:*", "dev:serve": "nodemon dist/local.js", "postbuild": "cd dist && zip -r index.zip index.js"}, "author": "", "license": "ISC", "dependencies": {"@braintrust/proxy": "workspace:*", "@supabase/supabase-js": "^2.32.0", "ai": "2.2.22", "aws-lambda": "^1.0.7", "axios": "^1.9.0", "binary-search": "^1.3.6", "combined-stream": "^1.0.8", "cors": "^2.8.5", "dotenv": "^16.3.1", "esbuild": "^0.19.9", "eventsource-parser": "^1.1.1", "express": "^4.19.2", "openai": "^4.42.0", "redis": "^4.6.8"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/combined-stream": "^1.0.3", "@types/cors": "^2.8.13", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.17", "@types/node": "^20.5.0", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "typescript": "^5.0.4"}}