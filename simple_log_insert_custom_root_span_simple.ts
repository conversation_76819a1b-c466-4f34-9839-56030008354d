#!/usr/bin/env tsx

/**
 * Simple script to insert a log into a Braintrust project using the SDK with a custom root span ID.
 * 
 * This is a minimal example showing how to:
 * 1. Initialize a logger for a project
 * 2. Create a span with a custom root span ID
 * 3. Insert a single log entry
 * 4. Flush the log to ensure it's sent to the server
 * 
 * Usage:
 *     npx tsx simple_log_insert_custom_root_span_simple.ts
 * 
 * Make sure to set your BRAINTRUST_API_KEY environment variable.
 */

import { initLogger } from "braintrust";

async function main() {
  // Initialize logger for a project (creates project if it doesn't exist)
  const logger = initLogger({ projectName: "pedro-repro4667" });
  
  // Define our custom root span ID
  const customRootSpanId = "my-custom-trace-2024-01-15";
  
  // Create a span with custom root span ID and log data
  const span = logger.startSpan({
    name: "custom_root_span",
    spanId: customRootSpanId, // For root spans, this becomes both spanId and rootSpanId
    event: {
      input: "What is the weather like today?",
      output: "I don't have access to real-time weather data.",
      scores: { helpfulness: 0.3, accuracy: 1.0 },
      metadata: {
        model: "example-model",
        timestamp: "2024-01-15T10:30:00Z",
        user_id: "user123"
      },
      tags: ["weather", "information_request"]
    }
  });
  
  console.log(`Successfully logged entry with custom root span ID: ${customRootSpanId}`);
  console.log(`Span ID: ${span.spanId}`);
  console.log(`Root Span ID: ${span.rootSpanId}`);
  console.log(`Row ID: ${span.id}`);
  
  // End the span
  span.end();
  
  // Flush to ensure the log is sent to the server
  await logger.flush();
  console.log("Log has been sent to Braintrust server");
}

// Run the main function and handle any errors
main().catch((error) => {
  console.error("Error running script:", error);
  process.exit(1);
});
