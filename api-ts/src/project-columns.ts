import { Request, Response } from "express";
import { objectNullish, parseNoStrip } from "@braintrust/core";
import { getRequestContext, RequestContext } from "./request_context";
import {
  AccessDeniedError,
  BadRequestError,
  BT_ENABLE_AUDIT_HEADER,
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  getWasCachedToken,
  parseUrlBool,
  wrapZodError,
} from "./util";
import { z } from "zod";
import {
  customColumnSchema,
  CustomColumn,
  AclObjectType,
} from "@braintrust/core/typespecs";
import { join, sql } from "@braintrust/btql/planner";
import { getPG } from "./db/pg";
import { OBJECT_CACHE } from "./object_cache";
import { parseHeader } from "./auth-header";
import { setAuditHeaders } from "./audit";
import { objectCacheEntryToAuditObject } from "./object_cache";
import { flushSummaryCache } from "./summary";

const customColumnReadSchema = z.object({
  ids: z.union([z.string(), z.array(z.string())]),
  object_type: customColumnSchema.shape.object_type.nullish(),
  object_id: customColumnSchema.shape.object_id.nullish(),
  subtype: customColumnSchema.shape.subtype.nullish(),
  column_name: customColumnSchema.shape.name.nullish(),
});

const customColumnWriteSchema = customColumnSchema.omit({
  id: true,
  created: true,
});

const checkProjectPermissions = async ({
  req,
  res,
  ctx,
  objectId,
  objectType,
  restrictObjectType,
  permission,
}: {
  req: Request;
  res: Response;
  ctx: RequestContext;
  objectId: string;
  objectType: AclObjectType;
  restrictObjectType?: AclObjectType;
  permission: "read" | "update" | "delete";
}): Promise<{ projectId: string }> => {
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    aclObjectType: objectType,
    overrideRestrictObjectType: restrictObjectType,
    objectId,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  if (!permissions.permissions.includes(permission)) {
    throw new AccessDeniedError({
      permission,
      aclObjectType: objectType,
      overrideRestrictObjectType: restrictObjectType,
      objectId,
    });
  }

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: [objectCacheEntryToAuditObject(permissions)],
    });
  }

  if (permissions.acl_object_type === "project") {
    return {
      projectId: permissions.object_id,
    };
  }

  const project = permissions.parent_cols.get("project");
  if (!project) {
    throw new BadRequestError(
      `Object ${permissions.object_id} of type ${permissions.acl_object_type} has no project`,
    );
  }

  return {
    projectId: project.id,
  };
};

export async function v1CustomColumnReadHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    parseNoStrip(objectNullish(customColumnReadSchema), ctx.data),
  );

  if (!params.object_type || !params.object_id) {
    throw new BadRequestError("Must specify both object_type and object_id");
  }

  await checkProjectPermissions({
    req,
    res,
    ctx,
    objectType: params.object_type,
    objectId: params.object_id,
    restrictObjectType: params.subtype || undefined,
    permission: "read",
  });

  const columns = await loadCustomColumnsUnchecked({
    ids: params.ids
      ? Array.isArray(params.ids)
        ? params.ids
        : [params.ids]
      : undefined,
    objectId: params.object_id,
    objectType: params.object_type,
    subtype: params.subtype || undefined,
    columnName: params.column_name ?? undefined,
  });
  res.json({
    objects: columns,
  });
}

export async function v1CustomColumnWriteHandler(req: Request, res: Response) {
  if (!(req.method === "POST" || req.method === "PUT")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  const update = req.method !== "POST";
  const params = wrapZodError(() =>
    parseNoStrip(customColumnWriteSchema, ctx.data),
  );

  const { projectId } = await checkProjectPermissions({
    req,
    res,
    ctx,
    objectType: params.object_type,
    objectId: params.object_id,
    restrictObjectType: params.subtype || undefined,
    permission: "update",
  });

  const insertedRow = await saveCustomColumn({
    columnName: params.name,
    columnExpr: params.expr,
    objectType: params.object_type,
    objectId: params.object_id,
    subtype: params.subtype,
    ignoreExisting: !update,
  });

  // Do this synchronously so that future reads will see the new column
  await flushSummaryCache({ projectId });

  res.json(insertedRow);
}

export async function v1CustomColumnIdWriteHandler(
  req: Request,
  res: Response,
) {
  if (!(req.method === "DELETE" || req.method === "PATCH")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  const columns = await loadCustomColumnsUnchecked({
    ids: [req.params.id],
  });
  const permission = req.method === "DELETE" ? "delete" : "update";
  if (columns.length === 0) {
    throw new AccessDeniedError({
      permission,
      objectId: req.params.id,
      objectType: "column",
    });
  }

  const column = columns[0];

  const { projectId } = await checkProjectPermissions({
    req,
    res,
    ctx,
    objectType: column.object_type,
    objectId: column.object_id,
    restrictObjectType: column.subtype || undefined,
    permission,
  });

  let row: CustomColumn;
  if (req.method === "DELETE") {
    const deletedColumn = await deleteCustomColumn({
      columnId: req.params.id,
    });
    if (!deletedColumn) {
      throw new AccessDeniedError({
        permission,
        objectId: req.params.id,
        objectType: "column",
      });
    }
    row = deletedColumn;
  } else {
    const nameAndExpr = wrapZodError(() =>
      parseNoStrip(
        objectNullish(
          z.object({
            name: z.string(),
            expr: z.string(),
          }),
        ),
        ctx.data,
      ),
    );
    if (!nameAndExpr.name && !nameAndExpr.expr) {
      row = column;
    } else {
      const updatedRow = await updateCustomColumn({
        columnId: req.params.id,
        columnName: nameAndExpr.name,
        columnExpr: nameAndExpr.expr,
      });
      if (!updatedRow) {
        throw new AccessDeniedError({
          permission: "update",
          objectId: req.params.id,
          objectType: "column",
        });
      }
      row = updatedRow;
    }
  }

  await flushSummaryCache({ projectId });

  res.json(row);
}

function rowToCustomColumn(row: Record<string, unknown>): CustomColumn {
  const { column_name, column_expr, created_at, ...rest } = row;
  return customColumnSchema.parse({
    ...rest,
    name: column_name,
    expr: column_expr,
    created: created_at,
  });
}

export async function loadCustomColumnsUnchecked({
  ids,
  objectType,
  objectId,
  subtype,
  columnName,
}: {
  ids?: string[];
  objectType?: AclObjectType;
  objectId?: string | null;
  subtype?: AclObjectType;
  columnName?: string;
}): Promise<CustomColumn[]> {
  if (!objectType && !objectId && !subtype && !(ids && ids.length > 0)) {
    throw new Error("No scope provided");
  }

  const scopeFilters =
    objectType && objectId
      ? sql`AND (
      FALSE
      OR (${sql`object_type = ${objectType}`}
      AND ${sql`object_id = ${objectId}`}
      ${subtype ? sql` AND subtype = ${subtype}` : sql``}))`
      : sql``;

  const whereClause = sql`WHERE
        TRUE
        ${scopeFilters}
        ${columnName ? sql`AND column_name = ${columnName}` : sql``}
        ${
          ids && ids.length > 0
            ? sql`AND id IN (${join(
                ids.map((id) => sql`${id}`),
                ", ",
              )})`
            : sql``
        }
  `;

  const pg = getPG();
  const query = sql`
  SELECT * FROM project_columns ${whereClause}
  `;
  const { query: queryText, params } = query.toNumericParamQuery();

  const { rows } = await pg.query(queryText, params);
  return rows.map((row) => rowToCustomColumn(row));
}

async function saveCustomColumn({
  columnName,
  columnExpr,
  objectType,
  objectId,
  subtype,
  ignoreExisting,
}: {
  columnName: string;
  columnExpr: string;
  objectType: AclObjectType;
  objectId: string;
  subtype: AclObjectType | null;
  ignoreExisting?: boolean;
}): Promise<CustomColumn> {
  const pg = getPG();
  const query = sql`
    WITH upsert AS (
      INSERT INTO project_columns (column_name, column_expr, object_type, object_id${subtype ? sql`, subtype` : sql``})
      VALUES (${columnName}, ${columnExpr}, ${objectType}, ${objectId}${subtype ? sql`, ${subtype}` : sql``})
      ON CONFLICT (column_name, object_type, object_id, subtype)
        ${ignoreExisting ? sql`DO NOTHING` : sql`DO UPDATE SET column_expr = ${columnExpr}`}
      RETURNING *
    )
    SELECT * FROM upsert
    UNION ALL
    SELECT * FROM project_columns
    WHERE column_name = ${columnName} AND object_type = ${objectType} AND object_id = ${objectId}${subtype ? sql` AND subtype = ${subtype}` : sql``}
      AND NOT EXISTS (SELECT 1 FROM upsert)
    LIMIT 1
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);
  return rowToCustomColumn(rows[0]);
}

async function updateCustomColumn({
  columnId,
  columnName,
  columnExpr,
}: {
  columnId: string;
  columnName?: string | null;
  columnExpr?: string | null;
}): Promise<CustomColumn | undefined> {
  const clauses = [
    ...(columnName ? [sql`column_name = ${columnName}`] : []),
    ...(columnExpr ? [sql`column_expr = ${columnExpr}`] : []),
  ];

  if (clauses.length === 0) {
    throw new BadRequestError("Must specify either name or expression");
  }

  const pg = getPG();
  const query = sql`
    UPDATE project_columns
    SET ${join(clauses, ", ")}
    WHERE id = ${columnId}
    RETURNING *
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);

  if (rows.length === 0) {
    return undefined;
  }
  return rowToCustomColumn(rows[0]);
}

async function deleteCustomColumn({
  columnId,
}: {
  columnId: string;
}): Promise<CustomColumn | undefined> {
  const pg = getPG();
  const query = sql`
    DELETE FROM project_columns WHERE id = ${columnId}
    RETURNING *
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);
  if (rows.length === 0) {
    return undefined;
  }
  return rowToCustomColumn(rows[0]);
}
