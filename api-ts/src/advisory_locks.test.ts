import { expect, test } from "vitest";
import { IS_MERGE_FIELD } from "@braintrust/core";
import { computeAdvisoryLockIds } from "./advisory_locks";

test("advisory locks basic", () => {
  const rows = [
    { id: "x", [IS_MERGE_FIELD]: true },
    { id: "x", [IS_MERGE_FIELD]: true },
    { id: "x", [IS_MERGE_FIELD]: true },
    { id: "q", [IS_MERGE_FIELD]: false },
    { id: "z", [IS_MERGE_FIELD]: true },
  ];

  const lockIds = computeAdvisoryLockIds({
    rows,
    minLockId: 0,
    maxLockId: 1000,
  });
  expect(lockIds.length).toEqual(2);
  expect(lockIds[0]).lessThan(lockIds[1]);
});

test("advisory locks max num locks", () => {
  const rows = Array.from({ length: 10000 }, (_, i) => ({
    id: i.toString(),
    [IS_MERGE_FIELD]: true,
  }));
  // Make sure we get a double-digit number in there to make sure the sorting is
  // not purely lexicographic.
  const lockIds = computeAdvisoryLockIds({ rows, minLockId: 0, maxLockId: 11 });
  expect(lockIds).toEqual(Array.from({ length: 11 }, (_, i) => i));
});
