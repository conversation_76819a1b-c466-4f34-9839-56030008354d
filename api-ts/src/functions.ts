import { z } from "zod";
import {
  InternalServerError,
  NotFoundError,
  normalizeXact,
  postDefaultHeaders,
  objectTypeToAclObjectType,
  AccessDeniedError,
  HTTPError,
  wrapZodError,
} from "./util";
import { customFetchRequest } from "./custom_fetch";
import {
  FunctionId,
  promptSessionEventSchema as promptSessionEventSchemaLax,
  FunctionObject,
  functionSchema,
  promptDataSchema,
  functionDataSchema,
} from "@braintrust/core/typespecs";
import { InvocableFunction } from "@braintrust/local/functions";
import { runBtql } from "./btql";
import { OBJECT_CACHE } from "./object_cache";
import {
  useBundledFunctionInvokeMethod,
  useInlineFunctionInvokeMethod,
} from "./proxy/invoke-code";
import { checkTokenAuthorized } from "./token_auth";
import { loadFunctionSecrets } from "./function-secrets";
import { capitalize } from "@braintrust/core";
import { getLogger } from "./instrumentation/logger";

export async function useFunction({
  body,
  appOrigin,
  ctxToken,
  orgName,
}: {
  body: FunctionId;
  appOrigin: string;
  ctxToken: string | undefined;
  orgName: string | undefined;
}): Promise<InvocableFunction> {
  if ("global_function" in body) {
    await checkTokenAuthorized({
      ctxToken,
      appOrigin,
    });

    return {
      id: body.global_function,
      slug: body.global_function,
      project_id: "",
      org_id: "",
      _xact_id: "",
      name: body.global_function,
      function_data: {
        type: "global",
        name: body.global_function,
      },
      function_type: "scorer",
    };
  }

  if ("prompt_session_id" in body) {
    return loadPromptSessionMetadata({
      promptSessionId: body.prompt_session_id,
      rowId: body.prompt_session_function_id,
      version: body.version,
      appOrigin,
      ctxToken,
    });
  }

  if ("inline_context" in body) {
    return {
      id: `inline_code_${body.inline_context.runtime}_${body.inline_context.version}`,
      name:
        body.name ??
        `${capitalize(body.inline_context.runtime)} ${
          body.inline_context.version
        } function`,
      slug: `inline_code_${body.inline_context.runtime}_${body.inline_context.version}`,
      project_id: "",
      org_id: "",
      _xact_id: "",
      invoke_method: await useInlineFunctionInvokeMethod({
        runtime: body.inline_context,
        ctxToken,
        appOrigin,
        orgName,
      }),
      function_data: {
        type: "code",
        data: {
          type: "inline",
          runtime_context: body.inline_context,
          code: body.code,
        },
      },
    };
  }

  if ("inline_prompt" in body || "inline_function" in body) {
    if ("inline_prompt" in body && body.inline_prompt?.prompt) {
      return {
        id: `inline_prompt_${body.inline_prompt.prompt?.type}`,
        name: body.name ?? `Prompt`,
        slug: `inline_prompt_${body.inline_prompt.prompt?.type}`,
        project_id: "",
        org_id: "",
        _xact_id: "",
        prompt_data: body.inline_prompt,
        function_data: {
          type: "prompt",
        },
        function_type: body.function_type,
      };
    }

    if (!("inline_function" in body) || !body.inline_function) {
      // This should never happen, but Typescript is lost.
      throw new InternalServerError(
        "Neither inline prompt nor inline function provided",
      );
    }

    const inlineFunction = wrapZodError(() =>
      functionDataSchema.parse(body.inline_function),
    );

    return {
      id: `inline_function_${inlineFunction.type}`,
      name: body.name ?? `Function`,
      slug: `inline_function_${inlineFunction.type}`,
      project_id: "",
      org_id: "",
      _xact_id: "",
      prompt_data: body.inline_prompt,
      function_data: inlineFunction,
    };
  }

  const functionMetadata = await loadFunctionMetadata({
    functionId: body,
    version: body.version,
    appOrigin,
    ctxToken,
  });

  const envSecrets = await loadFunctionSecrets({
    functionId: functionMetadata.id,
    projectId: functionMetadata.project_id,
    orgId: functionMetadata.org_id,
  });

  switch (functionMetadata.function_data.type) {
    case "prompt":
      return functionMetadata;
    case "code":
      let invoke_method: InvocableFunction["invoke_method"];
      if (functionMetadata.function_data.data.type === "inline") {
        invoke_method = await useInlineFunctionInvokeMethod({
          runtime: functionMetadata.function_data.data.runtime_context,
          ctxToken,
          appOrigin,
          orgName,
        });
      } else {
        invoke_method = await useBundledFunctionInvokeMethod({
          codeBundle: functionMetadata.function_data.data,
        });
      }
      return {
        ...functionMetadata,
        env_secrets: envSecrets,
        invoke_method,
      };
    case "graph":
      return {
        ...functionMetadata,
        env_secrets: envSecrets,
      };
    case "global":
      throw new Error("Global functions should have been handled above");
    case "remote_eval":
      throw new Error("Remote evals should not be executed directly (in use)");
    default:
      const _: never = functionMetadata.function_data;
      throw new Error(`Unsupported function type ${_}`);
  }
}

const functionSchemaDefaultPrompt = functionSchema
  .omit({ function_data: true })
  .merge(
    z.strictObject({
      function_data: functionSchema.shape.function_data.nullish(),
    }),
  );

async function loadFunctionMetadata({
  functionId,
  version,
  appOrigin,
  ctxToken,
}: {
  functionId: Extract<
    FunctionId,
    { function_id: string } | { project_name: string; slug: string }
  >;
  version: string | undefined;
  appOrigin: string;
  ctxToken: string | undefined;
}): Promise<FunctionObject> {
  // NOTE, this deviates from objectTypeToAclObjectType, since there we are checking
  // access to the "project_prompts" table, but here we are checking access to an individual
  // prompt id.
  const headers = postDefaultHeaders({ token: ctxToken });
  const response = await customFetchRequest(`${appOrigin}/api/prompt/get`, {
    method: "POST",
    headers,
    body: JSON.stringify(
      "function_id" in functionId
        ? { ids: [functionId.function_id] }
        : getFunctionIdForObjectMetadata(functionId),
    ),
  });

  if (!response.ok) {
    throw new HTTPError(
      response.statusCode,
      `Failed to get function metadata: ${await response.body.text()}`,
    );
  }

  const promptMetadataRows = z
    .array(z.strictObject({ id: z.string(), project_id: z.string() }).strip())
    .parse(await response.body.json());

  if (promptMetadataRows.length === 0) {
    throw new NotFoundError(
      `Function not found (searching for ${redactFunctionId(functionId)} in metadata)`,
    );
  }

  const functionMetadata = promptMetadataRows[0];

  const functionInfo = await runBtql({
    body: {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: ["project_functions"],
          },
          args: [{ op: "literal", value: functionMetadata.project_id }],
        },
        select: [{ op: "star" }],
        filter: {
          op: "eq",
          left: { op: "ident", name: ["id"] },
          right: { op: "literal", value: functionMetadata.id },
        },
      },
      version: version ? normalizeXact(version) : undefined,
    },
    appOrigin: appOrigin,
    ctxToken,
    skipAclCheck: true,
  });

  if ("explain" in functionInfo) {
    throw new InternalServerError("Unexpected explain result");
  }

  if (functionInfo.rows.length === 0) {
    throw new NotFoundError(
      `Function not found (searching for ${redactFunctionId(functionId)} in data plane)`,
    );
  } else if (functionInfo.rows.length > 1) {
    throw new InternalServerError("Function has multiple entries");
  }

  const functionData = functionSchemaDefaultPrompt.safeParse(
    functionInfo.rows[0],
  );

  if (!functionData.success) {
    getLogger().error(
      {
        error: functionData.error.errors,
      },
      "Failed to parse function metadata",
    );
    throw new InternalServerError("Failed to parse function metadata");
  }

  return {
    ...functionData.data,
    function_data: functionData.data.function_data ?? { type: "prompt" },
  };
}

function getFunctionIdForObjectMetadata<
  T extends Extract<
    FunctionId,
    { function_id: string } | { project_name: string; slug: string }
  >,
>(functionId: T): FunctionId {
  if ("function_id" in functionId) {
    return { function_id: functionId.function_id, version: functionId.version };
  } else {
    return {
      project_name: functionId.project_name,
      slug: functionId.slug,
      version: functionId.version,
    };
  }
}

const promptSessionEventSchemaStrict = promptSessionEventSchemaLax
  .omit({ prompt_data: true })
  .extend({
    prompt_data: promptDataSchema.nullish(),
    function_data: functionDataSchema
      .nullish()
      .transform((data) => data ?? ({ type: "prompt" } as const)),
  });

const _promptSessionFunctionSchema = promptSessionEventSchemaStrict.extend({
  slug: z.string(),
  name: z.string(),
  org_id: z.string(),
  function_data: functionSchema.shape.function_data.default({
    type: "prompt",
  }),
  function_type: functionSchema.shape.function_type,
});
export type PromptSessionFunction = z.infer<
  typeof _promptSessionFunctionSchema
>;

async function loadPromptSessionMetadata({
  promptSessionId,
  rowId,
  version,
  appOrigin,
  ctxToken,
}: {
  promptSessionId: string;
  rowId: string;
  version: string | undefined;
  appOrigin: string;
  ctxToken: string | undefined;
}): Promise<PromptSessionFunction> {
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType("prompt_session");
  const aclCheck = await OBJECT_CACHE.checkAndGet({
    appOrigin: appOrigin,
    authToken: z.string().nullish().parse(ctxToken) ?? undefined,
    aclObjectType,
    overrideRestrictObjectType,
    objectId: promptSessionId,
  });

  if (!aclCheck || !aclCheck.permissions.includes("read")) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType,
      overrideRestrictObjectType,
      objectId: promptSessionId,
    });
  }

  const functionInfo = await runBtql({
    body: {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: ["prompt_session"],
          },
          args: [{ op: "literal", value: promptSessionId }],
        },
        select: [{ op: "star" }],
        filter: {
          op: "eq",
          left: { op: "ident", name: ["id"] },
          right: { op: "literal", value: rowId },
        },
      },
      version: version ? normalizeXact(version) : undefined,
    },
    appOrigin: appOrigin,
    ctxToken,
    skipAclCheck: true,
  });

  if ("explain" in functionInfo) {
    throw new InternalServerError("Unexpected explain result");
  }

  if (functionInfo.rows.length === 0) {
    throw new NotFoundError("Playground function not found");
  } else if (functionInfo.rows.length > 1) {
    throw new InternalServerError("Playground function has multiple entries");
  }

  const functionData = promptSessionEventSchemaStrict.safeParse(
    functionInfo.rows[0],
  );

  if (!functionData.success) {
    getLogger().error(
      {
        error: functionData.error.errors,
      },
      "Failed to parse prompt session metadata",
    );
    throw new InternalServerError("Failed to parse prompt session metadata");
  }

  const organization = aclCheck.parent_cols.get("organization");
  if (!organization) {
    throw new Error("Function must have an organization");
  }
  return {
    slug: functionData.data.id,
    name: functionData.data.id,
    org_id: organization.id,
    ...functionData.data,
  };
}

function redactFunctionId(functionId: FunctionId): string {
  if ("inline_prompt" in functionId) {
    return "<redacted: inline prompt>";
  }
  if ("inline_function" in functionId) {
    return "<redacted: inline function>";
  }
  if ("inline_context" in functionId) {
    return "<redacted: inline code>";
  }
  return JSON.stringify(functionId);
}
