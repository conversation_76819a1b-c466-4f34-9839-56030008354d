// base OTEL boilerplate
import { NodeSDK } from "@opentelemetry/sdk-node";
import { getNodeAutoInstrumentations } from "@opentelemetry/auto-instrumentations-node";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import {
  AggregationTemporalityPreference,
  OTLPMetricExporter,
} from "@opentelemetry/exporter-metrics-otlp-http";
import {
  BatchSpanProcessor,
  SimpleSpanProcessor,
  ConsoleSpanExporter,
  ReadableSpan,
  SpanProcessor,
} from "@opentelemetry/sdk-trace-base";
import { ExportResult } from "@opentelemetry/core";
import {
  trace,
  context,
  Span,
  Tracer,
  Context,
  baggageEntryMetadataFromString,
  propagation,
  metrics,
  Attributes,
  Meter,
  Counter,
  Histogram,
} from "@opentelemetry/api";
import { z } from "zod";
import { _urljoin } from "@braintrust/core";

// OTEL integrations
import { HttpInstrumentation } from "@opentelemetry/instrumentation-http";
import { ExpressInstrumentation } from "@opentelemetry/instrumentation-express";

// Relative imports
import {
  DEPLOYMENT_MODE,
  GIT_COMMIT,
  OTLP_HTTP_ENDPOINT,
  OTLP_METRICS_EXPORT_INTERVAL_MS,
} from "../env";
import { getLogger } from "./logger";
import { PeriodicExportingMetricReader } from "@opentelemetry/sdk-metrics";
import {
  LogCounterFn,
  LogHistogramFn,
  setLogCounterFn,
  setLogHistogramFn,
} from "./api";

const IS_DATADOG_LAMBDA_MODE =
  DEPLOYMENT_MODE === "lambda" && process.env.DD_API_KEY;

// Custom console exporter with formatting
class PrettyConsoleSpanExporter extends ConsoleSpanExporter {
  export(
    spans: ReadableSpan[],
    resultCallback: (result: ExportResult) => void,
  ) {
    for (const span of spans) {
      const startTime = new Date(
        span.startTime[0] * 1000 + span.startTime[1] / 1e6,
      ).toISOString();
      const duration = (
        span.duration[0] * 1000 +
        span.duration[1] / 1e6
      ).toFixed(2);

      const logger = getLogger();

      const loggerMethod =
        span.status.code === 1
          ? logger.error
          : span.status.code === 2
            ? logger.warn
            : logger.info;

      if (span.status.code === 0) {
        // TODO: Allow showing all spans (including info spans) with an env var
        return;
      }

      loggerMethod.bind(logger)(
        {
          traceId: span.spanContext().traceId,
          spanId: span.spanContext().spanId,
          attributes: span.attributes,
          events: span.events,
          startTime,
          durationMs: duration,
        },
        span.name,
      );
    }

    resultCallback({ code: 0 });
  }
}

class RootSpanAttributeProcessor implements SpanProcessor {
  onStart(span: Span, parentContext: Context): void {
    if (!trace.getSpan(parentContext)) {
      if (GIT_COMMIT) {
        span.setAttribute("commit", GIT_COMMIT);
      }
    }
  }
  onEnd(span: ReadableSpan): void {}
  shutdown(): Promise<void> {
    return Promise.resolve();
  }
  forceFlush(): Promise<void> {
    return Promise.resolve();
  }
}

const otelLogCounter: LogCounterFn = ({ name, value, attributes }) => {
  const counter = getCounter(name);
  counter.add(value, attributes);
};

const otelLogHistogram: LogHistogramFn = ({ name, value, attributes }) => {
  const histogram = getHistogram(name);
  histogram.record(value, attributes);
};

export function setupInstrumentation() {
  if (DEPLOYMENT_MODE === "lambda") {
    throw new Error(
      "OTEL provider is not supported in lambda mode. Must use a 3rd party provider like Datadog.",
    );
  }

  // Create exporters
  const prettyConsoleExporter = new PrettyConsoleSpanExporter();

  const spanProcessors: SpanProcessor[] = [
    new RootSpanAttributeProcessor(),
    new SimpleSpanProcessor(prettyConsoleExporter),
  ];
  let metricReader: PeriodicExportingMetricReader | undefined = undefined;
  if (OTLP_HTTP_ENDPOINT) {
    const otlpExporter = new OTLPTraceExporter({
      url: _urljoin(OTLP_HTTP_ENDPOINT, "/v1/traces"),
      headers: {}, // Add any headers if needed
    });
    spanProcessors.push(new BatchSpanProcessor(otlpExporter));

    metricReader = new PeriodicExportingMetricReader({
      exporter: new OTLPMetricExporter({
        url: _urljoin(OTLP_HTTP_ENDPOINT, "/v1/metrics"),
        headers: {}, // Add any headers if needed
        temporalityPreference: AggregationTemporalityPreference.DELTA, // Required for Datadog
      }),
      exportIntervalMillis: OTLP_METRICS_EXPORT_INTERVAL_MS,
    });
  }

  const sdk = new NodeSDK({
    serviceName: "braintrust-api",
    spanProcessors,
    metricReader,
    instrumentations: [
      getNodeAutoInstrumentations(),
      new HttpInstrumentation(),
      new ExpressInstrumentation(),
    ],
  });

  setLogCounterFn(otelLogCounter);
  setLogHistogramFn(otelLogHistogram);

  sdk.start();
  return sdk;
}

// Get the current active span
export function getTracer(): Tracer {
  return trace.getTracer("braintrust-api");
}

export function getCurrentSpan(): Span | undefined {
  return trace.getSpan(context.active());
}

export const baggageSchema = z.object({
  traceparent: z.string().optional(),
  // From what I can tell, these do not actually get set (?)
  tracestate: z.string().optional(),
  baggage: z.string().optional(),
});
export type MadeBaggage = z.infer<typeof baggageSchema>;

export function makeBaggage({
  span,
  properties,
}: {
  span: Span;
  properties?: Record<string, string>;
}): MadeBaggage {
  const baggage = propagation.createBaggage(
    Object.fromEntries(
      Object.entries(properties ?? {}).map(([key, value]) => [
        key,
        { value, metadata: baggageEntryMetadataFromString("") },
      ]),
    ),
  );

  const ctxWithBaggage = trace.setSpan(context.active(), span);
  propagation.setBaggage(ctxWithBaggage, baggage);

  const carrier: Record<string, string> = {};
  propagation.inject(ctxWithBaggage, carrier);

  const ret = baggageSchema.parse(carrier);
  if (IS_DATADOG_LAMBDA_MODE && ret.traceparent) {
    // Infuriatingly, datadog's OTEL "mirage" NEVER marks spans as recording. So we need to
    // manually update the traceparent to include the sampling decision.
    const parts = ret.traceparent.split("-");
    if (parts.length === 4 && parts[3] === "00") {
      ret.traceparent = `${parts[0]}-${parts[1]}-${parts[2]}-01`;
    } else {
      getLogger().warn(
        {
          traceparent: ret.traceparent,
        },
        "Datadog traceparent is malformed. Expect 4 parts and the last part to be 00 (unsampled). Got",
      );
    }
  }
  return ret;
}

// This must be done in module scope because it has to run before express initializes
let otelSdk: NodeSDK | undefined = undefined;
if (DEPLOYMENT_MODE === "lambda") {
  if (IS_DATADOG_LAMBDA_MODE) {
    const ddTrace = require("dd-trace");
    if (!ddTrace) {
      throw new Error("dd-trace is not installed");
    }
    const tracer = ddTrace.init({
      service: "braintrust-api",
      version: GIT_COMMIT,
    });
    const { TracerProvider } = tracer;
    const provider = new TracerProvider();
    provider.register();
  } else {
    // NOTE: We could, but do not currently, setup raw OTEL for other cases.
  }
} else {
  otelSdk = setupInstrumentation();
}

export async function shutdownTracer() {
  if (otelSdk) {
    const start = Date.now();
    await otelSdk.shutdown();
    getLogger().info("Shutdown tracer in", Date.now() - start, "ms");
  }
}

let meter: Meter | undefined = undefined;
function getMeter(): Meter {
  if (!meter) {
    meter = metrics.getMeter("braintrust-api");
  }
  return meter;
}

const counterCache: Record<string, Counter<Attributes>> = {};
function getCounter(name: string): Counter<Attributes> {
  if (!counterCache[name]) {
    counterCache[name] = getMeter().createCounter(name);
  }
  return counterCache[name];
}

const histogramCache: Record<string, Histogram<Attributes>> = {};
function getHistogram(name: string): Histogram<Attributes> {
  if (!histogramCache[name]) {
    histogramCache[name] = getMeter().createHistogram(name);
  }
  return histogramCache[name];
}
