// Import this in case we're running in the automation worker and do not have it initialized.
import "../db/duckdb-conn";

import {
  BadRequestError,
  extractErrorText,
  InternalServerError,
} from "../util";
import {
  buildParquet,
  runBtql,
  saveFileToObjectStore,
  writeRowsToStream,
} from "../btql";
import { PassThrough, Readable } from "stream";
import { createGzip } from "zlib";
import { createReadStream } from "fs";
import * as fs from "fs/promises";
import { sql } from "@braintrust/btql/planner";
import {
  btqlExportAutomationConfigSchema,
  ProjectAutomation,
} from "@braintrust/core/typespecs";
import { otelTraced, otelWrapTraced } from "../instrumentation/api";
import { getLogger } from "../instrumentation/logger";
import {
  AssumeRoleCommand,
  AssumeRoleResponse,
  STSClient,
} from "@aws-sdk/client-sts";
import { DeleteObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { z } from "zod";
import {
  BtqlExportJobState,
  makeExternalId,
} from "@braintrust/local/api-schema";
import { type TestAutomationResponse } from "../automations";
import { parseObjectStoreURL } from "../object-storage/url";
import { IS_RESPONSE_BUCKET_LOCAL } from "../env";
import { getS3Client } from "../object-storage/s3";
import { S3ObjectStore } from "../object-storage/object-storage";

const AUTOMATION_BATCH_SIZE = 1000;
const MAX_ITERS = 100;

export const triggerBtqlExport = otelWrapTraced(
  "triggerBtqlExport",
  async function triggerBtqlExport({
    appOrigin,
    orgId,
    automation,
    serviceToken,
    cron,
    iterations,
  }: {
    appOrigin: string;
    orgId: string;
    automation: ProjectAutomation;
    serviceToken: string | undefined;
    cron: BtqlExportJobState;
    iterations?: number;
  }): Promise<BtqlExportJobState> {
    if (automation.config.event_type !== "btql_export") {
      throw new BadRequestError(
        `Automation ${automation.id} is not a BTQL export automation`,
      );
    }

    const pino = getLogger();
    const response: BtqlExportJobState = {
      type: "btql_export",
      cursor: cron.cursor,
      last_execution: {
        rows: 0,
        bytes: 0,
        files: [],
        error: undefined,
        duration_ms: 0,
      },
      all_executions: {
        rows: cron.all_executions.rows,
        bytes: cron.all_executions.bytes,
        duration_ms: cron.all_executions.duration_ms,
      },
    };

    const awsCredentials = automation.config.credentials;

    let client: S3Client;
    try {
      client = await getAssumedS3Client({
        orgId,
        projectId: automation.project_id,
        automationId: automation.id,
        awsCredentials,
      });
    } catch (e) {
      response.last_execution.error = e instanceof Error ? e.message : `${e}`;
      return response;
    }

    const objectStore = new S3ObjectStore(client);

    const config = automation.config;

    iterations = Math.min(iterations ?? MAX_ITERS, MAX_ITERS);
    const batchSize = config.batch_size ?? AUTOMATION_BATCH_SIZE;
    const startTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      const continueLoop = await otelTraced(
        "export_iteration",
        async (span) => {
          span.setAttributes({
            "automation.id": automation.id,
            "btql_export.iteration": i,
            "btql.export.batch_size": batchSize,
          });

          let cleanup = () => {};
          try {
            const result = await otelTraced("runBtql", async (span) => {
              span.setAttributes({
                "automation.id": automation.id,
                "btql_export.cursor": response.cursor ?? "<empty>",
                "btql_export.iteration": i,
                "btql.export.batch_size": batchSize,
              });
              return await runBtql({
                body: {
                  query:
                    makeBtqlQuery(
                      config.export_definition,
                      automation.project_id,
                    ) +
                    ` sort: _xact_id asc | limit: ${batchSize}` +
                    (response.cursor
                      ? sql` | cursor: ${response.cursor}`.toPlainStringQuery()
                      : ""),
                  use_brainstore: true,
                  brainstore_realtime: true,
                },
                skipAclCheck: false,
                appOrigin,
                ctxToken: serviceToken,
              });
            });

            if ("explain" in result) {
              throw new InternalServerError(
                "Only JSONL and Parquet are supported for BTQL exports",
              );
            }

            const { rows, resultSchema, duckdbSchema, cursor } = result;
            if (rows.length === 0) {
              // When there are no rows, we return cursor: None, so break and return the current state
              // rather than errantly overwriting the cursor.
              return false;
            }

            // This is a mini version of the logic in runBtqlRequest. We may want to consolidate them at some point
            const { path, byteLen } = await otelTraced(
              "write rows to object storage",
              async (span) => {
                span.setAttributes({
                  "automation.id": automation.id,
                  "btql_export.iteration": i,
                  "btql_export.rows": rows.length,
                  "btql_export.export_path": config.export_path,
                });

                let bytes: Readable;
                let byteLen;
                switch (config.format) {
                  case "jsonl": {
                    let writeStream = new PassThrough();
                    writeStream = writeStream.pipe(createGzip());
                    writeRowsToStream(rows, writeStream).catch((e) => {
                      pino.error(
                        {
                          error: extractErrorText(e),
                          "automation.id": automation.id,
                        },
                        `Failed to write jsonl rows to stream (automation id ${automation.id}): ${e instanceof Error ? e.message : `${e}`}`,
                      );
                    });

                    bytes = writeStream;
                    byteLen =
                      JSON.stringify(rows[0] ?? null).length * rows.length; // Rough estimate
                    break;
                  }
                  case "parquet": {
                    const { path, cleanup: cleanupFile } = await buildParquet(
                      rows,
                      resultSchema,
                      duckdbSchema,
                    );
                    bytes = createReadStream(path);
                    byteLen = (await fs.stat(path)).size;
                    cleanup = cleanupFile;
                    break;
                  }
                  default:
                    const _: never = config.format;
                    throw new InternalServerError(`Unsupported format ${_}`);
                }

                const path = await saveFileToObjectStore({
                  client: objectStore,
                  stream: bytes,
                  isGzip: true,
                  format: config.format,
                  metadata: cursor ? { "bt-cursor": cursor } : undefined,
                  exportPath: config.export_path,
                });
                span.setAttributes({
                  "btql_export.written_path": path,
                  "btql_export.byte_len": byteLen,
                });
                return { path, byteLen };
              },
            );

            response.cursor = cursor;
            response.last_execution.rows += rows.length;
            response.all_executions.rows += rows.length;
            response.last_execution.bytes += byteLen;
            response.all_executions.bytes += byteLen;
            response.last_execution.files.push(path);

            // This should check MAX_ITERS, not iterations, because we want to check if we've reached the acceptable
            // limit for the cron job.
            if (i === MAX_ITERS - 1) {
              response.last_execution.error =
                "Max iterations reached while running the cron job. This may result in the export falling behind.";
            }
            return true;
          } catch (e) {
            if (e instanceof Error) {
              span.recordException(e);
            }
            pino.error(
              {
                error: extractErrorText(e),
                "automation.id": automation.id,
                "btql_export.iteration": i,
                "btql_export.total_iterations": iterations,
                "btql_export.cursor": response.cursor ?? "<empty>",
                "btql.export.batch_size": batchSize,
              },
              `Failed to export BTQL (automation id ${automation.id}): ${extractErrorText(e)}`,
            );
            response.last_execution.error =
              e instanceof Error ? e.message : `${e}`;
            return false;
          } finally {
            cleanup();
          }
        },
      );
      if (!continueLoop) {
        break;
      }
    }
    const durationMs = Date.now() - startTime;
    response.last_execution.duration_ms = durationMs;
    response.all_executions.duration_ms += durationMs;
    return response;
  },
);

function makeBtqlQuery(
  exportDefinition: z.infer<
    typeof btqlExportAutomationConfigSchema.shape.export_definition
  >,
  projectId: string,
) {
  switch (exportDefinition.type) {
    case "log_traces":
      return sql`select: * | from: project_logs(${projectId}) summary | preview_length: -1`.toPlainStringQuery();
    case "log_spans":
      return sql`select: * | from: project_logs(${projectId}) spans`.toPlainStringQuery();
    case "btql_query":
      return exportDefinition.btql_query;
    default:
      const _: never = exportDefinition;
      throw new InternalServerError(`Unsupported export definition type: ${_}`);
  }
}

export const testBtqlExport = otelWrapTraced(
  "testBtqlExport",
  async function testBtqlExport({
    orgId,
    automation,
  }: {
    orgId: string;
    automation: Pick<ProjectAutomation, "config" | "project_id">;
  }): Promise<TestAutomationResponse> {
    if (automation.config.event_type !== "btql_export") {
      throw new BadRequestError(`Automation is not a BTQL export automation`);
    }

    const pino = getLogger();
    const awsCredentials = automation.config.credentials;

    let client: S3Client;
    try {
      client = await getAssumedS3Client({
        orgId,
        projectId: automation.project_id,
        automationId: null,
        awsCredentials,
      });
    } catch (e) {
      return {
        kind: "error",
        message: e instanceof Error ? e.message : `${e}`,
      };
    }

    const objectStore = new S3ObjectStore(client);

    const bytes = new PassThrough();
    await writeRowsToStream([{ a: 1, b: 2 }], bytes);
    let fileName: string | undefined;
    try {
      fileName = await saveFileToObjectStore({
        client: objectStore,
        stream: bytes,
        isGzip: false,
        format: "jsonl",
        metadata: { "bt-cursor": "1" },
        exportPath: automation.config.export_path,
      });
    } catch (e) {
      return {
        kind: "error",
        message: `Failed to write test file to bucket: ${e instanceof Error ? e.message : `${e}`}`,
      };
    }

    if (fileName) {
      try {
        const url = parseObjectStoreURL(fileName);
        await client.send(
          new DeleteObjectCommand({
            Bucket: url.bucket,
            Key: url.key,
          }),
        );
      } catch (e) {
        pino.error(
          {
            error: extractErrorText(e),
          },
          `Failed to delete test file from bucket: ${extractErrorText(e)}`,
        );
        return {
          kind: "error",
          message: `Failed to delete test file from bucket: ${extractErrorText(e)}`,
        };
      }
    }

    return {
      kind: "success",
      payload: {
        tested_file_name: fileName,
      },
    };
  },
);

async function getAssumedS3Client({
  orgId,
  projectId,
  automationId,
  awsCredentials,
}: {
  orgId: string;
  projectId: string;
  automationId: string | null;
  awsCredentials: z.infer<
    typeof btqlExportAutomationConfigSchema.shape.credentials
  >;
}): Promise<S3Client> {
  const pino = getLogger();
  if (IS_RESPONSE_BUCKET_LOCAL) {
    return await getS3Client();
  } else {
    // Create STS client
    const sts = new STSClient({});

    // Assume the role
    let assumeRoleResponse: AssumeRoleResponse;
    try {
      const assumeRoleCommand = new AssumeRoleCommand({
        RoleArn: awsCredentials.role_arn,
        RoleSessionName: "S3AccessSession",
        DurationSeconds: 3600, // 1 hour (adjust as needed)
        ExternalId: makeExternalId({
          orgId,
          projectId,
          automationExtId: awsCredentials.external_id,
        }),
      });

      assumeRoleResponse = await sts.send(assumeRoleCommand);

      if (!assumeRoleResponse.Credentials) {
        throw new Error("Failed to assume role (missing credentials)");
      }
    } catch (e) {
      pino.error(
        {
          error: extractErrorText(e),
          "automation.id": automationId,
        },
        `Failed to assume role (automation id ${automationId}): ${e}`,
      );
      throw new Error(
        `Failed to assume role: ${e instanceof Error ? e.message : `${e}`}`,
      );
    }

    const { AccessKeyId, SecretAccessKey, SessionToken } =
      assumeRoleResponse.Credentials;
    if (!AccessKeyId || !SecretAccessKey) {
      throw new Error("Failed to assume role (incomplete credentials)");
    }

    return new S3Client({
      credentials: {
        accessKeyId: AccessKeyId,
        secretAccessKey: SecretAccessKey,
        sessionToken: SessionToken,
      },
      // This allows us to access buckets in other regions (eg for exports) automatically, without
      // extra code.
      // NOTE: If you refactor this code, it is CRITICAL that you carry along this setting.
      followRegionRedirects: true,
    });
  }
}
