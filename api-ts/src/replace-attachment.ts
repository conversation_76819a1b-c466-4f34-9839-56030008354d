import { AttachmentReference } from "@braintrust/core/typespecs";
import { isArray, isObject } from "@braintrust/core";
import { getBase64Parts, isBase64File } from "@braintrust/local/functions";

export function replacePayloadWithAttachmentsCallback(args: {
  data: Record<string, unknown>;
  replaceWithAttachment: (args: {
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }) => unknown;
  attachments: Record<string, AttachmentReference>;
}): Record<string, unknown> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return replacePayloadWithAttachmentsCallbackInner(args) as Record<
    string,
    unknown
  >;
}

function replacePayloadWithAttachmentsCallbackInner({
  data,
  replaceWithAttachment,
  attachments,
}: {
  data: unknown;
  replaceWithAttachment: (args: {
    data: ArrayBuffer;
    contentType: string;
    filename: string;
  }) => unknown;
  attachments: Record<string, AttachmentReference>;
}): unknown {
  if (isArray(data)) {
    return data.map((item) =>
      replacePayloadWithAttachmentsCallbackInner({
        data: item,
        replaceWithAttachment,
        attachments,
      }),
    );
  } else if (isObject(data)) {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        replacePayloadWithAttachmentsCallbackInner({
          data: value,
          replaceWithAttachment,
          attachments,
        }),
      ]),
    );
  } else if (typeof data === "string") {
    if (attachments[data]) {
      return attachments[data];
    } else if (isBase64File(data, () => true)) {
      const { mimeType, data: arrayBuffer } = getBase64Parts(data);
      const filename = `file.${mimeType.split("/")[1]}`;
      return replaceWithAttachment({
        data: arrayBuffer,
        contentType: mimeType,
        filename,
      });
    } else {
      return data;
    }
  } else {
    return data;
  }
}
