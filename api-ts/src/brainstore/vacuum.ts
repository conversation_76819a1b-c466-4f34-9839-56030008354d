import { Request, Response } from "express";
import { z } from "zod";
import { assertBrainstoreEnabled, runBrainstore } from "./brainstore";
import { authCheckObjectIds } from "./auth";
import { getRequestContext } from "../request_context";
import { wrapZodError } from "../util";
import { globalVacuumStatusSchema } from "@braintrust/local/app-schema";

const brainstoreVacuumStatusRequest = z.object({
  object_ids: z.array(z.string()),
  vacuum_index_stale_seconds: z.number().int().nullish(),
});

export async function getVacuumStatusRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumStatusRequest.parse(ctx.data),
  );
  const objectIds = params.object_ids;

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/status",
      args: {
        object_ids: objectIds,
        vacuum_index_stale_seconds:
          params.vacuum_index_stale_seconds ?? undefined,
      },
      schema: globalVacuumStatusSchema,
      isWrite: true,
    }),
  );
}

const brainstoreVacuumResetStateRequest = z.object({
  object_ids: z.array(z.string()),
  sleep_for_vacuum_index_stale_seconds: z.boolean().nullish(),
});

export async function resetVacuumStateRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumResetStateRequest.parse(ctx.data),
  );
  const objectIds = params.object_ids;

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  await runBrainstore({
    path: "/vacuum/reset_state",
    args: {
      object_ids: objectIds,
      sleep_for_vacuum_index_stale_seconds:
        params.sleep_for_vacuum_index_stale_seconds ?? undefined,
    },
    schema: z.object({ success: z.boolean() }),
    isWrite: true,
  });

  res.json({ success: true });
}

const brainstoreVacuumObjectRequest = z.object({
  segment_id_cursor: z.string().nullish(),
  dry_run: z.boolean().nullish(),
});

const brainstoreVacuumObjectResponse = z.object({
  success: z.boolean(),
  segment_id_cursor: z.string().nullish(),
  num_processed_segments: z.number(),
  planned_num_deletes: z.number(),
  planned_total_bytes: z.number(),
  num_deleted_files: z.number(),
  error: z.string().nullish(),
});

export async function vacuumObjectRequest(req: Request, res: Response) {
  assertBrainstoreEnabled();
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    brainstoreVacuumObjectRequest.parse(ctx.data),
  );
  const objectIds = [req.params.object_id];

  await authCheckObjectIds({
    objectIds,
    ctx,
  });

  res.json(
    await runBrainstore({
      path: "/vacuum/index_stateless",
      args: {
        object_ids: objectIds,
        segment_id_cursor: params.segment_id_cursor,
        dry_run: params.dry_run,
      },
      schema: brainstoreVacuumObjectResponse,
      isWrite: true,
    }),
  );
}
