import { z } from "zod";
import { ident, join, sql } from "@braintrust/btql/planner";
import { executePG } from "./btql";
import { OBJECT_CACHE } from "./object_cache";
import { AccessDeniedError } from "./util";
import {
  AclObjectType,
  View,
  ViewData,
  viewDataSchema,
} from "@braintrust/core/typespecs";
import { viewMetadataSchema } from "@braintrust/local/app-schema";

const VIEW_STORAGE_TABLE = "views";

const viewStorageSchema = z.strictObject({
  view_data_id: z.string().uuid(),
  view_data: viewDataSchema.nullable(),
});
type ViewStorage = z.infer<typeof viewStorageSchema>;

export async function hydrateViewData(object: unknown): Promise<View> {
  return (await hydrateViewDatas([object]))[0];
}

export async function hydrateViewDatas(objects: unknown): Promise<View[]> {
  const viewMetadatas = z.array(viewMetadataSchema).parse(objects);
  if (viewMetadatas.length === 0) {
    return [];
  }
  const viewDataIds = viewMetadatas.map((v) => v.view_data_id);
  const backendViews = await fetchViewDatas(viewDataIds);
  if (backendViews.length !== viewDataIds.length) {
    throw new Error("Mismatched view data");
  }
  const dataMap = Object.fromEntries(
    backendViews.map((v) => [v.view_data_id, v.view_data]),
  );
  return viewMetadatas.map((v) => {
    const { view_data_id, ...rest } = v;
    return {
      ...rest,
      view_data: dataMap[view_data_id],
    };
  });
}

async function fetchViewDatas(ids: string[]): Promise<ViewStorage[]> {
  const query = sql`
    SELECT id AS view_data_id, view_data
      FROM ${ident(VIEW_STORAGE_TABLE)}
     WHERE id = ANY(ARRAY[${join(
       ids.map((id) => sql`${id}`),
       ", ",
     )}]::uuid[])
  `;
  const rows = await executePG(query);
  return viewStorageSchema.array().parse(rows);
}

export async function insertViewData({
  appOrigin,
  authToken,
  aclObjectType,
  objectId,
  view_data,
}: {
  appOrigin: string;
  authToken: string | undefined;
  aclObjectType: AclObjectType;
  objectId: string;
  view_data: ViewData;
}): Promise<ViewStorage> {
  const overrideRestrictObjectType = undefined;
  const aclCheck = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken,
    aclObjectType,
    objectId,
    overrideRestrictObjectType,
  });
  if (!aclCheck.permissions.includes("update")) {
    throw new AccessDeniedError({
      permission: "update",
      aclObjectType,
      overrideRestrictObjectType,
      objectId,
    });
  }

  const query = sql`
    INSERT INTO ${ident(VIEW_STORAGE_TABLE)} (view_data)
    VALUES (${JSON.stringify(view_data ?? null)}::jsonb)
    RETURNING id AS view_data_id, view_data
    `;
  const rows = await executePG(query);
  if (rows.length !== 1) {
    throw new Error("Expected 1 row");
  }
  return viewStorageSchema.parse(rows[0]);
}
