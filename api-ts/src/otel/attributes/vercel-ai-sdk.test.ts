import { describe, expect, test } from "vitest";
import { convertAttributesToSpan } from "./index";

const testCases = {
  generateTextBasic: {
    "operation.name": "ai.generateText",
    "ai.operationId": "ai.generateText",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.settings.temperature": 0.72,
    "ai.settings.topP": 0.52,
    "ai.telemetry.metadata.foo.nested.0": "zero",
    "ai.telemetry.metadata.foo.nested.1": "one",
    "ai.prompt":
      '{"prompt":"What is the weather typically like in Seattle? Give a one sentence response."}',
    "ai.response.finishReason": "stop",
    "ai.response.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
    "ai.finishReason": "stop",
    "ai.result.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
  },
  generateTextTools: {
    "operation.name": "ai.generateText",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.settings.temperature": 0.5,
    "ai.telemetry.metadata.foo": "bar",
    "ai.prompt":
      '{"prompt":"What is the weather in San Francisco and what attractions should I visit?"}',
    "ai.settings.maxSteps": 1,
    "ai.response.finishReason": "tool-calls",
    "ai.response.toolCalls":
      '[{"toolCallType":"function","toolCallId":"call_Dkaya8Vs6S9AKVudxLAh7UhX","toolName":"weather","args":"{\\"location\\": \\"San Francisco\\"}"},{"toolCallType":"function","toolCallId":"call_LtCrKXW0j2HIKa0ltnijxobP","toolName":"cityAttractions","args":"{\\"city\\": \\"San Francisco\\"}"}]',
    "ai.usage.promptTokens": 81,
    "ai.usage.completionTokens": 46,
    "ai.finishReason": "tool-calls",
  },
  generateTextDoGenerateTools: {
    "operation.name": "ai.generateText.doGenerate",
    "ai.model.id": "gpt-4o-mini",
    "ai.telemetry.metadata.foo": "bar",
    "ai.prompt.format": "prompt",
    "ai.prompt.messages":
      '[{"role":"user","content":[{"type":"text","text":"What is the weather in San Francisco and what attractions should I visit?"}]}]',
    "gen_ai.system": "openai.chat",
    "gen_ai.request.model": "gpt-4o-mini",
    "gen_ai.request.temperature": 0.5,
    "ai.result.toolCalls":
      '[{"toolCallType":"function","toolCallId":"call_Dkaya8Vs6S9AKVudxLAh7UhX","toolName":"weather","args":"{\\"location\\": \\"San Francisco\\"}"},{"toolCallType":"function","toolCallId":"call_LtCrKXW0j2HIKa0ltnijxobP","toolName":"cityAttractions","args":"{\\"city\\": \\"San Francisco\\"}"}]',
    "gen_ai.response.id": "chatcmpl-AJlaCjUBDxFwQhTKToWqMBD635TfK",
    "gen_ai.response.model": "gpt-4o-mini-2024-07-18",
    "gen_ai.usage.input_tokens": 81,
    "gen_ai.usage.output_tokens": 46,
  },
  generateTextDoGenerateMessages: {
    "operation.name": "ai.generateText.doGenerate",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.telemetry.metadata.foo": "bar",
    "ai.prompt.format": "prompt",
    "ai.prompt.messages":
      '[{"role":"user","content":[{"type":"text","text":"What is the weather typically like in Seattle? Give a one sentence response."}]}]',
    "gen_ai.system": "openai.chat",
    "gen_ai.request.model": "gpt-4o-mini",
    "gen_ai.request.temperature": 0.72,
    "gen_ai.request.top_p": 0.52,
    "ai.response.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
    "ai.result.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
  },
  generateTextMultimodal: {
    "operation.name": "ai.generateText",
    "ai.model.id": "gpt-4o-mini",
    "ai.settings.temperature": 0.5,
    "ai.telemetry.metadata.foo": "baz",
    "ai.prompt":
      '{"messages":[{"role":"system","content":"Speak like a pirate. Arrrr."},{"role":"user","content":[{"type":"text","text":"Tell a brief story about the provided image(s)."},{"type":"image","image":"data:image/png;base64,iVBORw0KGgo"},{"type":"image","image":"https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}]}]}',
    "ai.response.text":
      "Arrr, gather 'round, me hearties! Let me spin ye a tale of a jolly banana named Bananarama.",
  },
  generateTextDoGenerateMultimodal: {
    "operation.name": "ai.generateText.doGenerate",
    "ai.model.id": "gpt-4o-mini",
    "ai.telemetry.metadata.foo": "baz",
    "ai.prompt.format": "messages",
    "ai.prompt.messages":
      '[{"role":"system","content":"Speak like a pirate. Arrrr."},{"role":"user","content":[{"type":"text","text":"Tell a brief story about the provided image(s)."},{"type":"image","image":{"0":137,"1":80,"2":78,"3":71,"4":13,"5":10},"mimeType":"image/png"},{"type":"image","image":"https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}]}]',
    "gen_ai.system": "openai.chat",
    "gen_ai.request.model": "gpt-4o-mini",
    "ai.result.text":
      "Arrr, gather 'round, me hearties! Let me spin ye a tale of a jolly banana named Bananarama. This bright yellow fellow lived in a bustling fruit market, always sportin' a cheerful grin. One fateful day, Bananarama dreamt of adventure beyond the market stalls.\n\nWith a mighty leap, he rolled away, escapin' the clutches of the fruit vendor! He sailed down the street, dodgin' curious critters and mischievous seagulls. His quest? To find the legendary Fruit Kingdom, where all fruits lived in harmony.\n\nAlong his journey, he made merry friends—a wise old apple and a zesty lemon—who joined him on his quest. Together, they faced challenges, shared laughter, and discovered the true treasure of friendship.\n\nAt long last, they reached the Fruit Kingdom, where Bananarama became a hero, celebrated for his bravery and sunny spirit. And so, they lived happily ever after, spreadin' joy and fruity fun across the land! Arrr! 🍌✨",
  },
  toolCall: {
    "operation.name": "ai.toolCall",
    "ai.operationId": "ai.toolCall",
    "ai.toolCall.name": "weather",
    "ai.toolCall.id": "call_Dkaya8Vs6S9AKVudxLAh7UhX",
    "ai.toolCall.args": '{"location":"San Francisco"}',
    "ai.toolCall.result": '{"location":"San Francisco","temperature":68}',
  },
  streamObject: {
    "operation.name": "ai.streamObject",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.settings.temperature": 0.9,
    "ai.telemetry.metadata.foo": "baz",
    "ai.prompt": '{"prompt":"Generate a celery salad recipe."}',
    "ai.schema":
      '{"type":"object","properties":{"recipe":{"type":"object","properties":{"name":{"type":"string"},"ingredients":{"type":"array","items":{"type":"object","properties":{"name":{"type":"string"},"amount":{"type":"string"}},"required":["name","amount"],"additionalProperties":false}},"steps":{"type":"array","items":{"type":"string"}}},"required":["name","ingredients","steps"],"additionalProperties":false}},"required":["recipe"],"additionalProperties":false,"$schema":"http://json-schema.org/draft-07/schema#"}',
    "ai.settings.output": "object",
    "ai.usage.promptTokens": "NaN",
    "ai.usage.completionTokens": "NaN",
    "ai.response.object":
      '{"recipe":{"name":"Crunchy Celery Salad","ingredients":[{"name":"Celery","amount":"4 stalks, chopped"},{"name":"Red bell pepper","amount":"1, diced"},{"name":"Carrot","amount":"1, grated"},{"name":"Red onion","amount":"¼, finely chopped"},{"name":"Fresh parsley","amount":"¼ cup, chopped"},{"name":"Lemon juice","amount":"2 tablespoons"},{"name":"Olive oil","amount":"2 tablespoons"},{"name":"Honey","amount":"1 teaspoon"},{"name":"Salt","amount":"to taste"},{"name":"Black pepper","amount":"to taste"}],"steps":["In a large mixing bowl, combine chopped celery, diced red bell pepper, grated carrot, and finely chopped red onion.","Add the chopped parsley to the vegetable mixture.","In a separate small bowl, whisk together the lemon juice, olive oil, honey, salt, and black pepper until well combined.","Pour the dressing over the salad and toss everything together until the vegetables are evenly coated with the dressing.","Taste and adjust seasoning if necessary.","Chill in the refrigerator for about 15-30 minutes before serving to allow the flavors to meld.","Serve chilled as a refreshing side dish."]}}',
  },
  streamTextDoStream: {
    "operation.name": "ai.streamText.doStream",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.telemetry.metadata.foo": "baz",
    "ai.prompt.format": "prompt",
    "ai.prompt.messages":
      '[{"role":"user","content":[{"type":"text","text":"What is the weather typically like in Mexico City?"}]}]',
    "ai.response.msToFirstChunk": 50.01574993133545,
    "ai.stream.msToFirstChunk": 50.01574993133545,
    "ai.response.msToFinish": 64.01300001144409,
    "ai.response.avgCompletionTokensPerSecond": "NaN",
    "ai.response.finishReason": "stop",
    "ai.usage.promptTokens": "NaN",
    "ai.usage.completionTokens": "NaN",
    "ai.finishReason": "stop",
    "ai.result.text":
      "Mexico City has a temperate climate, characterized by mild temperatures and a distinct wet and dry season. Here are some key features of the weather in Mexico City:\n\n1. **Temperature**: The average temperature typically ranges from about 10°C (50°F) at night to 25°C (77°F) during the day. However, temperatures can vary, with warmer days in the spring and cooler nights in the winter.\n\n2. **Seasons**:\n   - **Dry Season**: This usually runs from November to April, with little to no rainfall. During this time, the weather is generally sunny and pleasant.\n   - **Wet Season**: From May to October, Mexico City experiences its rainy season, with the most rainfall occurring in July and August. Afternoon thunderstorms are common during this period.\n\n3. **Altitude**: Mexico City is situated at a high altitude (about 2,240 meters or 7,350 feet above sea level), which contributes to its cooler temperatures compared to other regions in Mexico.\n\n4. **Humidity**: The city can experience varying humidity levels, especially during the rainy season, but overall, it tends to be moderate.\n\n5. **Air Quality**: Due to its elevation and urban environment, air quality can fluctuate, particularly during the dry season when pollution can become more concentrated.\n\nOverall, Mexico City enjoys a generally mild climate, making it a year-round destination, though visitors should be prepared for rain if traveling during the wet season.",
    "gen_ai.usage.input_tokens": "NaN",
    "gen_ai.usage.output_tokens": "NaN",
  },
  braintrustFields: {
    "operation.name": "ai.generateText.doGenerate",
    "ai.model.provider": "openai.chat",
    "ai.model.id": "gpt-4o-mini",
    "ai.telemetry.metadata.foo": "bar",
    "ai.prompt.format": "prompt",
    "ai.prompt.messages":
      '[{"role":"user","content":[{"type":"text","text":"What is the weather typically like in Seattle? Give a one sentence response."}]}]',
    "gen_ai.system": "openai.chat",
    "gen_ai.request.model": "gpt-4o-mini",
    "gen_ai.request.temperature": 0.8,
    "gen_ai.usage.input_tokens": 100,
    "gen_ai.usage.output_tokens": 400,
    "ai.response.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
    "ai.result.text":
      "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
    "braintrust.input.0.role": "user",
    "braintrust.input.0.content":
      "Tell me a story about a dragon and a knight.",
    "braintrust.output.0.role": "assistant",
    "braintrust.output.0.content":
      "The dragon and the knight faced off in a fierce battle, each determined to emerge victorious. The dragon breathed fire, but the knight's armor was strong. With a mighty swing of his sword, the knight struck the dragon down, ending the battle and saving the kingdom.",
    "braintrust.metadata.story": "fantasy",
    "braintrust.metadata.characters.0": "dragon",
    "braintrust.metadata.characters.1": "knight",
    "braintrust.metrics.foo": 12,
    "braintrust.metrics.bar": 43,
  },
  serializedBraintrustFields: {
    "braintrust.input_json": JSON.stringify([
      {
        role: "user",
        content: "Tell me a story about a dragon and a knight.",
      },
    ]),
    "braintrust.output_json": JSON.stringify([
      {
        role: "assistant",
        content:
          "The dragon and the knight faced off in a fierce battle, each determined to emerge victorious. The dragon breathed fire, but the knight's armor was strong. With a mighty swing of his sword, the knight struck the dragon down, ending the battle and saving the kingdom.",
      },
    ]),
    "braintrust.metadata": JSON.stringify({
      story: "fantasy",
      characters: ["dragon", "knight"],
    }),
    "braintrust.metrics": JSON.stringify({
      foo: 12,
      bar: 43,
    }),
  },
  serializedBraintrustFieldsInvalid: {
    "braintrust.input_json": "Tell me a story about a dragon and a knight.",
    "braintrust.output_json": "inv}alid json",
    "braintrust.metadata": "not a record",
    "braintrust.metrics": JSON.stringify(["foo", "bar"]),
  },
};

function runTestCase(input: Record<string, unknown>) {
  const result = convertAttributesToSpan(input);
  expect(result).toMatchSnapshot();
}

describe("vercel-ai-sdk convertAttributesToSpan", () => {
  for (const [name, attributes] of Object.entries(testCases)) {
    test(name, () => runTestCase(attributes));
  }
});
