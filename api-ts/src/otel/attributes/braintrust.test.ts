import { convertAttributesToSpan } from ".";
import { describe, expect, test } from "vitest";

describe("braintrust", () => {
  describe("convertAttributesToSpan", () => {
    test("handle eval span with expected", () => {
      const input = {
        "braintrust.expected": JSON.stringify({ a: [1, 2, 3] }),
        "braintrust.span_attributes": JSON.stringify({ type: "eval" }),
        "braintrust.input_json": JSON.stringify("test input"),
        "braintrust.output_json": JSON.stringify("test output"),
      };

      const expected = {
        input: [{ content: "test input", role: "user" }],
        output: [{ content: "test output", role: "assistant" }],
        metadata: {},
        metrics: {},
        expected: { a: [1, 2, 3] },
        span_attributes: { type: "eval" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles scores", () => {
      const input = {
        "braintrust.scores": JSON.stringify({
          "gut-check": 0.95,
          hunch: 0.9,
        }),
        "braintrust.span_attributes": JSON.stringify({ type: "score" }),
      };

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        scores: {
          "gut-check": 0.95,
          hunch: 0.9,
        },
        span_attributes: { type: "score" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles basic input/output with metadata, metrics, tags and type", () => {
      const input = {
        "braintrust.input_json": JSON.stringify("test input"),
        "braintrust.output_json": JSON.stringify("test output"),
        "braintrust.metadata": JSON.stringify({ source: "test" }),
        "braintrust.metrics": JSON.stringify({ score: 0.95 }),
        "braintrust.span_attributes": JSON.stringify({ type: "eval" }),
      };

      const expected = {
        input: [
          {
            content: "test input",
            role: "user",
          },
        ],
        output: [
          {
            content: "test output",
            role: "assistant",
          },
        ],
        metadata: { source: "test" },
        metrics: { score: 0.95 },
        span_attributes: { type: "eval" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles message arrays with role/content structure", () => {
      const input = {
        "braintrust.input": [
          { role: "user", content: "Hello" },
          { role: "system", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        "braintrust.output": [
          { role: "assistant", content: "How can I help?" },
        ],
      };

      const expected = {
        input: [
          { role: "user", content: "Hello" },
          { role: "system", content: "Hello" },
          { role: "assistant", content: "Hi there!" },
        ],
        output: [{ role: "assistant", content: "How can I help?" }],
        metadata: {},
        metrics: {},
        span_attributes: { type: "llm" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles JSON serialized input/output", () => {
      const input = {
        "braintrust.input_json": JSON.stringify("test input json"),
        "braintrust.output_json": JSON.stringify("test output json"),
        "braintrust.metadata": JSON.stringify({ source: "json test" }),
        "braintrust.metrics": JSON.stringify({ score: 0.85 }),
      };

      const expected = {
        input: [{ content: "test input json", role: "user" }],
        output: [{ content: "test output json", role: "assistant" }],
        metadata: { source: "json test" },
        metrics: { score: 0.85 },
        span_attributes: { type: "llm" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles invalid JSON gracefully", () => {
      const input = {
        "braintrust.input_json": "invalid json{",
        "braintrust.output_json": "[not valid json",
        "braintrust.metadata": "not an object",
        "braintrust.metrics": JSON.stringify(["not", "metrics"]),
      };

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" }, // FIXME[matt] this is weird
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles empty values", () => {
      const input = {};

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" }, // FIXME[matt] this is weird
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles nested metadata", () => {
      const input = {
        "braintrust.metadata": JSON.stringify({
          model: {
            name: "gpt-4",
            version: "v2",
          },
          tags: ["test", "demo"],
        }),
      };

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {
          model: {
            name: "gpt-4",
            version: "v2",
          },
          tags: ["test", "demo"],
        },
        metrics: {},
        span_attributes: { type: "task" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles complex metrics", () => {
      const input = {
        "braintrust.metrics": JSON.stringify({
          accuracy: 0.95,
          latency_ms: 150,
          tokens: 512,
        }),
      };

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {
          accuracy: 0.95,
          latency_ms: 150,
          tokens: 512,
        },
        span_attributes: { type: "task" },
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });

    test("handles tags", () => {
      const input = {
        "braintrust.tags": ["test", "demo"],
      };

      const expected = {
        input: undefined,
        output: undefined,
        metadata: {},
        metrics: {},
        span_attributes: { type: "task" },
        tags: ["test", "demo"],
      };

      const result = convertAttributesToSpan(input);
      expect(result).toEqual(expected);
    });
  });
});
