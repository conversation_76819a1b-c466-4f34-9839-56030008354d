import { z } from "zod";
import { get } from "lodash";
import { isEmpty, isObject } from "@braintrust/core";
import {
  Message,
  chatCompletionContentPartSchema,
  chatCompletionMessageParamSchema,
  chatCompletionMessageToolCallSchema,
} from "@braintrust/core/typespecs";
import {
  MessageField,
  SpanSpec,
  deserializeIfString,
  mustParseJson,
  translateModelParams,
} from "./attributes";

export const genAISpanSpec: SpanSpec = {
  input: (attributes) =>
    translateGenAIPrompt({
      value: get(attributes, "gen_ai.prompt"),
      valueJson: get(attributes, "gen_ai.prompt_json"),
      defaultRole: "user",
    }),
  output: (attributes) =>
    translateGenAIPrompt({
      value: get(attributes, "gen_ai.completion"),
      valueJson: get(attributes, "gen_ai.completion_json"),
      defaultRole: "assistant",
    }),
  metadata: (attributes) => {
    return translateModelParams(
      deserializeIfString(get(attributes, "gen_ai.request")),
    );
  },
  metrics: (attributes) => {
    const metrics = deserializeIfString(get(attributes, "gen_ai.usage"));
    if (!isObject(metrics)) {
      return {};
    }
    return {
      ...metrics,
      prompt_tokens: metrics.prompt_tokens ?? metrics.input_tokens,
      completion_tokens: metrics.completion_tokens ?? metrics.output_tokens,
    };
  },
};

const genAIAssistantMessageParamSchema = z.object({
  role: z.literal("assistant"),
  content: z.string().nullish(),
  name: z
    .string()
    .nullish()
    .transform((x) => x ?? undefined),
  tool_calls: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        arguments: z.string(),
      }),
    )
    .nullish()
    .transform((value) =>
      value?.map(
        (tc): z.infer<typeof chatCompletionMessageToolCallSchema> => ({
          id: tc.id,
          type: "function",
          function: {
            arguments: tc.arguments,
            name: tc.name,
          },
        }),
      ),
    ),
});

const genAIMessageSchema = z.union([
  chatCompletionMessageParamSchema,
  genAIAssistantMessageParamSchema,
]);

function translateGenAIPrompt({
  value,
  valueJson,
  defaultRole,
}: {
  value: unknown;
  valueJson: unknown;
  defaultRole: "user" | "assistant";
}): MessageField | undefined {
  if (isEmpty(value) && isEmpty(valueJson)) {
    return undefined;
  }

  const prompt = valueJson ? mustParseJson(valueJson) : value;

  if (typeof prompt === "string") {
    return {
      isLLM: true,
      data: [
        {
          role: defaultRole,
          content: prompt,
        },
      ],
    };
  }

  try {
    const parsed = z.array(genAIMessageSchema).parse(prompt);

    const messages = parsed.map((message): Message => {
      const { role, content } = message;
      if (role !== "user" || typeof content !== "string") {
        return message;
      }

      try {
        const contentArray = z.array(z.unknown()).parse(JSON.parse(content));
        return {
          role: "user",
          content: contentArray.map((part) =>
            chatCompletionContentPartSchema.parse(part),
          ),
        };
      } catch {
        return {
          role: "user",
          content,
        };
      }
    });

    return {
      isLLM: true,
      data: messages,
    };
  } catch {
    return { isLLM: false, data: prompt };
  }
}
