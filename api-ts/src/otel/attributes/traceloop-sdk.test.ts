import { describe, expect, test } from "vitest";
import { convertAttributesToSpan } from "./index";

const testCases = {
  basic: {
    "llm.request.type": "chat",
    "traceloop.workflow.name": "population",
    "traceloop.entity.path": "run_population_tokyo",
    "gen_ai.system": "OpenAI",
    "gen_ai.request.model": "gpt-4o-mini",
    "gen_ai.request.max_tokens": 20,
    "gen_ai.request.temperature": 0.9,
    "llm.frequency_penalty": 0.5,
    "llm.headers": "None",
    "llm.is_streaming": false,
    "gen_ai.openai.api_base": "http://0.0.0.0:8001/v1/",
    "gen_ai.prompt.0.role": "user",
    "gen_ai.prompt.0.content": "What is the population of Tokyo?",
    "gen_ai.response.model": "gpt-4o-mini-2024-07-18",
    "gen_ai.openai.system_fingerprint": "fp_e2bde53e6e",
    "llm.usage.total_tokens": 34,
    "gen_ai.usage.completion_tokens": 20,
    "gen_ai.usage.prompt_tokens": 14,
    "gen_ai.completion.0.finish_reason": "length",
    "gen_ai.completion.0.role": "assistant",
    "gen_ai.completion.0.content":
      "As of my last update in October 2023, the population of Tokyo was estimated to be around",
  },
  stream: {
    "llm.request.type": "chat",
    "traceloop.workflow.name": "weather_stream",
    "traceloop.entity.path": "run_weather_stream",
    "gen_ai.system": "OpenAI",
    "gen_ai.request.model": "gpt-4o-mini",
    "llm.headers": "None",
    "llm.is_streaming": true,
    "gen_ai.openai.api_base": "http://0.0.0.0:8001/v1/",
    "gen_ai.prompt.0.role": "system",
    "gen_ai.prompt.0.content":
      "You are an assistant that can access weather information.",
    "gen_ai.prompt.1.role": "user",
    "gen_ai.prompt.1.content":
      "What is the weather in San Diego and what attractions should I visit?",
    "llm.request.functions.0.name": "weather",
    "llm.request.functions.0.description": "Get the weather for a location",
    "llm.request.functions.0.parameters":
      '{"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the weather for"}}}',
    "gen_ai.usage.completion_tokens": 0,
    "gen_ai.usage.prompt_tokens": 23,
    "llm.usage.total_tokens": 23,
    "gen_ai.response.model": "gpt-4o-mini-2024-07-18",
    "gen_ai.completion.0.finish_reason": "tool_calls",
    "gen_ai.completion.0.role": "assistant",
    "gen_ai.completion.0.tool_calls.0.id": "call_iCyA3NrzZhAGAu47JMjZV9GY",
    "gen_ai.completion.0.tool_calls.0.name": "weather",
    "gen_ai.completion.0.tool_calls.0.arguments": '{"location": "San Diego"}',
    "gen_ai.completion.0.tool_calls.1.id": "call_G4GpNvN4F2dNRprSuqX0TPQp",
    "gen_ai.completion.0.tool_calls.1.name": "weather",
    "gen_ai.completion.0.tool_calls.1.arguments":
      '{"location": "popular attractions in San Diego"}',
  },
  multimodal: {
    "llm.request.type": "chat",
    "traceloop.workflow.name": "multimodal",
    "traceloop.entity.path": "run_multimodal",
    "gen_ai.system": "OpenAI",
    "gen_ai.request.model": "gpt-4o-mini",
    "llm.headers": "None",
    "llm.is_streaming": false,
    "gen_ai.openai.api_base": "http://0.0.0.0:8001/v1/",
    "gen_ai.prompt.0.role": "system",
    "gen_ai.prompt.0.content": "Speak like a pirate. Arrrr.",
    "gen_ai.prompt.1.role": "user",
    "gen_ai.prompt.1.content":
      '[{"type": "text", "text": "Tell a brief story about the provided image(s)."}, {"type": "image_url", "image_url": {"url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png"}}]',
    "gen_ai.response.model": "gpt-4o-mini-2024-07-18",
    "gen_ai.openai.system_fingerprint": "fp_8552ec53e1",
    "llm.usage.total_tokens": 8780,
    "gen_ai.usage.completion_tokens": 251,
    "gen_ai.usage.prompt_tokens": 8529,
    "gen_ai.completion.0.finish_reason": "stop",
    "gen_ai.completion.0.role": "assistant",
    "gen_ai.completion.0.content":
      "Arrr, gather 'round, mateys, for a tale of a jolly little fruit!",
  },
  tools: {
    "llm.request.type": "chat",
    "traceloop.workflow.name": "calculate",
    "traceloop.entity.path": "run_calculate",
    "gen_ai.system": "OpenAI",
    "gen_ai.request.model": "gpt-4o-mini",
    "llm.headers": "None",
    "llm.is_streaming": false,
    "gen_ai.openai.api_base": "http://0.0.0.0:8001/v1/",
    "gen_ai.prompt.0.role": "user",
    "gen_ai.prompt.0.content": "Calculate the sum of 123 and 456.",
    "llm.request.functions.0.name": "add_numbers",
    "llm.request.functions.0.description": "Add two numbers",
    "llm.request.functions.0.parameters":
      '{"type": "object", "properties": {"a": {"type": "number", "description": "First number"}, "b": {"type": "number", "description": "Second number"}}, "required": ["a", "b"]}',
    "gen_ai.response.model": "gpt-4o-mini-2024-07-18",
    "gen_ai.openai.system_fingerprint": "fp_8552ec53e1",
    "llm.usage.total_tokens": 80,
    "gen_ai.usage.completion_tokens": 18,
    "gen_ai.usage.prompt_tokens": 62,
    "gen_ai.completion.0.finish_reason": "tool_calls",
    "gen_ai.completion.0.role": "assistant",
    "gen_ai.completion.0.tool_calls.0.id": "call_EXzHE31NyQa3y3pItz3v4Ztf",
    "gen_ai.completion.0.tool_calls.0.name": "add_numbers",
    "gen_ai.completion.0.tool_calls.0.arguments": '{"a":123,"b":456}',
  },
  serialized: {
    "gen_ai.prompt_json": JSON.stringify([
      { role: "user", content: "Hello" },
      { role: "assistant", content: "Hi there!" },
    ]),
    "gen_ai.completion_json": JSON.stringify([
      { role: "assistant", content: "Here's your response" },
    ]),
    "gen_ai.request": JSON.stringify({
      model: "gpt-4o-mini",
      max_tokens: 20,
      temperature: 0.9,
    }),
    "gen_ai.usage": JSON.stringify({
      prompt_tokens: 10,
      completion_tokens: 20,
    }),
  },
  serializedEmpty: {
    "gen_ai.prompt_json": JSON.stringify([]),
    "gen_ai.completion_json": JSON.stringify(""),
    "gen_ai.request": JSON.stringify({}),
    "gen_ai.usage": JSON.stringify({}),
  },
  serializedInvalid: {
    "gen_ai.prompt_json": "just a string",
    "gen_ai.completion_json": "[not valid json{",
    "gen_ai.request": '"abc"',
    "gen_ai.usage": JSON.stringify(["foo", "bar"]),
  },
};

function runTestCase(input: Record<string, unknown>) {
  const result = convertAttributesToSpan(input);
  expect(result).toMatchSnapshot();
}

describe("traceloop convertAttributesToSpan", () => {
  for (const [name, attributes] of Object.entries(testCases)) {
    test(name, () => runTestCase(attributes));
  }
});
