import { z } from "zod";
import { set } from "lodash";
import {
  isEmpty,
  isObject,
  mergeDicts,
  SpanTypeAttribute,
} from "@braintrust/core";
import { Message } from "@braintrust/core/typespecs";
import { translateParams } from "@braintrust/proxy/schema";

export const spanSchema = z.object({
  input: z.unknown().optional(),
  output: z.unknown().optional(),
  metadata: z.record(z.unknown()).optional(),
  metrics: z.record(z.number()).optional(),
  span_attributes: z.record(z.string()).optional(),
  scores: z.record(z.union([z.number(), z.null()])).optional(),
  expected: z.unknown().optional(),
  tags: z.array(z.string()).optional(),
});

export type Span = z.infer<typeof spanSchema>;

export function reconstructAttributes(
  flatAttributes: Record<string, unknown>,
): unknown {
  const attributes = {};
  for (const [key, value] of Object.entries(flatAttributes)) {
    // TODO(austin): This is a hack to work around the fact that the Vercel AI SDK
    // puts the schema name under `ai.schema.name` which "conflicts" with the actual
    // JSON schema at `ai.schema`. Maybe they'll change it, but for now just rename
    // the attribute.
    const btKey =
      key === "ai.schema.name" && "ai.schema" in flatAttributes
        ? "ai.schema_name"
        : key;
    set(attributes, btKey, value);
  }
  return attributes;
}

function sanitizeMetrics(
  metrics: Record<string, unknown>,
): Record<string, number> | undefined {
  const ret: Record<string, number> = {};
  for (const [key, value] of Object.entries(metrics)) {
    if (typeof value === "number") {
      ret[key] = value;
    }
  }
  return ret;
}

function sanitizeSpanAttrs(
  attrs: Record<string, unknown>,
): Record<string, string> {
  const ret: Record<string, string> = {};
  for (const [key, value] of Object.entries(attrs)) {
    if (typeof value === "string") {
      ret[key] = value;
    }
  }
  return ret;
}

function postprocessMetrics(
  metrics: Record<string, unknown>,
): Record<string, number> | undefined {
  const ret = sanitizeMetrics(metrics);
  if (
    ret &&
    isEmpty(ret.tokens) &&
    typeof ret.prompt_tokens === "number" &&
    typeof ret.completion_tokens === "number"
  ) {
    ret.tokens = ret.prompt_tokens + ret.completion_tokens;
  }
  return ret;
}

export type MessageField =
  | {
      isLLM: true;
      data: Message[];
    }
  | {
      isLLM: false;
      data: unknown;
    };

type MessageFieldSpec = (source: unknown) => MessageField | undefined;
type MapSpec = (source: unknown) => Record<string, unknown> | undefined;
type TagsSpec = (source: unknown) => string[] | undefined;

type AnySpec = (source: unknown) => unknown | undefined;

export interface SpanSpec {
  input?: MessageFieldSpec;
  output?: MessageFieldSpec;
  metadata?: MapSpec;
  metrics?: MapSpec;
  span_attributes?: MapSpec;
  scores?: MapSpec;
  expected?: AnySpec;
  tags?: TagsSpec;
}

type MakeSpanArgs = {
  attributes: unknown;
  specs: Record<string, SpanSpec>;
  debug?: boolean;
};

type MessageFields = "input" | "output";

function makeMessagesField({
  attributes,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: MessageFields }): MessageField {
  // Try to process the span with each spec and use the first valid result.
  for (const [name, spec] of Object.entries(specs)) {
    const fieldSpec = spec[field];
    if (!fieldSpec) continue;

    try {
      const field = fieldSpec(attributes);
      if (!isEmpty(field)) {
        return field;
      }
    } catch (error) {
      if (debug) {
        const log = { error, attributes, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return { isLLM: false, data: undefined };
}

type MapFields = "metadata" | "metrics" | "span_attributes" | "scores";

function makeMapField({
  attributes,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: MapFields }): Record<string, unknown> {
  // Reconstruct as much as we can from each spec and deep-merge them all together.
  const result: Record<string, unknown> = {};
  for (const [name, spec] of Object.entries(specs)) {
    const mapSpec = spec[field];
    if (!mapSpec) continue;

    try {
      const partialResult = mapSpec(attributes);
      if (!isEmpty(partialResult)) {
        const sanitizedPartialResult = Object.fromEntries(
          Object.entries(partialResult).filter(([_, value]) => !isEmpty(value)),
        );
        mergeDicts(result, sanitizedPartialResult);
      }
    } catch (error) {
      if (debug) {
        const log = { error, attributes, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return result;
}

function makeAnyField({
  attributes,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: "expected" }): unknown | undefined {
  for (const [name, spec] of Object.entries(specs)) {
    if (!spec[field]) continue;
    const fieldSpec: AnySpec = spec[field];
    try {
      const result = fieldSpec(attributes);
      if (!isEmpty(result)) {
        return result;
      }
    } catch (error) {
      if (debug) {
        const log = { error, attributes, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return undefined;
}

function makeTagsField({
  attributes,
  specs,
  field,
  debug,
}: MakeSpanArgs & { field: "tags" }): string[] | undefined {
  for (const [name, spec] of Object.entries(specs)) {
    if (!spec[field]) continue;
    const fieldSpec: TagsSpec = spec[field];
    try {
      const result = fieldSpec(attributes);
      if (!isEmpty(result)) {
        return result;
      }
    } catch (error) {
      if (debug) {
        const log = { error, attributes, name, field };
        console.log(JSON.stringify(log, null, 2));
      }
    }
  }
  return undefined;
}

export function makeSpan(args: MakeSpanArgs): Span {
  const input = makeMessagesField({ ...args, field: "input" });
  const output = makeMessagesField({ ...args, field: "output" });

  const metadata = makeMapField({ ...args, field: "metadata" });
  const metrics = makeMapField({ ...args, field: "metrics" });
  const attrs = makeMapField({ ...args, field: "span_attributes" });
  const scores = makeMapField({ ...args, field: "scores" });
  const expected = makeAnyField({ ...args, field: "expected" });
  const tags = makeTagsField({ ...args, field: "tags" });

  // set a default type if not submitted.
  // NOTE[matt] I'm not sure if makes sense to label random spans tasks, but
  // I'm keeping it here until I understand this more
  if (!attrs.type) {
    const spanType =
      input.isLLM || output.isLLM
        ? SpanTypeAttribute.LLM
        : SpanTypeAttribute.TASK;
    attrs.type = spanType;
  }

  const span: Span = {
    input: input.data,
    output: output.data,
    metadata,
    metrics: postprocessMetrics(metrics),
    span_attributes: sanitizeSpanAttrs(attrs),
  };

  if (Object.keys(scores).length > 0) {
    span.scores = sanitizeScores(scores);
  }

  if (expected !== undefined) {
    span.expected = expected;
  }

  if (tags !== undefined) {
    span.tags = tags;
  }

  return span;
}

function sanitizeScores(
  scores: Record<string, unknown>,
): Record<string, number | null> {
  const ret: Record<string, number | null> = {};
  for (const [key, value] of Object.entries(scores)) {
    if (typeof value === "number") {
      ret[key] = value;
    }
  }
  return ret;
}

export function mustParseJson(value: unknown): unknown {
  if (typeof value !== "string") {
    throw new Error("Cannot deserialize non-string value");
  }
  return JSON.parse(value);
}

export function deserializeIfString(value: unknown): unknown {
  return typeof value === "string" ? JSON.parse(value) : value;
}

export function translateModelParams(params: unknown): Record<string, unknown> {
  if (!isObject(params)) {
    return {};
  }
  return translateParams("openai", params);
}
