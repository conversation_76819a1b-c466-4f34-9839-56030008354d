import { aclObjectTypeEnum } from "@braintrust/core/typespecs";
import { ERROR_CONTEXT_CACHE } from "./error_context_cache";
import {
  BT_AUDIT_NORMALIZED_URL_HEADER,
  BT_AUDIT_RESOURCES_HEADER,
  BT_AUDIT_USER_EMAIL_HEADER,
  BT_AUDIT_USER_ID_HEADER,
} from "./util";
import { Request, Response } from "express";
import { aclObjectTableInfos } from "@braintrust/local/app-schema";
import { getRequestContext } from "./request_context";
import { gzip } from "zlib";

export type AuditObject = {
  acl_object_type: string;
  object_id: string;
  object_name: string;
  parent_cols: Record<string, { id: string; name: string }>;
};

async function auditInfo({
  appOrigin,
  authToken,
  objects,
}: {
  appOrigin: string;
  authToken?: string;
  objects: AuditObject[];
}) {
  const { userId, userEmail } = await ERROR_CONTEXT_CACHE.getErrorContext({
    appOrigin,
    authToken,
  });
  const resources = new Map<
    string,
    { type: string; id: string; name: string }
  >();
  for (const {
    acl_object_type: type,
    object_id: id,
    object_name: name,
    parent_cols: parents,
  } of objects) {
    resources.set(`${type}:${id}`, { type, id, name });
    for (const [t, { id, name }] of Object.entries(parents)) {
      const type = aclObjectTypeEnum.parse(t);
      if (aclObjectTableInfos[type].kind !== "virtual") {
        resources.set(`${type}:${id}`, { type, id, name });
      }
    }
  }
  return {
    userId,
    userEmail,
    resources: Array.from(resources.values()),
  };
}

function normalizedUrl(req: Request): string {
  return req.route.path
    .split("/")
    .map((part: string) =>
      part.at(0) === ":"
        ? part === ":id"
          ? "[id]"
          : req.params[part.substring(1)]
        : part,
    )
    .join("/");
}

// The deserialization code is found in //sdk/py/src/braintrust/audit.py.
const AUDIT_RESOURCES_PROTOCOL_VERSION = 1;

export async function setAuditHeaders({
  req,
  res,
  objects,
}: {
  req: Request;
  res: Response;
  objects: AuditObject[];
}) {
  const { appOrigin, token: authToken } = getRequestContext(req);
  const { userId, userEmail, resources } = await auditInfo({
    appOrigin,
    authToken,
    objects,
  });
  if (userId) {
    res.setHeader(BT_AUDIT_USER_ID_HEADER, userId);
  }
  if (userEmail) {
    res.setHeader(BT_AUDIT_USER_EMAIL_HEADER, userEmail);
  }
  res.setHeader(BT_AUDIT_NORMALIZED_URL_HEADER, normalizedUrl(req));
  if (resources.length > 0) {
    const compressed = await new Promise<Buffer>((resolve, reject) => {
      gzip(JSON.stringify(resources), (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
    res.setHeader(
      BT_AUDIT_RESOURCES_HEADER,
      JSON.stringify({
        v: AUDIT_RESOURCES_PROTOCOL_VERSION,
        p: compressed.toString("base64"),
      }),
    );
  }
}
