import { z } from "zod";
import { getRedis } from "./redis";
import { mapAt, deterministicReplacer } from "@braintrust/core";
import { functionCacheKeyPrefix } from "./proxy/functions";
import { REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX } from "./util";
import { sha256Digest } from "./proxy/proxy";

const promptCacheInvalidationSchema = z.strictObject({
  projectId: z.string(),
  slug: z.string().nullish(),
  promptId: z.string(),
});

export type PromptCacheInvalidation = z.infer<
  typeof promptCacheInvalidationSchema
>;

export type PrefixSetVersionSpecifier =
  | { kind: "version"; value: string }
  | { kind: "environment_id"; value: string };

export type CacheVersionSpecifier =
  | { kind: "version"; value: string }
  | { kind: "environment_slug"; value: string };

export async function executePromptCacheInvalidations({
  promptCacheInvalidations,
  projectIdToName,
}: {
  promptCacheInvalidations: PromptCacheInvalidation[];
  projectIdToName: Map<string, string>;
}) {
  // De-duplicate the invalidations.
  const invalidationsSet = new Set<string>();
  for (const entry of promptCacheInvalidations) {
    invalidationsSet.add(JSON.stringify(entry, deterministicReplacer));
  }
  promptCacheInvalidations = promptCacheInvalidationSchema
    .array()
    .parse([...invalidationsSet.keys()].map((x) => JSON.parse(x)));

  const invalidationPrefixes = promptCacheInvalidations
    .map((x) =>
      collectPromptCacheInvalidationPrefixes({
        ...x,
        projectName: mapAt(projectIdToName, x.projectId),
      }),
    )
    .flat();
  await commitPromptCacheInvalidations(invalidationPrefixes);
}

function collectPromptCacheInvalidationPrefixes({
  projectId,
  projectName,
  slug,
  promptId,
}: PromptCacheInvalidation & { projectName: string }): string[] {
  const prefixes = [];
  if (projectId && slug) {
    prefixes.push(promptCacheKeyPrefix({ projectId, slug }));
  }
  if (projectName && slug) {
    prefixes.push(
      promptCacheKeyPrefix({ projectName, slug }),
      functionCacheKeyPrefix({ project_name: projectName, slug }),
    );
  }
  if (promptId) {
    prefixes.push(
      promptCacheKeyPrefix({ promptId }),
      functionCacheKeyPrefix({ function_id: promptId }),
    );
  }
  return prefixes;
}

async function commitPromptCacheInvalidations(
  prefixes: string[],
): Promise<void> {
  const redisConn = await getRedis();
  // Grab all the cache keys from each prefix-set. Delete all the cache keys and
  // the prefix sets themselves.
  const prefixSetKeys = prefixes.map(makePromptKeysPrefixSetKey);
  const prefixMatchingKeys = (
    await Promise.all(
      prefixSetKeys.map((prefixSetKey) => {
        return redisConn.sMembers(prefixSetKey);
      }),
    )
  ).flat();
  const allKeys = prefixMatchingKeys.concat(prefixSetKeys);
  if (!allKeys.length) {
    return;
  }
  await redisConn.del(allKeys);
}

export async function invalidateEnvironmentCache(
  environmentId: string,
): Promise<void> {
  const redisConn = await getRedis();

  // Get the environment-specific prefix set key
  const prefixSetKey = makeEnvironmentPrefixSetKey(environmentId);
  // Get all cache keys from this prefix set
  const cacheKeys = await redisConn.sMembers(prefixSetKey);

  // Delete all cache keys and the prefix set itself
  const allKeys = cacheKeys.concat([prefixSetKey]);
  await redisConn.del(allKeys);
}

export async function invalidatePromptEnvironmentCache({
  environmentId,
  environmentSlug,
  promptId,
  promptSlug,
  projectName,
  projectId,
}: {
  environmentId: string;
  environmentSlug: string;
  promptId: string;
  promptSlug: string;
  projectName: string;
  projectId: string;
}): Promise<void> {
  if (!environmentSlug) {
    return; // Environment slug is required for cache keys
  }

  const redisConn = await getRedis();

  // Collect all possible cache prefixes for this prompt
  const promptPrefixes = collectPromptCacheInvalidationPrefixes({
    promptId,
    slug: promptSlug,
    projectName: projectName,
    projectId,
  });

  // Get the environment-specific prefix set key
  const environmentPrefixSetKey = makeEnvironmentPrefixSetKey(environmentId);

  // Get all cache keys from the environment prefix set
  const allEnvironmentCacheKeys = await redisConn.sMembers(
    environmentPrefixSetKey,
  );

  const prefixPatterns = new Set(
    promptPrefixes.map(
      (prefix) => `${prefix}:environment_slug:${environmentSlug}:`,
    ),
  );

  // Filter cache keys using the prefix patterns set
  const keysToInvalidate = allEnvironmentCacheKeys.filter((cacheKey) => {
    return Array.from(prefixPatterns).some((pattern) =>
      cacheKey.startsWith(pattern),
    );
  });

  if (keysToInvalidate.length === 0) {
    return; // No keys to invalidate
  }

  // Remove the keys from Redis and from the environment prefix set
  await Promise.all([
    redisConn.del(keysToInvalidate),
    redisConn.sRem(environmentPrefixSetKey, keysToInvalidate),
  ]);
}

export function promptCacheKeyPrefix(
  args:
    | { promptId: string }
    | { projectId: string; slug: string }
    | { projectName: string; slug: string },
): string {
  if ("promptId" in args) {
    return `load_prompt_id:${args.promptId}`;
  } else if ("projectId" in args) {
    return `load_prompt_project_id_slug:${args.projectId}:${args.slug}`;
  } else {
    return `load_prompt_project_id_slug:${args.projectName}:${args.slug}`;
  }
}

function makePromptKeysPrefixSetKey(promptPrefix: string): string {
  return `${REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX}:${promptPrefix}`;
}

function makeEnvironmentPrefixSetKey(environmentId: string): string {
  return `${REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX}:environment_id:${environmentId}`;
}

export function constructCacheKey({
  prefix,
  versionSpecifier,
  token,
}: {
  prefix: string;
  versionSpecifier?: CacheVersionSpecifier;
  token?: string;
}): string {
  const tokenDigest = sha256Digest(token ?? "anon");
  if (versionSpecifier) {
    return `${prefix}:${versionSpecifier.kind}:${versionSpecifier.value}:${tokenDigest}`;
  } else {
    return `${prefix}:latest:${tokenDigest}`;
  }
}

export function constructPrefixSetKey({
  prefix,
  versionSpecifier,
}: {
  prefix: string;
  versionSpecifier?: PrefixSetVersionSpecifier;
}): string | undefined {
  // If there is a version, don't include the key in any prefix set (versions don't get auto-invalidated)
  if (versionSpecifier?.kind === "version") {
    return undefined;
  } else if (versionSpecifier?.kind === "environment_id") {
    // Environment-specific prefix set for invalidation by environment
    return makeEnvironmentPrefixSetKey(versionSpecifier.value);
  } else {
    // General prefix set for latest/default caching
    return makePromptKeysPrefixSetKey(prefix);
  }
}

export function constructPrefixSetAndCacheKeys({
  prefix,
  versionSpecifier,
  token,
}: {
  prefix: string;
  versionSpecifier?: CacheVersionSpecifier;
  token?: string;
}): { prefixSetKey: string | undefined; cacheKey: string } {
  // Only version specifiers work for prefix sets; environment_slug specifiers are ignored
  const prefixSetVersionSpecifier: PrefixSetVersionSpecifier | undefined =
    versionSpecifier?.kind === "version" ? versionSpecifier : undefined;

  return {
    prefixSetKey: constructPrefixSetKey({
      prefix,
      versionSpecifier: prefixSetVersionSpecifier,
    }),
    cacheKey: constructCacheKey({ prefix, versionSpecifier, token }),
  };
}
