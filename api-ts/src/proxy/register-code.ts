import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { getPG } from "../db/pg";
import { CODE_BUNDLE_BUCKET_NAME, CODE_BUNDLE_BUCKET_PREFIX } from "../env";
import { BadRequestError, ForbiddenError, InternalServerError } from "../util";
import { Runtime, runtimeContextSchema } from "@braintrust/core/typespecs";
import type { GetResFn } from "./proxy";
import { finish } from "./request";
import { extractAllowedOrigin, ORIGIN_HEADER } from "../cors";
import { parseBraintrustAuthHeader } from "../auth-header";
import { checkTokenAuthorized } from "../token_auth";
import { wrapperCodeHash } from "./code-wrappers";
import { canUseLambdaQuarantine } from "../lambda-quarantine/pool";
import { makeObjectStore } from "../object-storage/object-storage";
import { getLogger } from "../instrumentation/logger";
export const registerCodeRequestSchema = z
  .strictObject({
    org_id: z.string(),
    runtime_context: runtimeContextSchema,
  })
  .strip();

export async function handleUploadCode({
  headers,
  body: bodyRaw,
  setHeader,
  setStatusCode,
  getRes,
}: {
  headers: Record<string, string>;
  body: unknown;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
}) {
  const parsed = registerCodeRequestSchema.safeParse(bodyRaw);
  if (!parsed.success) {
    throw new BadRequestError(`Invalid request: ${parsed.error.message}`);
  }
  if (!CODE_BUNDLE_BUCKET_NAME || CODE_BUNDLE_BUCKET_NAME.length === 0) {
    throw new InternalServerError("Code uploading not configured");
  }

  const body = parsed.data;
  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const ctxToken = parseBraintrustAuthHeader(headers);

  const me = await (await checkTokenAuthorized({ ctxToken, appOrigin })).me;

  let found = false;
  for (const orgs of me.organizations) {
    if (orgs.id === body.org_id) {
      found = true;
    }
  }
  if (!found) {
    throw new ForbiddenError(`Access denied to org ${body.org_id}`);
  }

  const codeHash = await wrapperCodeHash({
    runtimeSpec: body.runtime_context,
    inline: false,
    lambda: canUseLambdaQuarantine(),
  });

  const db = getPG();
  let committed = false;
  const path = `${CODE_BUNDLE_BUCKET_PREFIX}${uuidv4()}${runtimeToSuffix(
    body.runtime_context.runtime,
  )}`;
  let url;
  let bundleId;

  const conn = await db.connect();
  try {
    await conn.query("BEGIN");
    const bundleInfo = await conn.query(
      `INSERT INTO code_bundles (org_id, user_id, path, code_hash) VALUES ($1, $2, $3, $4) RETURNING id`,
      [body.org_id, me.id, path, codeHash],
    );
    bundleId = bundleInfo.rows[0].id;

    await conn.query("COMMIT");
    committed = true;

    const objectStore = await makeObjectStore();
    url = await objectStore.signedPutUrl({
      bucket: CODE_BUNDLE_BUCKET_NAME,
      key: path,
      expiresIn: 60 * 60,
      contentType: "application/octet-stream",
      allowOverwrite: true,
    });
  } catch (e) {
    getLogger().error({ error: e }, "Failed to get signed url");
    throw e;
  } finally {
    try {
      if (!committed) {
        await conn.query("ROLLBACK");
      }
    } finally {
      conn.release();
    }
  }

  setHeader("Content-Type", "application/json");
  return finish({
    getRes,
    setStatusCode,
    statusCode: 200,
    message: JSON.stringify({ url, bundleId }),
  });
}

function runtimeToSuffix(runtime: Runtime) {
  switch (runtime) {
    case "node":
      return ".js.gz";
    case "python":
      return ".py.zip";
    default:
      throw new Error(`Unsupported runtime ${runtime}`);
  }
}
