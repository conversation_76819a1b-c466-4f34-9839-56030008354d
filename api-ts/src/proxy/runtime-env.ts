import { _urljoin } from "@braintrust/core";

export function getRuntimeEnv({
  ctxToken,
  appOrigin,
  proxyUrl,
  orgName,
}: {
  ctxToken: string | undefined;
  appOrigin: string;
  proxyUrl: string;
  orgName: string | undefined;
}) {
  const ret: Record<string, string> = {};
  ret["BRAINTRUST_APP_URL"] = appOrigin;
  if (ctxToken) {
    ret["BRAINTRUST_API_KEY"] = ctxToken;
  }
  if (orgName) {
    ret["BRAINTRUST_ORG_NAME"] = orgName;
    ret["OPENAI_BASE_URL"] = _urljoin(
      proxyUrl,
      "btorg",
      encodeURIComponent(orgName),
    );
    ret["ANTHROPIC_BASE_URL"] = _urljoin(
      proxyUrl,
      "btorg",
      encodeURIComponent(orgName),
      "anthropic",
    );
  } else {
    ret["OPENAI_BASE_URL"] = proxyUrl;
    ret["ANTHROPIC_BASE_URL"] = _urljoin(proxyUrl, "anthropic");
  }

  if (ctxToken) {
    ret["OPENAI_API_KEY"] = ctxToken;
    ret["ANTHROPIC_API_KEY"] = ctxToken;
  }

  return ret;
}
