import { beforeAll, expect, test } from "vitest";
import fs from "fs";
import path from "path";
import {
  createConditionalJsonProcessingStream,
  createFinalValuePassThroughStream,
  devNullWritableStream,
  InvokeResponse,
} from "@braintrust/local/functions";
import { callEventSchema, streamingModeEnum } from "@braintrust/core/typespecs";
import {
  createParser,
  EventSourceParser,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";

const snapshotFileDir = path.join(__dirname, "test-proxy", "snapshots");
const testSnapshots: Record<string, string> = {};

beforeAll(() => {
  // Read each file in the snapshots directory
  for (const file of fs.readdirSync(snapshotFileDir)) {
    testSnapshots[file] = fs.readFileSync(
      path.join(snapshotFileDir, file),
      "utf-8",
    );
  }
});

// XXX: Make sure each snapshot file ends with a double new line!
function createSnapshotStream(name: string, chunkSize: number): ReadableStream {
  const data = testSnapshots[name];
  if (!data) {
    throw new Error(`Snapshot ${name} not found`);
  }

  const encoder = new TextEncoder();
  let pos = 0;

  const readable = new ReadableStream<Uint8Array>({
    start(controller) {
      while (true) {
        if (pos >= data.length) {
          controller.close();
          return;
        }

        const chunk = data.slice(pos, pos + chunkSize);
        pos += chunkSize;

        controller.enqueue(encoder.encode(chunk));
      }
    },
  });

  return readable;
}

const longAppleStory = `The origin story of Apple is a fascinating tale of innovation, perseverance, and a passion for changing the world. Here's the story of how Apple, one of the most iconic and influential companies in the world, was founded.

**The Early Years: Steve Jobs and Steve Wozniak Meet**

In 1971, two individuals, Steve Jobs and Steve Wozniak, met at the Homebrew Computer Club in Mountain View, California. Wozniak, a brilliant engineer, had just designed the first personal computer, the Apple I, while working as an intern at Hewlett-Packard (HP). Jobs, a charismatic entrepreneur, was a 21-year-old college dropout who was fascinated by electronics and design.

The two Steves quickly became friends and collaborators, united by their passion for innovation and their vision for a more accessible and user-friendly computer for the masses. Wozniak showed Jobs his Apple I design, which didn't have a keyboard, monitor, or casing. Jobs was impressed and saw an opportunity to turn Wozniak's design into a commercial success.

**The Founding of Apple**

In April 1976, Jobs and Wozniak founded Apple Computer in Jobs' parents' garage in Los Gatos, California. The company was formally incorporated as Apple Computer, Inc. on January 3, 1977. Initially, Apple's headquarters was a small garage, where Wozniak continued to work on the Apple I. Jobs was responsible for marketing, selling, and developing new products.

**The Apple I and II: Early Success**

In June 1976, Wozniak and Jobs designed the Apple I, a kit computer that could be assembled by hobbyists. The Apple I was priced at $666.66, and Wozniak and Jobs hoped to sell 50 machines, but they ended up selling over 200. The Apple II, introduced in 1977, was one of the first highly successful mass-produced personal computers, designed to be user-friendly and aesthetically pleasing.

**The Apple Logo and Name**

The Apple logo, designed by Ron Wayne, a close friend of Jobs, was inspired by a picture of an apple on a poster Jobs had in his room. The logo was meant to symbolize the idea that "an apple a day keeps the doctor away." Wayne also designed Apple's original company logo, which featured a picture of Isaac Newton under an apple tree.

**The Early Years of Apple (1977-1980)**

Apple's early years were marked by rapid growth, innovation, and controversy. In 1978, Apple introduced the Apple III, which was designed to be a business-oriented computer but ultimately failed to gain market share. In 1979, Apple introduced the Apple Lisa, which was a graphical user interface (GUI) computer that was a precursor to the Macintosh.

**The Macintosh Revolution (1984)**

The Macintosh 128k, released in 1984, revolutionized the personal computer industry with its user-friendly GUI, innovative mouse, and colorful graphics. The Macintosh was a commercial success, and Apple's stock soared.

The story of Apple's origin is one of innovation, perseverance, and a passion for changing the world. From its humble beginnings in a garage to its current status as a global technology leader, Apple has remained committed to its vision of empowering humanity through technology.`;

const cases: {
  name: string;
  auto: unknown;
  parallel: unknown;
  parallelWithToolCalls: unknown;
}[] = [
  {
    name: "pure_text.txt",
    auto: "1+1 equals 2.",
    parallel: [],
    parallelWithToolCalls: [],
  },
  {
    name: "newlines.txt",
    auto: "one\n\n\ntwo\n\n\nthree",
    parallel: [],
    parallelWithToolCalls: [],
  },
  {
    name: "tool_call.txt",
    auto: {
      location: "San Francisco, CA",
      unit: "fahrenheit",
    },
    parallel: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "fahrenheit",
        },
      },
    ],
    parallelWithToolCalls: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "fahrenheit",
        },
        tool_call_id: "call_QSe1Xpc9C49xQEz8CEXWvHWt",
      },
    ],
  },
  {
    name: "function_call.txt",
    auto: {
      location: "San Francisco, CA",
      unit: "fahrenheit",
    },
    parallel: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "fahrenheit",
        },
      },
    ],
    parallelWithToolCalls: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "fahrenheit",
        },
      },
    ],
  },
  {
    name: "mixed_tool_call.txt",
    auto: {
      location: "San Francisco, CA",
    },
    parallel: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
        },
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "call_tXYN5HezaDu7GJiGziuwGpty",
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
        },
      },
    ],
  },
  {
    // Just returns the first tool call (both are the same tool)
    name: "tool_call_double.txt",
    auto: {
      location: "San Francisco, CA",
    },
    parallel: [
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
        },
      },
      {
        function_name: "get_current_weather",
        arguments: {
          location: "Los Angeles, CA",
        },
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "call_qbpM9di9qvu8kg1GYK8E6tC3",
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
        },
      },
      {
        tool_call_id: "call_In61sMB6kJHANG26F7zQIe4p",
        function_name: "get_current_weather",
        arguments: {
          location: "Los Angeles, CA",
        },
      },
    ],
  },
  {
    // Just returns the first tool call (two different tool calls)
    name: "multi_tool_call.txt",
    auto: { a: 1, b: 1 },
    parallel: [
      {
        function_name: "calculator",
        arguments: { a: 1, b: 1 },
      },
      {
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "celsius",
        },
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "call_JtCL1UXxEhe0iHlYkqhrFqTk",
        function_name: "calculator",
        arguments: { a: 1, b: 1 },
      },
      {
        tool_call_id: "call_01ZYvC1tzKeHxpXIJuUf2rqc",
        function_name: "get_current_weather",
        arguments: {
          location: "San Francisco, CA",
          unit: "celsius",
        },
      },
    ],
  },
  {
    name: "cerebras_tool.txt",
    auto: { a: 5, b: 10 },
    parallel: [
      {
        function_name: "add",
        arguments: { a: 5, b: 10 },
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "36e1f8312",
        function_name: "add",
        arguments: { a: 5, b: 10 },
      },
    ],
  },
  {
    name: "cerebras_long.txt",
    auto: longAppleStory,
    parallel: longAppleStory,
    parallelWithToolCalls: longAppleStory,
  },
  {
    name: "newline-tools.txt",
    auto: {
      reasons:
        "The medical term 'cephalgia' was replaced with 'headache' even though 'cephalgia' was used verbatim by the patient in the transcript, causing an error.",
      choice: "Fail",
    },
    parallel: [
      {
        arguments: {
          reasons:
            "The medical term 'cephalgia' was replaced with 'headache' even though 'cephalgia' was used verbatim by the patient in the transcript, causing an error.",
          choice: "Fail",
        },
        function_name: "select_choice",
      },
    ],
    parallelWithToolCalls: [
      {
        function_name: "select_choice",
        arguments: {
          reasons:
            "The medical term 'cephalgia' was replaced with 'headache' even though 'cephalgia' was used verbatim by the patient in the transcript, causing an error.",
          choice: "Fail",
        },
        tool_call_id: "call_E86RNn4q8uKtnC1NimgWj4Fz",
      },
    ],
  },
  {
    name: "multi_tool_call_single_chunk.txt",
    auto: {
      location: "Paris, France",
    },
    parallel: [
      {
        function_name: "weather",
        arguments: { location: "Paris, France" },
      },
      {
        function_name: "traffic",
        arguments: { location: "Paris, France" },
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "a7db71e4-be8d-4e70-bb46-cd8294d5a84c",
        function_name: "weather",
        arguments: { location: "Paris, France" },
      },
      {
        tool_call_id: "dac1d3b8-6f21-4972-8abc-ef3cc924b1da",
        function_name: "traffic",
        arguments: { location: "Paris, France" },
      },
    ],
  },
  {
    name: "tool_call_empty.txt",
    auto: {},
    parallel: [
      {
        function_name: "get_system_status",
        arguments: {},
      },
    ],
    parallelWithToolCalls: [
      {
        tool_call_id: "call_zcyf06n0mBAUUUlhycLY5X66",
        function_name: "get_system_status",
        arguments: {},
      },
    ],
  },
  {
    name: "claude_reasoning.txt",
    auto: `The Latin name for the sun is "Sol." \n\nThis is where we get words like "solar system," "solar energy," and "solar panels" in English. In Roman mythology, Sol was also personified as a deity, though not as prominently featured in their mythology as Helios was in Greek mythology.`,
    parallel: [],
    parallelWithToolCalls: [],
  },
  {
    name: "claude_reasoning_tool.txt",
    auto: {
      location: "Boston",
    },
    parallel: [
      {
        arguments: {
          location: "Boston",
        },
        function_name: "get_current_weather",
      },
    ],
    parallelWithToolCalls: [
      {
        arguments: {
          location: "Boston",
        },
        function_name: "get_current_weather",
        tool_call_id: "toolu_01QGBtHZqP7g19eEh1e2Ygp5",
      },
    ],
  },
];

for (const c of cases) {
  for (const mode of streamingModeEnum.options) {
    for (const includeToolCallIds of [true, false]) {
      test(`stream-parser.test.ts ${c.name} ${mode} ${includeToolCallIds}`, async () => {
        const name = c.name;

        const readable = createSnapshotStream(name, 24);
        const writable = devNullWritableStream();

        let seenDoneEvent = false;
        let seenErrorEvent = false;
        const result = await new Promise((resolve, reject) => {
          try {
            const valueProcessor = createConditionalJsonProcessingStream({
              mode,
              isFail: () => false,
              includeToolCallIds,
              isJSONResponse: false,
            });

            const seenDoneEventProcessor = new TransformStream({
              transform(chunk, controller) {
                const decoded = new TextDecoder().decode(chunk);
                if (decoded.includes("event: done")) {
                  seenDoneEvent = true;
                }
                if (decoded.includes("event: error")) {
                  seenErrorEvent = true;
                }
                controller.enqueue(chunk);
              },
            });

            const finalValueProcessor = createFinalValuePassThroughStream({
              onFinal: resolve,
              getStatusCode: () => 200,
            });

            readable
              .pipeThrough(valueProcessor)
              .pipeThrough(seenDoneEventProcessor)
              .pipeThrough(finalValueProcessor)
              .pipeTo(writable)
              .catch((e) => {
                // Keep console.error in tests for better visibility during test runs
                console.error("Error in pipeline:", e);
                reject(e);
              });
          } catch (e) {
            // Keep console.error in tests for better visibility during test runs
            console.error("Error setting up test:", e);
            reject(e);
          }
        });

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        const { data: resultData, error } = result as InvokeResponse;
        if (error) {
          throw error;
        }

        expect(seenDoneEvent).toBe(true);
        expect(seenErrorEvent).toBe(false);

        expect(resultData).toEqual(
          c[
            includeToolCallIds && mode === "parallel"
              ? "parallelWithToolCalls"
              : mode
          ],
        );
      });
    }
  }
}

test("empty anthropic stream", async () => {
  const readable = createSnapshotStream("tool_call_empty_claude.txt", 24);
  const jsonChunks: string[] = [];
  await new Promise((resolve, reject) => {
    try {
      const valueProcessor = createConditionalJsonProcessingStream({
        mode: "parallel",
        isFail: () => false,
        includeToolCallIds: true,
        isJSONResponse: false,
      });

      readable
        .pipeThrough(valueProcessor)
        .pipeThrough(createJsonChunksProcessor(jsonChunks))
        .pipeTo(
          new WritableStream({
            close() {
              resolve(null);
            },
          }),
        )
        .catch((e) => {
          console.error(e);
          reject(e);
        });
    } catch (e) {
      console.error(e);
      reject(e);
    }
  });

  const jsonValue = JSON.parse(jsonChunks.join(""));
  console.log(jsonValue);
  expect(jsonValue).toEqual([
    {
      function_name: "get_system_status",
      tool_call_id: "toolu_014nEz81byEZEFEgjVA6vbFD",
      arguments: {},
    },
  ]);
});

function createJsonChunksProcessor(jsonChunks: string[]): TransformStream {
  const decoder = new TextDecoder();
  let eventSourceParser: EventSourceParser;

  return new TransformStream({
    start(controller) {
      eventSourceParser = createParser((event) => {
        if (event.type === "reconnect-interval") {
          return;
        }

        const data = callEventSchema.parse(event);
        if (data.event === "json_delta") {
          jsonChunks.push(data.data);
        }
      });
    },
    transform(chunk, controller) {
      eventSourceParser.feed(decoder.decode(chunk));
      controller.enqueue(chunk);
    },
  });
}
