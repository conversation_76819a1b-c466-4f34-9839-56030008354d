import { codeBundleSchema } from "@braintrust/core/typespecs";
import { z } from "zod";
import { hookDataSchema } from "./lambda-util";

export const wrapperArgsSchema = z.strictObject({
  location: codeBundleSchema.shape.location,
  arg: z.any().optional(),
  hookData: hookDataSchema.optional(),
  code: z.string(),
  parent: z.string().optional(),
});
export type WrapperArgs = z.infer<typeof wrapperArgsSchema>;
