import { codeBundleSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

export const hookDataSchema = z.object({
  expected: z.any().optional(),
  metadata: z.any().optional(),
});
export type HookData = z.infer<typeof hookDataSchema>;

const baseEventFormat = z.object({
  input: z.any(),
  hook_data: hookDataSchema.optional(),
  env: z.record(z.string()),
  parent: z.string().optional(),
});

export const bundledEventFormat = baseEventFormat.extend({
  location: codeBundleSchema.shape.location,
});

export const inlineEventFormat = baseEventFormat.extend({
  code: z.string(),
});

export function initializeRuntimeEnv(env: Record<string, string>) {
  for (const [key, value] of Object.entries(env)) {
    process.env[key] = value;
  }
}
