import braintrust

project = braintrust.projects.create(name=f"prompt with tools")

all_messages = []
all_messages.append(
    {
        "role": "system",
        "content": "This is a test prompt for the Braintrust trial. Please respond with a simple greeting!!!",
    }
)
all_messages.append({"role": "user", "content": "Hi"})

project.prompts.create(
    name="test_prompt",
    slug="test-prompt",
    model="claude-3-5-sonnet-latest",
    messages=all_messages,
    if_exists="error",
)

project.publish()
