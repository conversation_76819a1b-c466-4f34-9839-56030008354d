import {
  SpanComponentsV3,
  SpanComponentsV3Data,
  SpanObjectTypeV3,
} from "@braintrust/core";
import argparse from "argparse";
import { Levenshtein } from "autoevals";
import { initDataset, newId, runEvaluator, withParent } from "braintrust";

const noopProgressReporter = {
  start: () => {},
  stop: () => {},
  increment: () => {},
};

async function main() {
  const parser = new argparse.ArgumentParser();
  parser.add_argument("--prompt-session-id", { required: true });
  const args = parser.parse_args();

  const generation = newId();
  console.log("Generation", generation);

  const parent: SpanComponentsV3Data = {
    object_type: SpanObjectTypeV3.PROMPT_SESSION,
    object_id: args.prompt_session_id,
    propagated_event: {
      span_attributes: {
        generation,
      },
    },
  };

  const spanParent = new SpanComponentsV3(parent).toStr();

  const evaluator = {
    evalName: "playx",
    projectName: "playx",
    data: initDataset({
      project: "Playx",
      dataset: "Playx",
    }),
    task: async (_input: string) => {
      const words = [
        "World",
        "Hello",
        "Planet",
        "Universe",
        "Galaxy",
        "Star",
        "Ocean",
        "Mountain",
      ];
      return words[Math.floor(Math.random() * words.length)];
    },
    scores: [Levenshtein],
  };

  const result = await withParent(spanParent, async () => {
    return await runEvaluator(null, evaluator, noopProgressReporter, []);
  });

  console.log(result);
}

main().catch(console.error);
