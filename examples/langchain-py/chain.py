import asyncio

from braintrust import flush, init_logger
from braintrust_langchain import BraintrustCallbackHandler
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI


async def main():
    init_logger(project="langchain-example")

    handler = BraintrustCallbackHandler()

    prompt = ChatPromptTemplate.from_template("What is 1 + {number}?")
    model = ChatOpenAI(model="gpt-4o-mini")

    chain = prompt | model

    await chain.ainvoke({"number": "2"}, config={"callbacks": [handler]})

    flush()


if __name__ == "__main__":
    asyncio.run(main())
