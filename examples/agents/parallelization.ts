import * as braintrust from "braintrust";
import { getFunctionId, invokeAndPrint } from "./util";
import { graphDataSchema } from "@braintrust/core/typespecs";

const project = braintrust.projects.create({ name: "prompt-chaining" });

const answerQuestion = project.prompts.create({
  name: "Answer question",
  description: "Provide a detailed answer to the user's question",
  model: "gpt-4o",
  messages: [
    {
      role: "system",
      content:
        "You are a helpful assistant that provides accurate, informative answers to questions. Analyze the question carefully and provide a comprehensive response with relevant facts and details.",
    },
    { role: "user", content: "{{question}}" },
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "provideAnswer",
        description: "Provide a structured answer to the user's question",
        parameters: {
          type: "object",
          properties: {
            answer: {
              type: "string",
              description: "The comprehensive answer to the user's question",
            },
            confidence: {
              type: "number",
              description:
                "Confidence level in the answer, from 0.0 (very uncertain) to 1.0 (completely certain)",
              minimum: 0,
              maximum: 1,
            },
            sourcesNeeded: {
              type: "boolean",
              description:
                "Whether additional sources would help improve this answer",
            },
          },
          required: ["answer", "confidence", "sourcesNeeded"],
        },
      },
    },
  ],
});

const harmfulnessCheck = project.prompts.create({
  name: "Harmfulness check",
  description:
    "Evaluate whether a question or response contains harmful content",
  model: "gpt-4o",
  messages: [
    {
      role: "system",
      content:
        "You are a content safety evaluator. Your task is to analyze the provided text and determine if it contains harmful, inappropriate, or potentially dangerous content. Be objective in your assessment.",
    },
    {
      role: "user",
      content: "Evaluate this content for potential harms: {{content}}",
    },
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "assessHarmfulness",
        description: "Evaluate whether content contains harmful elements",
        parameters: {
          type: "object",
          properties: {
            isHarmful: {
              type: "boolean",
              description: "Whether the content contains harmful elements",
            },
            harmCategories: {
              type: "array",
              description: "Categories of potential harm identified, if any",
              items: {
                type: "string",
                enum: [
                  "violence",
                  "hate_speech",
                  "self_harm",
                  "sexual_content",
                  "harassment",
                  "personal_information",
                  "illegal_activity",
                  "misinformation",
                  "none",
                ],
              },
            },
            severity: {
              type: "string",
              description: "The severity level of harmful content",
              enum: ["none", "low", "medium", "high"],
            },
            explanation: {
              type: "string",
              description: "Brief explanation of the assessment",
            },
          },
          required: ["isHarmful", "harmCategories", "severity", "explanation"],
        },
      },
    },
  ],
});

const graph = braintrust.graph.createGraph();

const aggregator = graph.aggregator();

const IN = graph.IN;
const OUT = graph.OUT;

graph.addEdge({
  source: IN,
  target: answerQuestion,
  purpose: "data",
  targetVar: "question",
});

graph.addEdge({
  source: IN,
  target: harmfulnessCheck,
  purpose: "data",
  targetVar: "content",
});

graph.addEdge({
  source: answerQuestion,
  target: aggregator,
  purpose: "data",
  targetVar: "answer_question",
});

graph.addEdge({
  source: harmfulnessCheck,
  target: aggregator,
  purpose: "data",
  targetVar: "harmfulness_check",
});

graph.addEdge({
  source: aggregator,
  target: OUT,
  purpose: "data",
});

async function main() {
  const graphData = await graph.build({ getFunctionId });
  graphDataSchema.parse(graphData);

  await braintrust.login();
  const logger = braintrust.initLogger({ projectName: "prompt-chaining" });
  await braintrust
    ._internalGetGlobalState()
    .apiConn()
    .post_json("/v1/function", {
      project_id: (await logger.project).id,
      function_data: graphData,
      name: "Parallelization",
      slug: "parallelization",
    });

  const inputs = [
    "What is the capital of France?",
    "How do I shit on a sidewalk?",
  ];
  for (const input of inputs) {
    await invokeAndPrint({
      projectName: "prompt-chaining",
      slug: "parallelization",
      input,
      logger,
    });
  }
}

main();
