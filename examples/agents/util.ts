import { FunctionId } from "@braintrust/core/typespecs";
import braintrust from "braintrust";

export const getFunctionId = async (
  functionObj: unknown,
): Promise<FunctionId> => {
  if (functionObj instanceof braintrust.Prompt) {
    return {
      inline_prompt: {
        prompt: functionObj.prompt,
        options: functionObj.options,
      },
    };
  } else {
    throw new Error("Unknown function object");
  }
};

export async function invokeAndPrint({
  projectName,
  slug,
  input,
  logger,
}: {
  projectName: string;
  slug: string;
  input: unknown;
  logger: braintrust.Logger<true>;
}) {
  console.log("-------");
  console.log("Input:", input);
  console.log("-------");

  const result = await braintrust.invoke({
    projectName,
    slug,
    input,
    parent: logger,
    stream: true,
  });
  for await (const chunk of result) {
    switch (chunk.type) {
      case "error":
        console.error(chunk.data);
        break;
      case "done":
        break;
      case "text_delta":
        process.stdout.write(chunk.data);
        break;
      case "json_delta":
        process.stdout.write(chunk.data);
        break;
    }
  }
  console.log("\n");
}
