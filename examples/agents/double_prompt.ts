import { FunctionId, graphDataSchema } from "@braintrust/core/typespecs";
import * as braintrust from "braintrust";
import { z } from "zod";

const project = braintrust.projects.create({ name: "graph-examples" });

export const isMathQuestionSchema = z.object({
  isMathQuestion: z.boolean(),
});

const initialLLMCall = project.prompts.create({
  name: "Is math question?",
  description: "Is the question a math question?",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content:
        "Identify whether the question is a math question or not. If it is, return just the formula to be solved.",
    },
    { role: "user", content: "{{input}}" },
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "isMathQuestion",
        description: "Is the question a math question?",
        parameters: {
          type: "object",
          properties: {
            is_math_question: { type: "boolean" },
            formula: { type: "string" },
          },
          required: ["is_math_question", "formula"],
        },
      },
    },
  ],
});

const solveFormula = project.prompts.create({
  name: "Solve formula",
  description: "Solve the formula",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content: "Solve the formula and return the result.",
    },
    { role: "user", content: "{{input.formula}}" },
  ],
});

const graph = braintrust.graph.createGraph();

const getFunctionId = async (functionObj: unknown): Promise<FunctionId> => {
  if (functionObj instanceof braintrust.Prompt) {
    return {
      inline_prompt: {
        prompt: functionObj.prompt,
        options: functionObj.options,
      },
    };
  } else {
    throw new Error("Unknown function object");
  }
};

const IN = graph.IN;
const OUT = graph.OUT;

graph.addEdge({
  source: IN,
  target: initialLLMCall,
  purpose: "data",
});

graph.addEdge({
  source: initialLLMCall,
  target: solveFormula,
  purpose: "data",
});

graph.addEdge({
  source: solveFormula,
  target: OUT,
  purpose: "data",
});

async function main() {
  const graphData = await graph.build({ getFunctionId });
  graphDataSchema.parse(graphData);
  console.log(JSON.stringify(graphData, null, 2));

  await braintrust.login();
  const logger = braintrust.initLogger({ projectName: "prompt-chaining" });
  await braintrust
    ._internalGetGlobalState()
    .apiConn()
    .post_json("/v1/function", {
      project_id: (await logger.project).id,
      function_data: graphData,
      name: "double-prompt",
      slug: "double-prompt",
    });
  const result = await braintrust.invoke({
    projectName: "prompt-chaining",
    slug: "double-prompt",
    input:
      "bla bla bla im trying to add numbers together and i have 3 apples and 4 oranges and want to know how many fruit",
    parent: logger,
  });
  console.log(result);
}

main();
