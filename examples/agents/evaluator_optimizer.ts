import * as braintrust from "braintrust";
import { getFunctionId, invokeAndPrint } from "./util";
import { graphDataSchema } from "@braintrust/core/typespecs";

const project = braintrust.projects.create({ name: "graph-examples" });

// Generator prompt that produces the initial response
const generator = project.prompts.create({
  name: "Solution Generator",
  description: "Generate a solution to the user's problem",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content: "Generate a concise solution to the user's problem.",
    },
    { role: "user", content: "{{problem}}" },
  ],
});

// Evaluator prompt that improves the initial solution
const evaluator = project.prompts.create({
  name: "Solution Evaluator",
  description: "Evaluate and improve the generated solution",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content:
        "Evaluate the solution and provide feedback on how to improve it, or mark it as good enough.",
    },
    {
      role: "user",
      content: "Problem: {{problem}}\n\nProposed solution: {{solution}}.",
    },
  ],
  tools: [
    {
      type: "function",
      function: {
        name: "evaluateSolution",
        description:
          "Evaluate the solution and provide feedback on how to improve it, or mark it as good enough.",
        parameters: {
          type: "object",
          properties: {
            critique: {
              type: "string",
              description: "The critique of the solution",
            },
            isGoodEnough: {
              type: "boolean",
              description: "Whether the solution is good enough",
            },
          },
          required: ["critique", "isGoodEnough"],
        },
      },
    },
  ],
});

const graph = braintrust.graph.createGraph();

const IN = graph.IN;
const OUT = graph.OUT;

graph.addEdge({
  source: IN,
  target: generator,
  purpose: "data",
  targetVar: "problem",
});

graph.addEdge({
  source: generator,
  target: evaluator,
  purpose: "data",
  targetVar: "solution",
});

const doneGate = graph.gate({
  condition: "input.isGoodEnough",
});

graph.addEdge({
  source: evaluator,
  target: doneGate,
  purpose: "data",
});

// // NOTE: There's probably a nicer way to model this.
const aggregator = graph.aggregator();
graph.addEdge({
  source: doneGate,
  target: aggregator,
  purpose: "control",
  sourceVar: "true",
});

graph.addEdge({
  source: generator,
  target: aggregator,
  purpose: "data",
});
graph.addEdge({
  source: aggregator,
  target: OUT,
  purpose: "data",
});

const messageMaker = graph.promptTemplate({
  prompt: {
    type: "chat",
    messages: [
      {
        role: "user",
        content:
          "here is some critique from an expert. can you improve it?\n\n{{critique}}",
      },
    ],
  },
});

graph.addEdge({
  source: evaluator,
  target: messageMaker,
  purpose: "data",
});

graph.addEdge({
  source: doneGate,
  target: messageMaker,
  purpose: "control",
  sourceVar: "false",
});

graph.addEdge({
  source: messageMaker,
  target: generator,
  purpose: "messages",
  targetVar: "messages",
});

async function main() {
  const graphData = await graph.build({ getFunctionId });
  graphDataSchema.parse(graphData);

  await braintrust.login();
  const logger = braintrust.initLogger({ projectName: "prompt-chaining" });
  await braintrust
    ._internalGetGlobalState()
    .apiConn()
    .post_json("/v1/function", {
      project_id: (await logger.project).id,
      function_data: graphData,
      name: "Evaluator optimizer",
      slug: "evaluator-optimizer",
    });

  const inputs = ["How do I divide two floating point numbers?"];
  for (const input of inputs) {
    await invokeAndPrint({
      projectName: "prompt-chaining",
      slug: "evaluator-optimizer",
      input,
      logger,
    });
  }
}

main();
