import { graphDataSchema } from "@braintrust/core/typespecs";
import * as braintrust from "braintrust";
import { z } from "zod";
import { getFunctionId } from "./util";

const project = braintrust.projects.create({ name: "prompt-chaining" });

export const isMathQuestionSchema = z.object({
  isMathQuestion: z.boolean(),
});

const initialLLMCall = project.prompts.create({
  name: "Is math question?",
  description: "Is the question a math question?",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content:
        "Identify whether the question is a math question or not. If it is, return just the formula to be solved.",
    },
    { role: "user", content: "{{input}}" },
  ],
  params: {
    tool_choice: {
      type: "function",
      function: {
        name: "isMathQuestion",
      },
    },
  },
  tools: [
    {
      type: "function",
      function: {
        name: "isMathQuestion",
        description: "Is the question a math question?",
        parameters: {
          type: "object",
          properties: {
            is_math_question: { type: "boolean" },
            formula: { type: "string" },
          },
          required: ["is_math_question", "formula"],
        },
      },
    },
  ],
});

const solveFormula = project.prompts.create({
  name: "Solve formula",
  description: "Solve the formula",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content: "Solve the formula and return the result.",
    },
    { role: "user", content: "{{formula}}" },
  ],
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const validateFormula = project.prompts.create({
  name: "Validate formula",
  description: "Validate the formula",
  model: "gpt-4o-mini",
  messages: [
    {
      role: "system",
      content:
        "Validate the formula. Return the formula and an analysis of its correctness.",
    },
    {
      role: "user",
      content: "Formula: {{parsed.formula}}, Result: {{result}}",
    },
  ],
});

const graph = braintrust.graph.createGraph();

const IN = graph.IN;
const OUT = graph.OUT;

graph.addEdge({
  source: IN,
  target: initialLLMCall,
  purpose: "data",
});

const gate = graph.gate({
  condition: "input.is_math_question",
});

graph.addEdge({
  source: initialLLMCall,
  target: gate,
  targetVar: "input",
  purpose: "data",
});

graph.addEdge({
  source: initialLLMCall,
  target: solveFormula,
  targetVar: "input",
  purpose: "data",
});

graph.addEdge({
  source: gate,
  target: solveFormula,
  sourceVar: "true",
  purpose: "control",
});

graph.addEdge({
  source: initialLLMCall,
  target: validateFormula,
  targetVar: "parsed",
  purpose: "data",
});

graph.addEdge({
  source: solveFormula,
  target: validateFormula,
  targetVar: "result",
  purpose: "data",
});

const invalidQuestion = graph.literal("invalid question");

graph.addEdge({
  source: gate,
  target: invalidQuestion,
  sourceVar: "false",
  purpose: "control",
});

graph.addEdge({
  source: invalidQuestion,
  target: OUT,
  purpose: "data",
  targetVar: "input1",
});

graph.addEdge({
  source: validateFormula,
  target: OUT,
  purpose: "data",
  targetVar: "input2",
});

async function main() {
  const graphData = await graph.build({ getFunctionId });
  graphDataSchema.parse(graphData);

  await braintrust.login();
  const logger = braintrust.initLogger({ projectName: "prompt-chaining" });
  await braintrust
    ._internalGetGlobalState()
    .apiConn()
    .post_json("/v1/function", {
      project_id: (await logger.project).id,
      function_data: graphData,
      name: "prompt-chaining",
      slug: "prompt-chaining",
    });

  const inputs = ["What is the sum of 2 and 2?", "Is Eden a UX expert?"];
  for (const input of inputs) {
    console.log("Input:", input);
    console.log("-------");

    const result = await braintrust.invoke({
      projectName: "prompt-chaining",
      slug: "prompt-chaining",
      input: {
        input: input,
      },
      parent: logger,
      stream: true,
    });
    for await (const chunk of result) {
      switch (chunk.type) {
        case "error":
          console.error(chunk.data);
          break;
        case "done":
          break;
        case "text_delta":
          process.stdout.write(chunk.data);
          break;
        case "json_delta":
          process.stdout.write(chunk.data);
          break;
        default:
          console.error("unexpected chunk", chunk);
      }
    }
    console.log();
  }
}

main();

// graph.IN.then(initialLLMCall).then((input) =>
//   graph.gate({
//     // XXX Need to be able to precisely distinguish whether the condition
//     // is dependent on the input passed in or not.
//     condition: input.math_question,
//     true: graph.call(solveFormula, { formula: input.formula }),
//     false: graph.OUT.call(graph.literal("invalid question")), // TODO: this is probably something like false -> literal -> OUT in the graph itself. can we compress this to make it simpler?
//   })
// );

// IN.then(initialLLMCall).then(
//   (input: { is_math_question: boolean; formula: string }) =>
//     graph.gate({
//       condition: input.is_math_question,
//       true: solveFormula({ formula: input.formula })
//         .then((result) =>
//           validateFormula({
//             result,
//             formula: input.formula,
//           })
//         )
//         .then(OUT),
//       false: OUT("invalid question"),
//     })
// );
