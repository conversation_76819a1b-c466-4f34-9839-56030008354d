import { PromptData } from "@braintrust/core/typespecs";

const baseUrl = "http://localhost:8300";
const headers = {
  Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
  "Content-Type": "application/json",
};

async function main() {
  {
    const listResponse = await fetch(`${baseUrl}/list`, {
      method: "GET",
      headers,
    });
    const data = await listResponse.json();
    console.log(data);
  }

  console.log("should error");
  {
    const evalResponse = await fetch(`${baseUrl}/eval`, {
      method: "POST",
      headers,
      body: JSON.stringify({ name: "Simple eval" }),
    });
    const data = await evalResponse.json();
    console.log(JSON.stringify(data, null, 2));
  }

  console.log("ok this should work");
  {
    const prompt: PromptData = {
      prompt: {
        type: "chat",
        messages: [
          {
            role: "user",
            content: "{{input}}",
          },
        ],
      },
      options: {
        model: "gpt-4o",
      },
    };
    const evalResponse = await fetch(`${baseUrl}/eval`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        name: "Simple eval",
        parameters: {
          main: prompt,
        },
      }),
    });
    const reader = evalResponse.body?.getReader();
    if (!reader) {
      throw new Error("No reader");
    }
    const decoder = new TextDecoder();
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }
      console.log(decoder.decode(value));
    }
  }
}

main();
