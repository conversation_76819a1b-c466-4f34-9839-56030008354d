import { BraintrustCallbackHandler } from "@braintrust/langchain-js";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { flush, initLogger } from "braintrust";

initLogger({
  projectName: "langchain-example",
});

const handler = new BraintrustCallbackHandler();

const prompt = ChatPromptTemplate.fromTemplate(`What is 1 + {number}?`);
const model = new ChatOpenAI({
  model: "gpt-4o-mini",
});

const chain = prompt.pipe(model);

await chain.invoke({ number: "2" }, { callbacks: [handler] });

await flush();
