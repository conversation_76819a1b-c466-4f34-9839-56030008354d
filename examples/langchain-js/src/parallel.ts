import { BraintrustCallbackHandler } from "@braintrust/langchain-js";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableMap } from "@langchain/core/runnables";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "langchain-example",
});

const handler = new BraintrustCallbackHandler();

const model = new ChatOpenAI({ model: "gpt-4o-mini" });

const jokeChain = PromptTemplate.fromTemplate(
  "Tell me a joke about {topic}",
).pipe(model);
const poemChain = PromptTemplate.fromTemplate(
  "write a 2-line poem about {topic}",
).pipe(model);

const mapChain = RunnableMap.from({
  joke: joke<PERSON>hain,
  poem: poem<PERSON>hain,
});

await mapChain.invoke({ topic: "bear" }, { callbacks: [handler] });
