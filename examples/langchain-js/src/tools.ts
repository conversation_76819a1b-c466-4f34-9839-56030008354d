import { BraintrustCallback<PERSON>and<PERSON> } from "@braintrust/langchain-js";
import { tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";
import { z } from "zod";

initLogger({
  projectName: "langchain-example",
});

const handler = new BraintrustCallbackHandler();

const llm = new ChatOpenAI({
  model: "gpt-4o-mini",
});

const calculatorSchema = z.object({
  operation: z
    .enum(["add", "subtract", "multiply", "divide"])
    .describe("The type of operation to execute."),
  number1: z.number().describe("The first number to operate on."),
  number2: z.number().describe("The second number to operate on."),
});

const calculatorTool = tool(
  ({ operation, number1, number2 }) => {
    // Functions must return strings
    if (operation === "add") {
      return `${number1 + number2}`;
    } else if (operation === "subtract") {
      return `${number1 - number2}`;
    } else if (operation === "multiply") {
      return `${number1 * number2}`;
    } else if (operation === "divide") {
      return `${number1 / number2}`;
    } else {
      throw new Error("Invalid operation.");
    }
  },
  {
    name: "calculator",
    description: "Can perform mathematical operations.",
    schema: calculatorSchema,
  },
);

const llmWithTools = llm.bindTools([calculatorTool]);

await llmWithTools.invoke("What is 3 * 12", {
  callbacks: [handler],
});
