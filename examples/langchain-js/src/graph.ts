import { BraintrustCallbackHand<PERSON> } from "@braintrust/langchain-js";
import { END, START, StateGraph, StateGraphArgs } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { initLogger } from "braintrust";

initLogger({
  projectName: "langchain-example",
});

const handler = new BraintrustCallbackHandler();

// derived from: https://techcommunity.microsoft.com/blog/educatordeveloperblog/an-absolute-beginners-guide-to-langgraph-js/4212496
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type HelloWorldGraphState = Record<string, any>;

const graphStateChannels: StateGraphArgs<HelloWorldGraphState>["channels"] = {};

const model = new ChatOpenAI({
  model: "gpt-4o-mini",
  callbacks: [handler],
});

async function sayHello(state: HelloWorldGraphState) {
  const res = await model.invoke("Say hello");
  return res.content;
}

function sayBye(state: HelloWorldGraphState) {
  console.log(`From the 'sayBye' node: Bye world!`);
  return {};
}

const graphBuilder = new StateGraph({ channels: graphStateChannels }) // Add our nodes to the Graph
  .addNode("sayHello", sayHello)
  .addNode("sayBye", sayBye) // Add the edges between nodes
  .addEdge(START, "sayHello")
  .addEdge("sayHello", "sayBye")
  .addEdge("sayBye", END);

const helloWorldGraph = graphBuilder.compile();

await helloWorldGraph.invoke({}, { callbacks: [handler] });
