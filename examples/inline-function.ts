import { InvokeFunctionRequest } from "@braintrust/core/typespecs";

const jsCode = `
async function handler(arg: any) {
  throw new Error("not implemented");
  console.log(arg);
  return {
   score: 0.5,
   metadata: arg,
 };
}
`;

const pyCode = `
def handler(input: str, output, expected):
    return {
        "score": 0.5,
        "metadata": input,
    }
`;

const code = {
  node: jsCode,
  python: pyCode,
};

const versions = {
  node: "20",
  python: "3.12",
};

async function main() {
  for (const runtime of ["node"] as const) {
    const body: InvokeFunctionRequest = {
      inline_context: {
        runtime: runtime,
        version: versions[runtime],
      },
      code: code[runtime],
      input: {
        input: "hello",
        output: "world",
        expected: "globe",
      },
    };

    const result = await fetch("http://localhost:8000/function/invoke", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      },
      body: JSON.stringify(body),
    });
    if (!result.ok) {
      throw new Error(
        `Failed to invoke function: ${result.statusText}: ${await result.text()}`,
      );
    }
    console.log("SUCCESS!");
    const data = await result.json();
    console.log(data);
  }
}

main();
