import { init<PERSON>ogger, wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import <PERSON>A<PERSON> from "openai";
import { ChatCompletionTool } from "openai/resources";
import { OpenAIMessage } from "@braintrust/core/typespecs";

const messages: OpenAIMessage[] = [
  {
    role: "system",
    content: [
      {
        type: "text",
        text: "You are an AI assistant specialized in helping users with Braintrust platform tasks.\nBraintrust is a platform for creating and managing AI prompts, writing evals, collecting logs, creating datasets, and more.\n\nWhen working with datasets:\n- Dataset rows will be provided as JSON objects in a structured format\n- Analyze patterns in inputs and outputs to understand the task\n- Consider edge cases and potential gaps in the dataset\n- Respect the existing format when suggesting new examples\n\nWhen working with prompts:\n- If the prompt has not been run yet, use the rerun_prompt tool to get fresh performance results\n- Examine the prompt structure carefully before suggesting changes\n- Preserve important instructions and constraints\n- Focus on clarity, specificity, and alignment with the dataset\n- Consider how different models might interpret the prompt\n\nUnderstand the user's request carefully and use the most appropriate tool for the task.\nIf multiple tools are needed, use them in a logical sequence to achieve the user's goal.\nProvide clear explanations of what you're doing and why, especially when suggesting changes to prompts or datasets.\nIf no tools are appropriate for the request, respond conversationally with helpful guidance about Braintrust capabilities.\n\nWhen you need to generate content:\n- Use triple backticks for code blocks and JSON: ```language\ncode\n```\n- Format lists with clear bullet points or numbers\n- Use markdown formatting for headings and emphasis where appropriate\n- Present complex information in tables when it improves readability\n\nIf the user asks you to improve something, run enough tools to run the task, generate results, and iterate on it. Do not\nsuggest something and ask for permission to continue without actually trying it.\n",
        cache_control: {
          type: "ephemeral",
        },
      },
    ],
  },
  {
    role: "user",
    content:
      "please improve the performance, re-run as needed. do not ask me for permission to proceed. just proceed with steps.",
  },
  {
    role: "assistant",
    content: [
      {
        type: "text",
        text: "I'll help you improve the performance of the tasks in this playground. Let me first get a summary of the playground to understand what we're working with.",
        cache_control: {
          type: "ephemeral",
        },
      },
    ],
    tool_calls: [
      {
        id: "toolu_01X444KcosFKgGWnT2SUVXBk",
        type: "function",
        function: {
          name: "get_playground_summary",
          arguments: "{}",
        },
      },
    ],
  },
  {
    role: "tool",
    content:
      "- definition:\n    messages:\n      - content: Write a SQL-style query for {{input}}\n        role: user\n    model: gpt-4o-mini\n    params:\n      temperature: 0\n    tools:\n      - function:\n          description: Break down the query either into a MATCH or SQL call\n          name: QUERY\n          parameters:\n            $schema: http://json-schema.org/draft-07/schema#\n            additionalProperties: false\n            properties:\n              explanation:\n                description: Explanation of why I determined this query to be a match or SQL call\n                type: string\n              filter:\n                description: SQL filter clause (only if the query is not a match)\n                type:\n                  - string\n                  - 'null'\n              match:\n                description: Set to true if the query is a simple substring match\n                type: boolean\n              sort:\n                description: SQL filter clause (only if the query is not a match)\n                type:\n                  - string\n                  - 'null'\n              tags:\n                anyOf:\n                  - description: List of tags (only if the query is not a match)\n                    items:\n                      type: string\n                    type: array\n                  - type: 'null'\n                description: List of tags (only if the query is not a match)\n            required:\n              - match\n              - explanation\n            type: object\n        type: function\n  index: 0\n  metrics:\n    completion_tokens:\n      avg: 59.75\n    prompt_tokens:\n      avg: 228.833\n    total_tokens:\n      avg: 288.583\n  scores:\n    BTQL query checker:\n      avg: 0.042\n      max: 0.25\n      min: 0\n    QuerySimilarity:\n      avg: 0.452\n      max: 0.759\n      min: 0\n  taskName: test",
    tool_call_id: "toolu_01X444KcosFKgGWnT2SUVXBk",
  },
  {
    role: "assistant",
    content:
      "Now, let me examine the results of this task to understand what's happening and where improvements are needed.",
    tool_calls: [
      {
        id: "toolu_01AUgVZuz984k8e6vxwCVC1c",
        type: "function",
        function: {
          name: "get_results",
          arguments: '{"index":0,"numSamples":5}',
        },
      },
    ],
  },
  {
    role: "tool",
    content:
      "- expected:\n    explanation: We can filter by the prompt_tokens metric, which is a pre-defined metric, to only consider records with more than 1000.\n    filter: metrics.prompt_tokens > 1000\n  id: 1933ba9b-3fe5-4cf2-a051-c1c7978d4d76\n  input: more than 1000 prompt tokens\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a condition for filtering based on a numeric value (more than 1000), which is characteristic of SQL queries.\n    filter: prompt_tokens > 1000\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0.25\n    QuerySimilarity: 0.532\n- expected:\n    explanation: The query does not have semantic meaning that can be related to the named columns and JSON fields in the provided schema, so we fall back to a simple substring match to be safe.\n    filter: created <= NOW() - INTERVAL 1 DAY\n  id: 9c2eb14e-fea8-43b0-ab94-fce9b71e15d9\n  input: older than a day ago\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields: []\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a time condition that requires a SQL filter clause to express the 'older than a day ago' condition.\n    filter: timestamp < NOW() - INTERVAL '1 day'\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.571\n- expected:\n    explanation: The query does not have semantic meaning that can be related to the named columns and JSON fields in the provided schema, so we fall back to a simple substring match to be safe.\n    filter: input MATCH 'claude' or output MATCH 'claude'\n  id: f61f8f7f-da11-427a-bc28-0bf3d916cc33\n  input: references to claude\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query is looking for references to 'claude', which requires a specific SQL filter to search for that term in a database.\n    filter: reference_text LIKE '%claude%'\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.276\n- expected:\n    explanation: The query refers to the `output` column, so we use the `ILIKE` operator to check if the output contains the words 'snow' or 'winter'.\n    filter: output MATCH 'snow' OR output MATCH 'winter'\n  id: 675e2f39-50d3-4069-9cfb-7f1d0958c656\n  input: logs with snow or winter in the output\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query is looking for specific keywords in logs, which requires a SQL filter clause to search for those terms.\n    filter: logs LIKE '%snow%' OR logs LIKE '%winter%'\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.42\n- expected:\n    explanation: The query refers to logs created during a specific time period (December 2023), so we use date comparison operators on the 'created' field.\n    filter: created >= '2023-12-01' AND created < '2024-01-01'\n  id: 5f1a619b-79bd-4d73-b7d5-9d389a121bcb\n  input: created during December 2023\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a time frame for data retrieval, which requires a SQL filter clause to specify the date range.\n    filter: created_at >= '2023-12-01' AND created_at < '2024-01-01'\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0.25\n    QuerySimilarity: 0.759",
    tool_call_id: "toolu_01AUgVZuz984k8e6vxwCVC1c",
  },
  {
    role: "assistant",
    content:
      'Based on the results, I can see that this is a task for generating SQL-style queries based on natural language inputs. The model is currently using gpt-4o-mini and has low scores on "BTQL query checker" and "QuerySimilarity" metrics.\n\nLet me improve the prompt to better guide the model in generating the correct SQL queries. Here\'s my plan:\n\n1. Enhance the prompt to provide more context about the schema and expected output format\n2. Add examples to demonstrate the correct query format\n3. Provide clearer instructions on when to use MATCH vs SQL filters\n4. Rerun the task to see if performance improves\n\nLet me edit the task:',
    tool_calls: [
      {
        id: "toolu_015zK44yNTnESRQe8h1b2q1d",
        type: "function",
        function: {
          name: "edit_task",
          arguments:
            '{"index":0,"edits":[{"path":["definition","messages",0,"content"],"diff":"@@ -1 +1,37 @@\\n-Write a SQL-style query for {{input}}\\n+# SQL Query Generation Task\\n+\\n+You are a specialized SQL query generator for a log analysis system. Your task is to convert natural language queries into either SQL filter clauses or simple substring matches based on the query\'s nature.\\n+\\n+## Available Schema Fields:\\n+- span_type_info\\n+- _xact_id\\n+- input\\n+- output\\n+- error\\n+- expected\\n+- tags\\n+- scores\\n+- metrics (contains nested fields like metrics.prompt_tokens)\\n+- metadata\\n+- created (timestamp field)\\n+- id\\n+\\n+## Rules:\\n+1. If the query can be expressed as a SQL filter condition, use the appropriate SQL syntax\\n+2. Use MATCH operator for text search within fields (e.g., `output MATCH \'keyword\'`)\\n+3. For date/time queries, use the \'created\' field with proper SQL date functions\\n+4. For numeric comparisons on metrics, use dot notation (e.g., `metrics.prompt_tokens > 1000`)\\n+5. Only set match=true if the query is a simple substring match that cannot be expressed as a SQL filter\\n+\\n+## Examples:\\n+- \\"logs with errors\\" → filter: \\"error IS NOT NULL\\"\\n+- \\"high token count\\" → filter: \\"metrics.total_tokens > 500\\"\\n+- \\"mentions GPT-4\\" → filter: \\"input MATCH \'GPT-4\' OR output MATCH \'GPT-4\'\\"\\n+- \\"from last week\\" → filter: \\"created >= NOW() - INTERVAL \'7 days\'\\"\\n+\\n+Now, generate a SQL-style query for: {{input}}"}]}',
        },
      },
    ],
  },
  {
    role: "tool",
    content: "ok: true",
    tool_call_id: "toolu_015zK44yNTnESRQe8h1b2q1d",
  },
  {
    role: "assistant",
    content: "Now let's rerun the task with the improved prompt:",
    tool_calls: [
      {
        id: "toolu_01AHCKqEh7CVFz87LzhvhpC2",
        type: "function",
        function: {
          name: "run_task",
          arguments: '{"index":0}',
        },
      },
    ],
  },
  {
    role: "tool",
    content:
      "summary:\n  index: 0\n  metrics:\n    completion_tokens:\n      avg: 58.417\n    estimated_cost:\n      avg: 0\n    prompt_tokens:\n      avg: 524.833\n    total_tokens:\n      avg: 583.25\n  scores:\n    BTQL query checker:\n      avg: 0.167\n      max: 0.75\n      min: 0\n    QuerySimilarity:\n      avg: 0.576\n      max: 0.818\n      min: 0\n  taskName: ai-search",
    tool_call_id: "toolu_01AHCKqEh7CVFz87LzhvhpC2",
  },
  {
    role: "assistant",
    content: [
      {
        type: "text",
        text: "I see some improvement in the scores, but let's check the detailed results to see what's still not working correctly:",
        cache_control: {
          type: "ephemeral",
        },
      },
    ],
    tool_calls: [
      {
        id: "toolu_01JyPH6f5upq7WY9mGprGJfQ",
        type: "function",
        function: {
          name: "get_results",
          arguments: '{"index":0,"numSamples":5}',
        },
      },
    ],
  },
  {
    role: "tool",
    content:
      "- expected:\n    explanation: We can compute duration by subtracting end - start, and confirming that it's > 1.\n    filter: metrics.end - metrics.start > 1\n  id: 2dff820c-788b-4720-9dd2-24af15357f35\n  input: took longer than 1s\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields: []\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a duration condition which can be expressed as a SQL filter.\n    filter: metrics.duration > 1000\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.46\n- expected:\n    explanation: We can selectively filter on each of the score fields to make sure none of them is set.\n    filter: scores.\"asdf\" IS NULL and scores.\"basdf\" IS NULL and scores.\"casdf\" IS NULL\n  id: 3474472e-3ae1-47a8-8c72-dbbd18e34ac3\n  input: no scores are set\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a condition where scores are not set, which can be expressed as a SQL filter clause.\n    filter: scores IS NULL\n    match: false\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.34\n- expected:\n    explanation: We should support this case, but we do not yet track which logs have been inserted into datasets.\n    message: Queries that check whether a log is in a dataset are not yet supported\n    unsupported: true\n  id: 6226d385-78a3-41f1-a1db-461a9bfa7244\n  input: in the dataset\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query 'in the dataset' is too vague and does not specify any particular condition or field to filter on, making it a simple substring match.\n    match: true\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.136\n- expected:\n    explanation: The query does not have semantic meaning that can be related to the named columns and JSON fields in the provided schema, so we fall back to a simple substring match to be safe.\n    filter: metadata.email = '<EMAIL>'\n  id: da5cd2e4-9624-4199-958a-67fe921ea169\n  input: user <NAME_EMAIL>\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields:\n      - asdf\n      - basdf\n      - casdf\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query specifies a user email, which can be expressed as a SQL filter condition.\n    filter: metadata.user_email = '<EMAIL>'\n    match: false\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.747\n- expected:\n    explanation: span_attributes has a field named 'name' that we can filter on.\n    match: span_attributes.name ILIKE '%playground%'\n  id: 3b09da75-e2c3-4b00-b851-212fd48cd839\n  input: traces with playground in the name\n  metadata:\n    aiSchemaColumns:\n      - span_type_info\n      - _xact_id\n      - input\n      - output\n      - error\n      - expected\n      - tags\n      - scores\n      - metrics\n      - metadata\n      - created\n      - id\n    orgName: braintrustdata.com\n    scoreFields: []\n    searchType: logs\n  metrics: {}\n  output:\n    explanation: The query is looking for a substring match in the name, which can be interpreted as searching within the 'tags' field for the term 'playground'.\n    filter: null\n    match: true\n    sort: null\n    tags: null\n  scores:\n    BTQL query checker: 0\n    QuerySimilarity: 0.264",
    tool_call_id: "toolu_01JyPH6f5upq7WY9mGprGJfQ",
  },
];

const tools: ChatCompletionTool[] = [
  {
    type: "function",
    function: {
      name: "get_playground_summary",
      description:
        "Get the summary of a playground, including each task name and its aggregate scores and metrics",
      parameters: {
        type: "object",
        properties: {},
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_results",
      description:
        "Get the results of a task, including input, expected output, metadata, and scores",
      parameters: {
        type: "object",
        properties: {
          index: {
            type: "number",
            description:
              "The index of the task to get results for. You can discover the tasks and their indices by calling get_playground_summary.",
          },
          numSamples: {
            type: "number",
            minimum: 1,
            maximum: 100,
          },
        },
        required: ["index", "numSamples"],
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      },
    },
  },
  {
    type: "function",
    function: {
      name: "edit_task",
      description: "Edit a task",
      parameters: {
        type: "object",
        properties: {
          index: {
            type: "number",
            description: "The index of the task to edit",
          },
          edits: {
            type: "array",
            items: {
              type: "object",
              properties: {
                path: {
                  type: "array",
                  items: {
                    type: ["string", "number"],
                  },
                  description:
                    "The path to the value to edit (relative to definition). Use strings for keys and numbers (0-indexed) for array indices. The path must point to a value (e.g. a string), not an array or object.",
                },
                diff: {
                  type: "string",
                  description:
                    "The diff to apply to the value. Use standard `diff` format, like what would be generated by `git diff`. The diff will be applied with the `applyPatch` function from the `diff` package.",
                },
              },
              required: ["path", "diff"],
              additionalProperties: false,
            },
          },
        },
        required: ["index", "edits"],
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      },
    },
  },
  {
    type: "function",
    function: {
      name: "run_task",
      description: "Rerun a task",
      parameters: {
        type: "object",
        properties: {
          index: {
            type: "number",
            description: "The index of the task to rerun",
          },
        },
        required: ["index"],
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      },
    },
  },
  {
    type: "function",
    function: {
      name: "edit_data",
      description:
        "Add or edit dataset rows. If the id is provided, the row will be updated. If the id is not provided, a new row will be inserted. To delete a row, specify its id and set the `delete` field to true.",
      parameters: {
        type: "object",
        properties: {
          edits: {
            type: "array",
            items: {
              type: "object",
              properties: {
                purpose: {
                  type: "string",
                  description:
                    "A quick summary of what you're trying to do with this edit. Why this row is a useful addition, or why you want to edit or delete it.",
                },
                id: {
                  type: "string",
                  description:
                    "The id of the data to edit. Leave blank to insert a new row. If provided, the row will be updated.",
                },
                delete: {
                  type: "boolean",
                  description:
                    "If true, the row will be deleted. If false, the row will be updated. If true, the id field is required.",
                },
                input: {
                  description:
                    "The value of the `input` field. This is generally the input to the LLM/agent.",
                },
                expected: {
                  description:
                    "The value of the `expected` field. This is generally the expected output of the LLM/agent. Leave blank if not applicable.",
                },
                metadata: {
                  type: "object",
                  additionalProperties: {},
                  description:
                    "Metadata to include with the data. Keys are strings, values are arbitrary JSON values.",
                },
              },
              additionalProperties: false,
            },
            minItems: 0,
            maxItems: 10,
          },
        },
        required: ["edits"],
        additionalProperties: false,
        $schema: "http://json-schema.org/draft-07/schema#",
      },
    },
  },
];

const MODEL = "claude-3-7-sonnet-20250219";
// const MODEL = "gpt-4o";
async function main() {
  const logger = initLogger({
    projectName: "prompt-caching-test",
  });
  const client = wrapOpenAI(
    new OpenAI({
      baseURL: "http://127.0.0.1:8000/v1/proxy",
      apiKey: process.env.BRAINTRUST_API_KEY,
      defaultHeaders: {
        "x-bt-use-cache": "never",
      },
    }),
  );

  const prefix = messages.slice(-2);

  console.log("RUNNING PREFIX");
  let start = Date.now();
  let completion = await client.chat.completions.create({
    model: MODEL,
    messages: prefix,
    tools,
    temperature: 0,
    max_tokens: 10240,
  });

  console.log(completion.choices[0].message.content);
  console.log("USAGE");
  console.log(completion.usage);
  console.log("Ran in", Date.now() - start, "ms");

  console.log("RUNNING WHOLE THING");
  start = Date.now();
  completion = await client.chat.completions.create({
    model: MODEL,
    messages,
    tools,
    temperature: 0,
    max_tokens: 10240,
  });

  console.log(completion.choices[0].message.content);
  console.log("USAGE");
  console.log(completion.usage);
  console.log("Ran in", Date.now() - start, "ms");

  await logger.flush();
}

main().catch(console.error);
