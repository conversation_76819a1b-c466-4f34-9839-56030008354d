lockfileVersion: "6.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      ajv:
        specifier: ^8.13.0
        version: 8.17.1
      compute-cosine-similarity:
        specifier: ^1.1.0
        version: 1.1.0
      js-levenshtein:
        specifier: ^1.1.6
        version: 1.1.6
      js-yaml:
        specifier: ^4.1.0
        version: 4.1.0
      linear-sum-assignment:
        specifier: ^1.0.7
        version: 1.0.7
      mustache:
        specifier: ^4.2.0
        version: 4.2.0
      openai:
        specifier: ^4.47.1
        version: 4.47.1
      zod:
        specifier: ^3.22.4
        version: 3.24.2
      zod-to-json-schema:
        specifier: ^3.22.5
        version: 3.24.4(zod@3.24.2)
    devDependencies:
      "@rollup/plugin-yaml":
        specifier: ^4.1.2
        version: 4.1.2
      "@types/js-levenshtein":
        specifier: ^1.1.3
        version: 1.1.3
      "@types/js-yaml":
        specifier: ^4.0.9
        version: 4.0.9
      "@types/mustache":
        specifier: ^4.2.5
        version: 4.2.5
      "@types/node":
        specifier: ^20.10.5
        version: 20.17.24
      msw:
        specifier: ^2.7.3
        version: 2.7.3(@types/node@20.17.24)(typescript@5.8.2)
      tsup:
        specifier: ^8.4.0
        version: 8.4.0(tsx@3.14.0)(typescript@5.8.2)
      tsx:
        specifier: ^3.14.0
        version: 3.14.0
      typedoc:
        specifier: ^0.25.4
        version: 0.25.13(typescript@5.8.2)
      typedoc-plugin-markdown:
        specifier: ^3.17.1
        version: 3.17.1(typedoc@0.25.13)
      typescript:
        specifier: ^5.3.3
        version: 5.8.2
      vitest:
        specifier: ^2.1.9
        version: 2.1.9(@types/node@20.17.24)(msw@2.7.3)

  evals:
    dependencies:
      autoevals:
        specifier: workspace:*
        version: link:..
      braintrust:
        specifier: ^0.0.140
        version: 0.0.140
      zod:
        specifier: ^3.22.4
        version: 3.24.2
    devDependencies:
      "@types/node":
        specifier: ^20.10.5
        version: 20.17.24
      duckdb:
        specifier: ^1.0.0
        version: 1.2.0
      tsx:
        specifier: ^3.14.0
        version: 3.14.0

packages:
  /@ai-sdk/provider@0.0.6:
    resolution:
      {
        integrity: sha512-kiPqIsSnUimckaUn87WepxfjPNdy8SXlPP7P6yWuG3e1NmyFHcyuH6EBBZxXLmu0oZtkb+QEeP3UDWGSc+wwKQ==,
      }
    engines: { node: ">=18" }
    dependencies:
      json-schema: 0.4.0
    dev: false

  /@asteasolutions/zod-to-openapi@6.4.0(zod@3.24.2):
    resolution:
      {
        integrity: sha512-8cxfF7AHHx2PqnN4Cd8/O8CBu/nVYJP9DpnfVLW3BFb66VJDnqI/CczZnkqMc3SNh6J9GiX7JbJ5T4BSP4HZ2Q==,
      }
    peerDependencies:
      zod: ^3.20.2
    dependencies:
      openapi3-ts: 4.4.0
      zod: 3.24.2
    dev: false

  /@braintrust/core@0.0.44:
    resolution:
      {
        integrity: sha512-5aA7A4i9TCt3lr6u/ogpRyZztghVEOuoTnP6nHoUaqvVo9AQHPgh2FarxsVB6yYnbWoV28o5AizO/kZseE8aBA==,
      }
    dependencies:
      "@asteasolutions/zod-to-openapi": 6.4.0(zod@3.24.2)
      uuid: 9.0.1
      zod: 3.24.2
    dev: false

  /@bundled-es-modules/cookie@2.0.1:
    resolution:
      {
        integrity: sha512-8o+5fRPLNbjbdGRRmJj3h6Hh1AQJf2dk3qQ/5ZFb+PXkRNiSoMGGUKlsgLfrxneb72axVJyIYji64E2+nNfYyw==,
      }
    dependencies:
      cookie: 0.7.2
    dev: true

  /@bundled-es-modules/statuses@1.0.1:
    resolution:
      {
        integrity: sha512-yn7BklA5acgcBr+7w064fGV+SGIFySjCKpqjcWgBAIfrAkY+4GQTJJHQMeT3V/sgz23VTEVV8TtOmkvJAhFVfg==,
      }
    dependencies:
      statuses: 2.0.1
    dev: true

  /@bundled-es-modules/tough-cookie@0.1.6:
    resolution:
      {
        integrity: sha512-dvMHbL464C0zI+Yqxbz6kZ5TOEp7GLW+pry/RWndAR8MJQAXZ2rPmIs8tziTZjeIyhSNZgZbCePtfSbdWqStJw==,
      }
    dependencies:
      "@types/tough-cookie": 4.0.5
      tough-cookie: 4.1.4
    dev: true

  /@esbuild/aix-ppc64@0.21.5:
    resolution:
      {
        integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/aix-ppc64@0.25.1:
    resolution:
      {
        integrity: sha512-kfYGy8IdzTGy+z0vFGvExZtxkFlA4zAxgKEahG9KE1ScBjpQnFsNOX8KTU5ojNru5ed5CVoJYXFtoxaq5nFbjQ==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm64@0.18.20:
    resolution:
      {
        integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    optional: true

  /@esbuild/android-arm64@0.21.5:
    resolution:
      {
        integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-50tM0zCJW5kGqgG7fQ7IHvQOcAn9TKiVRuQ/lN0xR+T2lzEFvAi1ZcS8DiksFcEpf1t/GYOeOfCAgDHFpkiSmA==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.18.20:
    resolution:
      {
        integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [android]
    requiresBuild: true
    optional: true

  /@esbuild/android-arm@0.21.5:
    resolution:
      {
        integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.25.1:
    resolution:
      {
        integrity: sha512-dp+MshLYux6j/JjdqVLnMglQlFu+MuVeNrmT5nk6q07wNhCdSnB7QZj+7G8VMUGh1q+vj2Bq8kRsuyA00I/k+Q==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.18.20:
    resolution:
      {
        integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [android]
    requiresBuild: true
    optional: true

  /@esbuild/android-x64@0.21.5:
    resolution:
      {
        integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.25.1:
    resolution:
      {
        integrity: sha512-GCj6WfUtNldqUzYkN/ITtlhwQqGWu9S45vUXs7EIYf+7rCiiqH9bCloatO9VhxsL0Pji+PF4Lz2XXCES+Q8hDw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.18.20:
    resolution:
      {
        integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    optional: true

  /@esbuild/darwin-arm64@0.21.5:
    resolution:
      {
        integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-5hEZKPf+nQjYoSr/elb62U19/l1mZDdqidGfmFutVUjjUZrOazAtwK+Kr+3y0C/oeJfLlxo9fXb1w7L+P7E4FQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.18.20:
    resolution:
      {
        integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    optional: true

  /@esbuild/darwin-x64@0.21.5:
    resolution:
      {
        integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.25.1:
    resolution:
      {
        integrity: sha512-hxVnwL2Dqs3fM1IWq8Iezh0cX7ZGdVhbTfnOy5uURtao5OIVCEyj9xIzemDi7sRvKsuSdtCAhMKarxqtlyVyfA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.18.20:
    resolution:
      {
        integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    optional: true

  /@esbuild/freebsd-arm64@0.21.5:
    resolution:
      {
        integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-1MrCZs0fZa2g8E+FUo2ipw6jw5qqQiH+tERoS5fAfKnRx6NXH31tXBKI3VpmLijLH6yriMZsxJtaXUyFt/8Y4A==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.18.20:
    resolution:
      {
        integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    optional: true

  /@esbuild/freebsd-x64@0.21.5:
    resolution:
      {
        integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.25.1:
    resolution:
      {
        integrity: sha512-0IZWLiTyz7nm0xuIs0q1Y3QWJC52R8aSXxe40VUxm6BB1RNmkODtW6LHvWRrGiICulcX7ZvyH6h5fqdLu4gkww==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.18.20:
    resolution:
      {
        integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-arm64@0.21.5:
    resolution:
      {
        integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-jaN3dHi0/DDPelk0nLcXRm1q7DNJpjXy7yWaWvbfkPvI+7XNSc/lDOnCLN7gzsyzgu6qSAmgSvP9oXAhP973uQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.18.20:
    resolution:
      {
        integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-arm@0.21.5:
    resolution:
      {
        integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.25.1:
    resolution:
      {
        integrity: sha512-NdKOhS4u7JhDKw9G3cY6sWqFcnLITn6SqivVArbzIaf3cemShqfLGHYMx8Xlm/lBit3/5d7kXvriTUGa5YViuQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.18.20:
    resolution:
      {
        integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-ia32@0.21.5:
    resolution:
      {
        integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.25.1:
    resolution:
      {
        integrity: sha512-OJykPaF4v8JidKNGz8c/q1lBO44sQNUQtq1KktJXdBLn1hPod5rE/Hko5ugKKZd+D2+o1a9MFGUEIUwO2YfgkQ==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.18.20:
    resolution:
      {
        integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==,
      }
    engines: { node: ">=12" }
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-loong64@0.21.5:
    resolution:
      {
        integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==,
      }
    engines: { node: ">=12" }
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.25.1:
    resolution:
      {
        integrity: sha512-nGfornQj4dzcq5Vp835oM/o21UMlXzn79KobKlcs3Wz9smwiifknLy4xDCLUU0BWp7b/houtdrgUz7nOGnfIYg==,
      }
    engines: { node: ">=18" }
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.18.20:
    resolution:
      {
        integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==,
      }
    engines: { node: ">=12" }
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-mips64el@0.21.5:
    resolution:
      {
        integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==,
      }
    engines: { node: ">=12" }
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.25.1:
    resolution:
      {
        integrity: sha512-1osBbPEFYwIE5IVB/0g2X6i1qInZa1aIoj1TdL4AaAb55xIIgbg8Doq6a5BzYWgr+tEcDzYH67XVnTmUzL+nXg==,
      }
    engines: { node: ">=18" }
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.18.20:
    resolution:
      {
        integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-ppc64@0.21.5:
    resolution:
      {
        integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.25.1:
    resolution:
      {
        integrity: sha512-/6VBJOwUf3TdTvJZ82qF3tbLuWsscd7/1w+D9LH0W/SqUgM5/JJD0lrJ1fVIfZsqB6RFmLCe0Xz3fmZc3WtyVg==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.18.20:
    resolution:
      {
        integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==,
      }
    engines: { node: ">=12" }
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-riscv64@0.21.5:
    resolution:
      {
        integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==,
      }
    engines: { node: ">=12" }
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.25.1:
    resolution:
      {
        integrity: sha512-nSut/Mx5gnilhcq2yIMLMe3Wl4FK5wx/o0QuuCLMtmJn+WeWYoEGDN1ipcN72g1WHsnIbxGXd4i/MF0gTcuAjQ==,
      }
    engines: { node: ">=18" }
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.18.20:
    resolution:
      {
        integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==,
      }
    engines: { node: ">=12" }
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-s390x@0.21.5:
    resolution:
      {
        integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==,
      }
    engines: { node: ">=12" }
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.25.1:
    resolution:
      {
        integrity: sha512-cEECeLlJNfT8kZHqLarDBQso9a27o2Zd2AQ8USAEoGtejOrCYHNtKP8XQhMDJMtthdF4GBmjR2au3x1udADQQQ==,
      }
    engines: { node: ">=18" }
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.18.20:
    resolution:
      {
        integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    optional: true

  /@esbuild/linux-x64@0.21.5:
    resolution:
      {
        integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.25.1:
    resolution:
      {
        integrity: sha512-xbfUhu/gnvSEg+EGovRc+kjBAkrvtk38RlerAzQxvMzlB4fXpCFCeUAYzJvrnhFtdeyVCDANSjJvOvGYoeKzFA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-O96poM2XGhLtpTh+s4+nP7YCCAfb4tJNRVZHfIE7dgmax+yMP2WgMd2OecBuaATHKTHsLWHQeuaxMRnCsH8+5g==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.18.20:
    resolution:
      {
        integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    optional: true

  /@esbuild/netbsd-x64@0.21.5:
    resolution:
      {
        integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.25.1:
    resolution:
      {
        integrity: sha512-X53z6uXip6KFXBQ+Krbx25XHV/NCbzryM6ehOAeAil7X7oa4XIq+394PWGnwaSQ2WRA0KI6PUO6hTO5zeF5ijA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-Na9T3szbXezdzM/Kfs3GcRQNjHzM6GzFBeU1/6IV/npKP5ORtp9zbQjvkDJ47s6BCgaAZnnnu/cY1x342+MvZg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.18.20:
    resolution:
      {
        integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    optional: true

  /@esbuild/openbsd-x64@0.21.5:
    resolution:
      {
        integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.25.1:
    resolution:
      {
        integrity: sha512-T3H78X2h1tszfRSf+txbt5aOp/e7TAz3ptVKu9Oyir3IAOFPGV6O9c2naym5TOriy1l0nNf6a4X5UXRZSGX/dw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.18.20:
    resolution:
      {
        integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    optional: true

  /@esbuild/sunos-x64@0.21.5:
    resolution:
      {
        integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.25.1:
    resolution:
      {
        integrity: sha512-2H3RUvcmULO7dIE5EWJH8eubZAI4xw54H1ilJnRNZdeo8dTADEZ21w6J22XBkXqGJbe0+wnNJtw3UXRoLJnFEg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.18.20:
    resolution:
      {
        integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    optional: true

  /@esbuild/win32-arm64@0.21.5:
    resolution:
      {
        integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.25.1:
    resolution:
      {
        integrity: sha512-GE7XvrdOzrb+yVKB9KsRMq+7a2U/K5Cf/8grVFRAGJmfADr/e/ODQ134RK2/eeHqYV5eQRFxb1hY7Nr15fv1NQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.18.20:
    resolution:
      {
        integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    optional: true

  /@esbuild/win32-ia32@0.21.5:
    resolution:
      {
        integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.25.1:
    resolution:
      {
        integrity: sha512-uOxSJCIcavSiT6UnBhBzE8wy3n0hOkJsBOzy7HDAuTDE++1DJMRRVCPGisULScHL+a/ZwdXPpXD3IyFKjA7K8A==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.18.20:
    resolution:
      {
        integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    optional: true

  /@esbuild/win32-x64@0.21.5:
    resolution:
      {
        integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.25.1:
    resolution:
      {
        integrity: sha512-Y1EQdcfwMSeQN/ujR5VayLOJ1BHaK+ssyk0AEzPjC+t1lITgsnccPqFjb6V+LsTp/9Iov4ysfjxLaGJ9RPtkVg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@gar/promisify@1.1.3:
    resolution:
      {
        integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==,
      }
    dev: true

  /@inquirer/confirm@5.1.8(@types/node@20.17.24):
    resolution:
      {
        integrity: sha512-dNLWCYZvXDjO3rnQfk2iuJNL4Ivwz/T2+C3+WnNfJKsNGSuOs3wAo2F6e0p946gtSAk31nZMfW+MRmYaplPKsg==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true
    dependencies:
      "@inquirer/core": 10.1.9(@types/node@20.17.24)
      "@inquirer/type": 3.0.5(@types/node@20.17.24)
      "@types/node": 20.17.24
    dev: true

  /@inquirer/core@10.1.9(@types/node@20.17.24):
    resolution:
      {
        integrity: sha512-sXhVB8n20NYkUBfDYgizGHlpRVaCRjtuzNZA6xpALIUbkgfd2Hjz+DfEN6+h1BRnuxw0/P4jCIMjMsEOAMwAJw==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true
    dependencies:
      "@inquirer/figures": 1.0.11
      "@inquirer/type": 3.0.5(@types/node@20.17.24)
      "@types/node": 20.17.24
      ansi-escapes: 4.3.2
      cli-width: 4.1.0
      mute-stream: 2.0.0
      signal-exit: 4.1.0
      wrap-ansi: 6.2.0
      yoctocolors-cjs: 2.1.2
    dev: true

  /@inquirer/figures@1.0.11:
    resolution:
      {
        integrity: sha512-eOg92lvrn/aRUqbxRyvpEWnrvRuTYRifixHkYVpJiygTgVSBIHDqLh0SrMQXkafvULg3ck11V7xvR+zcgvpHFw==,
      }
    engines: { node: ">=18" }
    dev: true

  /@inquirer/type@3.0.5(@types/node@20.17.24):
    resolution:
      {
        integrity: sha512-ZJpeIYYueOz/i/ONzrfof8g89kNdO2hjGuvULROo3O8rlB2CRtSseE5KeirnyE4t/thAn/EwvS/vuQeJCn+NZg==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@types/node": ">=18"
    peerDependenciesMeta:
      "@types/node":
        optional: true
    dependencies:
      "@types/node": 20.17.24
    dev: true

  /@isaacs/cliui@8.0.2:
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
      }
    engines: { node: ">=12" }
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi@7.0.0
    dev: true

  /@isaacs/fs-minipass@4.0.1:
    resolution:
      {
        integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==,
      }
    engines: { node: ">=18.0.0" }
    dependencies:
      minipass: 7.1.2
    dev: true

  /@jridgewell/gen-mapping@0.3.8:
    resolution:
      {
        integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
      }
    engines: { node: ">=6.0.0" }
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.5.0
      "@jridgewell/trace-mapping": 0.3.25
    dev: true

  /@jridgewell/resolve-uri@3.1.2:
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
      }
    engines: { node: ">=6.0.0" }
    dev: true

  /@jridgewell/set-array@1.2.1:
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
      }
    engines: { node: ">=6.0.0" }
    dev: true

  /@jridgewell/sourcemap-codec@1.5.0:
    resolution:
      {
        integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
      }
    dev: true

  /@jridgewell/trace-mapping@0.3.25:
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
      }
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.5.0
    dev: true

  /@kwsites/file-exists@1.1.1:
    resolution:
      {
        integrity: sha512-m9/5YGR18lIwxSFDwfE3oA7bWuq9kdau6ugN4H2rJeyhFQZcG9AgSHkQtSD15a8WvTgfz9aikZMrKPHvbpqFiw==,
      }
    dependencies:
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@kwsites/promise-deferred@1.1.1:
    resolution:
      {
        integrity: sha512-GaHYm+c0O9MjZRu0ongGBRbinu8gVAMd2UZjji6jVmqKtZluZnptXGWhz1E8j8D2HJ3f/yMxKAUC0b+57wncIw==,
      }
    dev: false

  /@mapbox/node-pre-gyp@2.0.0:
    resolution:
      {
        integrity: sha512-llMXd39jtP0HpQLVI37Bf1m2ADlEb35GYSh1SDSLsBhR+5iCxiNGlT31yqbNtVHygHAtMy6dWFERpU2JgufhPg==,
      }
    engines: { node: ">=18" }
    hasBin: true
    dependencies:
      consola: 3.4.1
      detect-libc: 2.0.3
      https-proxy-agent: 7.0.6
      node-fetch: 2.7.0
      nopt: 8.1.0
      semver: 7.7.1
      tar: 7.4.3
    transitivePeerDependencies:
      - encoding
      - supports-color
    dev: true

  /@mswjs/interceptors@0.37.6:
    resolution:
      {
        integrity: sha512-wK+5pLK5XFmgtH3aQ2YVvA3HohS3xqV/OxuVOdNx9Wpnz7VE/fnC+e1A7ln6LFYeck7gOJ/dsZV6OLplOtAJ2w==,
      }
    engines: { node: ">=18" }
    dependencies:
      "@open-draft/deferred-promise": 2.2.0
      "@open-draft/logger": 0.3.0
      "@open-draft/until": 2.1.0
      is-node-process: 1.2.0
      outvariant: 1.4.3
      strict-event-emitter: 0.5.1
    dev: true

  /@next/env@14.2.24:
    resolution:
      {
        integrity: sha512-LAm0Is2KHTNT6IT16lxT+suD0u+VVfYNQqM+EJTKuFRRuY2z+zj01kueWXPCxbMBDt0B5vONYzabHGUNbZYAhA==,
      }
    dev: false

  /@npmcli/fs@2.1.2:
    resolution:
      {
        integrity: sha512-yOJKRvohFOaLqipNtwYB9WugyZKhC/DZC4VYPmpaCzDBrA8YpK3qHZ8/HGscMnE4GqbkLNuVcCnxkeQEdGt6LQ==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      "@gar/promisify": 1.1.3
      semver: 7.7.1
    dev: true

  /@npmcli/move-file@2.0.1:
    resolution:
      {
        integrity: sha512-mJd2Z5TjYWq/ttPLLGqArdtnC74J6bOzg4rMDnN+p1xTacZ2yPRCk2y0oSWQtygLR9YVQXgOcONrwtnk3JupxQ==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This functionality has been moved to @npmcli/fs
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2
    dev: true

  /@open-draft/deferred-promise@2.2.0:
    resolution:
      {
        integrity: sha512-CecwLWx3rhxVQF6V4bAgPS5t+So2sTbPgAzafKkVizyi7tlwpcFpdFqq+wqF2OwNBmqFuu6tOyouTuxgpMfzmA==,
      }
    dev: true

  /@open-draft/logger@0.3.0:
    resolution:
      {
        integrity: sha512-X2g45fzhxH238HKO4xbSr7+wBS8Fvw6ixhTDuvLd5mqh6bJJCFAPwU9mPDxbcrRtfxv4u5IHCEH77BmxvXmmxQ==,
      }
    dependencies:
      is-node-process: 1.2.0
      outvariant: 1.4.3
    dev: true

  /@open-draft/until@2.1.0:
    resolution:
      {
        integrity: sha512-U69T3ItWHvLwGg5eJ0n3I62nWuE6ilHlmz7zM0npLBRvPRd7e6NYmg54vvRtP5mZG7kZqZCFVdsTWo7BPtBujg==,
      }
    dev: true

  /@pkgjs/parseargs@0.11.0:
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
      }
    engines: { node: ">=14" }
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/plugin-yaml@4.1.2:
    resolution:
      {
        integrity: sha512-RpupciIeZMUqhgFE97ba0s98mOFS7CWzN3EJNhJkqSv9XLlWYtwVdtE6cDw6ASOF/sZVFS7kRJXftaqM2Vakdw==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      "@rollup/pluginutils": 5.1.4
      js-yaml: 4.1.0
      tosource: 2.0.0-alpha.3
    dev: true

  /@rollup/pluginutils@5.1.4:
    resolution:
      {
        integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      "@types/estree": 1.0.6
      estree-walker: 2.0.2
      picomatch: 4.0.2
    dev: true

  /@rollup/rollup-android-arm-eabi@4.36.0:
    resolution:
      {
        integrity: sha512-jgrXjjcEwN6XpZXL0HUeOVGfjXhPyxAbbhD0BlXUB+abTOpbPiN5Wb3kOT7yb+uEtATNYF5x5gIfwutmuBA26w==,
      }
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-android-arm64@4.36.0:
    resolution:
      {
        integrity: sha512-NyfuLvdPdNUfUNeYKUwPwKsE5SXa2J6bCt2LdB/N+AxShnkpiczi3tcLJrm5mA+eqpy0HmaIY9F6XCa32N5yzg==,
      }
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-arm64@4.36.0:
    resolution:
      {
        integrity: sha512-JQ1Jk5G4bGrD4pWJQzWsD8I1n1mgPXq33+/vP4sk8j/z/C2siRuxZtaUA7yMTf71TCZTZl/4e1bfzwUmFb3+rw==,
      }
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-x64@4.36.0:
    resolution:
      {
        integrity: sha512-6c6wMZa1lrtiRsbDziCmjE53YbTkxMYhhnWnSW8R/yqsM7a6mSJ3uAVT0t8Y/DGt7gxUWYuFM4bwWk9XCJrFKA==,
      }
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-freebsd-arm64@4.36.0:
    resolution:
      {
        integrity: sha512-KXVsijKeJXOl8QzXTsA+sHVDsFOmMCdBRgFmBb+mfEb/7geR7+C8ypAml4fquUt14ZyVXaw2o1FWhqAfOvA4sg==,
      }
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-freebsd-x64@4.36.0:
    resolution:
      {
        integrity: sha512-dVeWq1ebbvByI+ndz4IJcD4a09RJgRYmLccwlQ8bPd4olz3Y213uf1iwvc7ZaxNn2ab7bjc08PrtBgMu6nb4pQ==,
      }
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-gnueabihf@4.36.0:
    resolution:
      {
        integrity: sha512-bvXVU42mOVcF4le6XSjscdXjqx8okv4n5vmwgzcmtvFdifQ5U4dXFYaCB87namDRKlUL9ybVtLQ9ztnawaSzvg==,
      }
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-musleabihf@4.36.0:
    resolution:
      {
        integrity: sha512-JFIQrDJYrxOnyDQGYkqnNBtjDwTgbasdbUiQvcU8JmGDfValfH1lNpng+4FWlhaVIR4KPkeddYjsVVbmJYvDcg==,
      }
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-KqjYVh3oM1bj//5X7k79PSCZ6CvaVzb7Qs7VMWS+SlWB5M8p3FqufLP9VNp4CazJ0CsPDLwVD9r3vX7Ci4J56A==,
      }
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-musl@4.36.0:
    resolution:
      {
        integrity: sha512-QiGnhScND+mAAtfHqeT+cB1S9yFnNQ/EwCg5yE3MzoaZZnIV0RV9O5alJAoJKX/sBONVKeZdMfO8QSaWEygMhw==,
      }
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-loongarch64-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-1ZPyEDWF8phd4FQtTzMh8FQwqzvIjLsl6/84gzUxnMNFBtExBtpL51H67mV9xipuxl1AEAerRBgBwFNpkw8+Lg==,
      }
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-powerpc64le-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-VMPMEIUpPFKpPI9GZMhJrtu8rxnp6mJR3ZzQPykq4xc2GmdHj3Q4cA+7avMyegXy4n1v+Qynr9fR88BmyO74tg==,
      }
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-riscv64-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-ttE6ayb/kHwNRJGYLpuAvB7SMtOeQnVXEIpMtAvx3kepFQeowVED0n1K9nAdraHUPJ5hydEMxBpIR7o4nrm8uA==,
      }
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-s390x-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-4a5gf2jpS0AIe7uBjxDeUMNcFmaRTbNv7NxI5xOCs4lhzsVyGR/0qBXduPnoWf6dGC365saTiwag8hP1imTgag==,
      }
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-gnu@4.36.0:
    resolution:
      {
        integrity: sha512-5KtoW8UWmwFKQ96aQL3LlRXX16IMwyzMq/jSSVIIyAANiE1doaQsx/KRyhAvpHlPjPiSU/AYX/8m+lQ9VToxFQ==,
      }
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-musl@4.36.0:
    resolution:
      {
        integrity: sha512-sycrYZPrv2ag4OCvaN5js+f01eoZ2U+RmT5as8vhxiFz+kxwlHrsxOwKPSA8WyS+Wc6Epid9QeI/IkQ9NkgYyQ==,
      }
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-arm64-msvc@4.36.0:
    resolution:
      {
        integrity: sha512-qbqt4N7tokFwwSVlWDsjfoHgviS3n/vZ8LK0h1uLG9TYIRuUTJC88E1xb3LM2iqZ/WTqNQjYrtmtGmrmmawB6A==,
      }
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-ia32-msvc@4.36.0:
    resolution:
      {
        integrity: sha512-t+RY0JuRamIocMuQcfwYSOkmdX9dtkr1PbhKW42AMvaDQa+jOdpUYysroTF/nuPpAaQMWp7ye+ndlmmthieJrQ==,
      }
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-x64-msvc@4.36.0:
    resolution:
      {
        integrity: sha512-aRXd7tRZkWLqGbChgcMMDEHjOKudo1kChb1Jt1IfR8cY/KIpgNviLeJy5FUb9IpSuQj8dU2fAYNMPW/hLKOSTw==,
      }
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@tootallnate/once@2.0.0:
    resolution:
      {
        integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==,
      }
    engines: { node: ">= 10" }
    dev: true

  /@types/cookie@0.6.0:
    resolution:
      {
        integrity: sha512-4Kh9a6B2bQciAhf7FSuMRRkUWecJgJu9nPnx3yzpsfXX/c50REIqpHY4C82bXP90qrLtXtkDxTZosYO3UpOwlA==,
      }
    dev: true

  /@types/estree@1.0.6:
    resolution:
      {
        integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==,
      }
    dev: true

  /@types/js-levenshtein@1.1.3:
    resolution:
      {
        integrity: sha512-jd+Q+sD20Qfu9e2aEXogiO3vpOC1PYJOUdyN9gvs4Qrvkg4wF43L5OhqrPeokdv8TL0/mXoYfpkcoGZMNN2pkQ==,
      }
    dev: true

  /@types/js-yaml@4.0.9:
    resolution:
      {
        integrity: sha512-k4MGaQl5TGo/iipqb2UDG2UwjXziSWkh0uysQelTlJpX1qGlpUZYm8PnO4DxG1qBomtJUdYJ6qR6xdIah10JLg==,
      }
    dev: true

  /@types/mustache@4.2.5:
    resolution:
      {
        integrity: sha512-PLwiVvTBg59tGFL/8VpcGvqOu3L4OuveNvPi0EYbWchRdEVP++yRUXJPFl+CApKEq13017/4Nf7aQ5lTtHUNsA==,
      }
    dev: true

  /@types/node-fetch@2.6.12:
    resolution:
      {
        integrity: sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==,
      }
    dependencies:
      "@types/node": 20.17.24
      form-data: 4.0.2
    dev: false

  /@types/node@18.19.80:
    resolution:
      {
        integrity: sha512-kEWeMwMeIvxYkeg1gTc01awpwLbfMRZXdIhwRcakd/KlK53jmRC26LqcbIt7fnAQTu5GzlnWmzA3H6+l1u6xxQ==,
      }
    dependencies:
      undici-types: 5.26.5
    dev: false

  /@types/node@20.17.24:
    resolution:
      {
        integrity: sha512-d7fGCyB96w9BnWQrOsJtpyiSaBcAYYr75bnK6ZRjDbql2cGLj/3GsL5OYmLPNq76l7Gf2q4Rv9J2o6h5CrD9sA==,
      }
    dependencies:
      undici-types: 6.19.8

  /@types/statuses@2.0.5:
    resolution:
      {
        integrity: sha512-jmIUGWrAiwu3dZpxntxieC+1n/5c3mjrImkmOSQ2NC5uP6cYO4aAZDdSmRcI5C1oiTmqlZGHC+/NmJrKogbP5A==,
      }
    dev: true

  /@types/tough-cookie@4.0.5:
    resolution:
      {
        integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==,
      }
    dev: true

  /@vitest/expect@2.1.9:
    resolution:
      {
        integrity: sha512-UJCIkTBenHeKT1TTlKMJWy1laZewsRIzYighyYiJKZreqtdxSos/S1t+ktRMQWu2CKqaarrkeszJx1cgC5tGZw==,
      }
    dependencies:
      "@vitest/spy": 2.1.9
      "@vitest/utils": 2.1.9
      chai: 5.2.0
      tinyrainbow: 1.2.0
    dev: true

  /@vitest/mocker@2.1.9(msw@2.7.3)(vite@5.4.19):
    resolution:
      {
        integrity: sha512-tVL6uJgoUdi6icpxmdrn5YNo3g3Dxv+IHJBr0GXHaEdTcw3F+cPKnsXFhli6nO+f/6SDKPHEK1UN+k+TQv0Ehg==,
      }
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true
    dependencies:
      "@vitest/spy": 2.1.9
      estree-walker: 3.0.3
      magic-string: 0.30.17
      msw: 2.7.3(@types/node@20.17.24)(typescript@5.8.2)
      vite: 5.4.19(@types/node@20.17.24)
    dev: true

  /@vitest/pretty-format@2.1.9:
    resolution:
      {
        integrity: sha512-KhRIdGV2U9HOUzxfiHmY8IFHTdqtOhIzCpd8WRdJiE7D/HUcZVD0EgQCVjm+Q9gkUXWgBvMmTtZgIG48wq7sOQ==,
      }
    dependencies:
      tinyrainbow: 1.2.0
    dev: true

  /@vitest/runner@2.1.9:
    resolution:
      {
        integrity: sha512-ZXSSqTFIrzduD63btIfEyOmNcBmQvgOVsPNPe0jYtESiXkhd8u2erDLnMxmGrDCwHCCHE7hxwRDCT3pt0esT4g==,
      }
    dependencies:
      "@vitest/utils": 2.1.9
      pathe: 1.1.2
    dev: true

  /@vitest/snapshot@2.1.9:
    resolution:
      {
        integrity: sha512-oBO82rEjsxLNJincVhLhaxxZdEtV0EFHMK5Kmx5sJ6H9L183dHECjiefOAdnqpIgT5eZwT04PoggUnW88vOBNQ==,
      }
    dependencies:
      "@vitest/pretty-format": 2.1.9
      magic-string: 0.30.17
      pathe: 1.1.2
    dev: true

  /@vitest/spy@2.1.9:
    resolution:
      {
        integrity: sha512-E1B35FwzXXTs9FHNK6bDszs7mtydNi5MIfUWpceJ8Xbfb1gBMscAnwLbEu+B44ed6W3XjL9/ehLPHR1fkf1KLQ==,
      }
    dependencies:
      tinyspy: 3.0.2
    dev: true

  /@vitest/utils@2.1.9:
    resolution:
      {
        integrity: sha512-v0psaMSkNJ3A2NMrUEHFRzJtDPFn+/VWZ5WxImB21T9fjucJRmS7xCS3ppEnARb9y11OAzaD+P2Ps+b+BGX5iQ==,
      }
    dependencies:
      "@vitest/pretty-format": 2.1.9
      loupe: 3.1.3
      tinyrainbow: 1.2.0
    dev: true

  /abbrev@1.1.1:
    resolution:
      {
        integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==,
      }
    dev: true

  /abbrev@3.0.0:
    resolution:
      {
        integrity: sha512-+/kfrslGQ7TNV2ecmQwMJj/B65g5KVq1/L3SGVZ3tCYGqlzFuFCGBZJtMP99wH3NpEUyAjn0zPdPUg0D+DwrOA==,
      }
    engines: { node: ^18.17.0 || >=20.5.0 }
    dev: true

  /abort-controller@3.0.0:
    resolution:
      {
        integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==,
      }
    engines: { node: ">=6.5" }
    dependencies:
      event-target-shim: 5.0.1
    dev: false

  /agent-base@6.0.2:
    resolution:
      {
        integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==,
      }
    engines: { node: ">= 6.0.0" }
    dependencies:
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /agent-base@7.1.3:
    resolution:
      {
        integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==,
      }
    engines: { node: ">= 14" }
    dev: true

  /agentkeepalive@4.6.0:
    resolution:
      {
        integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==,
      }
    engines: { node: ">= 8.0.0" }
    dependencies:
      humanize-ms: 1.2.1

  /aggregate-error@3.1.0:
    resolution:
      {
        integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==,
      }
    engines: { node: ">=8" }
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ajv@8.17.1:
    resolution:
      {
        integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==,
      }
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
    dev: false

  /ansi-escapes@4.3.2:
    resolution:
      {
        integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==,
      }
    engines: { node: ">=8" }
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
      }
    engines: { node: ">=8" }

  /ansi-regex@6.1.0:
    resolution:
      {
        integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
      }
    engines: { node: ">=12" }
    dev: true

  /ansi-sequence-parser@1.1.3:
    resolution:
      {
        integrity: sha512-+fksAx9eG3Ab6LDnLs3ZqZa8KVJ/jYnX+D4Qe1azX+LFGFAXqynCQLOdLpNYN/l9e7l6hMWwZbrnctqr6eSQSw==,
      }
    dev: true

  /ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
      }
    engines: { node: ">=8" }
    dependencies:
      color-convert: 2.0.1

  /ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
      }
    engines: { node: ">=12" }
    dev: true

  /any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
      }
    dev: true

  /aproba@2.0.0:
    resolution:
      {
        integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==,
      }
    dev: true

  /are-we-there-yet@3.0.1:
    resolution:
      {
        integrity: sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2
    dev: true

  /argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
      }

  /assertion-error@2.0.1:
    resolution:
      {
        integrity: sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==,
      }
    engines: { node: ">=12" }
    dev: true

  /asynckit@0.4.0:
    resolution:
      {
        integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
      }
    dev: false

  /balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
      }

  /binary-search@1.3.6:
    resolution:
      {
        integrity: sha512-nbE1WxOTTrUWIfsfZ4aHGYu5DOuNkbxGokjV6Z2kxfJK3uaAb8zNK1muzOeipoLHZjInT4Br88BHpzevc681xA==,
      }
    dev: false

  /brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
      }
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
      }
    dependencies:
      balanced-match: 1.0.2

  /braintrust@0.0.140:
    resolution:
      {
        integrity: sha512-GEZ4sEw5o4IKn6xV4v4xLYR2GOBaOHd4T/ycFfH6XO9hnu17WL7zsplBASv9gDf/zTKILz/uPr+xo/r648SHIQ==,
      }
    hasBin: true
    dependencies:
      "@ai-sdk/provider": 0.0.6
      "@braintrust/core": 0.0.44
      "@next/env": 14.2.24
      argparse: 2.0.1
      chalk: 4.1.2
      cli-progress: 3.12.0
      dotenv: 16.4.7
      esbuild: 0.18.20
      graceful-fs: 4.2.11
      minimatch: 9.0.5
      mustache: 4.2.0
      pluralize: 8.0.0
      simple-git: 3.27.0
      uuid: 9.0.1
      zod: 3.24.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /buffer-from@1.1.2:
    resolution:
      {
        integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==,
      }
    dev: true

  /bundle-require@5.1.0(esbuild@0.25.1):
    resolution:
      {
        integrity: sha512-3WrrOuZiyaaZPWiEt4G3+IffISVC9HYlWueJEBWED4ZH4aIAC2PnkdnuRrR94M+w6yGWn4AglWtJtBI8YqvgoA==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }
    peerDependencies:
      esbuild: ">=0.18"
    dependencies:
      esbuild: 0.25.1
      load-tsconfig: 0.2.5
    dev: true

  /cac@6.7.14:
    resolution:
      {
        integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
      }
    engines: { node: ">=8" }
    dev: true

  /cacache@16.1.3:
    resolution:
      {
        integrity: sha512-/+Emcj9DAXxX4cwlLmRI9c166RuL3w30zp4R7Joiv2cQTtTtA+jeuCAjH3ZlGnYS3tKENSrKhAzVVP9GVyzeYQ==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      "@npmcli/fs": 2.1.2
      "@npmcli/move-file": 2.0.1
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 8.1.0
      infer-owner: 1.0.4
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 9.0.1
      tar: 6.2.1
      unique-filename: 2.0.1
    transitivePeerDependencies:
      - bluebird
    dev: true

  /call-bind-apply-helpers@1.0.2:
    resolution:
      {
        integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
    dev: false

  /chai@5.2.0:
    resolution:
      {
        integrity: sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==,
      }
    engines: { node: ">=12" }
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.3
      pathval: 2.0.0
    dev: true

  /chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
      }
    engines: { node: ">=10" }
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: false

  /check-error@2.1.1:
    resolution:
      {
        integrity: sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==,
      }
    engines: { node: ">= 16" }
    dev: true

  /cheminfo-types@1.8.1:
    resolution:
      {
        integrity: sha512-FRcpVkox+cRovffgqNdDFQ1eUav+i/Vq/CUd1hcfEl2bevntFlzznL+jE8g4twl6ElB7gZjCko6pYpXyMn+6dA==,
      }
    dev: false

  /chokidar@4.0.3:
    resolution:
      {
        integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==,
      }
    engines: { node: ">= 14.16.0" }
    dependencies:
      readdirp: 4.1.2
    dev: true

  /chownr@2.0.0:
    resolution:
      {
        integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==,
      }
    engines: { node: ">=10" }
    dev: true

  /chownr@3.0.0:
    resolution:
      {
        integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==,
      }
    engines: { node: ">=18" }
    dev: true

  /clean-stack@2.2.0:
    resolution:
      {
        integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==,
      }
    engines: { node: ">=6" }
    dev: true

  /cli-progress@3.12.0:
    resolution:
      {
        integrity: sha512-tRkV3HJ1ASwm19THiiLIXLO7Im7wlTuKnvkYaTkyoAPefqjNg7W7DHKUlGRxy9vxDvbyCYQkQozvptuMkGCg8A==,
      }
    engines: { node: ">=4" }
    dependencies:
      string-width: 4.2.3
    dev: false

  /cli-width@4.1.0:
    resolution:
      {
        integrity: sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==,
      }
    engines: { node: ">= 12" }
    dev: true

  /cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
      }
    engines: { node: ">=12" }
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0
    dev: true

  /color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
      }
    engines: { node: ">=7.0.0" }
    dependencies:
      color-name: 1.1.4

  /color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
      }

  /color-support@1.1.3:
    resolution:
      {
        integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==,
      }
    hasBin: true
    dev: true

  /combined-stream@1.0.8:
    resolution:
      {
        integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
      }
    engines: { node: ">= 0.8" }
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
      }
    engines: { node: ">= 6" }
    dev: true

  /compute-cosine-similarity@1.1.0:
    resolution:
      {
        integrity: sha512-FXhNx0ILLjGi9Z9+lglLzM12+0uoTnYkHm7GiadXDAr0HGVLm25OivUS1B/LPkbzzvlcXz/1EvWg9ZYyJSdhTw==,
      }
    dependencies:
      compute-dot: 1.1.0
      compute-l2norm: 1.1.0
      validate.io-array: 1.0.6
      validate.io-function: 1.0.2
    dev: false

  /compute-dot@1.1.0:
    resolution:
      {
        integrity: sha512-L5Ocet4DdMrXboss13K59OK23GXjiSia7+7Ukc7q4Bl+RVpIXK2W9IHMbWDZkh+JUEvJAwOKRaJDiFUa1LTnJg==,
      }
    dependencies:
      validate.io-array: 1.0.6
      validate.io-function: 1.0.2
    dev: false

  /compute-l2norm@1.1.0:
    resolution:
      {
        integrity: sha512-6EHh1Elj90eU28SXi+h2PLnTQvZmkkHWySpoFz+WOlVNLz3DQoC4ISUHSV9n5jMxPHtKGJ01F4uu2PsXBB8sSg==,
      }
    dependencies:
      validate.io-array: 1.0.6
      validate.io-function: 1.0.2
    dev: false

  /concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
      }
    dev: true

  /consola@3.4.1:
    resolution:
      {
        integrity: sha512-zaUUWockhqxFf4bSXS+kTJwxWvAyMuKtShx0BWcGrMEUqbETcBCT91iQs9pECNx7yz8VH4VeWW/1KAbhE8kiww==,
      }
    engines: { node: ^14.18.0 || >=16.10.0 }
    dev: true

  /console-control-strings@1.1.0:
    resolution:
      {
        integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==,
      }
    dev: true

  /cookie@0.7.2:
    resolution:
      {
        integrity: sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==,
      }
    engines: { node: ">= 0.6" }
    dev: true

  /cross-spawn@7.0.6:
    resolution:
      {
        integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
      }
    engines: { node: ">= 8" }
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /debug@4.4.0:
    resolution:
      {
        integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==,
      }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3

  /deep-eql@5.0.2:
    resolution:
      {
        integrity: sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==,
      }
    engines: { node: ">=6" }
    dev: true

  /delayed-stream@1.0.0:
    resolution:
      {
        integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
      }
    engines: { node: ">=0.4.0" }
    dev: false

  /delegates@1.0.0:
    resolution:
      {
        integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==,
      }
    dev: true

  /detect-libc@2.0.3:
    resolution:
      {
        integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==,
      }
    engines: { node: ">=8" }
    dev: true

  /dotenv@16.4.7:
    resolution:
      {
        integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==,
      }
    engines: { node: ">=12" }
    dev: false

  /duckdb@1.2.0:
    resolution:
      {
        integrity: sha512-zAHHRTMoZhWIwvOsyNkgV9c1nq0gR0j+ZyX0uTCRFZTNOlYO4lnErP5Fddt/6iKMXsTNL9v1oTG9E76S5jMh7w==,
      }
    requiresBuild: true
    dependencies:
      "@mapbox/node-pre-gyp": 2.0.0
      node-addon-api: 7.1.1
      node-gyp: 9.4.1
    transitivePeerDependencies:
      - bluebird
      - encoding
      - supports-color
    dev: true

  /dunder-proto@1.0.1:
    resolution:
      {
        integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0
    dev: false

  /eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
      }
    dev: true

  /emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
      }

  /emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
      }
    dev: true

  /encoding@0.1.13:
    resolution:
      {
        integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==,
      }
    requiresBuild: true
    dependencies:
      iconv-lite: 0.6.3
    dev: true
    optional: true

  /env-paths@2.2.1:
    resolution:
      {
        integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==,
      }
    engines: { node: ">=6" }
    dev: true

  /err-code@2.0.3:
    resolution:
      {
        integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==,
      }
    dev: true

  /es-define-property@1.0.1:
    resolution:
      {
        integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==,
      }
    engines: { node: ">= 0.4" }
    dev: false

  /es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
      }
    engines: { node: ">= 0.4" }
    dev: false

  /es-module-lexer@1.7.0:
    resolution:
      {
        integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==,
      }
    dev: true

  /es-object-atoms@1.1.1:
    resolution:
      {
        integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      es-errors: 1.3.0
    dev: false

  /es-set-tostringtag@2.1.0:
    resolution:
      {
        integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: false

  /esbuild@0.18.20:
    resolution:
      {
        integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==,
      }
    engines: { node: ">=12" }
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      "@esbuild/android-arm": 0.18.20
      "@esbuild/android-arm64": 0.18.20
      "@esbuild/android-x64": 0.18.20
      "@esbuild/darwin-arm64": 0.18.20
      "@esbuild/darwin-x64": 0.18.20
      "@esbuild/freebsd-arm64": 0.18.20
      "@esbuild/freebsd-x64": 0.18.20
      "@esbuild/linux-arm": 0.18.20
      "@esbuild/linux-arm64": 0.18.20
      "@esbuild/linux-ia32": 0.18.20
      "@esbuild/linux-loong64": 0.18.20
      "@esbuild/linux-mips64el": 0.18.20
      "@esbuild/linux-ppc64": 0.18.20
      "@esbuild/linux-riscv64": 0.18.20
      "@esbuild/linux-s390x": 0.18.20
      "@esbuild/linux-x64": 0.18.20
      "@esbuild/netbsd-x64": 0.18.20
      "@esbuild/openbsd-x64": 0.18.20
      "@esbuild/sunos-x64": 0.18.20
      "@esbuild/win32-arm64": 0.18.20
      "@esbuild/win32-ia32": 0.18.20
      "@esbuild/win32-x64": 0.18.20

  /esbuild@0.21.5:
    resolution:
      {
        integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==,
      }
    engines: { node: ">=12" }
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.21.5
      "@esbuild/android-arm": 0.21.5
      "@esbuild/android-arm64": 0.21.5
      "@esbuild/android-x64": 0.21.5
      "@esbuild/darwin-arm64": 0.21.5
      "@esbuild/darwin-x64": 0.21.5
      "@esbuild/freebsd-arm64": 0.21.5
      "@esbuild/freebsd-x64": 0.21.5
      "@esbuild/linux-arm": 0.21.5
      "@esbuild/linux-arm64": 0.21.5
      "@esbuild/linux-ia32": 0.21.5
      "@esbuild/linux-loong64": 0.21.5
      "@esbuild/linux-mips64el": 0.21.5
      "@esbuild/linux-ppc64": 0.21.5
      "@esbuild/linux-riscv64": 0.21.5
      "@esbuild/linux-s390x": 0.21.5
      "@esbuild/linux-x64": 0.21.5
      "@esbuild/netbsd-x64": 0.21.5
      "@esbuild/openbsd-x64": 0.21.5
      "@esbuild/sunos-x64": 0.21.5
      "@esbuild/win32-arm64": 0.21.5
      "@esbuild/win32-ia32": 0.21.5
      "@esbuild/win32-x64": 0.21.5
    dev: true

  /esbuild@0.25.1:
    resolution:
      {
        integrity: sha512-BGO5LtrGC7vxnqucAe/rmvKdJllfGaYWdyABvyMoXQlfYMb2bbRuReWR5tEGE//4LcNJj9XrkovTqNYRFZHAMQ==,
      }
    engines: { node: ">=18" }
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.25.1
      "@esbuild/android-arm": 0.25.1
      "@esbuild/android-arm64": 0.25.1
      "@esbuild/android-x64": 0.25.1
      "@esbuild/darwin-arm64": 0.25.1
      "@esbuild/darwin-x64": 0.25.1
      "@esbuild/freebsd-arm64": 0.25.1
      "@esbuild/freebsd-x64": 0.25.1
      "@esbuild/linux-arm": 0.25.1
      "@esbuild/linux-arm64": 0.25.1
      "@esbuild/linux-ia32": 0.25.1
      "@esbuild/linux-loong64": 0.25.1
      "@esbuild/linux-mips64el": 0.25.1
      "@esbuild/linux-ppc64": 0.25.1
      "@esbuild/linux-riscv64": 0.25.1
      "@esbuild/linux-s390x": 0.25.1
      "@esbuild/linux-x64": 0.25.1
      "@esbuild/netbsd-arm64": 0.25.1
      "@esbuild/netbsd-x64": 0.25.1
      "@esbuild/openbsd-arm64": 0.25.1
      "@esbuild/openbsd-x64": 0.25.1
      "@esbuild/sunos-x64": 0.25.1
      "@esbuild/win32-arm64": 0.25.1
      "@esbuild/win32-ia32": 0.25.1
      "@esbuild/win32-x64": 0.25.1
    dev: true

  /escalade@3.2.0:
    resolution:
      {
        integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==,
      }
    engines: { node: ">=6" }
    dev: true

  /estree-walker@2.0.2:
    resolution:
      {
        integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==,
      }
    dev: true

  /estree-walker@3.0.3:
    resolution:
      {
        integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==,
      }
    dependencies:
      "@types/estree": 1.0.6
    dev: true

  /event-target-shim@5.0.1:
    resolution:
      {
        integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==,
      }
    engines: { node: ">=6" }
    dev: false

  /expect-type@1.2.1:
    resolution:
      {
        integrity: sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==,
      }
    engines: { node: ">=12.0.0" }
    dev: true

  /exponential-backoff@3.1.2:
    resolution:
      {
        integrity: sha512-8QxYTVXUkuy7fIIoitQkPwGonB8F3Zj8eEO8Sqg9Zv/bkI7RJAzowee4gr81Hak/dUTpA2Z7VfQgoijjPNlUZA==,
      }
    dev: true

  /fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
      }
    dev: false

  /fast-uri@3.0.6:
    resolution:
      {
        integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==,
      }
    dev: false

  /fdir@6.4.3(picomatch@4.0.2):
    resolution:
      {
        integrity: sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==,
      }
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: 4.0.2
    dev: true

  /fft.js@4.0.4:
    resolution:
      {
        integrity: sha512-f9c00hphOgeQTlDyavwTtu6RiK8AIFjD6+jvXkNkpeQ7rirK3uFWVpalkoS4LAwbdX7mfZ8aoBfFVQX1Re/8aw==,
      }
    dev: false

  /foreground-child@3.3.1:
    resolution:
      {
        integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==,
      }
    engines: { node: ">=14" }
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0
    dev: true

  /form-data-encoder@1.7.2:
    resolution:
      {
        integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==,
      }
    dev: false

  /form-data@4.0.2:
    resolution:
      {
        integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==,
      }
    engines: { node: ">= 6" }
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35
    dev: false

  /formdata-node@4.4.1:
    resolution:
      {
        integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==,
      }
    engines: { node: ">= 12.20" }
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3
    dev: false

  /fs-minipass@2.1.0:
    resolution:
      {
        integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==,
      }
    engines: { node: ">= 8" }
    dependencies:
      minipass: 3.3.6
    dev: true

  /fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==,
      }
    dev: true

  /fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
      }
    dev: false

  /gauge@4.0.4:
    resolution:
      {
        integrity: sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5
    dev: true

  /get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
      }
    engines: { node: 6.* || 8.* || >= 10.* }
    dev: true

  /get-intrinsic@1.3.0:
    resolution:
      {
        integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0
    dev: false

  /get-proto@1.0.1:
    resolution:
      {
        integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1
    dev: false

  /get-tsconfig@4.10.0:
    resolution:
      {
        integrity: sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==,
      }
    dependencies:
      resolve-pkg-maps: 1.0.0
    dev: true

  /glob@10.4.5:
    resolution:
      {
        integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
      }
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1
    dev: true

  /glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==,
      }
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /glob@8.1.0:
    resolution:
      {
        integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==,
      }
    engines: { node: ">=12" }
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0
    dev: true

  /gopd@1.2.0:
    resolution:
      {
        integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==,
      }
    engines: { node: ">= 0.4" }
    dev: false

  /graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
      }

  /graphql@16.10.0:
    resolution:
      {
        integrity: sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==,
      }
    engines: { node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0 }
    dev: true

  /handlebars@4.7.8:
    resolution:
      {
        integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==,
      }
    engines: { node: ">=0.4.7" }
    hasBin: true
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.19.3
    dev: true

  /has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
      }
    engines: { node: ">=8" }
    dev: false

  /has-symbols@1.1.0:
    resolution:
      {
        integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==,
      }
    engines: { node: ">= 0.4" }
    dev: false

  /has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      has-symbols: 1.1.0
    dev: false

  /has-unicode@2.0.1:
    resolution:
      {
        integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==,
      }
    dev: true

  /hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
      }
    engines: { node: ">= 0.4" }
    dependencies:
      function-bind: 1.1.2
    dev: false

  /headers-polyfill@4.0.3:
    resolution:
      {
        integrity: sha512-IScLbePpkvO846sIwOtOTDjutRMWdXdJmXdMvk6gCBHxFO8d+QKOQedyZSxFTTFYRSmlgSTDtXqqq4pcenBXLQ==,
      }
    dev: true

  /http-cache-semantics@4.1.1:
    resolution:
      {
        integrity: sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==,
      }
    dev: true

  /http-proxy-agent@5.0.0:
    resolution:
      {
        integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==,
      }
    engines: { node: ">= 6" }
    dependencies:
      "@tootallnate/once": 2.0.0
      agent-base: 6.0.2
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /https-proxy-agent@5.0.1:
    resolution:
      {
        integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==,
      }
    engines: { node: ">= 6" }
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /https-proxy-agent@7.0.6:
    resolution:
      {
        integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==,
      }
    engines: { node: ">= 14" }
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /humanize-ms@1.2.1:
    resolution:
      {
        integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==,
      }
    dependencies:
      ms: 2.1.3

  /iconv-lite@0.6.3:
    resolution:
      {
        integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==,
      }
    engines: { node: ">=0.10.0" }
    requiresBuild: true
    dependencies:
      safer-buffer: 2.1.2
    dev: true
    optional: true

  /imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
      }
    engines: { node: ">=0.8.19" }
    dev: true

  /indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
      }
    engines: { node: ">=8" }
    dev: true

  /infer-owner@1.0.4:
    resolution:
      {
        integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==,
      }
    dev: true

  /inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==,
      }
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: true

  /inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
      }
    dev: true

  /install@0.13.0:
    resolution:
      {
        integrity: sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA==,
      }
    engines: { node: ">= 0.10" }
    dev: false

  /ip-address@9.0.5:
    resolution:
      {
        integrity: sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==,
      }
    engines: { node: ">= 12" }
    dependencies:
      jsbn: 1.1.0
      sprintf-js: 1.1.3
    dev: true

  /is-any-array@2.0.1:
    resolution:
      {
        integrity: sha512-UtilS7hLRu++wb/WBAw9bNuP1Eg04Ivn1vERJck8zJthEvXCBEBpGR/33u/xLKWEQf95803oalHrVDptcAvFdQ==,
      }
    dev: false

  /is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
      }
    engines: { node: ">=8" }

  /is-lambda@1.0.1:
    resolution:
      {
        integrity: sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==,
      }
    dev: true

  /is-node-process@1.2.0:
    resolution:
      {
        integrity: sha512-Vg4o6/fqPxIjtxgUH5QLJhwZ7gW5diGCVlXpuUfELC62CuxM1iHcRe51f2W1FDy04Ai4KJkagKjx3XaqyfRKXw==,
      }
    dev: true

  /isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
      }
    dev: true

  /jackspeak@3.4.3:
    resolution:
      {
        integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
      }
    dependencies:
      "@isaacs/cliui": 8.0.2
    optionalDependencies:
      "@pkgjs/parseargs": 0.11.0
    dev: true

  /joycon@3.1.1:
    resolution:
      {
        integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==,
      }
    engines: { node: ">=10" }
    dev: true

  /js-levenshtein@1.1.6:
    resolution:
      {
        integrity: sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g==,
      }
    engines: { node: ">=0.10.0" }
    dev: false

  /js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
      }
    hasBin: true
    dependencies:
      argparse: 2.0.1

  /jsbn@1.1.0:
    resolution:
      {
        integrity: sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==,
      }
    dev: true

  /json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==,
      }
    dev: false

  /json-schema@0.4.0:
    resolution:
      {
        integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==,
      }
    dev: false

  /jsonc-parser@3.3.1:
    resolution:
      {
        integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==,
      }
    dev: true

  /lilconfig@3.1.3:
    resolution:
      {
        integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
      }
    engines: { node: ">=14" }
    dev: true

  /linear-sum-assignment@1.0.7:
    resolution:
      {
        integrity: sha512-jfLoSGwZNyjfY8eK4ayhjfcIu3BfWvP6sWieYzYI3AWldwXVoWEz1gtrQL10v/8YltYLBunqNjeVFXPMUs+MJg==,
      }
    dependencies:
      cheminfo-types: 1.8.1
      install: 0.13.0
      ml-matrix: 6.12.1
      ml-spectra-processing: 14.10.0
    dev: false

  /lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
      }
    dev: true

  /load-tsconfig@0.2.5:
    resolution:
      {
        integrity: sha512-IXO6OCs9yg8tMKzfPZ1YmheJbZCiEsnBdcB03l0OcfK9prKnJb96siuHCr5Fl37/yo9DnKU+TLpxzTUspw9shg==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }
    dev: true

  /lodash.sortby@4.7.0:
    resolution:
      {
        integrity: sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==,
      }
    dev: true

  /loupe@3.1.3:
    resolution:
      {
        integrity: sha512-kkIp7XSkP78ZxJEsSxW3712C6teJVoeHHwgo9zJ380de7IYyJ2ISlxojcH2pC5OFLewESmnRi/+XCDIEEVyoug==,
      }
    dev: true

  /lru-cache@10.4.3:
    resolution:
      {
        integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
      }
    dev: true

  /lru-cache@7.18.3:
    resolution:
      {
        integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==,
      }
    engines: { node: ">=12" }
    dev: true

  /lunr@2.3.9:
    resolution:
      {
        integrity: sha512-zTU3DaZaF3Rt9rhN3uBMGQD3dD2/vFQqnvZCDv4dl5iOzq2IZQqTxu90r4E5J+nP70J3ilqVCrbho2eWaeW8Ow==,
      }
    dev: true

  /magic-string@0.30.17:
    resolution:
      {
        integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==,
      }
    dependencies:
      "@jridgewell/sourcemap-codec": 1.5.0
    dev: true

  /make-fetch-happen@10.2.1:
    resolution:
      {
        integrity: sha512-NgOPbRiaQM10DYXvN3/hhGVI2M5MtITFryzBGxHM5p4wnFxsVCbxkrBrDsk+EZ5OB4jEOT7AjDxtdF+KVEFT7w==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      agentkeepalive: 4.6.0
      cacache: 16.1.3
      http-cache-semantics: 4.1.1
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 2.1.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      promise-retry: 2.0.1
      socks-proxy-agent: 7.0.0
      ssri: 9.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /marked@4.3.0:
    resolution:
      {
        integrity: sha512-PRsaiG84bK+AMvxziE/lCFss8juXjNaWzVbN5tXAm4XjeaS9NAHhop+PjQxz2A9h8Q4M/xGmzP8vqNwy6JeK0A==,
      }
    engines: { node: ">= 12" }
    hasBin: true
    dev: true

  /math-intrinsics@1.1.0:
    resolution:
      {
        integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==,
      }
    engines: { node: ">= 0.4" }
    dev: false

  /mime-db@1.52.0:
    resolution:
      {
        integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
      }
    engines: { node: ">= 0.6" }
    dev: false

  /mime-types@2.1.35:
    resolution:
      {
        integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
      }
    engines: { node: ">= 0.6" }
    dependencies:
      mime-db: 1.52.0
    dev: false

  /minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
      }
    dependencies:
      brace-expansion: 1.1.11
    dev: true

  /minimatch@5.1.6:
    resolution:
      {
        integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==,
      }
    engines: { node: ">=10" }
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimatch@9.0.5:
    resolution:
      {
        integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    dependencies:
      brace-expansion: 2.0.1

  /minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
      }
    dev: true

  /minipass-collect@1.0.2:
    resolution:
      {
        integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==,
      }
    engines: { node: ">= 8" }
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-fetch@2.1.2:
    resolution:
      {
        integrity: sha512-LT49Zi2/WMROHYoqGgdlQIZh8mLPZmOrN2NdJjMXxYe4nkN6FUyuPuOAOedNJDrx0IRGg9+4guZewtp8hE6TxA==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13
    dev: true

  /minipass-flush@1.0.5:
    resolution:
      {
        integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==,
      }
    engines: { node: ">= 8" }
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-pipeline@1.2.4:
    resolution:
      {
        integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==,
      }
    engines: { node: ">=8" }
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass-sized@1.0.3:
    resolution:
      {
        integrity: sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==,
      }
    engines: { node: ">=8" }
    dependencies:
      minipass: 3.3.6
    dev: true

  /minipass@3.3.6:
    resolution:
      {
        integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==,
      }
    engines: { node: ">=8" }
    dependencies:
      yallist: 4.0.0
    dev: true

  /minipass@5.0.0:
    resolution:
      {
        integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==,
      }
    engines: { node: ">=8" }
    dev: true

  /minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    dev: true

  /minizlib@2.1.2:
    resolution:
      {
        integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==,
      }
    engines: { node: ">= 8" }
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0
    dev: true

  /minizlib@3.0.1:
    resolution:
      {
        integrity: sha512-umcy022ILvb5/3Djuu8LWeqUa8D68JaBzlttKeMWen48SjabqS3iY5w/vzeMzMUNhLDifyhbOwKDSznB1vvrwg==,
      }
    engines: { node: ">= 18" }
    dependencies:
      minipass: 7.1.2
      rimraf: 5.0.10
    dev: true

  /mkdirp@1.0.4:
    resolution:
      {
        integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==,
      }
    engines: { node: ">=10" }
    hasBin: true
    dev: true

  /mkdirp@3.0.1:
    resolution:
      {
        integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==,
      }
    engines: { node: ">=10" }
    hasBin: true
    dev: true

  /ml-array-max@1.2.4:
    resolution:
      {
        integrity: sha512-BlEeg80jI0tW6WaPyGxf5Sa4sqvcyY6lbSn5Vcv44lp1I2GR6AWojfUvLnGTNsIXrZ8uqWmo8VcG1WpkI2ONMQ==,
      }
    dependencies:
      is-any-array: 2.0.1
    dev: false

  /ml-array-min@1.2.3:
    resolution:
      {
        integrity: sha512-VcZ5f3VZ1iihtrGvgfh/q0XlMobG6GQ8FsNyQXD3T+IlstDv85g8kfV0xUG1QPRO/t21aukaJowDzMTc7j5V6Q==,
      }
    dependencies:
      is-any-array: 2.0.1
    dev: false

  /ml-array-rescale@1.3.7:
    resolution:
      {
        integrity: sha512-48NGChTouvEo9KBctDfHC3udWnQKNKEWN0ziELvY3KG25GR5cA8K8wNVzracsqSW1QEkAXjTNx+ycgAv06/1mQ==,
      }
    dependencies:
      is-any-array: 2.0.1
      ml-array-max: 1.2.4
      ml-array-min: 1.2.3
    dev: false

  /ml-matrix@6.12.1:
    resolution:
      {
        integrity: sha512-TJ+8eOFdp+INvzR4zAuwBQJznDUfktMtOB6g/hUcGh3rcyjxbz4Te57Pgri8Q9bhSQ7Zys4IYOGhFdnlgeB6Lw==,
      }
    dependencies:
      is-any-array: 2.0.1
      ml-array-rescale: 1.3.7
    dev: false

  /ml-spectra-processing@14.10.0:
    resolution:
      {
        integrity: sha512-4fyF6tojgVgh6m9nmFvaIlGhrvHq+swn64IxQ44F4k4o7Qkl8xKOJWfQ4EsfoX66GqZn2PFfcn1xUGRNwB8+3w==,
      }
    dependencies:
      binary-search: 1.3.6
      cheminfo-types: 1.8.1
      fft.js: 4.0.4
      is-any-array: 2.0.1
      ml-matrix: 6.12.1
      ml-xsadd: 3.0.1
    dev: false

  /ml-xsadd@3.0.1:
    resolution:
      {
        integrity: sha512-Fz2q6dwgzGM8wYKGArTUTZDGa4lQFA2Vi6orjGeTVRy22ZnQFKlJuwS9n8NRviqz1KHAHAzdKJwbnYhdo38uYg==,
      }
    dev: false

  /ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
      }

  /msw@2.7.3(@types/node@20.17.24)(typescript@5.8.2):
    resolution:
      {
        integrity: sha512-+mycXv8l2fEAjFZ5sjrtjJDmm2ceKGjrNbBr1durRg6VkU9fNUE/gsmQ51hWbHqs+l35W1iM+ZsmOD9Fd6lspw==,
      }
    engines: { node: ">=18" }
    hasBin: true
    requiresBuild: true
    peerDependencies:
      typescript: ">= 4.8.x"
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      "@bundled-es-modules/cookie": 2.0.1
      "@bundled-es-modules/statuses": 1.0.1
      "@bundled-es-modules/tough-cookie": 0.1.6
      "@inquirer/confirm": 5.1.8(@types/node@20.17.24)
      "@mswjs/interceptors": 0.37.6
      "@open-draft/deferred-promise": 2.2.0
      "@open-draft/until": 2.1.0
      "@types/cookie": 0.6.0
      "@types/statuses": 2.0.5
      graphql: 16.10.0
      headers-polyfill: 4.0.3
      is-node-process: 1.2.0
      outvariant: 1.4.3
      path-to-regexp: 6.3.0
      picocolors: 1.1.1
      strict-event-emitter: 0.5.1
      type-fest: 4.37.0
      typescript: 5.8.2
      yargs: 17.7.2
    transitivePeerDependencies:
      - "@types/node"
    dev: true

  /mustache@4.2.0:
    resolution:
      {
        integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==,
      }
    hasBin: true
    dev: false

  /mute-stream@2.0.0:
    resolution:
      {
        integrity: sha512-WWdIxpyjEn+FhQJQQv9aQAYlHoNVdzIzUySNV1gHUPDSdZJ3yZn7pAAbQcV7B56Mvu881q9FZV+0Vx2xC44VWA==,
      }
    engines: { node: ^18.17.0 || >=20.5.0 }
    dev: true

  /mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
      }
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0
    dev: true

  /nanoid@3.3.11:
    resolution:
      {
        integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true
    dev: true

  /negotiator@0.6.4:
    resolution:
      {
        integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==,
      }
    engines: { node: ">= 0.6" }
    dev: true

  /neo-async@2.6.2:
    resolution:
      {
        integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==,
      }
    dev: true

  /node-addon-api@7.1.1:
    resolution:
      {
        integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==,
      }
    dev: true

  /node-domexception@1.0.0:
    resolution:
      {
        integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==,
      }
    engines: { node: ">=10.5.0" }
    dev: false

  /node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0

  /node-gyp@9.4.1:
    resolution:
      {
        integrity: sha512-OQkWKbjQKbGkMf/xqI1jjy3oCTgMKJac58G2+bjZb3fza6gW2YrCSdMQYaoTb70crvE//Gngr4f0AgVHmqHvBQ==,
      }
    engines: { node: ^12.13 || ^14.13 || >=16 }
    hasBin: true
    dependencies:
      env-paths: 2.2.1
      exponential-backoff: 3.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      make-fetch-happen: 10.2.1
      nopt: 6.0.0
      npmlog: 6.0.2
      rimraf: 3.0.2
      semver: 7.7.1
      tar: 6.2.1
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color
    dev: true

  /nopt@6.0.0:
    resolution:
      {
        integrity: sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    hasBin: true
    dependencies:
      abbrev: 1.1.1
    dev: true

  /nopt@8.1.0:
    resolution:
      {
        integrity: sha512-ieGu42u/Qsa4TFktmaKEwM6MQH0pOWnaB3htzh0JRtx84+Mebc0cbZYN5bC+6WTZ4+77xrL9Pn5m7CV6VIkV7A==,
      }
    engines: { node: ^18.17.0 || >=20.5.0 }
    hasBin: true
    dependencies:
      abbrev: 3.0.0
    dev: true

  /npmlog@6.0.2:
    resolution:
      {
        integrity: sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    deprecated: This package is no longer supported.
    dependencies:
      are-we-there-yet: 3.0.1
      console-control-strings: 1.1.0
      gauge: 4.0.4
      set-blocking: 2.0.0
    dev: true

  /object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
      }
    engines: { node: ">=0.10.0" }
    dev: true

  /once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
      }
    dependencies:
      wrappy: 1.0.2
    dev: true

  /openai@4.47.1:
    resolution:
      {
        integrity: sha512-WWSxhC/69ZhYWxH/OBsLEirIjUcfpQ5+ihkXKp06hmeYXgBBIUCa9IptMzYx6NdkiOCsSGYCnTIsxaic3AjRCQ==,
      }
    hasBin: true
    dependencies:
      "@types/node": 18.19.80
      "@types/node-fetch": 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
      web-streams-polyfill: 3.3.3
    transitivePeerDependencies:
      - encoding
    dev: false

  /openapi3-ts@4.4.0:
    resolution:
      {
        integrity: sha512-9asTNB9IkKEzWMcHmVZE7Ts3kC9G7AFHfs8i7caD8HbI76gEjdkId4z/AkP83xdZsH7PLAnnbl47qZkXuxpArw==,
      }
    dependencies:
      yaml: 2.7.0
    dev: false

  /outvariant@1.4.3:
    resolution:
      {
        integrity: sha512-+Sl2UErvtsoajRDKCE5/dBz4DIvHXQQnAxtQTF04OJxY0+DyZXSo5P5Bb7XYWOh81syohlYL24hbDwxedPUJCA==,
      }
    dev: true

  /p-map@4.0.0:
    resolution:
      {
        integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==,
      }
    engines: { node: ">=10" }
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /package-json-from-dist@1.0.1:
    resolution:
      {
        integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
      }
    dev: true

  /path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==,
      }
    engines: { node: ">=0.10.0" }
    dev: true

  /path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
      }
    engines: { node: ">=8" }
    dev: true

  /path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
      }
    engines: { node: ">=16 || 14 >=14.18" }
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    dev: true

  /path-to-regexp@6.3.0:
    resolution:
      {
        integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==,
      }
    dev: true

  /pathe@1.1.2:
    resolution:
      {
        integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==,
      }
    dev: true

  /pathval@2.0.0:
    resolution:
      {
        integrity: sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==,
      }
    engines: { node: ">= 14.16" }
    dev: true

  /picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
      }
    dev: true

  /picomatch@4.0.2:
    resolution:
      {
        integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==,
      }
    engines: { node: ">=12" }
    dev: true

  /pirates@4.0.6:
    resolution:
      {
        integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
      }
    engines: { node: ">= 6" }
    dev: true

  /pluralize@8.0.0:
    resolution:
      {
        integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==,
      }
    engines: { node: ">=4" }
    dev: false

  /postcss-load-config@6.0.1(tsx@3.14.0):
    resolution:
      {
        integrity: sha512-oPtTM4oerL+UXmx+93ytZVN82RrlY/wPUV8IeDxFrzIjXOLF1pN+EmKPLbubvKHT2HC20xXsCAH2Z+CKV6Oz/g==,
      }
    engines: { node: ">= 18" }
    peerDependencies:
      jiti: ">=1.21.0"
      postcss: ">=8.0.9"
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      jiti:
        optional: true
      postcss:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true
    dependencies:
      lilconfig: 3.1.3
      tsx: 3.14.0
    dev: true

  /postcss@8.5.3:
    resolution:
      {
        integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==,
      }
    engines: { node: ^10 || ^12 || >=14 }
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /promise-inflight@1.0.1:
    resolution:
      {
        integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==,
      }
    peerDependencies:
      bluebird: "*"
    peerDependenciesMeta:
      bluebird:
        optional: true
    dev: true

  /promise-retry@2.0.1:
    resolution:
      {
        integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==,
      }
    engines: { node: ">=10" }
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0
    dev: true

  /psl@1.15.0:
    resolution:
      {
        integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==,
      }
    dependencies:
      punycode: 2.3.1
    dev: true

  /punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
      }
    engines: { node: ">=6" }
    dev: true

  /querystringify@2.2.0:
    resolution:
      {
        integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==,
      }
    dev: true

  /readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
      }
    engines: { node: ">= 6" }
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2
    dev: true

  /readdirp@4.1.2:
    resolution:
      {
        integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==,
      }
    engines: { node: ">= 14.18.0" }
    dev: true

  /require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
      }
    engines: { node: ">=0.10.0" }
    dev: true

  /require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==,
      }
    engines: { node: ">=0.10.0" }
    dev: false

  /requires-port@1.0.0:
    resolution:
      {
        integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==,
      }
    dev: true

  /resolve-from@5.0.0:
    resolution:
      {
        integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==,
      }
    engines: { node: ">=8" }
    dev: true

  /resolve-pkg-maps@1.0.0:
    resolution:
      {
        integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==,
      }
    dev: true

  /retry@0.12.0:
    resolution:
      {
        integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==,
      }
    engines: { node: ">= 4" }
    dev: true

  /rimraf@3.0.2:
    resolution:
      {
        integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==,
      }
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /rimraf@5.0.10:
    resolution:
      {
        integrity: sha512-l0OE8wL34P4nJH/H2ffoaniAokM2qSmrtXHmlpvYr5AVVX8msAyW0l8NVJFDxlSK4u3Uh/f41cQheDVdnYijwQ==,
      }
    hasBin: true
    dependencies:
      glob: 10.4.5
    dev: true

  /rollup@4.36.0:
    resolution:
      {
        integrity: sha512-zwATAXNQxUcd40zgtQG0ZafcRK4g004WtEl7kbuhTWPvf07PsfohXl39jVUvPF7jvNAIkKPQ2XrsDlWuxBd++Q==,
      }
    engines: { node: ">=18.0.0", npm: ">=8.0.0" }
    hasBin: true
    dependencies:
      "@types/estree": 1.0.6
    optionalDependencies:
      "@rollup/rollup-android-arm-eabi": 4.36.0
      "@rollup/rollup-android-arm64": 4.36.0
      "@rollup/rollup-darwin-arm64": 4.36.0
      "@rollup/rollup-darwin-x64": 4.36.0
      "@rollup/rollup-freebsd-arm64": 4.36.0
      "@rollup/rollup-freebsd-x64": 4.36.0
      "@rollup/rollup-linux-arm-gnueabihf": 4.36.0
      "@rollup/rollup-linux-arm-musleabihf": 4.36.0
      "@rollup/rollup-linux-arm64-gnu": 4.36.0
      "@rollup/rollup-linux-arm64-musl": 4.36.0
      "@rollup/rollup-linux-loongarch64-gnu": 4.36.0
      "@rollup/rollup-linux-powerpc64le-gnu": 4.36.0
      "@rollup/rollup-linux-riscv64-gnu": 4.36.0
      "@rollup/rollup-linux-s390x-gnu": 4.36.0
      "@rollup/rollup-linux-x64-gnu": 4.36.0
      "@rollup/rollup-linux-x64-musl": 4.36.0
      "@rollup/rollup-win32-arm64-msvc": 4.36.0
      "@rollup/rollup-win32-ia32-msvc": 4.36.0
      "@rollup/rollup-win32-x64-msvc": 4.36.0
      fsevents: 2.3.3
    dev: true

  /safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
      }
    dev: true

  /safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
      }
    requiresBuild: true
    dev: true
    optional: true

  /semver@7.7.1:
    resolution:
      {
        integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==,
      }
    engines: { node: ">=10" }
    hasBin: true
    dev: true

  /set-blocking@2.0.0:
    resolution:
      {
        integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==,
      }
    dev: true

  /shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
      }
    engines: { node: ">=8" }
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
      }
    engines: { node: ">=8" }
    dev: true

  /shiki@0.14.7:
    resolution:
      {
        integrity: sha512-dNPAPrxSc87ua2sKJ3H5dQ/6ZaY8RNnaAqK+t0eG7p0Soi2ydiqbGOTaZCqaYvA/uZYfS1LJnemt3Q+mSfcPCg==,
      }
    dependencies:
      ansi-sequence-parser: 1.1.3
      jsonc-parser: 3.3.1
      vscode-oniguruma: 1.7.0
      vscode-textmate: 8.0.0
    dev: true

  /siginfo@2.0.0:
    resolution:
      {
        integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==,
      }
    dev: true

  /signal-exit@3.0.7:
    resolution:
      {
        integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
      }
    dev: true

  /signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
      }
    engines: { node: ">=14" }
    dev: true

  /simple-git@3.27.0:
    resolution:
      {
        integrity: sha512-ivHoFS9Yi9GY49ogc6/YAi3Fl9ROnF4VyubNylgCkA+RVqLaKWnDSzXOVzya8csELIaWaYNutsEuAhZrtOjozA==,
      }
    dependencies:
      "@kwsites/file-exists": 1.1.1
      "@kwsites/promise-deferred": 1.1.1
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /smart-buffer@4.2.0:
    resolution:
      {
        integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==,
      }
    engines: { node: ">= 6.0.0", npm: ">= 3.0.0" }
    dev: true

  /socks-proxy-agent@7.0.0:
    resolution:
      {
        integrity: sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww==,
      }
    engines: { node: ">= 10" }
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0
      socks: 2.8.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /socks@2.8.4:
    resolution:
      {
        integrity: sha512-D3YaD0aRxR3mEcqnidIs7ReYJFVzWdd6fXJYUM8ixcQcJRGTka/b3saV0KflYhyVJXKhb947GndU35SxYNResQ==,
      }
    engines: { node: ">= 10.0.0", npm: ">= 3.0.0" }
    dependencies:
      ip-address: 9.0.5
      smart-buffer: 4.2.0
    dev: true

  /source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
      }
    engines: { node: ">=0.10.0" }
    dev: true

  /source-map-support@0.5.21:
    resolution:
      {
        integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==,
      }
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
      }
    engines: { node: ">=0.10.0" }
    dev: true

  /source-map@0.8.0-beta.0:
    resolution:
      {
        integrity: sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==,
      }
    engines: { node: ">= 8" }
    dependencies:
      whatwg-url: 7.1.0
    dev: true

  /sprintf-js@1.1.3:
    resolution:
      {
        integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==,
      }
    dev: true

  /ssri@9.0.1:
    resolution:
      {
        integrity: sha512-o57Wcn66jMQvfHG1FlYbWeZWW/dHZhJXjpIcTfXldXEk5nz5lStPo3mK0OJQfGR3RbZUlbISexbljkJzuEj/8Q==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      minipass: 3.3.6
    dev: true

  /stackback@0.0.2:
    resolution:
      {
        integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==,
      }
    dev: true

  /statuses@2.0.1:
    resolution:
      {
        integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==,
      }
    engines: { node: ">= 0.8" }
    dev: true

  /std-env@3.9.0:
    resolution:
      {
        integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==,
      }
    dev: true

  /strict-event-emitter@0.5.1:
    resolution:
      {
        integrity: sha512-vMgjE/GGEPEFnhFub6pa4FmJBRBVOLpIII2hvCZ8Kzb7K0hlHo7mQv6xYrBvCL2LtAIBwFUK8wvuJgTVSQ5MFQ==,
      }
    dev: true

  /string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
      }
    engines: { node: ">=8" }
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  /string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
      }
    engines: { node: ">=12" }
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
      }
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
      }
    engines: { node: ">=8" }
    dependencies:
      ansi-regex: 5.0.1

  /strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
      }
    engines: { node: ">=12" }
    dependencies:
      ansi-regex: 6.1.0
    dev: true

  /sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    hasBin: true
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13
    dev: true

  /supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
      }
    engines: { node: ">=8" }
    dependencies:
      has-flag: 4.0.0
    dev: false

  /tar@6.2.1:
    resolution:
      {
        integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==,
      }
    engines: { node: ">=10" }
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0
    dev: true

  /tar@7.4.3:
    resolution:
      {
        integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==,
      }
    engines: { node: ">=18" }
    dependencies:
      "@isaacs/fs-minipass": 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.1
      mkdirp: 3.0.1
      yallist: 5.0.0
    dev: true

  /thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
      }
    engines: { node: ">=0.8" }
    dependencies:
      thenify: 3.3.1
    dev: true

  /thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
      }
    dependencies:
      any-promise: 1.3.0
    dev: true

  /tinybench@2.9.0:
    resolution:
      {
        integrity: sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==,
      }
    dev: true

  /tinyexec@0.3.2:
    resolution:
      {
        integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==,
      }
    dev: true

  /tinyglobby@0.2.12:
    resolution:
      {
        integrity: sha512-qkf4trmKSIiMTs/E63cxH+ojC2unam7rJ0WrauAzpT3ECNTxGRMlaXxVbfxMUC/w0LaYk6jQ4y/nGR9uBO3tww==,
      }
    engines: { node: ">=12.0.0" }
    dependencies:
      fdir: 6.4.3(picomatch@4.0.2)
      picomatch: 4.0.2
    dev: true

  /tinypool@1.0.2:
    resolution:
      {
        integrity: sha512-al6n+QEANGFOMf/dmUMsuS5/r9B06uwlyNjZZql/zv8J7ybHCgoihBNORZCY2mzUuAnomQa2JdhyHKzZxPCrFA==,
      }
    engines: { node: ^18.0.0 || >=20.0.0 }
    dev: true

  /tinyrainbow@1.2.0:
    resolution:
      {
        integrity: sha512-weEDEq7Z5eTHPDh4xjX789+fHfF+P8boiFB+0vbWzpbnbsEr/GRaohi/uMKxg8RZMXnl1ItAi/IUHWMsjDV7kQ==,
      }
    engines: { node: ">=14.0.0" }
    dev: true

  /tinyspy@3.0.2:
    resolution:
      {
        integrity: sha512-n1cw8k1k0x4pgA2+9XrOkFydTerNcJ1zWCO5Nn9scWHTD+5tp8dghT2x1uduQePZTZgd3Tupf+x9BxJjeJi77Q==,
      }
    engines: { node: ">=14.0.0" }
    dev: true

  /tosource@2.0.0-alpha.3:
    resolution:
      {
        integrity: sha512-KAB2lrSS48y91MzFPFuDg4hLbvDiyTjOVgaK7Erw+5AmZXNq4sFRVn8r6yxSLuNs15PaokrDRpS61ERY9uZOug==,
      }
    engines: { node: ">=10" }
    dev: true

  /tough-cookie@4.1.4:
    resolution:
      {
        integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==,
      }
    engines: { node: ">=6" }
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10
    dev: true

  /tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==,
      }

  /tr46@1.0.1:
    resolution:
      {
        integrity: sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==,
      }
    dependencies:
      punycode: 2.3.1
    dev: true

  /tree-kill@1.2.2:
    resolution:
      {
        integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==,
      }
    hasBin: true
    dev: true

  /ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
      }
    dev: true

  /tsup@8.4.0(tsx@3.14.0)(typescript@5.8.2):
    resolution:
      {
        integrity: sha512-b+eZbPCjz10fRryaAA7C8xlIHnf8VnsaRqydheLIqwG/Mcpfk8Z5zp3HayX7GaTygkigHl5cBUs+IhcySiIexQ==,
      }
    engines: { node: ">=18" }
    hasBin: true
    peerDependencies:
      "@microsoft/api-extractor": ^7.36.0
      "@swc/core": ^1
      postcss: ^8.4.12
      typescript: ">=4.5.0"
    peerDependenciesMeta:
      "@microsoft/api-extractor":
        optional: true
      "@swc/core":
        optional: true
      postcss:
        optional: true
      typescript:
        optional: true
    dependencies:
      bundle-require: 5.1.0(esbuild@0.25.1)
      cac: 6.7.14
      chokidar: 4.0.3
      consola: 3.4.1
      debug: 4.4.0
      esbuild: 0.25.1
      joycon: 3.1.1
      picocolors: 1.1.1
      postcss-load-config: 6.0.1(tsx@3.14.0)
      resolve-from: 5.0.0
      rollup: 4.36.0
      source-map: 0.8.0-beta.0
      sucrase: 3.35.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.12
      tree-kill: 1.2.2
      typescript: 5.8.2
    transitivePeerDependencies:
      - jiti
      - supports-color
      - tsx
      - yaml
    dev: true

  /tsx@3.14.0:
    resolution:
      {
        integrity: sha512-xHtFaKtHxM9LOklMmJdI3BEnQq/D5F73Of2E1GDrITi9sgoVkvIsrQUTY1G8FlmGtA+awCI4EBlTRRYxkL2sRg==,
      }
    hasBin: true
    dependencies:
      esbuild: 0.18.20
      get-tsconfig: 4.10.0
      source-map-support: 0.5.21
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /type-fest@0.21.3:
    resolution:
      {
        integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==,
      }
    engines: { node: ">=10" }
    dev: true

  /type-fest@4.37.0:
    resolution:
      {
        integrity: sha512-S/5/0kFftkq27FPNye0XM1e2NsnoD/3FS+pBmbjmmtLT6I+i344KoOf7pvXreaFsDamWeaJX55nczA1m5PsBDg==,
      }
    engines: { node: ">=16" }
    dev: true

  /typedoc-plugin-markdown@3.17.1(typedoc@0.25.13):
    resolution:
      {
        integrity: sha512-QzdU3fj0Kzw2XSdoL15ExLASt2WPqD7FbLeaqwT70+XjKyTshBnUlQA5nNREO1C2P8Uen0CDjsBLMsCQ+zd0lw==,
      }
    peerDependencies:
      typedoc: ">=0.24.0"
    dependencies:
      handlebars: 4.7.8
      typedoc: 0.25.13(typescript@5.8.2)
    dev: true

  /typedoc@0.25.13(typescript@5.8.2):
    resolution:
      {
        integrity: sha512-pQqiwiJ+Z4pigfOnnysObszLiU3mVLWAExSPf+Mu06G/qsc3wzbuM56SZQvONhHLncLUhYzOVkjFFpFfL5AzhQ==,
      }
    engines: { node: ">= 16" }
    hasBin: true
    peerDependencies:
      typescript: 4.6.x || 4.7.x || 4.8.x || 4.9.x || 5.0.x || 5.1.x || 5.2.x || 5.3.x || 5.4.x
    dependencies:
      lunr: 2.3.9
      marked: 4.3.0
      minimatch: 9.0.5
      shiki: 0.14.7
      typescript: 5.8.2
    dev: true

  /typescript@5.8.2:
    resolution:
      {
        integrity: sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==,
      }
    engines: { node: ">=14.17" }
    hasBin: true
    dev: true

  /uglify-js@3.19.3:
    resolution:
      {
        integrity: sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ==,
      }
    engines: { node: ">=0.8.0" }
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /undici-types@5.26.5:
    resolution:
      {
        integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==,
      }
    dev: false

  /undici-types@6.19.8:
    resolution:
      {
        integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==,
      }

  /unique-filename@2.0.1:
    resolution:
      {
        integrity: sha512-ODWHtkkdx3IAR+veKxFV+VBkUMcN+FaqzUUd7IZzt+0zhDZFPFxhlqwPF3YQvMHx1TD0tdgYl+kuPnJ8E6ql7A==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      unique-slug: 3.0.0
    dev: true

  /unique-slug@3.0.0:
    resolution:
      {
        integrity: sha512-8EyMynh679x/0gqE9fT9oilG+qEt+ibFyqjuVTsZn1+CMxH+XLlpvr2UZx4nVcCwTpx81nICr2JQFkM+HPLq4w==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }
    dependencies:
      imurmurhash: 0.1.4
    dev: true

  /universalify@0.2.0:
    resolution:
      {
        integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==,
      }
    engines: { node: ">= 4.0.0" }
    dev: true

  /url-parse@1.5.10:
    resolution:
      {
        integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==,
      }
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0
    dev: true

  /util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
      }
    dev: true

  /uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==,
      }
    hasBin: true
    dev: false

  /validate.io-array@1.0.6:
    resolution:
      {
        integrity: sha512-DeOy7CnPEziggrOO5CZhVKJw6S3Yi7e9e65R1Nl/RTN1vTQKnzjfvks0/8kQ40FP/dsjRAOd4hxmJ7uLa6vxkg==,
      }
    dev: false

  /validate.io-function@1.0.2:
    resolution:
      {
        integrity: sha512-LlFybRJEriSuBnUhQyG5bwglhh50EpTL2ul23MPIuR1odjO7XaMLFV8vHGwp7AZciFxtYOeiSCT5st+XSPONiQ==,
      }
    dev: false

  /vite-node@2.1.9(@types/node@20.17.24):
    resolution:
      {
        integrity: sha512-AM9aQ/IPrW/6ENLQg3AGY4K1N2TGZdR5e4gu/MmmR2xR3Ll1+dib+nook92g4TV3PXVyeyxdWwtaCAiUL0hMxA==,
      }
    engines: { node: ^18.0.0 || >=20.0.0 }
    hasBin: true
    dependencies:
      cac: 6.7.14
      debug: 4.4.0
      es-module-lexer: 1.7.0
      pathe: 1.1.2
      vite: 5.4.19(@types/node@20.17.24)
    transitivePeerDependencies:
      - "@types/node"
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
    dev: true

  /vite@5.4.19(@types/node@20.17.24):
    resolution:
      {
        integrity: sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==,
      }
    engines: { node: ^18.0.0 || >=20.0.0 }
    hasBin: true
    peerDependencies:
      "@types/node": ^18.0.0 || >=20.0.0
      less: "*"
      lightningcss: ^1.21.0
      sass: "*"
      sass-embedded: "*"
      stylus: "*"
      sugarss: "*"
      terser: ^5.4.0
    peerDependenciesMeta:
      "@types/node":
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      "@types/node": 20.17.24
      esbuild: 0.21.5
      postcss: 8.5.3
      rollup: 4.36.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /vitest@2.1.9(@types/node@20.17.24)(msw@2.7.3):
    resolution:
      {
        integrity: sha512-MSmPM9REYqDGBI8439mA4mWhV5sKmDlBKWIYbA3lRb2PTHACE0mgKwA8yQ2xq9vxDTuk4iPrECBAEW2aoFXY0Q==,
      }
    engines: { node: ^18.0.0 || >=20.0.0 }
    hasBin: true
    peerDependencies:
      "@edge-runtime/vm": "*"
      "@types/node": ^18.0.0 || >=20.0.0
      "@vitest/browser": 2.1.9
      "@vitest/ui": 2.1.9
      happy-dom: "*"
      jsdom: "*"
    peerDependenciesMeta:
      "@edge-runtime/vm":
        optional: true
      "@types/node":
        optional: true
      "@vitest/browser":
        optional: true
      "@vitest/ui":
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true
    dependencies:
      "@types/node": 20.17.24
      "@vitest/expect": 2.1.9
      "@vitest/mocker": 2.1.9(msw@2.7.3)(vite@5.4.19)
      "@vitest/pretty-format": 2.1.9
      "@vitest/runner": 2.1.9
      "@vitest/snapshot": 2.1.9
      "@vitest/spy": 2.1.9
      "@vitest/utils": 2.1.9
      chai: 5.2.0
      debug: 4.4.0
      expect-type: 1.2.1
      magic-string: 0.30.17
      pathe: 1.1.2
      std-env: 3.9.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinypool: 1.0.2
      tinyrainbow: 1.2.0
      vite: 5.4.19(@types/node@20.17.24)
      vite-node: 2.1.9(@types/node@20.17.24)
      why-is-node-running: 2.3.0
    transitivePeerDependencies:
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
    dev: true

  /vscode-oniguruma@1.7.0:
    resolution:
      {
        integrity: sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==,
      }
    dev: true

  /vscode-textmate@8.0.0:
    resolution:
      {
        integrity: sha512-AFbieoL7a5LMqcnOF04ji+rpXadgOXnZsxQr//r83kLPr7biP7am3g9zbaZIaBGwBRWeSvoMD4mgPdX3e4NWBg==,
      }
    dev: true

  /web-streams-polyfill@3.3.3:
    resolution:
      {
        integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==,
      }
    engines: { node: ">= 8" }
    dev: false

  /web-streams-polyfill@4.0.0-beta.3:
    resolution:
      {
        integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==,
      }
    engines: { node: ">= 14" }
    dev: false

  /webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==,
      }

  /webidl-conversions@4.0.2:
    resolution:
      {
        integrity: sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==,
      }
    dev: true

  /whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==,
      }
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  /whatwg-url@7.1.0:
    resolution:
      {
        integrity: sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==,
      }
    dependencies:
      lodash.sortby: 4.7.0
      tr46: 1.0.1
      webidl-conversions: 4.0.2
    dev: true

  /which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
      }
    engines: { node: ">= 8" }
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /why-is-node-running@2.3.0:
    resolution:
      {
        integrity: sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==,
      }
    engines: { node: ">=8" }
    hasBin: true
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2
    dev: true

  /wide-align@1.1.5:
    resolution:
      {
        integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==,
      }
    dependencies:
      string-width: 4.2.3
    dev: true

  /wordwrap@1.0.0:
    resolution:
      {
        integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==,
      }
    dev: true

  /wrap-ansi@6.2.0:
    resolution:
      {
        integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==,
      }
    engines: { node: ">=8" }
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
      }
    engines: { node: ">=10" }
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
      }
    engines: { node: ">=12" }
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
      }
    dev: true

  /y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
      }
    engines: { node: ">=10" }
    dev: true

  /yallist@4.0.0:
    resolution:
      {
        integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==,
      }
    dev: true

  /yallist@5.0.0:
    resolution:
      {
        integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==,
      }
    engines: { node: ">=18" }
    dev: true

  /yaml@2.7.0:
    resolution:
      {
        integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==,
      }
    engines: { node: ">= 14" }
    hasBin: true
    dev: false

  /yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
      }
    engines: { node: ">=12" }
    dev: true

  /yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
      }
    engines: { node: ">=12" }
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1
    dev: true

  /yoctocolors-cjs@2.1.2:
    resolution:
      {
        integrity: sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==,
      }
    engines: { node: ">=18" }
    dev: true

  /zod-to-json-schema@3.24.4(zod@3.24.2):
    resolution:
      {
        integrity: sha512-0uNlcvgabyrni9Ag8Vghj21drk7+7tp7VTwwR7KxxXXc/3pbXz2PHlDgj3cICahgF1kHm4dExBFj7BXrZJXzig==,
      }
    peerDependencies:
      zod: ^3.24.1
    dependencies:
      zod: 3.24.2
    dev: false

  /zod@3.24.2:
    resolution:
      {
        integrity: sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==,
      }
    dev: false
