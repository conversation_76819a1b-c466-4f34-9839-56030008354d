{"name": "autoevals", "version": "0.0.130", "description": "Universal library for evaluating AI models", "repository": {"type": "git", "url": "git+https://github.com/braintrustdata/autoevals.git"}, "homepage": "https://www.braintrust.dev/docs", "main": "./jsdist/index.js", "module": "./jsdist/index.mjs", "types": "./jsdist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./jsdist/index.d.ts", "import": "./jsdist/index.mjs", "module": "./jsdist/index.mjs", "require": "./jsdist/index.js"}}, "files": ["jsdist/**/*"], "scripts": {"build": "tsup", "watch": "tsup --watch", "docs": "npx typedoc --options typedoc.json js/index.ts", "test": "vitest", "prepublishOnly": "../scripts/node_prepublish_autoevals.py", "postpublish": "../scripts/node_postpublish_autoevals.py"}, "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-yaml": "^4.1.2", "@types/js-levenshtein": "^1.1.3", "@types/js-yaml": "^4.0.9", "@types/mustache": "^4.2.5", "@types/node": "^20.10.5", "msw": "^2.7.3", "tsup": "^8.4.0", "tsx": "^3.14.0", "typedoc": "^0.25.4", "typedoc-plugin-markdown": "^3.17.1", "typescript": "^5.3.3", "vitest": "^2.1.9"}, "dependencies": {"ajv": "^8.13.0", "compute-cosine-similarity": "^1.1.0", "js-levenshtein": "^1.1.6", "js-yaml": "^4.1.0", "linear-sum-assignment": "^1.0.7", "mustache": "^4.2.0", "openai": "^4.47.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}, "packageManager": "pnpm@8.15.5"}