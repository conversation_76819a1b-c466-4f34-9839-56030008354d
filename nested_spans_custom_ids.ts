#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to create a log with deeply nested spans using custom span IDs for all spans.
 * 
 * This demonstrates multi-level hierarchical logging with custom span IDs:
 * - Level 1: Main task span (custom root span ID)
 * - Level 2: Major sub-tasks (custom span IDs)
 * - Level 3: Detailed steps within sub-tasks (custom span IDs)
 * - Level 4: Individual operations within steps (custom span IDs)
 * 
 * Usage:
 *     npx tsx nested_spans_custom_ids.ts
 * 
 * Make sure to set your BRAINTRUST_API_KEY environment variable.
 */

import { initLogger, Span } from "braintrust";

async function main() {
  // Initialize logger for a project
  const logger = initLogger({ projectName: "pedro-repro4667" });
  
  // Level 1: Main task span with custom root span ID
  const mainSpan = logger.startSpan({
    name: "document_analysis_pipeline",
    spanId: "root-doc-analysis-2024-01-15", // This becomes both spanId and rootSpanId for root spans
    event: {
      input: "Analyze the quarterly financial report document",
      metadata: {
        document_id: "doc_q4_2023_financial",
        document_type: "financial_report",
        pages: 45,
        custom_span_id: "root-doc-analysis-2024-01-15"
      }
    }
  });
  
  console.log(`Created main span with custom ID: ${mainSpan.spanId}`);
  console.log(`Root span ID: ${mainSpan.rootSpanId}`);
  
  try {
    // Level 2: Document preprocessing
    const preprocessSpan = mainSpan.startSpan({
      name: "document_preprocessing",
      spanId: "preprocess-stage-001",
      event: {
        input: "Raw PDF document with 45 pages",
        metadata: { 
          stage: "preprocessing", 
          custom_span_id: "preprocess-stage-001" 
        }
      }
    });
    
    try {
      // Level 3: Text extraction from preprocessing
      const extractSpan = preprocessSpan.startSpan({
        name: "text_extraction",
        spanId: "extract-text-ocr-001",
        event: {
          input: "PDF file with mixed content",
          metadata: { 
            extraction_method: "OCR + text_layer", 
            custom_span_id: "extract-text-ocr-001" 
          }
        }
      });
      
      try {
        // Level 4: Individual page processing
        const page1Span = extractSpan.startSpan({
          name: "page_1_extraction",
          spanId: "page-001-exec-summary",
          event: {
            input: "Page 1: Executive Summary",
            output: "Extracted 1,247 characters",
            scores: { text_quality: 0.98, confidence: 0.95 },
            metadata: { custom_span_id: "page-001-exec-summary" }
          }
        });
        page1Span.end();
        
        const page2Span = extractSpan.startSpan({
          name: "page_2_extraction",
          spanId: "page-002-financial-overview",
          event: {
            input: "Page 2: Financial Overview",
            output: "Extracted 1,456 characters",
            scores: { text_quality: 0.94, confidence: 0.92 },
            metadata: { custom_span_id: "page-002-financial-overview" }
          }
        });
        page2Span.end();
        
        // Log extraction summary
        extractSpan.log({
          output: "Successfully extracted text from 45 pages",
          scores: { overall_quality: 0.96, pages_processed: 0.45 }
        });
      } finally {
        extractSpan.end();
      }
      
      // Level 3: Text cleaning from preprocessing
      const cleanSpan = preprocessSpan.startSpan({
        name: "text_cleaning",
        spanId: "clean-normalize-001",
        event: {
          input: "Raw extracted text with formatting artifacts",
          metadata: {
            cleaning_steps: ["remove_headers", "fix_spacing", "normalize_encoding"],
            custom_span_id: "clean-normalize-001"
          }
        }
      });
      
      try {
        // Level 4: Individual cleaning steps
        const headersSpan = cleanSpan.startSpan({
          name: "remove_headers_footers",
          spanId: "remove-headers-footers-001",
          event: {
            input: "Text with page headers and footers",
            output: "Clean text without headers/footers",
            scores: { artifacts_removed: 0.99 },
            metadata: { custom_span_id: "remove-headers-footers-001" }
          }
        });
        headersSpan.end();
        
        const whitespaceSpan = cleanSpan.startSpan({
          name: "normalize_whitespace",
          spanId: "normalize-whitespace-001",
          event: {
            input: "Text with irregular spacing",
            output: "Text with normalized spacing",
            scores: { formatting_quality: 0.97 },
            metadata: { custom_span_id: "normalize-whitespace-001" }
          }
        });
        whitespaceSpan.end();
        
        cleanSpan.log({
          output: "Cleaned and normalized text ready for analysis",
          scores: { cleaning_quality: 0.95 }
        });
      } finally {
        cleanSpan.end();
      }
      
      preprocessSpan.log({
        output: "Document preprocessing completed",
        scores: { preprocessing_success: 1.0 }
      });
    } finally {
      preprocessSpan.end();
    }
    
    // Level 2: Content analysis
    const analysisSpan = mainSpan.startSpan({
      name: "content_analysis",
      spanId: "analysis-financial-metrics-001",
      event: {
        input: "Cleaned document text",
        metadata: { 
          analysis_type: "financial_metrics_extraction", 
          custom_span_id: "analysis-financial-metrics-001" 
        }
      }
    });
    
    try {
      // Level 3: Financial metrics extraction
      const metricsSpan = analysisSpan.startSpan({
        name: "financial_metrics_extraction",
        spanId: "extract-metrics-revenue-profit-001",
        event: {
          input: "Financial report text",
          metadata: {
            target_metrics: ["revenue", "profit", "expenses", "growth_rate"],
            custom_span_id: "extract-metrics-revenue-profit-001"
          }
        }
      });
      
      try {
        // Level 4: Individual metric extraction
        const revenueSpan = metricsSpan.startSpan({
          name: "revenue_extraction",
          spanId: "revenue-q4-yoy-growth-001",
          event: {
            input: "Text sections mentioning revenue",
            output: { q4_revenue: "$2.4M", yoy_growth: "15%" },
            scores: { extraction_confidence: 0.94 },
            metadata: { custom_span_id: "revenue-q4-yoy-growth-001" }
          }
        });
        revenueSpan.end();
        
        const profitSpan = metricsSpan.startSpan({
          name: "profit_extraction",
          spanId: "profit-margin-net-income-001",
          event: {
            input: "Text sections mentioning profit/loss",
            output: { net_profit: "$340K", profit_margin: "14.2%" },
            scores: { extraction_confidence: 0.91 },
            metadata: { custom_span_id: "profit-margin-net-income-001" }
          }
        });
        profitSpan.end();
        
        metricsSpan.log({
          output: "Successfully extracted key financial metrics",
          scores: { metrics_completeness: 0.88 }
        });
      } finally {
        metricsSpan.end();
      }
      
      // Level 3: Sentiment analysis
      const sentimentSpan = analysisSpan.startSpan({
        name: "sentiment_analysis",
        spanId: "sentiment-exec-outlook-risks-001",
        event: {
          input: "Financial report narrative sections",
          metadata: {
            analysis_sections: ["executive_summary", "outlook", "risks"],
            custom_span_id: "sentiment-exec-outlook-risks-001"
          }
        }
      });
      
      try {
        // Level 4: Section-specific sentiment
        const execSentimentSpan = sentimentSpan.startSpan({
          name: "executive_summary_sentiment",
          spanId: "exec-summary-positive-sentiment-001",
          event: {
            input: "Executive summary text",
            output: { sentiment: "positive", confidence: 0.82 },
            scores: { positivity: 0.7 },
            metadata: { custom_span_id: "exec-summary-positive-sentiment-001" }
          }
        });
        execSentimentSpan.end();
        
        const outlookSentimentSpan = sentimentSpan.startSpan({
          name: "outlook_sentiment",
          spanId: "outlook-cautious-optimistic-001",
          event: {
            input: "Future outlook section",
            output: { sentiment: "cautiously_optimistic", confidence: 0.76 },
            scores: { positivity: 0.6 },
            metadata: { custom_span_id: "outlook-cautious-optimistic-001" }
          }
        });
        outlookSentimentSpan.end();
        
        sentimentSpan.log({
          output: "Overall document sentiment: positive with caution",
          scores: { overall_sentiment: 0.65 }
        });
      } finally {
        sentimentSpan.end();
      }
      
      analysisSpan.log({
        output: "Content analysis completed",
        scores: { analysis_quality: 0.89 }
      });
    } finally {
      analysisSpan.end();
    }
    
    // Level 2: Report generation
    const reportSpan = mainSpan.startSpan({
      name: "report_generation",
      spanId: "generate-executive-summary-001",
      event: {
        input: "Extracted metrics and sentiment analysis",
        metadata: {
          report_format: "executive_summary",
          custom_span_id: "generate-executive-summary-001"
        }
      }
    });

    try {
      // Level 3: Summary creation
      const summarySpan = reportSpan.startSpan({
        name: "summary_creation",
        spanId: "create-q4-summary-report-001",
        event: {
          input: "Financial metrics and sentiment data",
          output: "Q4 2023 showed strong performance with $2.4M revenue (15% YoY growth) and healthy 14.2% profit margin. Overall sentiment is positive with cautious optimism for future quarters.",
          scores: { summary_quality: 0.92, conciseness: 0.88 },
          metadata: { custom_span_id: "create-q4-summary-report-001" }
        }
      });
      summarySpan.end();

      reportSpan.log({
        output: "Executive summary report generated",
        scores: { report_completeness: 0.94 }
      });
    } finally {
      reportSpan.end();
    }

    console.log("Created all spans with custom IDs:");
    console.log("Preprocessing spans:");
    console.log("  - Preprocessing: preprocess-stage-001");
    console.log("  - Text extraction: extract-text-ocr-001");
    console.log("  - Page 1: page-001-exec-summary");
    console.log("  - Page 2: page-002-financial-overview");
    console.log("  - Text cleaning: clean-normalize-001");
    console.log("  - Headers removal: remove-headers-footers-001");
    console.log("  - Whitespace normalization: normalize-whitespace-001");
    console.log("Analysis spans:");
    console.log("  - Content analysis: analysis-financial-metrics-001");
    console.log("  - Metrics extraction: extract-metrics-revenue-profit-001");
    console.log("  - Revenue extraction: revenue-q4-yoy-growth-001");
    console.log("  - Profit extraction: profit-margin-net-income-001");
    console.log("  - Sentiment analysis: sentiment-exec-outlook-risks-001");
    console.log("  - Executive sentiment: exec-summary-positive-sentiment-001");
    console.log("  - Outlook sentiment: outlook-cautious-optimistic-001");
    console.log("Report generation spans:");
    console.log("  - Report generation: generate-executive-summary-001");
    console.log("  - Summary creation: create-q4-summary-report-001");

  } finally {
    // Log final result on main span
    mainSpan.log({
      output: "Document analysis pipeline completed successfully",
      expected: "Comprehensive financial document analysis with extracted metrics and insights",
      scores: {
        pipeline_success: 1.0,
        overall_quality: 0.91,
        processing_efficiency: 0.87
      },
      metadata: {
        total_spans_created: 15,
        max_nesting_depth: 4,
        pipeline_completed: true,
        all_spans_have_custom_ids: true
      },
      tags: ["document_analysis", "financial_report", "multi_level_processing", "custom_span_ids"]
    });
    mainSpan.end();
  }

  console.log("\nCreated deeply nested trace structure with custom span IDs:");
  console.log("Level 1: document_analysis_pipeline (root-doc-analysis-2024-01-15)");
  console.log("├── Level 2: document_preprocessing (preprocess-stage-001)");
  console.log("│   ├── Level 3: text_extraction (extract-text-ocr-001)");
  console.log("│   │   ├── Level 4: page_1_extraction (page-001-exec-summary)");
  console.log("│   │   └── Level 4: page_2_extraction (page-002-financial-overview)");
  console.log("│   └── Level 3: text_cleaning (clean-normalize-001)");
  console.log("│       ├── Level 4: remove_headers_footers (remove-headers-footers-001)");
  console.log("│       └── Level 4: normalize_whitespace (normalize-whitespace-001)");
  console.log("├── Level 2: content_analysis (analysis-financial-metrics-001)");
  console.log("│   ├── Level 3: financial_metrics_extraction (extract-metrics-revenue-profit-001)");
  console.log("│   │   ├── Level 4: revenue_extraction (revenue-q4-yoy-growth-001)");
  console.log("│   │   └── Level 4: profit_extraction (profit-margin-net-income-001)");
  console.log("│   └── Level 3: sentiment_analysis (sentiment-exec-outlook-risks-001)");
  console.log("│       ├── Level 4: executive_summary_sentiment (exec-summary-positive-sentiment-001)");
  console.log("│       └── Level 4: outlook_sentiment (outlook-cautious-optimistic-001)");
  console.log("└── Level 2: report_generation (generate-executive-summary-001)");
  console.log("    └── Level 3: summary_creation (create-q4-summary-report-001)");

  // Flush to ensure all logs are sent to the server
  await logger.flush();
  console.log("\nAll nested spans with custom IDs have been sent to Braintrust server");
  console.log("Check your Braintrust dashboard to explore the hierarchical trace structure");
  console.log("All spans will show their custom span IDs in the trace view");
}

// Run the main function and handle any errors
main().catch((error) => {
  console.error("Error running script:", error);
  process.exit(1);
});
