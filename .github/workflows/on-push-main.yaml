name: Push to Main
on:
  push:
    branches:
      - main

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: read

jobs:
  check-for-release:
    runs-on: ubuntu-latest
    outputs:
      version_changed: ${{ steps.check.outputs.version_changed }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: ".github"
      - name: Check for version change
        uses: ./.github/actions/check-version-change
        id: check

  ci:
    needs: [check-for-release]
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      # Don't publish if the VERSION file was modified. The release job will do that.
      publish_git_sha: ${{ needs.check-for-release.outputs.version_changed == 'true' && '' || github.sha }}
      release_as_latest: false
      run_serial: false
      skip_tests: false
      debug_enabled: false
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
