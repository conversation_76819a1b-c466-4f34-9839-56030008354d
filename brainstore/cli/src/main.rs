use std::borrow::Cow;

use base::{BaseArgs, CLIArgs};
use clap::{Parser, Subcommand};
use command_proc::{CommandProc, WorkerCommandArgs};
use otel_common::opentelemetry::trace::TracerProvider;
use serde::{Deserialize, Serialize};
use util::{anyhow::Result, futures::pin_mut};

pub mod base;
pub mod bench;
pub mod btql;
mod cleanup;
pub mod command_proc;
pub mod compaction_worker;
pub mod executor_pool;
pub mod healthcheck;
pub mod index;
pub mod metrics;
pub mod pg_pool;
pub mod ping;
pub mod retention;
pub mod utilities;
pub mod vacuum;
pub mod wal;
pub mod web;

#[cfg(feature = "distribute")]
mod license_check;

#[cfg(test)]
pub mod stress;

use agent::{
    memprof,
    setup_tracing::{
        make_env_filter, make_stderr_layer, setup_log_tracer, setup_otel_provider,
        OpenTelemetryTracingBridge, SetupOtelProviderArgs,
    },
    tracing_opentelemetry,
    tracing_subscriber::{layer::SubscriberExt, Registry},
};

#[cfg(not(target_env = "msvc"))]
use tikv_jemallocator::Jemalloc;

#[cfg(not(target_env = "msvc"))]
#[global_allocator]
static GLOBAL: Jemalloc = Jemalloc;

#[cfg(feature = "enable_memprof")]
#[allow(non_upper_case_globals)]
#[export_name = "malloc_conf"]
pub static malloc_conf: &[u8] = b"prof:true,prof_active:true,lg_prof_sample:19\0";

// Include the generated git commit information
include!(concat!(env!("OUT_DIR"), "/git_commit.rs"));

#[derive(Debug, Parser)]
#[command(author, version, about)]
struct Args {
    #[command(subcommand)]
    cmd: Commands,
}

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum Commands {
    Ping(ping::PingCommand),
    Cleanup(CLIArgs<cleanup::CleanupCommand>),
    #[command(subcommand)]
    Btql(crate::btql::Commands),
    #[command(subcommand)]
    Wal(wal::WalCommands),
    #[command(subcommand)]
    Index(index::IndexCommands),
    Web(web::WebCommand),
    Worker(command_proc::WorkerCommand),
    Xact(utilities::XactCommand),
    #[command(subcommand)]
    Retention(retention::RetentionCommands),
    #[command(subcommand)]
    Vacuum(vacuum::VacuumCommands),
    #[cfg(feature = "bench")]
    #[command(subcommand)]
    Bench(bench::BenchCommands),
}

#[allow(unused_variables)]
fn is_bench_command(cmd: &Commands) -> bool {
    #[cfg(feature = "bench")]
    {
        matches!(cmd, Commands::Bench(_))
    }
    #[cfg(not(feature = "bench"))]
    {
        false
    }
}

fn main() -> Result<()> {
    // Install the rustls crypto provider early to avoid conflicts
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");

    let args = Args::parse();

    let ret = main_inner(args.cmd)?;
    if !matches!(ret, util::Value::Null) {
        println!("{}", ret);
    }
    Ok(())
}

pub fn main_inner(cmd: Commands) -> Result<util::Value> {
    let worker_args = match &cmd {
        Commands::Worker(a) => {
            let args: WorkerCommandArgs = serde_json::from_str(&a.arg)?;
            Some(args)
        }
        _ => None,
    };

    let base_args = match &cmd {
        Commands::Ping(_) => Cow::Owned(BaseArgs::default()),
        Commands::Cleanup(a) => Cow::Borrowed(&a.base),
        Commands::Wal(a) => Cow::Borrowed(wal::base_args(a)),
        Commands::Index(a) => Cow::Borrowed(index::base_args(a)),
        Commands::Btql(a) => Cow::Borrowed(btql::base_args(a)),
        Commands::Web(a) => Cow::Borrowed(&a.base),
        Commands::Worker(_) => Cow::Owned(worker_args.as_ref().unwrap().base.clone()),
        Commands::Xact(a) => Cow::Borrowed(&a.base),
        Commands::Retention(a) => Cow::Borrowed(retention::base_args(a)),
        Commands::Vacuum(a) => Cow::Borrowed(vacuum::base_args(a)),
        #[cfg(feature = "bench")]
        Commands::Bench(a) => Cow::Borrowed(bench::base_args(a)),
    };

    #[cfg(feature = "distribute")]
    license_check::check_license_with_backoff(&base_args.app_url, base_args.license_key.as_ref())?;

    let (app_url, license_key) = (
        Some(base_args.app_url.clone()),
        base_args.license_key.clone(),
    );

    let verbose = base_args.verbose;
    util::global_opts::SUPPRESS_VERBOSE_INFO.store(
        verbose == 1 && base_args.suppress_verbose_info,
        std::sync::atomic::Ordering::Relaxed,
    );

    // This should be done _after_ logging is set up.
    let command_proc = match &cmd {
        Commands::Web(a) => Some(CommandProc::new(
            base_args.clone().into_owned(),
            a.command_proc_lifetime_executions,
            None,
        )?),
        _ => None,
    };

    let pretty_logging = match (base_args.pretty_logging, &cmd) {
        (Some(pretty_logging), _) => pretty_logging,
        (None, Commands::Web(_)) => false,
        (None, _) => true, /* non-web commands default to pretty logging */
    };

    setup_log_tracer(verbose)?;
    let env_filter = make_env_filter(verbose);
    // Only do JSON-logging if this is web
    let stderr_layer = make_stderr_layer(pretty_logging, verbose, base_args.suppress_verbose_info);

    let telemetry_args = base_args.telemetry_args.clone();

    let runtime = storage::merge::make_runtime()?;

    // Get additional OTEL attributes based on command type
    let additional_attributes = match &cmd {
        Commands::Web(web_cmd) => web_cmd.additional_otel_attributes(),
        _ => vec![],
    };

    #[allow(unused)]
    let (memprof_flusher, memprof_flush_handle) = memprof::make_flusher();
    #[cfg(feature = "enable_memprof")]
    if (matches!(
        cmd,
        Commands::Wal(_)
            | Commands::Index(_)
            | Commands::Btql(_)
            | Commands::Web(_)
            | Commands::Worker(_)
    ) || is_bench_command(&cmd))
        && base_args.telemetry_args.memory_profiling_enabled()
    {
        memprof::run_memprof(
            base_args
                .telemetry_args
                .make_memprof_input(SetupOtelProviderArgs {
                    app_url: app_url.as_ref().map(|s| s.as_str()),
                    license_key: license_key.as_ref().map(|s| s.as_str()),
                    git_commit: Some(GIT_COMMIT),
                    additional_attributes: additional_attributes.clone(),
                }),
            memprof_flusher,
        );
    }

    let cmd_fut = async move {
        // Create a new OpenTelemetry trace pipeline that exports to OTLP endpoint
        let (tracing_provider, _metrics_provider, logs_provider) = setup_otel_provider(
            &telemetry_args,
            SetupOtelProviderArgs {
                app_url: app_url.as_ref().map(|s| s.as_str()),
                license_key: license_key.as_ref().map(|s| s.as_str()),
                git_commit: Some(GIT_COMMIT),
                additional_attributes,
            },
        )?;

        let otel_tracer = tracing_provider.tracer("brainstore");
        let otel_layer = tracing_opentelemetry::layer().with_tracer(otel_tracer);

        let otel_logs_layer = OpenTelemetryTracingBridge::new(&logs_provider);

        let subscriber = Registry::default()
            .with(env_filter)
            .with(otel_layer)
            .with(otel_logs_layer)
            .with(stderr_layer);

        tracing::subscriber::set_global_default(subscriber)?;

        let result = match cmd {
            Commands::Ping(p) => ping::main(p).await,
            Commands::Cleanup(cleanup) => cleanup::main(cleanup).await,
            Commands::Wal(wal) => wal::main(wal).await,
            Commands::Index(index) => index::main(index).await,
            Commands::Btql(btql) => btql::main(btql).await,
            Commands::Web(web) => web::main(web, command_proc.unwrap()).await,
            Commands::Worker(worker) => command_proc::worker(worker).await,
            Commands::Xact(xact) => utilities::main(xact).await,
            Commands::Retention(retention) => retention::main(retention).await,
            Commands::Vacuum(vacuum) => vacuum::main(vacuum).await,
            #[cfg(feature = "bench")]
            Commands::Bench(bench) => bench::main(bench, memprof_flush_handle).await,
        }?;

        // Let OTEL flush
        if let Err(e) = tracing_provider.force_flush() {
            eprintln!("Error flushing tracing provider: {}", e);
        }
        if let Err(e) = tracing_provider.shutdown() {
            eprintln!("Error shutting down tracing provider: {}", e);
        }

        Ok::<util::Value, util::anyhow::Error>(result)
    };

    let result = runtime.block_on(async move {
        pin_mut!(cmd_fut);
        cmd_fut.await
    })?;

    {
        let max_wal_in_flight_bytes = storage::wal_stats::MAX_WAL_IN_FLIGHT_BYTES.max();
        if max_wal_in_flight_bytes > 0 {
            tracing::debug!("MAX_WAL_IN_FLIGHT_BYTES: {}", max_wal_in_flight_bytes);
        }
    }
    {
        let max_wal_batch_size =
            storage::wal_stats::MAX_WAL_BATCH_SIZE.load(std::sync::atomic::Ordering::Relaxed);
        if max_wal_batch_size > 0 {
            tracing::debug!("MAX_WAL_BATCH_SIZE: {}", max_wal_batch_size);
        }
    }
    {
        let total_wal_read_bytes =
            storage::wal_stats::TOTAL_WAL_READ_BYTES.load(std::sync::atomic::Ordering::Relaxed);
        if total_wal_read_bytes > 0 {
            tracing::debug!("TOTAL_WAL_READ_BYTES: {}", total_wal_read_bytes);
        }
    }

    Ok(result)
}
