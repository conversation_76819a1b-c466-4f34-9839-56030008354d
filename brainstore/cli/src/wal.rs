use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{
    collections::{HashMap, HashSet},
    fmt,
    sync::Arc,
};

use crate::{
    base::{AppState, BaseArgs, CLIArgs, SegmentIdArgs},
    index::{MergeIndexFullArgs, MergeIndexInputArgs},
};
use storage::{
    global_store::GlobalStore,
    index_document::{make_full_schema, IndexDocument},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    postgres_wal::{PostgresWAL, PostgresWalStreamBounded, PostgresWalStreamOpts},
    process_wal::{
        CompactSegmentWalInput, CompactSegmentWalOptionalInput, CompactSegmentWalOptions,
        ProcessObjectWalInput, ProcessObjectWalOptionalInput, ProcessObjectWalOptions,
    },
    wal::{
        fill_default_object_id, wal_insert_normalized, wal_insert_unnormalized, wal_stream,
        WALScope, Wal, WalMetadataStreamOptionalInput,
    },
    wal_entry::WalEntry,
    xact_manager::TransactionManager,
};
use tracing::{instrument, Instrument};
use util::{
    anyhow::Result,
    futures::{future::join_all, stream::BoxStream, StreamExt, TryStreamExt},
    system_types::{make_object_schema, FullObjectId, FullObjectIdOwned, FullRowIdOwned},
    uuid::Uuid,
    xact::TransactionId,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum WalCommands {
    Insert(CLIArgs<InsertWalArgs>),
    Process(CLIArgs<ProcessWalFullArgs>),
    Compact(CLIArgs<CompactWalFullArgs>),
    Load(CLIArgs<LoadWalArgs>),
    Cat(CLIArgs<CatWalArgs>),
    Status(CLIArgs<StatusArgs>),
}

pub async fn main(args: WalCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        WalCommands::Insert(args) => {
            let num_values = insert_wal(app_state, args.args).await?;
            Ok(util::Value::Number(num_values.into()))
        }
        WalCommands::Process(args) => {
            let segment_ids = process_wal(app_state, args.args).await?;
            Ok(json!(segment_ids))
        }
        WalCommands::Compact(args) => {
            compact_wal(app_state, args.args).await?;
            Ok(util::Value::Null)
        }
        WalCommands::Load(args) => {
            load_wal(app_state, args.args).await?;
            Ok(util::Value::Null)
        }
        WalCommands::Cat(args) => {
            cat_wal(app_state, args.args).await?;
            Ok(util::Value::Null)
        }
        WalCommands::Status(args) => status(app_state, args.args).await,
    }
}

pub fn base_args(args: &WalCommands) -> &BaseArgs {
    match args {
        WalCommands::Insert(a) => &a.base,
        WalCommands::Process(a) => &a.base,
        WalCommands::Compact(a) => &a.base,
        WalCommands::Load(a) => &a.base,
        WalCommands::Cat(a) => &a.base,
        WalCommands::Status(a) => &a.base,
    }
}

pub fn verbose(args: &WalCommands) -> u8 {
    match args {
        WalCommands::Insert(a) => a.base.verbose,
        WalCommands::Process(a) => a.base.verbose,
        WalCommands::Compact(a) => a.base.verbose,
        WalCommands::Load(a) => a.base.verbose,
        WalCommands::Cat(a) => a.base.verbose,
        WalCommands::Status(a) => a.base.verbose,
    }
}

pub type InsertWalCLIArgs = CLIArgs<InsertWalArgs>;

#[derive(Parser, Clone, Serialize, Deserialize)]
pub struct InsertWalArgs {
    #[arg(
        required = true,
        num_args = 1..,
        help = "The path to the files to load",
        env = "BRAINSTORE_WAL_INSERT_PATHS"
    )]
    pub path: Vec<String>,

    #[arg(
        long,
        help = "A JSON list of additional entries to add to the WAL, after the paths",
        env = "BRAINSTORE_WAL_INSERT_ADDITIONAL_ENTRIES"
    )]
    #[serde(default)]
    pub additional_entries: Option<String>,

    #[arg(
        long,
        help = "The batch size to use when inserting values. If 0, all values are inserted in a single batch.",
        default_value_t = default_batch_size(),
        env = "BRAINSTORE_WAL_INSERT_BATCH_SIZE"
    )]
    #[serde(default = "default_batch_size")]
    pub batch_size: usize,

    #[arg(
        long,
        help = "Whether to normalize the WAL entries",
        env = "BRAINSTORE_WAL_INSERT_NORMALIZE"
    )]
    #[serde(default)]
    pub normalize: bool,

    #[arg(
        long,
        help = "The default object id to use (if normalizing)",
        env = "BRAINSTORE_WAL_INSERT_DEFAULT_OBJECT_ID",
        default_value_t = FullObjectIdOwned::default()
    )]
    #[serde(default)]
    pub default_object_id: FullObjectIdOwned,

    #[arg(
        long,
        help = "Whether to sort the WAL entries by _xact_id",
        env = "BRAINSTORE_WAL_INSERT_SORT"
    )]
    #[serde(default)]
    pub sort: bool,
}

impl fmt::Debug for InsertWalArgs {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let additional_entries_str = self
            .additional_entries
            .as_ref()
            .map(|s| format!("<{} bytes>", s.len()));
        f.debug_struct("InsertWalArgs")
            .field("path", &self.path)
            .field("additional_entries", &additional_entries_str)
            .field("batch_size", &self.batch_size)
            .field("normalize", &self.normalize)
            .field("sort", &self.sort)
            .finish()
    }
}

fn default_batch_size() -> usize {
    1000
}

#[instrument(err, skip(wal, global_store, xact_manager, batch), fields(num_values=batch.len()))]
async fn insert_wal_batch(
    wal: Arc<dyn Wal>,
    global_store: Arc<dyn GlobalStore>,
    xact_manager: Arc<dyn TransactionManager>,
    batch: Vec<util::Value>,
    normalize: bool,
    default_object_id: FullObjectId<'_>,
) -> Result<usize> {
    let xact_manager = xact_manager.clone();

    let num_values = if normalize {
        let values = batch
            .into_iter()
            .map(|value| fill_default_object_id(value, default_object_id))
            .collect::<Vec<_>>();

        let num_values = values.len();

        wal_insert_unnormalized(wal.as_ref(), &*global_store, values, xact_manager.as_ref())
            .await?;
        num_values
    } else {
        let num_values = batch.len();
        wal_insert_normalized(wal.as_ref(), &*global_store, batch).await?;
        num_values
    };
    tracing::info!("Inserted {} values into WAL", num_values);

    Ok(num_values)
}

#[instrument(err, skip(stream))]
async fn sort_stream<'a>(
    stream: BoxStream<'a, Result<util::Value>>,
) -> Result<BoxStream<'a, Result<util::Value>>> {
    let mut all_values = stream.try_collect::<Vec<_>>().await?;
    tracing::info!(num_values = all_values.len(), "sorting values");

    all_values.sort_by_key(|value| {
        value
            .get("_xact_id")
            .map(|x| x.as_str().unwrap().to_owned())
    });

    Ok(async_stream::stream! {
        for value in all_values {
            yield Ok(value);
        }
    }
    .instrument(tracing::info_span!("sorted stream"))
    .boxed())
}

#[instrument(err, skip(app_state))]
pub async fn insert_wal(app_state: Arc<AppState>, args: InsertWalArgs) -> Result<usize> {
    let paths = args.path.clone();
    let additional_entries: Vec<util::Value> = args
        .additional_entries
        .as_ref()
        .map(|s| serde_json::from_str(s))
        .transpose()?
        .unwrap_or_default();
    let mut stream = storage::file_stream::open(&paths, 1000, additional_entries).await?;
    if args.sort {
        stream = sort_stream(stream).await?;
    }
    let wal = match app_state.config.realtime_wal.as_ref() {
        Some(wal) => wal.clone(),
        None => {
            util::anyhow::bail!("realtime_wal is not configured");
        }
    };
    let object_id = args.default_object_id.as_ref();
    let num_values = match args.batch_size {
        0 => {
            let rows = stream
                .try_collect::<Vec<_>>()
                .instrument(tracing::info_span!("collect full stream"))
                .await?;
            insert_wal_batch(
                wal.clone(),
                app_state.config.global_store.clone(),
                app_state.config.xact_manager.clone(),
                rows,
                args.normalize,
                object_id,
            )
            .await?
        }
        batch_size => {
            let mut batched_stream = util::futures::StreamExt::chunks(stream, batch_size)
                .instrument(tracing::info_span!("batched stream"));

            let mut insert_futures = Vec::new();
            while let Some(batch) = batched_stream.next().await {
                let batch = batch.into_iter().collect::<Result<Vec<_>>>()?;
                let wal = wal.clone();
                let global_store = app_state.config.global_store.clone();
                let xact_manager = app_state.config.xact_manager.clone();
                insert_futures.push(async move {
                    insert_wal_batch(
                        wal,
                        global_store,
                        xact_manager,
                        batch,
                        args.normalize,
                        object_id,
                    )
                    .await
                });
            }

            join_all(insert_futures)
                .instrument(tracing::info_span!("join all insert futures"))
                .await
                .into_iter()
                .collect::<Result<Vec<_>>>()?
                .into_iter()
                .sum::<usize>()
        }
    };

    Ok(num_values)
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct ProcessWalFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub process_opts: ProcessObjectWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub process_input: ProcessWalInputArgs,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct ProcessWalInputArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub pg_wal_stream_opts: PostgresWalStreamOpts,

    #[arg(
        help = "The object IDs to process",
        env = "BRAINSTORE_WAL_PROCESS_OBJECT_IDS"
    )]
    pub object_ids: Vec<String>,

    #[arg(long = "process-start-xact-id", name = "process_start_xact_id")]
    pub start_xact_id: Option<TransactionId>,

    #[arg(long = "process-end-xact-id", name = "process_end_xact_id")]
    pub end_xact_id: Option<TransactionId>,

    #[arg(long = "process-mark-as-compacted", name = "process_mark_as_compacted")]
    pub mark_as_compacted: Option<bool>,

    #[arg(long = "process-source", name = "process_source")]
    pub source: Option<String>,
}

#[instrument(err, skip(app_state))]
pub async fn process_wal(
    app_state: Arc<AppState>,
    args: ProcessWalFullArgs,
) -> Result<HashSet<Uuid>> {
    let object_ids = if args.process_input.object_ids.is_empty() {
        vec![FullObjectId::default().to_owned()]
    } else {
        args.process_input
            .object_ids
            .iter()
            .map(|id| id.parse::<FullObjectIdOwned>())
            .collect::<Result<Vec<_>>>()?
    };

    tracing::info!(
        num_objects = object_ids.len(),
        "Processing WAL for objects..."
    );

    let outputs = join_all(object_ids.iter().map(|object_id| {
        let span = tracing::info_span!("process wal object", object_id = object_id.to_string());
        let pg_wal_stream_opts = args.process_input.pg_wal_stream_opts.clone();
        let mut config = app_state.config.clone();
        let process_args = args.process_opts.clone();
        async move {
            // Wrap this in a bounded postgres wal stream IF it's a PostgresWAL.
            config.wal = match config.wal.as_ref().downcast_ref::<PostgresWAL>() {
                Some(_) => Arc::new(PostgresWalStreamBounded::new(
                    config.wal,
                    pg_wal_stream_opts,
                )),
                None => config.wal,
            };

            let start = std::time::Instant::now();
            let result = storage::process_wal::process_object_wal(
                ProcessObjectWalInput {
                    object_id: object_id.as_ref(),
                    config: &config,
                },
                ProcessObjectWalOptionalInput {
                    start_xact_id: args.process_input.start_xact_id,
                    end_xact_id: args.process_input.end_xact_id,
                    mark_as_compacted: args.process_input.mark_as_compacted.unwrap_or(false),
                    ..Default::default()
                },
                process_args,
            )
            .await?;
            tracing::info!(
                object_id = object_id.to_string(),
                "Processed WAL in {:?}",
                start.elapsed()
            );
            tracing::info!(
                object_id = object_id.to_string(),
                modified_segment_ids = result.modified_segment_ids.len(),
                "Modified segment IDs"
            );
            Ok::<_, util::anyhow::Error>(result)
        }
        .instrument(span)
    }))
    .await
    .into_iter()
    .collect::<Result<Vec<_>>>()?;

    let all_segment_ids = outputs
        .iter()
        .flat_map(|output| output.modified_segment_ids.iter())
        .copied()
        .collect::<HashSet<_>>();

    if log::log_enabled!(log::Level::Debug) {
        log::debug!("\n\nMetadata timers (after processing WAL)");
        storage::instrumented::format_timers(app_state.config.global_store.as_dyn_instrumented());
        log::debug!("\n\nIndex timers (after processing WAL)");
        storage::instrumented::format_timers(
            app_state.config.index.directory.as_dyn_instrumented(),
        );
    }
    if log::log_enabled!(log::Level::Debug) {
        log::debug!("\n\nMetadata cache metrics (after processing WAL)");
        storage::instrumented::format_cache_metrics(
            app_state.config.global_store.as_dyn_instrumented(),
        );
        log::debug!("\n\nIndex cache metrics (after processing WAL)");
        storage::instrumented::format_cache_metrics(
            app_state.config.index.directory.as_dyn_instrumented(),
        );
    }

    Ok(all_segment_ids)
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct CompactWalFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_opts: CompactSegmentWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_input: CompactWalInputArgs,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct CompactWalInputArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub segment_id_args: SegmentIdArgs,

    #[arg(
        long = "compact-start-xact-id",
        name = "compact_start_xact_id",
        env = "BRAINSTORE_COMPACT_START_XACT_ID"
    )]
    pub start_xact_id: Option<TransactionId>,

    /// Stop reading from the WAL at max(end_xact_id, last_compacted_xact_id) (inclusive). Note
    /// that we cannot stop before last_compacted_xact_id, otherwise our compacted index will be in
    /// an inconsistent state with respect to the recorded last_compacted_xact_id.
    #[arg(
        long = "compact-end-xact-id",
        name = "compact_end_xact_id",
        env = "BRAINSTORE_COMPACT_END_XACT_ID"
    )]
    pub end_xact_id: Option<TransactionId>,

    #[arg(
        short,
        long,
        default_value_t = false,
        help = "If true, then try acquiring a lock for the segment, and bail out quickly if we can't."
    )]
    #[serde(default)]
    pub try_acquire: bool,

    #[arg(
        short,
        long,
        default_value_t = false,
        help = "If true, then queue the compaction for later. Only available when running through the web server."
    )]
    #[serde(default)]
    pub queue: bool,
}

#[instrument(err, name = "compact_wal", skip(app_state))]
pub async fn compact_wal(app_state: Arc<AppState>, args: CompactWalFullArgs) -> Result<()> {
    if args.compact_wal_input.queue {
        util::anyhow::bail!("Queueing compaction is not available when running through the CLI");
    }

    let config = app_state
        .config
        .with_new_indexing_directory(app_state.base_args.file_cache_opts.clone())?;

    let segment_ids = args
        .compact_wal_input
        .segment_id_args
        .segment_ids(config.global_store.as_ref())
        .await?;

    tracing::info!(
        num_segments = segment_ids.len(),
        "Compacting WAL for segments..."
    );

    let segment_id_to_object_id = args
        .compact_wal_input
        .segment_id_args
        .segment_id_to_object_id(&segment_ids, config.global_store.as_ref())
        .await?;

    join_all(segment_ids.iter().map(|segment_id| {
        let span = tracing::info_span!("compact wal segment", segment_id = segment_id.to_string());

        let object_id = &segment_id_to_object_id[segment_id];
        let index_store = config.index.clone();
        let config_file_schema = app_state.config_schema.clone();
        let compact_wal_opts = args.compact_wal_opts.clone();
        let global_store = config.global_store.clone();
        let locks_manager = config.locks_manager.clone();
        async move {
            let schema = make_full_schema(
                &(match config_file_schema {
                    Some(schema) => schema,
                    None => make_object_schema(object_id.object_type)?,
                }),
            )?;

            let start = std::time::Instant::now();
            storage::process_wal::compact_segment_wal(
                CompactSegmentWalInput {
                    segment_id: *segment_id,
                    index_store: index_store.clone(),
                    schema,
                    global_store: global_store.clone(),
                    locks_manager: &*locks_manager,
                },
                CompactSegmentWalOptionalInput {
                    use_status_updater: true,
                    start_xact_id: args.compact_wal_input.start_xact_id,
                    end_xact_id: args.compact_wal_input.end_xact_id,
                    try_acquire: args.compact_wal_input.try_acquire,
                    ..Default::default()
                },
                compact_wal_opts,
            )
            .await?;
            tracing::info!(
                object_id = object_id.to_string(),
                segment_id = segment_id.to_string(),
                "Compacted WAL in {:?}",
                start.elapsed()
            );
            Ok::<(), util::anyhow::Error>(())
        }
        .instrument(span)
    }))
    .await
    .into_iter()
    .collect::<Result<()>>()?;

    if log::log_enabled!(log::Level::Debug) {
        eprintln!("\n\nMetadata timers (after compacting segment WALs)");
        storage::instrumented::format_timers(config.global_store.as_dyn_instrumented());
        eprintln!("\n\nIndex timers (after compacting segment WALs)");
        storage::instrumented::format_timers(config.index.directory.as_dyn_instrumented());
    }
    if log::log_enabled!(log::Level::Debug) {
        eprintln!("\n\nMetadata cache metrics (after compacting segment WALs)");
        storage::instrumented::format_cache_metrics(config.global_store.as_dyn_instrumented());
        eprintln!("\n\nIndex cache metrics (after compacting segment WALs)");
        storage::instrumented::format_cache_metrics(config.index.directory.as_dyn_instrumented());
    }

    Ok(())
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct LoadWalArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub insert_wal_opts: InsertWalArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub process_args: ProcessObjectWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_opts: CompactSegmentWalOptions,

    #[arg(
        long,
        help = "Whether to merge the segments after compacting",
        default_value_t = false,
        env = "BRAINSTORE_WAL_LOAD_MERGE"
    )]
    #[serde(default)]
    pub merge: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub merge_opts: storage::merge::MergeOpts,
}

#[instrument(err, skip(app_state))]
pub async fn load_wal(app_state: Arc<AppState>, args: LoadWalArgs) -> Result<()> {
    let object_id = args.insert_wal_opts.default_object_id.to_string();
    // First, insert the WAL entries.
    tracing::info!("Inserting WAL entries...");
    let num_values = insert_wal(app_state.clone(), args.insert_wal_opts).await?;
    tracing::info!(num_values = num_values, "Inserted values into WAL");

    // Then, process the WAL entries.
    log::info!("Processing WAL entries... {:?}", args.process_args);
    let segment_ids = process_wal(
        app_state.clone(),
        ProcessWalFullArgs {
            process_opts: args.process_args.clone(),
            process_input: ProcessWalInputArgs {
                pg_wal_stream_opts: PostgresWalStreamOpts::default(),
                object_ids: vec![object_id],
                start_xact_id: None,
                end_xact_id: None,
                mark_as_compacted: None,
                source: None,
            },
        },
    )
    .await?;

    if !segment_ids.is_empty() {
        let segment_id_args = SegmentIdArgs {
            segment_ids: segment_ids.into_iter().collect(),
            all: false,
            object_id: None,
        };

        let writer_opts = args.compact_wal_opts.writer_opts.clone();

        // Finally, compact the segment WALs.
        log::info!("Compacting segment WALs...");
        compact_wal(
            app_state.clone(),
            CompactWalFullArgs {
                compact_wal_opts: args.compact_wal_opts,
                compact_wal_input: CompactWalInputArgs {
                    segment_id_args: segment_id_args.clone(),
                    start_xact_id: None,
                    end_xact_id: None,
                    try_acquire: false,
                    queue: false,
                },
            },
        )
        .await?;

        if args.merge {
            log::info!("Merging segments...");
            super::index::merge_index(
                app_state.clone(),
                MergeIndexFullArgs {
                    merge_input: MergeIndexInputArgs {
                        segment_id_args: segment_id_args.clone(),
                        merge_opts: args.merge_opts,
                        dry_run: false,
                        try_acquire: false,
                        testing_sleep_ms: None,
                    },
                    writer_opts,
                    process_wal_opts: args.process_args,
                },
            )
            .await?;
        }
    } else {
        log::info!("No segments to compact");
    }

    Ok(())
}

#[derive(Parser, Debug, Clone, Deserialize, Serialize)]
pub struct CatWalArgs {
    #[arg(
        short,
        long,
        help = "The minimum transaction ID to cat",
        env = "BRAINSTORE_WAL_CAT_START_XACT_ID"
    )]
    pub start_xact_id: Option<u64>,

    #[arg(
        short,
        long,
        help = "The maximum transaction ID to cat",
        env = "BRAINSTORE_WAL_CAT_END_XACT_ID"
    )]
    pub end_xact_id: Option<u64>,

    #[arg(
        short,
        long,
        help = "The number of transactions to cat",
        env = "BRAINSTORE_WAL_CAT_NUM_XACTS"
    )]
    pub num_xacts: Option<u64>,

    #[arg(
        help = "The object ID to cat. Must specify exactly one of an object ID or a segment ID.",
        env = "BRAINSTORE_WAL_CAT_OBJECT_ID"
    )]
    pub object_id: Option<String>,

    #[arg(
        help = "The segment ID to cat. Must specify exactly one of an object ID or a segment ID.",
        env = "BRAINSTORE_WAL_CAT_SEGMENT_ID"
    )]
    pub segment_id: Option<Uuid>,

    #[arg(
        help = "Whether or not to print out the fully-merged set of WAL entries at the end",
        env = "BRAINSTORE_WAL_CAT_PRINT_FULLY_MERGED"
    )]
    pub print_fully_merged: Option<bool>,
}

pub async fn cat_wal(app_state: Arc<AppState>, args: CatWalArgs) -> Result<()> {
    let mut _object_id_opt: Option<FullObjectIdOwned> = None;
    let (wal, wal_scope) = if let Some(object_id_str) = args.object_id {
        let object_id: FullObjectIdOwned = object_id_str.parse()?;
        let wal_token = app_state
            .config
            .global_store
            .query_object_metadatas(&[object_id.as_ref()])
            .await?
            .remove(0)
            .wal_token;
        _object_id_opt = Some(object_id);
        (
            app_state.config.wal.clone(),
            WALScope::ObjectId(_object_id_opt.as_ref().unwrap().as_ref(), wal_token),
        )
    } else if let Some(segment_id) = args.segment_id {
        let index_store = &app_state.config.index;
        let segment_index_wal = ObjectAndGlobalStoreWal {
            object_store: index_store.store.clone(),
            global_store: app_state.config.global_store.clone(),
            directory: index_store.directory.clone(),
            store_prefix: index_store.prefix.clone(),
            store_type: index_store.store_type,
        };
        (
            Arc::new(segment_index_wal) as Arc<dyn Wal>,
            WALScope::Segment(segment_id),
        )
    } else {
        return Err(util::anyhow::anyhow!(
            "Must specify exactly one of an object ID or a segment ID"
        ));
    };
    let mut stream = wal_stream(
        wal.wal_metadata_stream(
            wal_scope,
            WalMetadataStreamOptionalInput {
                start_xact_id: args.start_xact_id.map(TransactionId),
                end_xact_id: args.end_xact_id.map(TransactionId),
                ..Default::default()
            },
        )
        .await?,
        Default::default(),
    );
    let mut fully_merged_entries: HashMap<FullRowIdOwned, WalEntry> = HashMap::new();
    let mut consumed = 0;
    'outer: while let Some(result) = stream.next().await {
        let all_wal_entries = result.item?.entries;
        for (xact_id, entries, _) in all_wal_entries {
            log::info!("---- xact id: {} (entries: {})", xact_id, entries.len());
            for entry in entries {
                if args.print_fully_merged.unwrap_or(false) {
                    let full_row_id = entry.full_row_id().to_owned();
                    fully_merged_entries
                        .entry(full_row_id)
                        .and_modify(|e| e.merge(entry.clone()).unwrap())
                        .or_insert(entry.clone());
                }
                println!("{}", serde_json::to_string(&entry.to_value())?);
                consumed += 1;
            }
            if let Some(num_xacts) = args.num_xacts {
                if consumed >= num_xacts {
                    break 'outer;
                }
            }
        }
    }
    if args.print_fully_merged.unwrap_or(false) {
        log::info!("Fully merged entries:");
        for wal_entry in fully_merged_entries.into_values() {
            let index_doc = IndexDocument { wal_entry };
            println!(
                "{}",
                serde_json::to_string(&index_doc.wal_entry.to_value())?
            );
        }
    }

    Ok(())
}

#[derive(Parser, Debug, Clone, Deserialize, Serialize)]
pub struct StatusArgs {
    #[arg(
        required = true,
        help = "The object ID to get the status for",
        env = "BRAINSTORE_WAL_STATUS_OBJECT_ID"
    )]
    pub object_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct WalStatus {
    // Since these are large numbers, and we treat it as a string in JSON, serialize it as a string.
    last_compacted_xact_id: Option<String>,
}

pub async fn status(app_state: Arc<AppState>, args: StatusArgs) -> Result<util::Value> {
    let object_id: FullObjectIdOwned = args.object_id.parse()?;

    let last_processed_xact_id = app_state
        .config
        .global_store
        .query_object_metadatas(&[object_id.as_ref()])
        .await?
        .remove(0)
        .last_processed_xact_id;

    let status = WalStatus {
        last_compacted_xact_id: last_processed_xact_id.map(|x| x.to_string()),
    };

    Ok(json!(status))
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct WalArgs {
    #[arg(
        short,
        long,
        help = "The WAL sequence to start from",
        env = "BRAINSTORE_WAL_START_SEQUENCE"
    )]
    pub start_sequence: Option<u64>,

    #[arg(
        short,
        long,
        help = "The WAL sequence to end at",
        env = "BRAINSTORE_WAL_END_SEQUENCE"
    )]
    pub end_sequence: Option<u64>,

    #[arg(
        short,
        long,
        help = "The maximum number of records to process",
        env = "BRAINSTORE_WAL_MAX_RECORDS"
    )]
    pub max_records: Option<u64>,

    #[arg(
        long,
        help = "Follow the WAL for new records",
        env = "BRAINSTORE_WAL_FOLLOW"
    )]
    pub follow: bool,
}
