use clap::Parser;
use fork::{fork, Fork};
use nix::{sys::wait::WaitStatus, unistd::Pid};
use serde::{Deserialize, Serialize};
use std::{fs, path::PathBuf, sync::Arc};
use tempfile::TempDir;
use tokio::io::{AsyncBufReadExt, AsyncReadExt, AsyncWriteExt};
use tracing::Instrument;

use agent::{setup_tracing::setup_log_tracer, tracing_opentelemetry::OpenTelemetrySpanExt};
use otel_common::{
    opentelemetry,
    opentelemetry::{
        trace::{SpanId, TraceContextExt, TraceFlags, TraceId},
        Context,
    },
};
use util::{anyhow, futures::future::join_all};

use crate::{base::BaseArgs, Commands};

#[derive(Serialize, Deserialize, Debug)]
pub struct CommandRequest {
    pub args: crate::Commands,
    pub trace_id: Option<[u8; 16]>,
    pub span_id: Option<[u8; 8]>,
    pub trace_flags: Option<u8>,
}

impl CommandRequest {
    pub fn new(args: crate::Commands) -> Self {
        let span = tracing::Span::current();
        let ctx = span.context();
        let ctx_span = ctx.span();
        let ctx_span_context = ctx_span.span_context();

        let (trace_id, span_id, trace_flags) = if ctx_span_context.is_valid() {
            (
                Some(ctx_span_context.trace_id().to_bytes()),
                Some(ctx_span_context.span_id().to_bytes()),
                Some(ctx_span_context.trace_flags().to_u8()),
            )
        } else {
            (None, None, None)
        };

        Self {
            args,
            trace_id,
            span_id,
            trace_flags,
        }
    }

    pub fn create_parent_span(&self) -> Option<tracing::Span> {
        match (
            self.trace_id.as_ref(),
            self.span_id.as_ref(),
            self.trace_flags,
        ) {
            (Some(trace_id), Some(span_id), Some(flags)) => {
                // Parse the IDs back into their binary formats
                let trace_id = TraceId::from_bytes(*trace_id);
                let span_id = SpanId::from_bytes(*span_id);
                let flags = TraceFlags::new(flags);

                // Create a new span context
                let parent_span_context = opentelemetry::trace::SpanContext::new(
                    trace_id,
                    span_id,
                    flags,
                    true,
                    Default::default(), // Empty trace state
                );
                let parent_otel_ctx = Context::new().with_remote_span_context(parent_span_context);

                // Create a new span with the parent context
                let child_span = tracing::span!(tracing::Level::INFO, "worker",);
                child_span.set_parent(parent_otel_ctx);

                Some(child_span)
            }
            _ => None,
        }
    }
}

#[derive(Serialize, Deserialize)]
pub struct CommandResponse(Result<util::Value, String>);

// Even though 100,000 is quite high, it means that we'll fragment memory, rather than
// create dueling/overlapping merge processes, both of which ultimately lead to OOMs.
pub const DEFAULT_LIFETIME_EXECUTIONS: usize = 100_000;

// The CommandProc is the main object that you can use to start a command process (which manages
// its own worker processes) and communicate with it.
pub struct CommandProc {
    socket_path: PathBuf,
    current_exe: String,
    _tmp_dir: Arc<TempDir>,
    child_pid: Option<i32>,
}

impl CommandProc {
    pub fn new(
        base: BaseArgs,
        lifetime_executions: usize,
        current_exe: Option<String>,
    ) -> anyhow::Result<Self> {
        let tmp_dir = Arc::new(TempDir::new()?);
        let socket_path = Self::new_socket_path(&tmp_dir)?;
        let current_exe = current_exe.unwrap_or_else(|| {
            std::env::current_exe()
                .unwrap()
                .to_str()
                .unwrap()
                .to_string()
        });

        let mut ret = CommandProc {
            socket_path,
            current_exe,
            _tmp_dir: tmp_dir,
            child_pid: None,
        };

        ret.start(base, lifetime_executions)?;
        Ok(ret)
    }

    pub fn new_socket_path(tmp_dir: &TempDir) -> anyhow::Result<PathBuf> {
        Ok(tempfile::Builder::new()
            .prefix("command-")
            .suffix(".sock")
            .tempfile_in(&tmp_dir.path())?
            .into_temp_path()
            .to_path_buf())
    }

    pub fn socket_path(&self) -> &PathBuf {
        &self.socket_path
    }

    fn start(&mut self, base: BaseArgs, lifetime_executions: usize) -> anyhow::Result<()> {
        match fork() {
            Ok(Fork::Parent(child_pid)) => {
                self.child_pid = Some(child_pid);
                return Ok(());
            }
            Ok(Fork::Child) => {
                if self.socket_path.exists() {
                    fs::remove_file(&self.socket_path)?;
                }

                setup_log_tracer(base.verbose)?;

                let single_threaded_runtime = tokio::runtime::Builder::new_current_thread()
                    .enable_all()
                    .build()
                    .unwrap();
                single_threaded_runtime.block_on(run_command_server(
                    base,
                    lifetime_executions,
                    self.socket_path.to_str().unwrap(),
                    self._tmp_dir.clone(),
                    &self.current_exe,
                ));

                std::process::exit(0);
            }
            Err(err) => return Err(anyhow::anyhow!("Fork failed: {}", err)),
        }
    }

    pub async fn run_command(&self, cmd: crate::Commands) -> anyhow::Result<util::Value> {
        let start = std::time::Instant::now();

        let is_optimize_loop = matches!(
            cmd,
            crate::Commands::Index(crate::index::IndexCommands::OptimizeLoop(_))
        );

        let command_request = CommandRequest::new(cmd);
        loop {
            match Self::run_command_internal(
                &self.socket_path.to_str().unwrap(),
                self.child_pid,
                &command_request,
            )
            .await
            {
                Ok(CommandResponse(Ok(v))) => {
                    return Ok(v);
                }
                Ok(CommandResponse(Err(e))) => return Err(anyhow::anyhow!(e)),
                Err(e) => {
                    if start.elapsed() < std::time::Duration::from_secs(10) {
                        log::error!(
                            "Error running command in worker: {:?}. Will retry after 100ms...",
                            e
                        );
                        std::thread::sleep(std::time::Duration::from_millis(100));
                    } else if is_optimize_loop {
                        // If this is an optimize loop, then we'll just return null and try again in the
                        // outer loop. This also happens during shutdown, and it's not nice to see the error.
                        return Ok(util::Value::Null);
                    } else {
                        log::error!(
                            "Error running command in worker after {:?}: {:?}. Will not retry...",
                            start.elapsed(),
                            e
                        );
                        return Err(e);
                    }
                }
            }
        }
    }

    async fn run_command_internal(
        socket_path: &str,
        child_pid: Option<i32>,
        request: &CommandRequest,
    ) -> anyhow::Result<CommandResponse> {
        let mut stream = connect_to_socket(socket_path, child_pid).await?;

        let (reader, writer) = stream.split();

        let mut writer = tokio::io::BufWriter::new(writer);
        writer
            .write_all(serde_json::to_string(&request)?.as_bytes())
            .await?;
        writer.write_all(b"\n").await?;
        writer.flush().await?;

        let mut response_str = String::new();
        let mut reader = tokio::io::BufReader::new(reader);
        reader.read_to_string(&mut response_str).await?;
        Ok(serde_json::from_str(&response_str)?)
    }
}

impl Drop for CommandProc {
    fn drop(&mut self) {
        // Clean up socket file
        if self.socket_path.exists() {
            let _ = fs::remove_file(&self.socket_path);
        }
    }
}

pub const SOCKET_CONNECT_TIMEOUT_SEC: u64 = 60;

pub async fn connect_to_socket(
    socket_path: &str,
    child_pid: Option<i32>,
) -> anyhow::Result<tokio::net::UnixStream> {
    let start = std::time::Instant::now();
    let mut last_sleep_ms = 1000;
    loop {
        let s = tokio::net::UnixStream::connect(&socket_path).await;
        match s {
            Ok(s) => {
                return Ok(s);
            }
            Err(e) => {
                if let Some(child_pid) = child_pid {
                    let status = nix::sys::wait::waitpid(
                        Pid::from_raw(child_pid),
                        Some(nix::sys::wait::WaitPidFlag::WNOHANG),
                    );
                    if let Ok(nix::sys::wait::WaitStatus::Exited(_, _)) = status {
                        return Err(anyhow::anyhow!("Child process {} has exited", child_pid));
                    } else {
                        tracing::warn!(
                            "Failed to connect to command process socket at {}: {}. Child process status: {:?}",
                            socket_path,
                            e,
                            status
                        );
                    }
                }

                // It can take a bit for the worker to start up.
                if start.elapsed().as_secs() >= SOCKET_CONNECT_TIMEOUT_SEC {
                    return Err(anyhow::anyhow!(
                        "Failed to connect to command process socket at {} after {:?}: {}",
                        socket_path,
                        start.elapsed(),
                        e
                    ));
                }

                if start.elapsed().as_secs() > 1 {
                    tracing::warn!(
                        "Failed to connect to command process socket at {} after {:?}: {}. Will sleep and retry...",
                        socket_path,
                        start.elapsed(),
                        e
                    );
                }

                tokio::time::sleep(tokio::time::Duration::from_millis(last_sleep_ms)).await;
                last_sleep_ms = (last_sleep_ms as f64 * 1.1) as u64;
            }
        }
    }
}
// Track active worker processes
struct WorkerProcess {
    pid: i32,
    socket_path: PathBuf,
    executions: usize,
}

pub async fn run_command_server(
    base: BaseArgs,
    lifetime_executions: usize,
    incoming_socket_path: &str,
    tmp_dir: Arc<TempDir>,
    current_exe: &str,
) {
    let listener =
        tokio::net::UnixListener::bind(incoming_socket_path).expect("Failed to bind to socket");

    let mut workers = Vec::new();
    let mut tasks = tokio::task::JoinSet::new();

    log::info!("Command process started successfully");

    loop {
        tokio::select! {
            // Accept new connections
            accept_result = listener.accept() => {
                match accept_result {
                    Ok((client_stream, _)) => {
                        // Get or create a worker
                        match get_or_create_worker(
                            &mut workers,
                            &base,
                            lifetime_executions,
                            &tmp_dir,
                            current_exe,
                        ).await {
                            Ok(worker_stream) => {
                                // Spawn a new task to handle this request
                                tasks.spawn(handle_request(
                                    client_stream,
                                    worker_stream,
                                ));
                            }
                            Err(e) => {
                                tracing::error!("Failed to get/create worker: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        tracing::error!("Failed to accept connection: {}", e);
                    }
                }
            }

            // Handle completed tasks
            Some(result) = tasks.join_next() => {
                match result {
                    Ok(Ok(_)) => (), // Task completed successfully
                    Ok(Err(e)) => tracing::error!("Task error: {}", e),
                    Err(e) => tracing::error!("Task panicked: {}", e),
                }
            }

            // Periodically clean up dead workers
            _ = tokio::time::sleep(tokio::time::Duration::from_secs(1)) => {
                cleanup_dead_workers(&mut workers);
            }
        }
    }
}

async fn handle_request(
    mut client_stream: tokio::net::UnixStream,
    mut worker_stream: tokio::net::UnixStream,
) -> anyhow::Result<()> {
    // Read the request
    let request = read_request(&mut client_stream).await?;

    // Send request to worker
    worker_stream.write_all(request.as_bytes()).await?;
    worker_stream.flush().await?;

    // Forward response back to client
    forward_response(&mut worker_stream, &mut client_stream).await?;

    Ok(())
}

async fn read_request(stream: &mut tokio::net::UnixStream) -> anyhow::Result<String> {
    let mut reader = tokio::io::BufReader::new(stream);
    let mut request = String::new();
    reader.read_line(&mut request).await?;
    Ok(request)
}

async fn forward_response(
    worker_stream: &mut tokio::net::UnixStream,
    client_stream: &mut tokio::net::UnixStream,
) -> anyhow::Result<()> {
    let mut response = String::new();
    let mut reader = tokio::io::BufReader::new(worker_stream);
    reader.read_to_string(&mut response).await?;
    client_stream.write_all(response.as_bytes()).await?;
    client_stream.flush().await?;
    Ok(())
}

async fn get_or_create_worker(
    workers: &mut Vec<WorkerProcess>,
    base: &BaseArgs,
    lifetime_executions: usize,
    tmp_dir: &Arc<TempDir>,
    current_exe: &str,
) -> anyhow::Result<tokio::net::UnixStream> {
    let mut failed_to_connect = false;
    let mut last_sleep_ms = 1;
    loop {
        if workers.is_empty() || failed_to_connect {
            // Create a new worker
            let socket_path = CommandProc::new_socket_path(tmp_dir)?;

            let pid = match fork() {
                Ok(Fork::Parent(pid)) => pid,
                Ok(Fork::Child) => {
                    let mut base = base.clone();
                    if let None = base.pretty_logging {
                        base.pretty_logging = Some(false);
                    }
                    let args = WorkerCommandArgs {
                        base,
                        socket_path: socket_path.to_str().unwrap().to_string(),
                        executions: lifetime_executions,
                        schema: None,
                    };

                    let args_str = serde_json::to_string(&args)?;
                    let current_exe_cstr = std::ffi::CString::new(current_exe)?;
                    let worker_cstr = std::ffi::CString::new("worker")?;
                    let args_cstr = std::ffi::CString::new(args_str)?;

                    nix::unistd::execvp(
                        &current_exe_cstr,
                        &[current_exe_cstr.clone(), worker_cstr, args_cstr],
                    )?;

                    std::process::exit(0);
                }
                Err(e) => return Err(anyhow::anyhow!("Fork failed: {}", e)),
            };

            let worker = WorkerProcess {
                pid,
                socket_path,
                executions: 0,
            };
            failed_to_connect = false;
            workers.push(worker);
        }

        let num_workers = workers.len();
        let worker = &mut workers[num_workers - 1];

        match tokio::net::UnixStream::connect(&worker.socket_path).await {
            Ok(stream) => {
                worker.executions += 1;
                return Ok(stream);
            }
            Err(e) => {
                // If we haven't yet sent any commands to the worker, then we should wait for it to start up for up to a minute.
                if worker.executions == 0 && last_sleep_ms < 60000 {
                    if last_sleep_ms > 1000 {
                        tracing::warn!(
                            "child process {} is still running despite the socket not being available ({}). will try to connect again",
                            worker.pid,
                            e
                        );
                    }
                    // If we haven't yet sent any commands to the worker, then we should wait for it to start up.
                    tokio::time::sleep(tokio::time::Duration::from_millis(last_sleep_ms)).await;
                    last_sleep_ms = last_sleep_ms * 2;
                } else {
                    failed_to_connect = true;
                }

                // New workers can also be defunct, so we should clean them up just in case.
                cleanup_dead_workers(workers);
            }
        }
    }
}

fn cleanup_dead_workers(workers: &mut Vec<WorkerProcess>) {
    workers.retain(|worker| {
        match nix::sys::wait::waitpid(
            Pid::from_raw(worker.pid),
            Some(nix::sys::wait::WaitPidFlag::WNOHANG),
        ) {
            Ok(WaitStatus::StillAlive) => true,
            _ => {
                // Clean up the socket file
                let _ = std::fs::remove_file(&worker.socket_path);
                false
            }
        }
    });
}

/// This is the command that is sent to the worker process. It's a single string, so that it's easy
/// to send via execvp().
#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct WorkerCommand {
    pub arg: String,
}

/// This is the actual set of arguments, which gets json-serialized into `WorkerCommand`.
#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct WorkerCommandArgs {
    #[command(flatten)]
    pub base: BaseArgs,

    #[arg(long, help = "The socket path to listen on")]
    pub socket_path: String,

    #[arg(long, help = "The number of commands to execute before shutting down")]
    pub executions: usize,

    #[arg(long, help = "The logical schema to use for binding BTQL queries.")]
    pub schema: Option<String>,
}

// When we start a child process, we go through the normal CLI flow in main.rs, and call this async
// function which can run individual commands.
pub async fn worker(args: WorkerCommand) -> anyhow::Result<util::Value> {
    let args: WorkerCommandArgs = serde_json::from_str(&args.arg)?;

    let listener = tokio::net::UnixListener::bind(&args.socket_path)?;

    let meter = otel_common::opentelemetry::global::meter("brainstore");
    let counter = meter.u64_counter("brainstore.command_proc.start").build();
    counter.add(1u64, &[]);

    let counter = meter.u64_counter("brainstore.command_proc.accept").build();

    let mut executions = 0;
    let mut active_requests = Vec::new();
    while executions < args.executions {
        let stream = listener.accept().await;
        counter.add(1u64, &[]);
        match stream {
            Ok((stream, _)) => {
                active_requests.push(tokio::spawn(process_command_stream(stream)));
            }
            Err(e) => {
                tracing::error!("Command worker failed to accept connection: {}", e);
            }
        }

        executions += 1;
    }
    drop(listener);

    join_all(active_requests)
        .await
        .into_iter()
        .collect::<Result<Vec<_>, _>>()?;

    Ok(util::Value::Null)
}

pub async fn process_command_stream(mut stream: tokio::net::UnixStream) {
    let result = async {
        let (reader, writer) = stream.split();

        let request: Result<CommandRequest, _> = async {
            let mut reader = tokio::io::BufReader::new(reader);
            let mut request_str = String::new();
            reader.read_line(&mut request_str).await?;

            let request: CommandRequest = serde_json::from_str(&request_str)?;
            Ok(request)
        }
        .await;

        let result = match request {
            Ok(request) => {
                let span = request
                    .create_parent_span()
                    .unwrap_or(tracing::Span::current());
                async move {
                    match request.args {
                        Commands::Wal(wal) => crate::wal::main(wal).await,
                        Commands::Index(index) => crate::index::main(index).await,
                        Commands::Btql(btql) => crate::btql::main(btql).await,
                        Commands::Ping(cmd) => crate::ping::main(cmd).await,
                        Commands::Retention(cmd) => crate::retention::main(cmd).await,
                        Commands::Vacuum(cmd) => crate::vacuum::main(cmd).await,
                        _ => Err(anyhow::anyhow!("Unsupported command: {:?}", request.args)),
                    }
                }
                .instrument(span)
                .await
            }
            Err(e) => Err(e),
        };

        let response = CommandResponse(match result {
            Ok(v) => Ok(v),
            Err(e) => Err(e.to_string()),
        });

        let mut writer = tokio::io::BufWriter::new(writer);
        writer
            .write_all(serde_json::to_string(&response)?.as_bytes())
            .await?;
        writer.flush().await?;

        Ok::<(), anyhow::Error>(())
    }
    .await;

    if let Err(e) = result {
        tracing::warn!("Command worker failed to process command: {}", e);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{base::BaseArgs, main_inner};
    use tokio::net::UnixStream;

    // These tests are marked "ignore" because they fork and therefore mess with other test runners (and even each other).
    // Each one is run individually in the Github action. If you add more tests, make sure to update .github/workflows/test-rust.yaml
    // to run the test individually
    #[test]
    #[ignore]
    fn test_worker_lifetime() {
        let base = BaseArgs {
            config: "".to_string(),
            ..Default::default()
        };

        let tmp_dir = Arc::new(TempDir::new().unwrap());
        let socket_path = CommandProc::new_socket_path(&tmp_dir).unwrap();

        let child_pid = match fork() {
            Ok(Fork::Parent(child_pid)) => child_pid,
            Ok(Fork::Child) => {
                main_inner(Commands::Worker(WorkerCommand {
                    arg: serde_json::to_string(&WorkerCommandArgs {
                        base,
                        socket_path: socket_path.to_str().unwrap().to_string(),
                        executions: 5,
                        schema: None,
                    })
                    .unwrap(),
                }))
                .unwrap();

                std::process::exit(0);
            }
            Err(err) => panic!("Fork failed: {}", err),
        };

        let rt = tokio::runtime::Runtime::new().unwrap();

        rt.block_on(async move {
            // Send 5 commands
            for _ in 0..5 {
                let mut stream =
                    connect_to_socket(&socket_path.to_str().unwrap(), Some(child_pid)).await?;

                let request =
                    CommandRequest::new(crate::Commands::Ping(crate::ping::PingCommand {
                        message: Some("test".to_string()),
                        watch_file: None,
                    }));
                let (reader, writer) = stream.split();
                let mut writer = tokio::io::BufWriter::new(writer);
                writer
                    .write_all(serde_json::to_string(&request)?.as_bytes())
                    .await?;
                writer.write_all(b"\n").await?;
                writer.flush().await?;

                let mut reader = tokio::io::BufReader::new(reader);
                let mut response_str = String::new();
                reader.read_to_string(&mut response_str).await?;
                let response: CommandResponse = serde_json::from_str(&response_str)?;
                assert_eq!(
                    response.0,
                    Ok(util::Value::String("Pong: test".to_string()))
                );
            }

            // Wait a bit for the worker to process commands and exit
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

            // Try to connect again - should fail since worker has exited
            let connect_result = UnixStream::connect(&socket_path).await;
            assert!(connect_result.is_err());

            Ok::<(), anyhow::Error>(())
        })
        .unwrap();
    }

    #[test]
    #[ignore]
    fn test_command_proc_sanity() {
        // Set BRAINSTORE_PING_MESSAGE to "default message". This lets us test that
        // the environment variable is inherited by the child process.
        std::env::set_var("BRAINSTORE_PING_DEFAULT_MESSAGE", "default message");

        let base = BaseArgs {
            config: "".to_string(),
            ..Default::default()
        };

        // Convert debug binary path to release binary path
        let current_exe = std::env::current_exe()
            .unwrap()
            .parent() // Get parent dir
            .unwrap()
            .parent() // Go up one more level
            .unwrap()
            .join("brainstore"); // Add binary name

        let command_proc =
            CommandProc::new(base, 5, Some(current_exe.to_str().unwrap().to_string())).unwrap();

        let rt = tokio::runtime::Runtime::new().unwrap();

        rt.block_on(async move {
            // Run the command again
            let result = command_proc
                .run_command(Commands::Ping(crate::ping::PingCommand {
                    message: None,
                    watch_file: None,
                }))
                .await
                .unwrap();
            assert_eq!(result.as_str().unwrap(), "Pong: default message");

            let mut handles = Vec::new();
            for i in 0..100 {
                handles.push(
                    command_proc.run_command(Commands::Ping(crate::ping::PingCommand {
                        message: Some(format!("test {}", i)),
                        watch_file: None,
                    })),
                );
            }

            join_all(handles).await;

            Ok::<(), anyhow::Error>(())
        })
        .unwrap();
    }

    #[test]
    #[ignore]
    fn test_command_proc_multiple_workers() {
        let base = BaseArgs {
            config: "".to_string(),
            ..Default::default()
        };

        // Convert debug binary path to release binary path
        let current_exe = std::env::current_exe()
            .unwrap()
            .parent() // Get parent dir
            .unwrap()
            .parent() // Go up one more level
            .unwrap()
            .join("brainstore"); // Add binary name

        let command_proc = Arc::new(
            CommandProc::new(base, 2, Some(current_exe.to_str().unwrap().to_string())).unwrap(),
        );

        let rt = tokio::runtime::Runtime::new().unwrap();

        rt.block_on(async move {
            // Create two temp files
            let tmp_dir = TempDir::new().unwrap();
            let file1_path = tmp_dir.path().join("file1");
            let file2_path = tmp_dir.path().join("file2");
            fs::write(&file1_path, "").unwrap();
            fs::write(&file2_path, "").unwrap();

            // Start two watch commands in parallel
            let cp = command_proc.clone();
            let fp1 = file1_path.to_str().unwrap().to_string();
            let watch1 = tokio::spawn(async move {
                cp.run_command(Commands::Ping(crate::ping::PingCommand {
                    message: Some("r1".to_string()),
                    watch_file: Some(fp1),
                }))
                .await
            });
            let cp = command_proc.clone();
            let fp2 = file2_path.to_str().unwrap().to_string();
            let watch2 = tokio::spawn(async move {
                cp.run_command(Commands::Ping(crate::ping::PingCommand {
                    message: Some("r2".to_string()),
                    watch_file: Some(fp2),
                }))
                .await
            });

            fs::remove_file(file2_path).unwrap();
            let r2 = watch2.await.unwrap().unwrap();
            assert_eq!(r2.as_str().unwrap(), "Pong: r2");

            // This isn't perfect, but give the watcher some time to start, so that the third command
            // doesn't run before the first command starts.
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

            // Run a third command - this should succeed even with 2 workers since it's a quick ping
            let cp = command_proc.clone();
            let result = cp
                .run_command(Commands::Ping(crate::ping::PingCommand {
                    message: Some("test".to_string()),
                    watch_file: None,
                }))
                .await;

            assert!(result.is_ok());
            let output = result.unwrap();
            assert_eq!(output.as_str().unwrap(), "Pong: test");

            // Delete the files to let the watchers finish
            fs::remove_file(file1_path).unwrap();

            // Wait for the watchers to complete
            let r1 = watch1.await.unwrap().unwrap();
            assert_eq!(r1.as_str().unwrap(), "Pong: r1");

            Ok::<(), anyhow::Error>(())
        })
        .unwrap();
    }
}
