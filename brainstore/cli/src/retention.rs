use std::sync::Arc;

use crate::base::{
    self, parse_time_based_retention_object_id_args, AppState, CLIArgs,
    TimeBasedRetentionObjectIdArgs, TimeBasedRetentionWorkerOptions,
};
use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::retention_worker::{
    time_based_retention, TimeBasedRetentionInput, TimeBasedRetentionOptionalInput,
    TimeBasedRetentionOptions,
};
use util::{anyhow::Result, system_types::FullObjectId};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum RetentionCommands {
    TimeBasedRetention(CLIArgs<TimeBasedRetentionFullArgs>),
}

pub fn base_args(args: &RetentionCommands) -> &base::BaseArgs {
    match args {
        RetentionCommands::TimeBasedRetention(a) => &a.base,
    }
}

pub async fn main(args: RetentionCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        RetentionCommands::TimeBasedRetention(a) => {
            run_time_based_retention(app_state, &a.args).await
        }
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: TimeBasedRetentionArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: TimeBasedRetentionOptions,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: TimeBasedRetentionObjectIdArgs,

    #[arg(long, default_value_t = false)]
    #[serde(default)]
    pub dry_run: bool,
}

pub async fn run_time_based_retention(
    app_state: Arc<AppState>,
    full_args: &TimeBasedRetentionFullArgs,
) -> Result<util::Value> {
    let object_ids = parse_time_based_retention_object_id_args(&full_args.args.object_id_args)?;
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let output = time_based_retention(
        TimeBasedRetentionInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: &app_state.config.index,
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.as_ref(),
            dry_run: full_args.args.dry_run,
        },
        TimeBasedRetentionOptionalInput::default(),
        &full_args.options,
    )
    .await?;

    Ok(json!(output))
}

pub fn launch_time_based_retention_worker(
    app_state: Arc<AppState>,
    args: TimeBasedRetentionWorkerOptions,
) {
    tokio::spawn(async move {
        let full_args = TimeBasedRetentionFullArgs {
            args: TimeBasedRetentionArgs {
                object_id_args: args.object_id_args.clone(),
                dry_run: args.time_based_retention_dry_run,
            },
            options: args.time_based_retention_options.clone(),
        };

        log::info!("Launching time-based retention worker");

        let mut iterations = 0;
        loop {
            log::debug!("Running time-based retention loop (iter {})", iterations);

            match run_time_based_retention(app_state.clone(), &full_args).await {
                Ok(_) => {}
                Err(e) => {
                    log::warn!("Error running time-based retention: {:?}", e);
                }
            };

            iterations += 1;

            if let Some(max_iterations) = args
                .background_time_based_retention_options
                .background_time_based_retention_max_iterations
            {
                if iterations >= max_iterations {
                    log::info!(
                        "Reached maximum number of iterations ({}), exiting time-based retention loop",
                        max_iterations
                    );
                    break;
                }
            }

            log::debug!(
                "Sleeping for {} seconds before next time-based retention iteration",
                args.background_time_based_retention_options
                    .background_time_based_retention_sleep_seconds
            );

            tokio::time::sleep(std::time::Duration::from_secs(
                args.background_time_based_retention_options
                    .background_time_based_retention_sleep_seconds,
            ))
            .await;
        }
    });
}
