/// ```compile_fail
/// #[tokio::main]
/// async fn f() {
///   let a = 20;
///   {
///       let a_ref = &a;
///       await_spawn_blocking!(|a: &i64| {
///           *a
///       }, a_ref).unwrap().unwrap();
///   }
/// }
/// ```
fn _test_cast_to_different_type() {}

/// ```compile_fail
/// #[tokio::main]
/// async fn f() {
///   let mut a = 20;
///   {
///       let a_ref = &a;
///       await_spawn_blocking!(|a: &i32| {
///           *a
///       }; a_ref).unwrap().unwrap();
///   }
/// }
/// ```
fn _test_incorrect_immut_reference() {}

/// ```compile_fail
/// #[tokio::main]
/// async fn f() {
///   let a = 20;
///   {
///       let a_ref = &a;
///       await_spawn_blocking!(|a: &mut i32| {
///           *a
///       }, a_ref).unwrap().unwrap();
///   }
/// }
/// ```
fn _test_incorrect_mut_reference() {}
