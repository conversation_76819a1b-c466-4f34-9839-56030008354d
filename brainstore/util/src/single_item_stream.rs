use futures::{Stream, StreamExt};

/// A SingleItemStream is a wrapper around a Stream which ensures only one element of the stream can
/// be consumed at a time. I.e. the element must be dropped before the next one can be consumed.
pub struct SingleItemStream<T>
where
    T: Stream + Unpin,
{
    stream: T,
}

pub struct SingleItemStreamItem<'a, T>
where
    T: Stream + Unpin,
{
    pub item: T::Item,
    _stream_ref: &'a mut SingleItemStream<T>,
}

impl<T> SingleItemStream<T>
where
    T: Stream + Unpin,
{
    pub fn new(stream: T) -> Self {
        Self { stream }
    }

    pub async fn next(&mut self) -> Option<SingleItemStreamItem<'_, T>> {
        let res = self.stream.next().await;
        res.map(|item| SingleItemStreamItem {
            item,
            _stream_ref: self,
        })
    }
}
