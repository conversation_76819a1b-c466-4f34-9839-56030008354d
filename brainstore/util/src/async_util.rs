use std::borrow::Cow;
use std::future::Future;
use std::pin::Pin;
use std::sync::atomic::AtomicBool;
use std::sync::Arc;

use futures::pin_mut;

pub type BoxedAsyncFn<'a, T> =
    Box<dyn Fn() -> Pin<Box<dyn Future<Output = T> + Send + 'a>> + Send + 'a>;

pub fn package_async_function<'a, T, F, Fut>(func: F) -> BoxedAsyncFn<'a, T>
where
    F: Fn() -> Fut + Send + 'a,
    Fut: Future<Output = T> + Send + 'a,
{
    Box::new(move || Box::pin(func()))
}

pub type BoxedAsyncFnOnce<'a, T> =
    Box<dyn FnOnce() -> Pin<Box<dyn Future<Output = T> + Send + 'a>> + Send + 'a>;

pub fn package_async_function_once<'a, T, F, Fut>(func: F) -> BoxedAsyncFnOnce<'a, T>
where
    F: FnOnce() -> Fut + Send + 'a,
    Fut: Future<Output = T> + Send + 'a,
{
    Box::new(move || Box::pin(func()))
}

// The AsyncTimeout structure provides an awaitable function that will wait in a loop until it
// reaches a predefined timeout. Inside the loop, it will log a warning as it continues waiting at
// some fixed interval.
#[derive(Debug)]
pub struct AsyncTimeout {
    pub timeout: std::time::Duration,
    pub interval: std::time::Duration,
}

impl Default for AsyncTimeout {
    fn default() -> Self {
        Self {
            timeout: std::time::Duration::from_secs(3600),
            interval: std::time::Duration::from_secs(60),
        }
    }
}

pub enum AsyncTimeoutResult {
    Timeout,
    Cancelled,
}

impl AsyncTimeout {
    pub fn new() -> Self {
        Self::default()
    }

    /// Run the timeout waiter in a loop, printing out every `interval` seconds that it is still
    /// waiting. If the `cancellation_flag` is ever flipped to true, return `Cancelled`.
    pub async fn wait<F: Fn() -> Cow<'static, str>>(
        &self,
        resource: F,
        cancellation_flag: Option<Arc<AtomicBool>>,
    ) -> AsyncTimeoutResult {
        let start = std::time::Instant::now();
        loop {
            if start.elapsed() > self.timeout {
                return AsyncTimeoutResult::Timeout;
            }
            tokio::time::sleep(self.interval).await;
            if let Some(cancellation_flag) = &cancellation_flag {
                if cancellation_flag.load(std::sync::atomic::Ordering::Relaxed) {
                    return AsyncTimeoutResult::Cancelled;
                }
            }
            log::warn!(
                "Waiting for resource {} for {} seconds",
                resource(),
                start.elapsed().as_secs_f64()
            );
        }
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub struct AsyncTimeoutError {
    pub resource: Cow<'static, str>,
}

impl std::fmt::Display for AsyncTimeoutError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Timed out waiting for resource {}", self.resource)
    }
}

impl std::error::Error for AsyncTimeoutError {}

/// Wait for `fut` to complete concurrently with a timeout. If the timeout completes with status
/// Timeout before `fut` completes, return an error. If the timeout completes with status
/// Cancelled, finish waiting for `fut` and return the result.
pub async fn await_with_async_timeout<Fut, T, F: Fn() -> Cow<'static, str> + Clone>(
    resource: F,
    fut: Fut,
    timeout: AsyncTimeout,
    cancellation_flag: Option<Arc<AtomicBool>>,
) -> Result<T, AsyncTimeoutError>
where
    Fut: Future<Output = T> + Send,
{
    let timeout_fut = timeout.wait(resource.clone(), cancellation_flag);
    pin_mut!(fut, timeout_fut);
    tokio::select! {
        res = &mut timeout_fut => {
            match res {
                AsyncTimeoutResult::Timeout => {
                    return Err(AsyncTimeoutError { resource: resource() });
                }
                AsyncTimeoutResult::Cancelled => {}
            }
        },
        res = &mut fut => {
            return Ok(res);
        }
    };
    Ok(fut.await)
}
