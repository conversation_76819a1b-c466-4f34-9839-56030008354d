use anyhow::{anyhow, Result};
use ptree::TreeBuilder;
use serde::{Deserialize, Serialize};
use std::ops::Deref;

use crate::{
    ptree::MakePTree,
    schema::{BaseOptions, Field, Schema, TantivyField, TantivyType, TextOptions},
};

// Implementation note: the stringified form of these types must not contain any slashes.
#[derive(
    <PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>py, Serialize, Deserialize, PartialEq, Eq, Hash, PartialOrd, Ord,
)]
pub enum ObjectType {
    #[serde(rename = "experiment")]
    Experiment,
    #[default]
    #[serde(rename = "dataset")]
    Dataset,
    #[serde(rename = "prompt_session")]
    PromptSession,
    #[serde(rename = "project_logs")]
    ProjectLogs,
    #[serde(rename = "playground_logs")]
    PlaygroundLogs,
    #[serde(rename = "project_prompts")]
    ProjectPrompts,
    #[serde(rename = "project_functions")]
    ProjectFunctions,
    #[serde(rename = "project")]
    Project, // This is an amalgam of project_logs and experiment
}

impl std::fmt::Display for ObjectType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ObjectType::Experiment => write!(f, "experiment"),
            ObjectType::Dataset => write!(f, "dataset"),
            ObjectType::Project => write!(f, "project"),
            ObjectType::PromptSession => write!(f, "prompt_session"),
            ObjectType::ProjectLogs => write!(f, "project_logs"),
            ObjectType::PlaygroundLogs => write!(f, "playground_logs"),
            ObjectType::ProjectPrompts => write!(f, "project_prompts"),
            ObjectType::ProjectFunctions => write!(f, "project_functions"),
        }
    }
}

impl std::str::FromStr for ObjectType {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        match s {
            "experiment" => Ok(ObjectType::Experiment),
            "dataset" => Ok(ObjectType::Dataset),
            "prompt_session" => Ok(ObjectType::PromptSession),
            "project_logs" => Ok(ObjectType::ProjectLogs),
            "playground_logs" => Ok(ObjectType::PlaygroundLogs),
            "project_prompts" => Ok(ObjectType::ProjectPrompts),
            "project_functions" => Ok(ObjectType::ProjectFunctions),
            "project" => Ok(ObjectType::Project),
            _ => Err(anyhow!("Invalid ObjectType: {}", s)),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct ObjectId<'a> {
    val: &'a str,
}

impl Default for ObjectId<'_> {
    fn default() -> Self {
        ObjectId { val: "singleton" }
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct ObjectIdOwned {
    val: String,
}

impl Default for ObjectIdOwned {
    fn default() -> Self {
        ObjectId::default().to_owned()
    }
}

impl<'a> ObjectId<'a> {
    pub fn new(val: &'a str) -> Result<ObjectId<'a>> {
        validate_object_id(val)?;
        Ok(ObjectId { val })
    }

    pub fn to_owned(&self) -> ObjectIdOwned {
        ObjectIdOwned {
            val: self.val.to_string(),
        }
    }

    pub fn as_str(&self) -> &str {
        self.val
    }
}

impl ObjectIdOwned {
    pub fn new(val: String) -> Result<ObjectIdOwned> {
        validate_object_id(&val)?;
        Ok(ObjectIdOwned { val })
    }

    pub fn as_ref(&self) -> ObjectId {
        ObjectId { val: &self.val }
    }
}

impl<'a> Deref for ObjectId<'a> {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        self.val
    }
}

impl Deref for ObjectIdOwned {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        &self.val
    }
}

#[derive(Clone, Copy, Debug, Default, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct FullObjectId<'a> {
    pub object_type: ObjectType,
    pub object_id: ObjectId<'a>,
}

#[derive(Clone, Debug, Default, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct FullObjectIdOwned {
    pub object_type: ObjectType,
    pub object_id: ObjectIdOwned,
}

impl<'a> FullObjectId<'a> {
    pub fn to_owned(&self) -> FullObjectIdOwned {
        FullObjectIdOwned {
            object_type: self.object_type,
            object_id: self.object_id.to_owned(),
        }
    }
}

impl FullObjectIdOwned {
    pub fn as_ref(&self) -> FullObjectId {
        FullObjectId {
            object_type: self.object_type,
            object_id: self.object_id.as_ref(),
        }
    }
}

impl MakePTree for [FullObjectIdOwned] {
    fn label(&self) -> String {
        self.iter()
            .map(|o| o.to_string())
            .collect::<Vec<_>>()
            .join(", ")
    }
    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl MakePTree for Vec<FullObjectIdOwned> {
    fn label(&self) -> String {
        MakePTree::label(self.as_slice())
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        MakePTree::make_ptree(self.as_slice(), builder)
    }
}

#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct FullRowId<'a, 'b> {
    pub object_type: ObjectType,
    pub object_id: ObjectId<'a>,
    pub id: &'b str,
}

#[derive(Clone, Debug, Default, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct FullRowIdOwned {
    pub object_type: ObjectType,
    pub object_id: ObjectIdOwned,
    pub id: String,
}

impl<'a, 'b> FullRowId<'a, 'b> {
    pub fn from_full_object_id(full_object_id: FullObjectId<'a>, id: &'b str) -> FullRowId<'a, 'b> {
        FullRowId {
            object_type: full_object_id.object_type,
            object_id: full_object_id.object_id,
            id,
        }
    }

    pub fn to_owned(&self) -> FullRowIdOwned {
        FullRowIdOwned {
            object_type: self.object_type,
            object_id: self.object_id.to_owned(),
            id: self.id.to_string(),
        }
    }

    pub fn object_id(&self) -> FullObjectId {
        FullObjectId {
            object_type: self.object_type,
            object_id: self.object_id,
        }
    }
}

impl FullRowIdOwned {
    pub fn from_full_object_id(full_object_id: FullObjectIdOwned, id: String) -> Self {
        Self {
            object_type: full_object_id.object_type,
            object_id: full_object_id.object_id,
            id,
        }
    }

    pub fn as_ref(&self) -> FullRowId {
        FullRowId {
            object_type: self.object_type,
            object_id: self.object_id.as_ref(),
            id: &self.id,
        }
    }

    pub fn object_id_owned(&self) -> FullObjectIdOwned {
        self.as_ref().object_id().to_owned()
    }
}

impl<'a> std::fmt::Display for ObjectId<'a> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.val)
    }
}

impl std::fmt::Display for ObjectIdOwned {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_ref())
    }
}

impl std::str::FromStr for ObjectIdOwned {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        ObjectIdOwned::new(s.to_string())
    }
}

impl<'a> Serialize for ObjectId<'a> {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        serializer.serialize_str(self.val)
    }
}

impl Serialize for ObjectIdOwned {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        self.as_ref().serialize(serializer)
    }
}

impl<'de> Deserialize<'de> for ObjectIdOwned {
    fn deserialize<D: serde::Deserializer<'de>>(deserializer: D) -> Result<Self, D::Error> {
        let s = String::deserialize(deserializer)?;
        match s.parse::<ObjectIdOwned>() {
            Ok(object_id) => Ok(object_id),
            Err(e) => Err(serde::de::Error::custom(e)),
        }
    }
}

impl<'a> std::fmt::Display for FullObjectId<'a> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.object_type, self.object_id)
    }
}

impl std::fmt::Display for FullObjectIdOwned {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_ref())
    }
}

impl std::str::FromStr for FullObjectIdOwned {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        let mut parts = s.splitn(2, ':');
        let object_type = parts
            .next()
            .ok_or_else(|| anyhow!("Invalid object type in string {}", s))?;
        let object_id = parts
            .next()
            .ok_or_else(|| anyhow!("Invalid object ID in string {}", s))?;
        Ok(FullObjectIdOwned {
            object_type: object_type.parse()?,
            object_id: ObjectIdOwned::new(object_id.to_string())?,
        })
    }
}

impl<'a> Serialize for FullObjectId<'a> {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        serializer.serialize_str(self.to_string().as_str())
    }
}

impl Serialize for FullObjectIdOwned {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        self.as_ref().serialize(serializer)
    }
}

impl<'de> Deserialize<'de> for FullObjectIdOwned {
    fn deserialize<D: serde::Deserializer<'de>>(deserializer: D) -> Result<Self, D::Error> {
        let s = String::deserialize(deserializer)?;
        match s.parse::<FullObjectIdOwned>() {
            Ok(full_object_id) => Ok(full_object_id),
            Err(e) => Err(serde::de::Error::custom(e)),
        }
    }
}

impl<'a, 'b> std::fmt::Display for FullRowId<'a, 'b> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.object_id(), self.id)
    }
}

impl std::fmt::Display for FullRowIdOwned {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_ref())
    }
}

impl std::str::FromStr for FullRowIdOwned {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        let mut colon_indices = s.char_indices().filter(|(_, c)| *c == ':').map(|(i, _)| i);
        let _ = colon_indices
            .next()
            .ok_or_else(|| anyhow!("Invalid full row id string {}", s))?;
        let second_colon_index = colon_indices
            .next()
            .ok_or_else(|| anyhow!("Invalid full row id string {}", s))?;
        let full_object_id: FullObjectIdOwned = s[..second_colon_index].parse()?;
        let id = &s[second_colon_index + 1..];
        Ok(FullRowIdOwned {
            object_type: full_object_id.object_type,
            object_id: full_object_id.object_id,
            id: id.to_string(),
        })
    }
}

impl<'a, 'b> Serialize for FullRowId<'a, 'b> {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        serializer.serialize_str(self.to_string().as_str())
    }
}

impl Serialize for FullRowIdOwned {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        self.as_ref().serialize(serializer)
    }
}

impl<'de> Deserialize<'de> for FullRowIdOwned {
    fn deserialize<D: serde::Deserializer<'de>>(deserializer: D) -> Result<Self, D::Error> {
        let s = String::deserialize(deserializer)?;
        match s.parse::<FullRowIdOwned>() {
            Ok(full_row_id) => Ok(full_row_id),
            Err(e) => Err(serde::de::Error::custom(e)),
        }
    }
}

fn validate_object_id(object_id: &str) -> Result<()> {
    if object_id.contains(':') {
        return Err(anyhow!("Object ID cannot contain colons: {}", object_id));
    }
    Ok(())
}

pub fn make_object_schema(object_type: ObjectType) -> Result<Schema> {
    let base_fields = vec![
        string_fast_field("org_id", 1736212233459),
        string_fast_field("project_id", 1736212233462),
        json_tok_field("span_attributes", true, false, 1736212233464, 1736212233467),
        repeated_string_fast_field("tags", 1736212233469, 1736212233472),
        // TODO: We may want to unroll this into its composite values for faster lookup
        json_data_field("origin", 1736212233474),
    ];

    let mut fields = base_fields;
    match object_type {
        ObjectType::ProjectLogs
        | ObjectType::Experiment
        | ObjectType::Project
        | ObjectType::PlaygroundLogs => {
            // Add all of the object ids so that all log types have identical schemas. This will allow
            // us to be able to query across them.
            fields.push(string_fast_field("experiment_id", 1736212233477));
            fields.push(string_fast_field("prompt_session_id", 1736212233480));
            fields.push(string_fast_field("log_id", 1736212233482));

            fields.extend_from_slice(&[
                json_tok_field("input", false, false, 1736212233485, 1736212233487),
                json_tok_field("output", false, false, 1736212233490, 1736212233492),
                json_tok_field("expected", false, false, 1736212233495, 1736212233497),
                json_tok_field("error", true, false, 1736212233500, 1736212233502),
                json_data_field("scores", 1736212233505),
                json_tok_field("metadata", true, true, 1736212233507, 1736212233510),
                json_data_field("metrics", 1736212233512),
                json_data_field("context", 1736212233515),
            ]);
        }
        ObjectType::Dataset => {
            fields.extend_from_slice(&[
                string_fast_field("dataset_id", 1736212233517),
                json_tok_field("input", false, false, 1736212233520, 1736212233523),
                json_tok_field("expected", false, false, 1736212233525, 1736212233528),
                json_tok_field("metadata", true, true, 1736212233530, 1736212233533),
            ]);
        }
        _ => {
            return Err(anyhow!("Unsupported object type: {}", object_type));
        }
    };

    Schema::new(object_type.to_string(), fields, Default::default())
}

fn string_fast_field(name: &str, field_ts: u64) -> Field {
    Field {
        name: name.to_string(),
        tantivy: vec![TantivyField {
            name: name.to_string(),
            field_ts,
            repeated: false,
            lossy_fast_field: true,
            field_type: TantivyType::Str(TextOptions {
                stored: true,
                fast: true,
                tokenize: false,
            }),
        }],
    }
}

fn repeated_string_fast_field(name: &str, field_ts0: u64, field_ts1: u64) -> Field {
    Field {
        name: name.to_string(),
        tantivy: vec![
            TantivyField {
                name: name.to_string(),
                field_ts: field_ts0,
                repeated: true,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: true,
                    fast: true,
                    tokenize: false,
                }),
            },
            TantivyField {
                name: format!("{}_text", name),
                field_ts: field_ts1,
                repeated: true,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: false,
                    fast: false,
                    tokenize: true,
                }),
            },
        ],
    }
}

fn json_data_field(name: &str, field_ts: u64) -> Field {
    Field {
        name: name.to_string(),
        tantivy: vec![TantivyField {
            name: name.to_string(),
            field_ts,
            repeated: false,
            lossy_fast_field: false,
            field_type: TantivyType::Json(TextOptions {
                stored: true,
                fast: true,
                tokenize: false,
            }),
        }],
    }
}

fn json_tok_field(
    name: &str,
    columnar: bool,
    lossy_fast_field: bool,
    field_ts0: u64,
    field_ts1: u64,
) -> Field {
    Field {
        name: name.to_string(),
        tantivy: vec![
            TantivyField {
                name: format!("{}_json", name),
                field_ts: field_ts0,
                repeated: false,
                lossy_fast_field,
                field_type: TantivyType::Json(TextOptions {
                    stored: true,
                    fast: columnar,
                    tokenize: false,
                }),
            },
            TantivyField {
                name: format!("{}_text", name),
                field_ts: field_ts1,
                repeated: false,
                lossy_fast_field: false,
                field_type: TantivyType::Str(TextOptions {
                    stored: false,
                    fast: false,
                    tokenize: true,
                }),
            },
        ],
    }
}

pub const PAGINATION_KEY_FIELD_TYPE: TantivyType = TantivyType::U64(BaseOptions {
    stored: true,
    fast: true,
});

pub const STRING_ID_FIELD_TYPE: TantivyType = TantivyType::Str(TextOptions {
    stored: true,
    fast: true,
    tokenize: false,
});
