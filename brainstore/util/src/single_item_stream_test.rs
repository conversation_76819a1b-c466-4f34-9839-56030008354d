use async_stream::stream;
use futures::pin_mut;

use crate::single_item_stream::SingleItemStream;

#[tokio::test]
async fn test_single_item_stream() {
    let my_stream = stream! {
        yield(10);
    };
    pin_mut!(my_stream);

    let mut single_item_stream = SingleItemStream::new(my_stream);
    {
        let x = single_item_stream.next().await;
        assert_eq!(x.unwrap().item, 10);
    }
    {
        let x = single_item_stream.next().await;
        assert!(x.is_none());
    }
}
