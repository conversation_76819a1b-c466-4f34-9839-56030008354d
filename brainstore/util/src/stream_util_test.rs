use crate::stream_util::merge_ordered_streams;
use futures::stream::{self, StreamExt};
use tokio::test;

#[test]
async fn test_empty_streams() {
    let streams: Vec<stream::Empty<i32>> = vec![];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, Vec::<i32>::new());
}

#[test]
async fn test_single_stream() {
    let stream1 = stream::iter(vec![1, 2, 3, 4, 5]);
    let streams = vec![stream1];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![1, 2, 3, 4, 5]);
}

#[test]
async fn test_two_streams_ascending() {
    let stream1 = stream::iter(vec![1, 3, 5, 7, 9]);
    let stream2 = stream::iter(vec![2, 4, 6, 8, 10]);
    let streams = vec![stream1, stream2];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
}

#[test]
async fn test_two_streams_descending() {
    let stream1 = stream::iter(vec![9, 7, 5, 3, 1]);
    let stream2 = stream::iter(vec![10, 8, 6, 4, 2]);
    let streams = vec![stream1, stream2];
    let merged = merge_ordered_streams(streams, |a, b| b.cmp(a)); // Reverse ordering
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![10, 9, 8, 7, 6, 5, 4, 3, 2, 1]);
}

#[test]
async fn test_three_streams() {
    let stream1 = stream::iter(vec![1, 4, 7, 10]);
    let stream2 = stream::iter(vec![2, 5, 8, 11]);
    let stream3 = stream::iter(vec![3, 6, 9, 12]);
    let streams = vec![stream1, stream2, stream3];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
}

#[test]
async fn test_different_length_streams() {
    let stream1 = stream::iter(vec![1, 3, 5, 7, 9]);
    let stream2 = stream::iter(vec![2, 4, 6]);
    let stream3 = stream::iter(vec![0, 8, 10, 12]);
    let streams = vec![stream1, stream2, stream3];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12]);
}

#[test]
async fn test_duplicate_values() {
    let stream1 = stream::iter(vec![1, 3, 3, 5, 7]);
    let stream2 = stream::iter(vec![2, 3, 4, 6, 8]);
    let streams = vec![stream1, stream2];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![1, 2, 3, 3, 3, 4, 5, 6, 7, 8]);
}

#[test]
async fn test_empty_streams_in_mix() {
    let stream1 = stream::iter(vec![1, 3, 5]);
    let stream2 = stream::iter(Vec::<i32>::new());
    let stream3 = stream::iter(vec![2, 4, 6]);
    let streams = vec![stream1, stream2, stream3];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![1, 2, 3, 4, 5, 6]);
}

#[test]
async fn test_custom_ordering() {
    // Test with a custom ordering function that sorts by the last digit
    let stream1 = stream::iter(vec![12, 23, 34, 45]);
    let stream2 = stream::iter(vec![11, 22, 33, 44]);
    let streams = vec![stream1, stream2];
    let merged = merge_ordered_streams(streams, |a, b| (a % 10).cmp(&(b % 10)));
    let result: Vec<i32> = merged.collect().await;
    assert_eq!(result, vec![11, 12, 22, 23, 33, 34, 44, 45]);
}

#[test]
async fn test_string_streams() {
    let stream1 = stream::iter(vec!["apple", "banana", "cherry"]);
    let stream2 = stream::iter(vec!["date", "elderberry", "fig"]);
    let streams = vec![stream1, stream2];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<&str> = merged.collect().await;
    assert_eq!(
        result,
        vec!["apple", "banana", "cherry", "date", "elderberry", "fig"]
    );
}

#[test]
async fn test_large_streams() {
    // Test with larger streams to ensure performance is reasonable
    let stream1 = stream::iter((1..=1000).step_by(3));
    let stream2 = stream::iter((2..=1000).step_by(3));
    let stream3 = stream::iter((3..=1000).step_by(3));
    let streams = vec![stream1, stream2, stream3];
    let merged = merge_ordered_streams(streams, |a, b| a.cmp(b));
    let result: Vec<i32> = merged.collect().await;

    // Verify the result is sorted
    for i in 1..result.len() {
        assert!(
            result[i - 1] <= result[i],
            "Result is not sorted at index {}",
            i
        );
    }

    // Verify all numbers from 1 to 1000 are present
    let mut expected = vec![false; 1000];
    for &num in &result {
        if num > 0 && num <= 1000 {
            expected[num as usize - 1] = true;
        }
    }
    assert!(
        expected.iter().all(|&x| x),
        "Not all numbers from 1 to 1000 are present"
    );
}
