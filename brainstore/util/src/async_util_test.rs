use std::sync::{atomic::AtomicBool, Arc};

use crate::async_util::{await_with_async_timeout, AsyncTimeout, AsyncTimeoutError};

#[tokio::test]
async fn test_async_timeout_success() {
    // The task completes first.
    let timeout = AsyncTimeout {
        timeout: std::time::Duration::from_secs(10),
        interval: std::time::Duration::from_secs(1),
    };

    let task = async {
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
        "success"
    };

    let result = await_with_async_timeout(|| "my task".into(), task, timeout, None).await;
    assert_eq!(result, Ok("success"));
}

#[tokio::test]
async fn test_async_timeout_timeout() {
    // The task completes first.
    let timeout = AsyncTimeout {
        timeout: std::time::Duration::from_millis(100),
        interval: std::time::Duration::from_millis(1),
    };

    let task = async {
        tokio::time::sleep(std::time::Duration::from_secs(10)).await;
        "success"
    };

    let result = await_with_async_timeout(|| "my task".into(), task, timeout, None).await;
    assert_eq!(
        result,
        Err(AsyncTimeoutError {
            resource: "my task".into()
        })
    );
}

#[tokio::test]
async fn test_async_timeout_cancelled() {
    let cancellation_flag = Arc::new(AtomicBool::new(false));

    // The timeout completes first, but is cancelled by the task.
    let timeout = AsyncTimeout {
        timeout: std::time::Duration::from_millis(100),
        interval: std::time::Duration::from_millis(1),
    };

    let task = {
        let cancellation_flag = cancellation_flag.clone();
        async move {
            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
            cancellation_flag.store(true, std::sync::atomic::Ordering::Relaxed);
            tokio::time::sleep(std::time::Duration::from_millis(500)).await;
            "success"
        }
    };

    let result = await_with_async_timeout(
        || "my task".into(),
        task,
        timeout,
        Some(cancellation_flag.clone()),
    )
    .await;
    assert_eq!(result, Ok("success"));
    assert!(cancellation_flag.load(std::sync::atomic::Ordering::Relaxed));
}
