pub mod async_util;
pub mod config;
pub mod functional;
pub mod global_opts;
pub mod json;
pub mod max_counter;
pub mod ptree;
pub mod schema;
pub mod single_item_stream;
pub mod spawn_blocking_util;
pub mod stream_util;
pub mod system_types;
pub mod test_util;
pub mod tracer;
pub mod unsafe_util;
pub mod url_util;
pub mod xact;

use std::str::FromStr;

// Export common dependencies for other crates to use.
pub use anyhow;
pub use async_stream;
pub use async_trait;
use byte_unit::UnitType;
pub use chrono;
pub use futures;
pub use itertools;
pub use once_cell;
pub use serde_json;
pub use serde_yaml;
pub use sha2;
pub use sysinfo;
pub use tokio;
pub use url;
pub use uuid;

pub use anyhow::{bail, Result};
pub use serde_json::Value;

use uuid::Uuid;

pub fn new_id() -> String {
    Uuid::new_v4().to_string()
}

pub fn now_ts_millis() -> i64 {
    let x = chrono::Utc::now();
    x.timestamp_millis()
}

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub struct ByteSize(byte_unit::Byte);

impl FromStr for ByteSize {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        byte_unit::Byte::parse_str(s, true)
            .map_err(|e| anyhow::anyhow!("Invalid byte size: {}", e))
            .map(|b| Self(b))
    }
}

impl Into<usize> for ByteSize {
    fn into(self) -> usize {
        self.0.as_u64() as usize
    }
}

impl From<usize> for ByteSize {
    fn from(size: usize) -> Self {
        Self(byte_unit::Byte::from_u64(size as u64))
    }
}

impl From<u64> for ByteSize {
    fn from(size: u64) -> Self {
        Self(byte_unit::Byte::from_u64(size))
    }
}

impl From<u32> for ByteSize {
    fn from(size: u32) -> Self {
        Self(byte_unit::Byte::from_u64(size as u64))
    }
}

impl std::fmt::Display for ByteSize {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:.2}", self.0.get_appropriate_unit(UnitType::Binary))
    }
}

impl ByteSize {
    pub fn parse_to_usize(s: &str) -> Result<usize> {
        Self::from_str(s).map(|b| b.into())
    }
    pub fn parse_to_u64(s: &str) -> Result<u64> {
        Self::from_str(s).map(|b| b.0.as_u64())
    }
    pub fn parse_to_u32(s: &str) -> Result<u32> {
        Self::from_str(s).map(|b| b.0.as_u64()).and_then(|b| {
            u32::try_from(b).map_err(|_| anyhow::anyhow!("Value {} is too large to fit in u32", b))
        })
    }
}

#[cfg(test)]
mod async_util_test;
mod await_spawn_blocking_doctest;
#[cfg(test)]
mod json_test;
mod single_item_stream_doctest;
#[cfg(test)]
mod single_item_stream_test;
#[cfg(test)]
mod spawn_blocking_util_test;
#[cfg(test)]
mod stream_util_test;
#[cfg(test)]
mod system_types_test;
