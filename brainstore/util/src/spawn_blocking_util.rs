/// A macro to await a blocking closure in a tokio context. Normally, running spawn_blocking
/// requires a fully static closure, which means all captured variables must be copied or moved.
/// This makes sense if we don't know when the future will be awaited, but if we want to await it
/// immediately, this static requirement is unnecessarily restrictive.
///
/// The macro accepts a closure whose arguments are the referenced variables as well as the
/// variables we want to pass to the closure. Inside the macro, we transmute the references to
/// static just for the duration of the closure execution, run the closure in a blocking context
/// and return the awaited result.
///
/// You can pass both immutable references and mutable references to the closure. The immutable
/// references must come first, separated by commas, and the mutable references must come after,
/// separated by semicolons. For example:
///
/// let a = 20;
/// let mut b = 10;
/// {
///     let a_ref = &a;
///     let b_ref = &mut b;
///     await_spawn_blocking!(|a: &i32, b: &mut i32| {
///         *b += *a;
///     }, a_ref; b_ref).unwrap();
///     assert_eq!(b, 30);
/// }
///
/// NOTE: If your function returns a value which references its input arguments, do not explicitly
/// label those arguments as having 'static lifetime. This will pass through the compiler since the
/// arguments are transmuted to static, even though the inputs really aren't. If your arguments are
/// truly static references, then you don't need await_spawn_blocking to launder them into your
/// function and can capture them normally.
///
/// E.g. DO NOT DO THIS:
///
/// struct MyResult {
///     pub a: &'static str,
/// }
///
/// pub async fn my_test_fn<'a>(a: &'a str) -> Result<MyResult> {
///     await_spawn_blocking!(move |a: &'static str| {
///         Ok(MyResult { a })
///     }, a)?
/// }
#[macro_export]
macro_rules! await_spawn_blocking {
    ($closure:expr $(, $args:ident)* $(; $mut_args:ident)*) => {{
        {
            let _reserved_span = tracing::Span::current();
            let _resource_fn = || format!("{}:{}", file!(), line!()).into();
            let bound_closure = {
                $(
                    let $args = unsafe { std::mem::transmute::<&'_ _, &'static _>($args) };
                )*
                $(
                    let $mut_args = unsafe { std::mem::transmute::<&'_ mut _, &'static mut _>($mut_args) };
                )*
                move || {
                    let _reserved_span_guard = _reserved_span.enter();
                    $closure($($args,)* $($mut_args,)*)
                }
            };
            $crate::spawn_blocking_util::spawn_blocking_with_async_timeout(
                &tokio::runtime::Handle::current(),
                bound_closure,
                $crate::async_util::AsyncTimeout::default(),
                _resource_fn,
            ).await
        }
    }};
}

// A utility function for starting a spawn_blocking task out of the given tokio handle which is
// paired with an AsyncTimeout. The timeout is cancelled once the spawn blocking task starts,
// indicating it has launched in the tokio thread pool.
pub fn spawn_blocking_with_async_timeout<F, R>(
    handle: &tokio::runtime::Handle,
    closure: F,
    async_timeout: crate::async_util::AsyncTimeout,
    resource_fn: impl Fn() -> std::borrow::Cow<'static, str> + Clone,
) -> impl std::future::Future<
    Output = Result<Result<R, tokio::task::JoinError>, crate::async_util::AsyncTimeoutError>,
>
where
    F: FnOnce() -> R + Send + 'static,
    R: Send + 'static,
{
    let cancellation_flag = std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false));
    let bound_closure = {
        let cancellation_flag = cancellation_flag.clone();
        move || {
            cancellation_flag.store(true, std::sync::atomic::Ordering::Relaxed);
            closure()
        }
    };
    let bound_fut = tracing::Instrument::instrument(
        handle.spawn_blocking(bound_closure),
        tracing::Span::current(),
    );
    crate::async_util::await_with_async_timeout(
        resource_fn,
        bound_fut,
        async_timeout,
        Some(cancellation_flag),
    )
}
