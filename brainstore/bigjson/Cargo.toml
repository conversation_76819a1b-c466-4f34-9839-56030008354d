[package]
name = "bigjson"
version = "0.1.0"
edition = "2021"

[dependencies]
# This fork is really annoying, but if we use serde_json directly, <PERSON><PERSON> will deduplicate the package
# and force everything (across crates) to use the same feature flags. So, I forked it, and just found
# the commit corresponding to v1.0.140 (https://github.com/serde-rs/json/commit/762783414e6c4f8d670c9d87eb04913efb80d3be)
big_serde_json = { package = "serde_json", git="https://github.com/ankrgyl/serde-json.git", rev="762783414e6c4f8d670c9d87eb04913efb80d3be", features = ["unbounded_depth", "arbitrary_precision"] }
