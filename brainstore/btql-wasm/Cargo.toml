[package]
name = "btql-wasm"
version = "0.1.0"
edition = "2021"
description = "WASM bindings for BTQL"

[dependencies]
# This is a workaround for the wasm-bindgen issue with getrandom.
getrandom = { version = "0.2.15", features = ["js"] }

wasm-bindgen = "0.2.100"
util = { path = "../util" }
btql = { path = "../btql", default-features = false, features = ["wasm"] }
serde-wasm-bindgen = "0.6.5"
console_error_panic_hook = "0.1.7"

[lib]
crate-type = ["cdylib"]
