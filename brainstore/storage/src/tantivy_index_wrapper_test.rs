use serde_json::json;
use tantivy::doc;
use util::schema::{Field, Schem<PERSON>, TantivyField, TantivyType, TextOptions};

use crate::{
    tantivy_index::{
        check_and_set_meta_schema, collect_meta_json, invert_document, make_tantivy_schema,
        write_meta_json, TantivyIndexScope,
    },
    tantivy_index_test_util::read_all_documents,
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
    test_util::TmpDirStore,
};

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    Schema::new(
        "test".to_string(),
        vec![Field {
            name: "field1".to_string(),
            tantivy: vec![TantivyField {
                name: "field1".to_string(),
                field_ts: 1,
                field_type: TantivyType::Str(text_options.clone()),
                repeated: false,
                lossy_fast_field: false,
            }],
        }],
        Default::default(),
    )
    .unwrap()
}

fn basic_schema2() -> Schema {
    let orig_schema = basic_schema();
    let mut new_fields = orig_schema.fields().clone();
    let field1 = new_fields[0].clone();
    new_fields.push(Field {
        name: "field2".to_string(),
        tantivy: vec![TantivyField {
            name: "field2".to_string(),
            field_ts: 2,
            ..field1.tantivy[0].clone()
        }],
    });
    Schema::new(orig_schema.name().clone(), new_fields, Default::default()).unwrap()
}

#[test]
fn test_write_with_schema_update() {
    let tmp_dir_store = TmpDirStore::new();
    let index_scope = TantivyIndexScope::Segment(Default::default());
    eprintln!("Writing to tmp dir {:?}", tmp_dir_store.tmp_dir.path());
    {
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            basic_schema(),
            &tmp_dir_store.store_info.prefix,
            &index_scope,
        )
        .unwrap();

        // Write a document, and check that we can read it back.
        {
            let mut writer = index.make_writer_blocking(&Default::default()).unwrap();
            let field1 = index.tantivy_schema().get_field("field1").unwrap();
            writer
                .add_document(doc!(
                    field1 => "hello"
                ))
                .unwrap();
            writer.commit().unwrap();
        }
        let docs = read_all_documents(&index.index.reader().unwrap());
        let docs = docs
            .into_values()
            .map(|doc| invert_document(&doc, &index.writable_schema.invert_fields).unwrap())
            .collect::<Vec<_>>();
        assert_eq!(docs, vec![json!({"field1":"hello"})]);
    }

    // If we try to create an index with an updated schema, it should fail.
    assert!(ReadWriteTantivyIndexWrapper::new_blocking(
        tmp_dir_store.store_info.directory.clone(),
        basic_schema2(),
        &tmp_dir_store.store_info.prefix,
        &index_scope,
    )
    .is_err());

    // But if we "flash" the index metadata with the updated schema, it should work.
    let async_runtime = tokio::runtime::Runtime::new().unwrap();
    async_runtime.block_on(async {
        let index_meta = collect_meta_json(
            tmp_dir_store.store_info.directory.as_ref(),
            &index_scope.path(&tmp_dir_store.store_info.prefix),
        )
        .await
        .unwrap()
        .unwrap();
        let index_meta = check_and_set_meta_schema(
            index_meta,
            &make_tantivy_schema(&basic_schema2()).unwrap().schema,
        )
        .unwrap();
        write_meta_json(
            tmp_dir_store.store_info.directory.as_ref(),
            &index_scope.path(&tmp_dir_store.store_info.prefix),
            &index_meta,
        )
        .await
        .unwrap();
    });

    {
        let index = ReadWriteTantivyIndexWrapper::new_blocking(
            tmp_dir_store.store_info.directory.clone(),
            basic_schema2(),
            &tmp_dir_store.store_info.prefix,
            &TantivyIndexScope::Segment(Default::default()),
        )
        .unwrap();

        // Write a document, and check that we can read it back.
        {
            let mut writer = index.make_writer_blocking(&Default::default()).unwrap();
            let field1 = index.tantivy_schema().get_field("field1").unwrap();
            let field2 = index.tantivy_schema().get_field("field2").unwrap();
            writer
                .add_document(doc!(
                    field1 => "goodbye",
                    field2 => "friend"
                ))
                .unwrap();
            writer.commit().unwrap();
        }
        let docs = read_all_documents(&index.index.reader().unwrap());
        let mut docs = docs
            .into_values()
            .map(|doc| invert_document(&doc, &index.writable_schema.invert_fields).unwrap())
            .collect::<Vec<_>>();
        docs.sort_by(|a, b| {
            a["field1"]
                .as_str()
                .unwrap()
                .cmp(b["field1"].as_str().unwrap())
        });
        assert_eq!(
            docs,
            vec![
                json!({"field1":"goodbye", "field2": "friend"}),
                json!({"field1":"hello"})
            ]
        );
    }
}
