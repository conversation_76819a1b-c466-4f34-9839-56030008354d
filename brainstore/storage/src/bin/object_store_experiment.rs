use object_store::path::Path as ObjectStorePath;
use object_store::ObjectStore;
use std::env;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use util::anyhow::{anyhow, Result};
use util::futures::future::join_all;
use util::futures::StreamExt;
use util::url::Url;
use util::url_util::ObjectStoreType;
use util::uuid::Uuid;

use storage::config_with_store::url_to_store;

/// Print usage information
fn print_usage() {
    println!("Usage: object_store_experiment");
    println!();
    println!("Required environment variables:");
    println!("  OPERATION       - The operation to perform: 'insert', 'delete', or 'list'");
    println!("  S3_URL          - The URL of the S3 bucket (e.g., 's3://my-bucket/prefix')");
    println!("  NUM_FILES       - The number of files to insert, delete, or list (max for list)");
    println!();
    println!("Example:");
    println!(
        "  OPERATION=insert S3_URL=s3://my-bucket/prefix NUM_FILES=1000 object_store_experiment"
    );
    println!(
        "  OPERATION=delete S3_URL=s3://my-bucket/prefix NUM_FILES=500 object_store_experiment"
    );
    println!("  OPERATION=list S3_URL=s3://my-bucket/prefix NUM_FILES=100 object_store_experiment");
}

/// Utility function to get an S3 URL from an environment variable and convert it to an object store
fn get_s3_store() -> Result<(Arc<dyn ObjectStore>, ObjectStoreType, ObjectStorePath)> {
    let s3_url = env::var("S3_URL").expect("S3_URL environment variable must be set");
    let url = Url::parse(&s3_url)?;
    let (store, store_type, path) = url_to_store(&url)?;
    Ok((Arc::from(store), store_type, path))
}

/// Insert files into the object store
async fn insert_files() -> Result<()> {
    // Get the number of files to insert from environment variable
    let num_files = env::var("NUM_FILES")
        .expect("NUM_FILES environment variable must be set")
        .parse::<usize>()
        .expect("NUM_FILES must be a valid number");

    // Get the S3 store
    let (store, store_type, prefix) = get_s3_store()?;

    println!(
        "Inserting {} files into {:?} store at prefix {:?}",
        num_files, store_type, prefix
    );

    // Create files with random names
    let content = "abcd";

    let total_files_inserted = AtomicUsize::new(0);

    let res = join_all(
        (0..num_files)
            .map(|i| {
                let prefix = &prefix;
                let store = &store;
                let content = &content;
                let total_files_inserted = &total_files_inserted;
                async move {
                    // Generate a random filename
                    let random_name: String = Uuid::new_v4().to_string();
                    let file_path = prefix.child(format!("{}_{}", i, random_name));
                    // Put the file in the store
                    store
                        .put(&file_path, content.as_bytes().to_vec().into())
                        .await?;
                    total_files_inserted.fetch_add(1, Ordering::Relaxed);
                    if total_files_inserted.load(Ordering::Relaxed) % 1000 == 0 {
                        println!(
                            "Inserted {} files",
                            total_files_inserted.load(Ordering::Relaxed)
                        );
                    }
                    Ok::<_, object_store::Error>(())
                }
            })
            .collect::<Vec<_>>(),
    )
    .await;
    for r in res {
        r.unwrap();
    }
    println!("Successfully inserted {} files", num_files);
    Ok(())
}

/// Delete files from the object store
async fn delete_files() -> Result<()> {
    // Get the number of files to delete from environment variable
    let num_files_to_delete = env::var("NUM_FILES")
        .expect("NUM_FILES environment variable must be set")
        .parse::<usize>()
        .expect("NUM_FILES must be a valid number");

    // Get the S3 store
    let (store, store_type, prefix) = get_s3_store()?;

    println!(
        "Deleting {} files from {:?} store at prefix {:?}",
        num_files_to_delete, store_type, prefix
    );

    // List files in the store
    let list_stream = store
        .list(Some(&prefix))
        .take(num_files_to_delete)
        .map(|meta| meta.map(|meta| meta.location))
        .boxed();

    // Delete the files
    let mut delete_stream = store.delete_stream(list_stream);
    let mut num_deleted = 0;
    while let Some(result) = delete_stream.next().await {
        match result {
            Ok(_) => {
                num_deleted += 1;
            }
            Err(e) => {
                println!("Error deleting file: {:?}", e);
            }
        }
        if num_deleted % 1000 == 0 {
            println!("Deleted {} files", num_deleted);
        }
    }

    println!("Deleted a total of {} files", num_deleted);
    Ok(())
}

/// List files from the object store
async fn list_files() -> Result<()> {
    // Get the maximum number of files to list from environment variable
    let max_files_to_list = env::var("NUM_FILES")
        .expect("NUM_FILES environment variable must be set")
        .parse::<usize>()
        .expect("NUM_FILES must be a valid number");

    // Get the S3 store
    let (store, store_type, prefix) = get_s3_store()?;

    println!(
        "Listing up to {} files from {:?} store at prefix {:?}",
        max_files_to_list, store_type, prefix
    );

    // List files in the store
    let mut list_stream = store.list(Some(&prefix)).take(max_files_to_list);

    let mut num_listed = 0;
    while let Some(result) = list_stream.next().await {
        match result {
            Ok(meta) => {
                println!("File {}: {:?}", num_listed + 1, meta.location);
                num_listed += 1;
            }
            Err(e) => {
                println!("Error listing file: {:?}", e);
            }
        }
        if num_listed % 1000 == 0 {
            println!("Listed {} files", num_listed);
        }
    }

    println!("Listed a total of {} files", num_listed);
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Check if the operation environment variable is set
    let operation = match env::var("OPERATION") {
        Ok(op) => op,
        Err(_) => {
            println!("Error: OPERATION environment variable must be set");
            print_usage();
            return Err(anyhow!("OPERATION environment variable must be set"));
        }
    };

    // Check if NUM_FILES is set
    if env::var("NUM_FILES").is_err() {
        println!("Error: NUM_FILES environment variable must be set");
        print_usage();
        return Err(anyhow!("NUM_FILES environment variable must be set"));
    }

    // Execute the requested operation
    match operation.to_lowercase().as_str() {
        "insert" => insert_files().await,
        "delete" => delete_files().await,
        "list" => list_files().await,
        _ => {
            println!(
                "Error: Invalid operation '{}'. Must be 'insert', 'delete', or 'list'",
                operation
            );
            print_usage();
            Err(anyhow!(
                "Invalid operation '{}'. Must be 'insert', 'delete', or 'list'",
                operation
            ))
        }
    }
}
