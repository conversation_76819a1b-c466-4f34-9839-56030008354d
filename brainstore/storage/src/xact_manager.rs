use std::sync::atomic::{AtomicU64, Ordering};
use util::async_trait::async_trait;
use util::{anyhow::Result, xact::TransactionId};

#[async_trait]
pub trait TransactionManager: Send + Sync {
    async fn next_xact_id(&self) -> Result<TransactionId>;

    // See xact.rs for an explanation of how this works:
    fn build_next_xact_id(&self, xact_id_raw: u64, ts: u64) -> TransactionId {
        let ret = (0x0de1u64 << 48) | ((ts & 0xffffffffffff) << 16) | (xact_id_raw & 0xffff);
        TransactionId(ret)
    }

    async fn status(&self) -> Result<String>;
}

#[derive(Debug, Default)]
pub struct MemoryTransactionManager {
    xact_id: AtomicU64,
}

#[async_trait]
impl TransactionManager for MemoryTransactionManager {
    async fn next_xact_id(&self) -> Result<TransactionId> {
        let ts = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs();
        let xact_id = self.xact_id.fetch_add(1, Ordering::Relaxed);
        Ok(self.build_next_xact_id(xact_id, ts))
    }

    async fn status(&self) -> Result<String> {
        Ok("MemoryTransactionManager is ok".into())
    }
}
