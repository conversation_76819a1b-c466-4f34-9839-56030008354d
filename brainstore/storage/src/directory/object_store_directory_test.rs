use std::collections::HashSet;
use std::env;
use std::io::Write;
use std::path::Path;
use std::sync::Arc;

use object_store::{local::LocalFileSystem, Error};
use tantivy::{
    collector::TopDocs, directory::TerminatingWrite, query::QueryParser, schema::Value, Directory,
    IndexSettings, IndexWriter, ReloadPolicy, TantivyDocument,
};
use tempfile::TempDir;
use util::futures::{stream, StreamExt};

use crate::directory::async_directory::AsyncDirectoryArc;

use super::{
    cached_directory::{FileCacheDirectory, FileCacheOpts},
    object_store_directory::ObjectStoreDirectory,
    prefix_directory::PrefixDirectory,
};

fn create_test_directory(use_mmap: bool) -> (TempDir, Box<AsyncDirectoryArc>) {
    let tmp_dir = TempDir::new().unwrap();
    let tmp_path = tmp_dir.path().to_path_buf();
    println!("tmp_path: {:?}", tmp_path);

    let store = Arc::new(LocalFileSystem::new_with_prefix(tmp_path.clone()).unwrap());

    let base_directory = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));
    let prefix_directory = AsyncDirectoryArc::new(PrefixDirectory {
        base_directory,
        prefix: std::path::Path::new("test_prefix").to_path_buf(),
    });
    let directory: Box<AsyncDirectoryArc> = Box::new(if use_mmap {
        AsyncDirectoryArc::new(
            FileCacheDirectory::new(prefix_directory, None, FileCacheOpts::default()).unwrap(),
        )
    } else {
        prefix_directory
    });

    (tmp_dir, directory)
}

#[test]
fn test_object_store_directory_basic_operations() {
    for use_mmap in [false, true] {
        let (tmp_dir, directory) = create_test_directory(use_mmap);

        // Test writing a file
        let test_content = b"Hello, world!";
        directory
            .atomic_write(Path::new("test.txt"), test_content)
            .unwrap();

        // Test reading the file
        let read_content = directory.atomic_read(Path::new("test.txt")).unwrap();
        assert_eq!(read_content, test_content);

        // Test file existence
        assert!(directory.exists(Path::new("test.txt")).unwrap());
        assert!(!directory.exists(Path::new("nonexistent.txt")).unwrap());

        // Test deleting the file
        directory.delete(Path::new("test.txt")).unwrap();
        assert!(!directory.exists(Path::new("test.txt")).unwrap());

        // Deleting a non-existent file should return a FileDoesNotExist error
        match directory.delete(Path::new("test.txt")) {
            Ok(_) => panic!("Expected FileDoesNotExist error"),
            Err(e) => match e {
                tantivy::directory::error::DeleteError::FileDoesNotExist(_) => (),
                _ => panic!("Unexpected error: {:?}", e),
            },
        }

        // If KEEP_TEST_DIR environment variable is set, don't clean up the temp directory
        if env::var("KEEP_TEST_DIR").is_ok() {
            let kept_path = tmp_dir.keep();
            println!("Test directory kept at: {:?}", kept_path);
        }
    }
}

#[test]
fn test_object_store_directory_file_handle() {
    for use_mmap in [false, true] {
        let (_tmp_dir, directory) = create_test_directory(use_mmap);

        // Write a test file
        let test_content = b"This is a test file content.";
        directory
            .atomic_write(Path::new("test_handle.txt"), test_content)
            .unwrap();

        // Get a file handle
        let file_handle = directory
            .get_file_handle(Path::new("test_handle.txt"))
            .unwrap();

        // Test reading from the file handle
        {
            let read_content = file_handle.read_bytes(0..test_content.len()).unwrap();
            assert_eq!(read_content.as_slice(), test_content);
        }

        // Test partial read
        {
            let partial_content = file_handle.read_bytes(5..10).unwrap();
            assert_eq!(partial_content.as_slice(), b"is a ");
        }

        // Test file length
        assert_eq!(file_handle.len(), test_content.len());
    }
}

#[test]
fn test_object_store_directory_open_write() {
    for use_mmap in [false, true] {
        let (_tmp_dir, directory) = create_test_directory(use_mmap);

        // Open a file for writing
        let mut writer = directory.open_write(Path::new("write_test.txt")).unwrap();

        // Write some content
        writer.write_all(b"Hello, ").unwrap();
        writer.write_all(b"world!").unwrap();
        writer.flush().unwrap();
        writer.terminate().unwrap();

        // Read the content back
        let read_content = directory.atomic_read(Path::new("write_test.txt")).unwrap();
        assert_eq!(read_content, b"Hello, world!");

        // Try to open the file again
        let writer = directory.open_write(Path::new("write_test.txt"));
        match writer {
            Ok(_) => {
                panic!("Expected an error when opening an existing file, but got Ok");
            }
            Err(tantivy::directory::error::OpenWriteError::FileAlreadyExists(_)) => {
                println!("Got expected error");
            }
            Err(e) => {
                panic!("Expected OpenWriteError::FileAlreadyExists, got {:?}", e);
            }
        };

        // Test concurrent file opening
        use std::sync::{Arc, Barrier};
        use std::thread;

        let thread_count = 10;
        let barrier = Arc::new(Barrier::new(thread_count));
        let directory = Arc::new(directory);

        let handles: Vec<_> = (0..thread_count)
            .map(|_| {
                let barrier = Arc::clone(&barrier);
                let directory = Arc::clone(&directory);
                thread::spawn(move || {
                    barrier.wait();
                    directory.open_write(Path::new("concurrent_test.txt"))
                })
            })
            .collect();

        let results: Vec<_> = handles.into_iter().map(|h| h.join().unwrap()).collect();

        // Count successful opens and errors
        let (successes, errors): (Vec<_>, Vec<_>) = results.into_iter().partition(Result::is_ok);

        // Ensure only one thread succeeded
        assert_eq!(successes.len(), 1, "Expected only one successful file open");

        // Ensure all other threads failed with FileAlreadyExists error
        assert_eq!(
            errors.len(),
            thread_count - 1,
            "Expected all other threads to fail"
        );
        for error in errors {
            match error {
                Err(tantivy::directory::error::OpenWriteError::FileAlreadyExists(_)) => {}
                Err(e) => {
                    panic!("Expected FileAlreadyExists error, got {:?}", e)
                }
                Ok(_) => {
                    panic!("Expected an error when opening an existing file, but got Ok");
                }
            }
        }

        // Clean up
        directory.delete(Path::new("concurrent_test.txt")).unwrap();
    }
}

#[test]
fn test_object_store_directory_with_tantivy_index() {
    for use_mmap in [false, true] {
        use tantivy::schema::{Schema, STORED, TEXT};
        use tantivy::{doc, Index, Term};

        let (_tmp_dir, directory) = create_test_directory(use_mmap);

        // Define a schema for the index
        let mut schema_builder = Schema::builder();
        let title = schema_builder.add_text_field("title", TEXT | STORED);
        let body = schema_builder.add_text_field("body", TEXT);
        let schema = schema_builder.build();

        // Create a new index using our ObjectStoreDirectory
        let index = Index::create(
            directory as Box<dyn tantivy::Directory>,
            schema.clone(),
            IndexSettings::default(),
        )
        .unwrap();

        // Add a document to the index
        let mut index_writer = index.writer(50_000_000).unwrap();
        let doc = doc!(
            title => "My first document",
            body => "This is the body of my first document."
        );
        index_writer.add_document(doc).unwrap();
        index_writer.commit().unwrap();

        // Reload the index reader
        // Create a reader with a reload policy
        let reader = index
            .reader_builder()
            .reload_policy(ReloadPolicy::OnCommitWithDelay)
            .try_into()
            .unwrap();

        // Acquire a searcher
        let searcher = reader.searcher();

        // Search for the document
        let query_parser = QueryParser::for_index(&index, vec![title, body]);
        let query = query_parser.parse_query("first").unwrap();
        let top_docs = searcher.search(&query, &TopDocs::with_limit(1)).unwrap();

        assert_eq!(top_docs.len(), 1);

        // Search for "My first document"
        let query = query_parser.parse_query("My first document").unwrap();
        let top_docs = searcher.search(&query, &TopDocs::with_limit(1)).unwrap();
        assert_eq!(top_docs.len(), 1);

        // Fetch the stored fields of the document
        let (_, doc_address) = top_docs[0];
        let retrieved_doc: TantivyDocument = searcher.doc(doc_address).unwrap();
        let retrieved_title: &str = retrieved_doc.get_first(title).unwrap().as_str().unwrap();

        assert_eq!(retrieved_title, "My first document");

        // Test deletion
        let index_writer2: Result<IndexWriter, _> = index.writer(50_000_000);
        match index_writer2 {
            Err(tantivy::error::TantivyError::LockFailure(_, _)) => {
                // Expected error
            }
            Ok(_) => {
                panic!("Expected an error when opening another index writer");
            }
            Err(e) => {
                panic!("Expected LockFailure error, got {:?}", e);
            }
        };
        index_writer.delete_term(Term::from_field_text(title, "first"));
        // index_writer.delete_all_documents().unwrap();
        index_writer.commit().unwrap();

        reader.reload().unwrap();

        // Re-acquire a searcher after commit
        let searcher = reader.searcher();
        let top_docs = searcher.search(&query, &TopDocs::with_limit(1)).unwrap();
        assert_eq!(top_docs.len(), 0);
    }
}

#[tokio::test]
async fn test_object_store_directory_async_delete_stream() {
    for use_mmap in [false, true] {
        let (_tmp_dir, directory) = create_test_directory(use_mmap);

        // Create a few test files to mark for deletion, as well as a couple
        // files we won't delete.
        let mut paths_to_delete = HashSet::new();
        for i in 0..3 {
            let path_str = format!("delete_file{}.txt", i);

            let path = Path::new(&path_str);
            let content = format!("Content for file {}", i).into_bytes();
            directory.async_atomic_write(path, &content).await.unwrap();
            assert!(directory.async_exists(path).await.unwrap());

            paths_to_delete.insert(path.to_path_buf());
        }
        let keep_path1 = Path::new("keep_file1.txt");
        let keep_path2 = Path::new("keep_file2.txt");
        directory
            .async_atomic_write(keep_path1, b"Keep me 1")
            .await
            .unwrap();
        directory
            .async_atomic_write(keep_path2, b"Keep me 2")
            .await
            .unwrap();

        let paths_to_delete_stream =
            stream::iter(paths_to_delete.iter().cloned().map(Ok::<_, Error>));

        let delete_stream = directory.delete_stream(Box::pin(paths_to_delete_stream));
        let results = delete_stream.collect::<Vec<_>>().await;
        for result in &results {
            assert!(result.is_ok());
        }

        // Verify that the correct files were deleted.
        for path in &paths_to_delete {
            assert!(!directory.async_exists(path).await.unwrap());
        }
        assert!(directory.async_exists(keep_path1).await.unwrap());
        assert!(directory.async_exists(keep_path2).await.unwrap());

        // Test deleting a non-existent file.
        let non_existent_path = Path::new("non_existent.txt");
        let non_existent_stream =
            stream::iter(vec![Ok::<_, Error>(non_existent_path.to_path_buf())]);

        let delete_stream = directory.delete_stream(Box::pin(non_existent_stream));
        let results = delete_stream.collect::<Vec<_>>().await;

        // Assert the stream produced either a success or an Error::NotFound.
        assert!(results.len() == 1);
        assert!(
            results[0].is_ok() || matches!(results[0], Err(Error::NotFound { .. })),
            "Expected either a success or an Error::NotFound but got: {:?}",
            results[0]
        );

        // Test with mixed existent and non-existent files. Even with an Error::NotFound,
        // the rest of the stream should still get processed.
        let new_path = Path::new("new_path.txt");
        let fake_path = Path::new("fake_path.txt");
        directory
            .async_atomic_write(new_path, b"New test 1")
            .await
            .unwrap();

        let mixed_paths_stream = stream::iter(vec![
            Ok::<_, Error>(new_path.to_path_buf()),
            Ok::<_, Error>(fake_path.to_path_buf()), // This one doesn't exist
            Ok::<_, Error>(keep_path1.to_path_buf()),
        ]);

        let mixed_delete_stream = directory.delete_stream(Box::pin(mixed_paths_stream));
        mixed_delete_stream.collect::<Vec<_>>().await;

        // Verify new_path and keep_path1 were deleted and keep_path2 still exists.
        assert!(!directory.async_exists(new_path).await.unwrap());
        assert!(!directory.async_exists(keep_path1).await.unwrap());
        assert!(directory.async_exists(keep_path2).await.unwrap());
    }
}
