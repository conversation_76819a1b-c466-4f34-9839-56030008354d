use std::{
    collections::{BTreeMap, HashMap},
    path::{Path, PathBuf},
    sync::Arc,
};

use tokio::runtime::Handle;
use tracing::instrument;
use util::{
    anyhow::Result,
    futures::future::join_all,
    tracer::{trace_if_async, EnterTraceGuard, TracedNode},
};

use crate::{
    instrumented::{CacheEntryStats, Instrumented},
    tantivy_footer::make_footer_fname,
    tantivy_index::{
        check_and_set_meta_schema, collect_meta_json, extract_segment_id, merge_index_meta,
        IndexMetaJson,
    },
    timer::TimerManager,
};
use tantivy::{
    directory::{error::OpenReadError, OwnedBytes, META_LOCK},
    SegmentId, SegmentOrdinal,
};

use super::AsyncDirectoryArc;

#[derive(Clone, Debug)]
pub struct DynamicReadonlyDirectory(Arc<DynamicReadonlyDirectoryInner>);

/// The DynamicReadonlyDirectory is a directory implementation geared towards reading across a
/// dynamic set of tantivy indices. It is *not* a general-purpose directory implementation, and
/// only supports the operations necessary for reading tantivy indices.
#[derive(Clone, Debug)]
struct DynamicReadonlyDirectoryInner {
    merged_metadata: IndexMetaJson,
    merged_metadata_bytes: OwnedBytes,

    index_path_to_info: HashMap<PathBuf, DynamicIndexInfo>,
    segment_id_to_index_path: HashMap<SegmentId, PathBuf>,
    segment_ord_to_segment_id: HashMap<SegmentOrdinal, SegmentId>,
}

#[derive(Clone, Debug)]
pub struct DynamicIndexInfo {
    pub directory: AsyncDirectoryArc,
    pub meta: IndexMetaJson,
}

#[derive(Clone, Debug)]
pub struct DynamicIndexSpec {
    pub directory: AsyncDirectoryArc,
    pub path: PathBuf,
    pub meta: IndexMetaJson,
}

impl DynamicReadonlyDirectory {
    pub async fn new(
        full_schema: tantivy::schema::Schema,
        index_specs: Vec<DynamicIndexSpec>,
    ) -> Result<Self> {
        let start = std::time::Instant::now();

        // Construct a single merged metadata. If there are no index specs, use a schema
        // constructed from full_schema.
        let merged_metadata = index_specs
            .iter()
            .try_fold(None, |acc, index_spec| {
                let index_meta = check_and_set_meta_schema(index_spec.meta.clone(), &full_schema)?;
                match acc {
                    None => Ok(Some(index_meta)),
                    Some(acc) => Some(merge_index_meta(acc, &index_meta)).transpose(),
                }
            })?
            .unwrap_or_else(|| IndexMetaJson {
                schema: full_schema,
                ..Default::default()
            });
        let merged_metadata_bytes = OwnedBytes::new(serde_json::to_vec(&merged_metadata)?);

        let segment_id_to_index_path: HashMap<SegmentId, PathBuf> = index_specs
            .iter()
            .flat_map(|index_spec| {
                index_spec
                    .meta
                    .segments
                    .iter()
                    .map(|segment_meta| (segment_meta.segment_id, index_spec.path.clone()))
            })
            .collect();

        let segment_ord_to_segment_id: HashMap<SegmentOrdinal, SegmentId> = merged_metadata
            .segments
            .iter()
            .enumerate()
            .map(|(idx, segment_meta)| (idx as SegmentOrdinal, segment_meta.segment_id))
            .collect();

        let index_path_to_info: HashMap<PathBuf, DynamicIndexInfo> = index_specs
            .into_iter()
            .map(|index_spec| {
                Ok((
                    index_spec.path,
                    DynamicIndexInfo {
                        directory: index_spec.directory,
                        meta: index_spec.meta,
                    },
                ))
            })
            .collect::<Result<_>>()?;

        if log::log_enabled!(log::Level::Debug) {
            log::debug!(
                "initializing distributed readonly directory took {:?}",
                start.elapsed()
            );
        }

        Ok(Self(Arc::new(DynamicReadonlyDirectoryInner {
            merged_metadata,
            merged_metadata_bytes,
            index_path_to_info,
            segment_id_to_index_path,
            segment_ord_to_segment_id,
        })))
    }

    pub async fn from_directory_and_paths(
        full_schema: tantivy::schema::Schema,
        base_directory: AsyncDirectoryArc,
        index_paths: Vec<PathBuf>,
    ) -> Result<Self> {
        let start = std::time::Instant::now();

        // Build up a list of index specs.
        let index_specs = join_all(index_paths.into_iter().map(|index_path| {
            let directory = base_directory.clone();
            let path = index_path;
            async move {
                let meta = collect_meta_json(directory.as_ref(), &path).await?;
                Ok(meta.map(|meta| DynamicIndexSpec {
                    directory,
                    path,
                    meta,
                }))
            }
        }))
        .await
        .into_iter()
        .filter_map(|x| x.transpose())
        .collect::<Result<Vec<_>>>()?;

        if log::log_enabled!(log::Level::Debug) {
            log::debug!(
                "Reading metadatas for distributed readonly directory took {:?}",
                start.elapsed()
            );
        }

        Self::new(full_schema, index_specs).await
    }

    pub fn merged_metadata(&self) -> &IndexMetaJson {
        &self.0.merged_metadata
    }

    pub fn schema(&self) -> &tantivy::schema::Schema {
        &self.merged_metadata().schema
    }

    pub fn index_path_to_info(&self) -> &HashMap<PathBuf, DynamicIndexInfo> {
        &self.0.index_path_to_info
    }

    pub fn segment_id_to_index_path(&self) -> &HashMap<SegmentId, PathBuf> {
        &self.0.segment_id_to_index_path
    }

    pub fn segment_ord_to_segment_id(&self) -> &HashMap<SegmentOrdinal, SegmentId> {
        &self.0.segment_ord_to_segment_id
    }

    pub fn make_readonly_index_blocking(self) -> Result<tantivy::Index, tantivy::TantivyError> {
        tantivy::IndexBuilder::new()
            .schema(self.0.merged_metadata.schema.clone())
            .settings(self.0.merged_metadata.index_settings.clone())
            .open_or_create(Box::new(self) as Box<dyn tantivy::directory::Directory>)
    }

    fn resolve_path(&self, path: &Path) -> Result<ResolvePathResult<'_>, OpenReadError> {
        // We only support files of the form `[segment_id].[extension]` or `meta.json`. For
        // everything else, we return FileDoesNotExist.
        if path == Path::new("meta.json") {
            Ok(ResolvePathResult::MetaJson)
        } else if path == Path::new(".managed.json") {
            // This is a special file that tantivy uses to store managed schema information. We
            // don't support it, so we return FileDoesNotExist.
            Err(OpenReadError::FileDoesNotExist(path.to_path_buf()))
        } else if let Some(segment_id) = extract_segment_id(path) {
            let index_path = self
                .0
                .segment_id_to_index_path
                .get(&segment_id)
                .ok_or_else(|| OpenReadError::FileDoesNotExist(path.to_path_buf()))?;
            let dir = &self
                .0
                .index_path_to_info
                .get(index_path)
                .ok_or_else(|| OpenReadError::FileDoesNotExist(path.to_path_buf()))?
                .directory;
            Ok(ResolvePathResult::DirectoryPath((
                dir,
                index_path.join(path),
            )))
        } else {
            Err(OpenReadError::FileDoesNotExist(path.to_path_buf()))
        }
    }

    #[instrument(err, skip(self, tracer), fields(num_directories = %self.0.index_path_to_info.len()))]
    pub async fn load_segment_footer(&self, tracer: Option<Arc<TracedNode>>) -> Result<usize> {
        trace_if_async(
            log::Level::Info,
            &tracer,
            "Load segment footer",
            |child| async move {
                child.increment_counter("num_directories", self.0.index_path_to_info.len() as u64);
                let directories = self
                    .0
                    .index_path_to_info
                    .iter()
                    .map(|(prefix, info)| {
                        Ok((
                            info.directory.clone(),
                            prefix.join(make_footer_fname(&info.meta)?),
                        ))
                    })
                    .collect::<Result<Vec<_>>>()?;

                let loaded = join_all(directories.into_iter().map(|(dir, path)| {
                    let dir = dir.clone();
                    async move { dir.load_redirects(&path).await }
                }))
                .await
                .into_iter()
                .collect::<Result<Vec<_>, _>>()?;

                let mut num_redirects = 0;
                for loaded in loaded {
                    if loaded {
                        num_redirects += 1;
                    }
                }
                child.increment_counter("num_footers_loaded", num_redirects as u64);

                Ok(num_redirects)
            },
        )
        .await
    }

    pub fn load_segment_footer_blocking(&self, tracer: Option<Arc<TracedNode>>) -> Result<usize> {
        Handle::current().block_on(self.load_segment_footer(tracer))
    }
}

impl tantivy::directory::Directory for DynamicReadonlyDirectory {
    fn get_file_handle(
        &self,
        path: &std::path::Path,
    ) -> Result<Arc<dyn tantivy::directory::FileHandle>, OpenReadError> {
        match self.resolve_path(path)? {
            ResolvePathResult::DirectoryPath((dir, path)) => dir.get_file_handle(path.as_ref()),
            ResolvePathResult::MetaJson => Ok(Arc::new(self.0.merged_metadata_bytes.clone())),
        }
    }

    fn exists(&self, path: &std::path::Path) -> Result<bool, OpenReadError> {
        match self.resolve_path(path) {
            Ok(ResolvePathResult::DirectoryPath((dir, path))) => dir.exists(path.as_ref()),
            Ok(ResolvePathResult::MetaJson) => Ok(true),
            Err(OpenReadError::FileDoesNotExist(_)) => Ok(false),
            Err(e) => Err(e),
        }
    }

    fn atomic_read(&self, path: &std::path::Path) -> Result<Vec<u8>, OpenReadError> {
        match self.resolve_path(path)? {
            ResolvePathResult::DirectoryPath((dir, full_path)) => {
                dir.atomic_read(full_path.as_ref())
            }
            ResolvePathResult::MetaJson => Ok(self.0.merged_metadata_bytes.as_slice().to_vec()),
        }
    }

    fn delete(
        &self,
        _path: &std::path::Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        panic!("delete not supported")
    }

    fn open_write(
        &self,
        _path: &std::path::Path,
    ) -> Result<tantivy::directory::WritePtr, tantivy::directory::error::OpenWriteError> {
        panic!("open_write not supported")
    }

    fn atomic_write(&self, _path: &std::path::Path, _data: &[u8]) -> std::io::Result<()> {
        panic!("atomic_write not supported")
    }

    fn sync_directory(&self) -> std::io::Result<()> {
        panic!("sync_directory not supported")
    }

    fn acquire_lock(
        &self,
        lock: &tantivy::directory::Lock,
    ) -> Result<tantivy::directory::DirectoryLock, tantivy::directory::error::LockError> {
        // The only lock we should be acquiring should be over the META_LOCK, which we ignore. The
        // rest, we panic on.
        if std::ptr::eq(&*META_LOCK, lock) {
            Ok(tantivy::directory::DirectoryLock::from(Box::new(())))
        } else {
            panic!("acquire_lock not supported for {:?}", lock);
        }
    }

    fn watch(
        &self,
        _watch_callback: tantivy::directory::WatchCallback,
    ) -> tantivy::Result<tantivy::directory::WatchHandle> {
        // Don't support event-driven updates to meta.json for the time being.
        Ok(tantivy::directory::WatchHandle::empty())
    }
}

impl Instrumented for DynamicReadonlyDirectory {
    fn enable_timing(&self) {
        unique_directories(&self.0.index_path_to_info)
            .into_iter()
            .for_each(|dir| {
                dir.enable_timing();
            });
    }

    fn reset_timing(&self) {
        unique_directories(&self.0.index_path_to_info)
            .into_iter()
            .for_each(|dir| {
                dir.reset_timing();
            });
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        unique_directories(&self.0.index_path_to_info)
            .into_iter()
            .fold(Vec::new(), |mut acc, dir| {
                acc.extend(dir.timers());
                acc
            })
    }

    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        unique_directories(&self.0.index_path_to_info)
            .into_iter()
            .fold(BTreeMap::new(), |mut acc, dir| {
                acc.extend(dir.cache_metrics());
                acc
            })
    }
}

fn unique_directories(
    index_path_to_info: &HashMap<PathBuf, DynamicIndexInfo>,
) -> Vec<&AsyncDirectoryArc> {
    let mut out: Vec<&AsyncDirectoryArc> = Vec::new();
    for info in index_path_to_info.values() {
        if !out.iter().any(|x| Arc::ptr_eq(*x, &info.directory)) {
            out.push(&info.directory);
        }
    }
    out
}

#[derive(Clone, Debug)]
enum ResolvePathResult<'a> {
    DirectoryPath((&'a AsyncDirectoryArc, PathBuf)),
    MetaJson,
}
