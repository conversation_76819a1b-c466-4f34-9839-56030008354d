use memmap2::{MmapMut, MmapOptions};
use rand::Rng;
use stable_deref_trait::StableDeref;
use std::{ops::Deref, sync::Arc};
use tantivy::{common::OwnedBytes, HasLen};
use util::{async_trait::async_trait, unsafe_util::UnsafeMakeStatic};

use crate::{
    directory::cached_directory::{
        BufferOrCacheEntry, CacheDirectory, CacheDirectoryKey, CacheEntry, CacheError, CacheFile,
        CacheFileStats, CacheFileTestingOptions, CacheKey, CacheMeta, FastHashMap, FlushType,
        StatType, CACHED_DIRECTORY_METERS,
    },
    hash_map::RawEntryCompatible,
    instrumented::CacheEntryStats,
    static_sync_runtime::STATIC_SYNC_RUNTIME,
    time,
    timer::TimerManager,
};

pub type MmapCacheDirectory = CacheDirectory<MmapCacheFile<CacheDirectoryKey>>;

#[derive(Clone)]
pub struct MmapCacheFile<K: CacheKey>(Arc<MmapCacheFileInner<K>>);

struct MmapCacheFileInner<K: CacheKey> {
    meta: std::sync::RwLock<CacheMeta<K>>,
    stats: CacheFileStats<K>,
    // This is reference counted so that when we hand out references to it, we don't need to worry
    // about it being dropped.
    mmap_mut: Arc<MmapMut>,
    #[allow(unused)] // Used by tests
    file_path: std::path::PathBuf,
    // This is an easy way to increment a reference to the tmp dir so it's not deleted
    // while these files are open.
    _tmp_dir: Option<Arc<tempfile::TempDir>>,
}

#[async_trait]
impl<K: CacheKey + Send + Sync + 'static> CacheFile<K> for MmapCacheFile<K> {
    fn new(
        file_name: &std::path::Path,
        tmp_dir: Option<Arc<tempfile::TempDir>>,
        file_size: usize,
        _memory_limit: usize,
        stats_enabled: bool,
        _testing_opts: CacheFileTestingOptions,
    ) -> Result<Self, std::io::Error> {
        // NOTE: We could technically do this async, but since it happens once at the beginning of the program,
        // it's not a big deal.
        let file = std::fs::OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .open(file_name)?;

        // Even though we won't use this full size up front, we need to set the file size so that mmap
        // won't throw a SIGBUS error. It doesn't actually use up this space on the filesystem, even though
        // `ls -lh` shows a large file. `du -h` shows the actual size.
        file.set_len(file_size as u64)?;

        // mmap is inherently considered unsafe by rust, so we _have_ to wrap it here.
        let mmap_mut = Arc::new(unsafe { MmapOptions::new().len(file_size).map_mut(&file)? });

        Ok(Self(Arc::new(MmapCacheFileInner {
            meta: std::sync::RwLock::new(CacheMeta::new(
                file_size, 0, /* do not cache anything */
                true,
            )),
            stats: CacheFileStats::new(stats_enabled),
            mmap_mut,
            file_path: file_name.to_path_buf(),
            _tmp_dir: tmp_dir,
        })))
    }

    fn sync_get_cached_page<Q>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K>,
    {
        match self.get_cached_entry(key) {
            BufferOrCacheEntry::Buffer(buf) => Ok(Some(buf)),
            BufferOrCacheEntry::CacheEntry(cache_entry) => {
                self.backfill_cached_page(key, cache_entry, timer)
            }
            BufferOrCacheEntry::None => Ok(None),
        }
    }

    async fn async_get_cached_page<Q, U>(
        &self,
        key: &Q,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K> + UnsafeMakeStatic<Target = U> + Send + Sync + 'static,
        U: RawEntryCompatible<K> + Send + Sync + 'static,
    {
        self.sync_get_cached_page(key, timer)
    }

    async fn write_page_to_cache(
        &self,
        key: K,
        buf: Arc<OwnedBytes>,
        flush_type: FlushType,
        timer: Arc<TimerManager>,
    ) -> Result<Arc<OwnedBytes>, CacheError> {
        let me = self.clone();
        let return_buf = buf.clone();

        let result_fut = STATIC_SYNC_RUNTIME.spawn_blocking(move || {
            if matches!(flush_type, FlushType::Slow) {
                let sleep_time = {
                    let mut rng = rand::thread_rng();
                    rng.gen_range(0..100)
                };
                std::thread::sleep(std::time::Duration::from_millis(sleep_time));
            }

            me.write_page_to_cache_blocking(key, buf, timer)
        });

        if matches!(flush_type, FlushType::Sync) {
            Ok(result_fut
                .await
                .map_err(|e| CacheError::Anyhow(e.into()))??)
        } else {
            Ok(return_buf)
        }
    }

    fn should_cache_large_reads(&self) -> bool {
        true
    }

    fn meta(&self) -> &std::sync::RwLock<CacheMeta<K>> {
        &self.0.meta
    }

    fn clear_memory(&self) {
        // No-op for mmap
    }

    fn clear_all(&self) {
        let mut meta = self
            .0
            .meta
            .write()
            .expect("Failed to acquire metadata lock");
        let file_size = meta.file_size;
        *meta = CacheMeta::new(file_size, 0 /* do not cache anything */, true);
    }

    fn stats(&self) -> FastHashMap<K, CacheEntryStats> {
        self.0.stats.stats()
    }

    fn incr_stat<Q>(&self, key: &Q, stat_type: StatType)
    where
        Q: RawEntryCompatible<K>,
    {
        self.0.stats.incr_stat(key, stat_type);
    }

    fn incr_stat_value<Q>(&self, key: &Q, stat_type: StatType, value: i64)
    where
        Q: RawEntryCompatible<K>,
    {
        self.0.stats.incr_stat_value(key, stat_type, value);
    }
}

impl<K: CacheKey + Send + Sync + 'static> MmapCacheFile<K> {
    fn get_mmap_owned_bytes(
        &self,
        physical_range: std::ops::Range<usize>,
    ) -> Result<Arc<OwnedBytes>, CacheError> {
        if physical_range.end > self.0.mmap_mut.len() {
            return Err(CacheError::NotEnoughSpace {
                requested: physical_range.end,
                available: self.0.mmap_mut.len(),
            });
        }

        Ok(Arc::new(OwnedBytes::new(MmapPtr(
            self.0.mmap_mut.clone(),
            physical_range,
        ))))
    }

    fn backfill_cached_page<Q>(
        &self,
        key: &Q,
        cache_entry: CacheEntry<K>,
        timer: &Arc<TimerManager>,
    ) -> Result<Option<Arc<OwnedBytes>>, std::io::Error>
    where
        Q: RawEntryCompatible<K>,
    {
        time!(timer, "disk backfill cache");

        let target_len = cache_entry.physical_range.len();
        let buf = self
            .get_mmap_owned_bytes(cache_entry.physical_range.clone())
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;

        let mut meta = self
            .0
            .meta
            .write()
            .expect("Failed to acquire metadata lock");
        let new_entry = meta.get_cache_entry_mut(key);
        let is_valid = match new_entry {
            None => false,
            Some(new_entry) => {
                if new_entry.id == cache_entry.id {
                    new_entry.cached_buf = Arc::downgrade(&buf);
                    let new_entry_id = new_entry.id;

                    // More recent chunks are more likely to be cache hits, so iterate backwards.
                    let mut found = false;
                    for i in (0..meta.chunks.len()).rev() {
                        let chunk = meta.chunks.get_mut(i).unwrap();
                        if chunk.id == new_entry_id {
                            chunk.cached_buf = Arc::downgrade(&buf);
                            found = true;
                            break;
                        }
                    }
                    assert!(
                        found,
                        "Failed to find cache entry in chunks with id {}",
                        new_entry_id
                    );

                    true
                } else {
                    false
                }
            }
        };

        CACHED_DIRECTORY_METERS
            .hot_read_bytes
            .record(target_len as u64, &[]);

        Ok(if is_valid {
            self.incr_stat(key, StatType::DiskHit);
            Some(buf)
        } else {
            self.incr_stat(key, StatType::Miss);
            None
        })
    }

    pub fn write_page_to_cache_blocking(
        &self,
        key: K,
        buf: Arc<OwnedBytes>,
        timer: Arc<TimerManager>,
    ) -> Result<Arc<OwnedBytes>, CacheError> {
        // When this cache_entry comes back to us, it has a weak reference to `buf`. Because
        // we passed `reject_active_buffers = true` to the cache meta while constructing it,
        // that means any request to evict this cache entry will fail. Of note, this is also
        // an extreme edge case, because it means we'd wrap around the entire cache file while
        // writing this page.
        let cache_entry = {
            let mut meta = self
                .0
                .meta
                .write()
                .expect("Failed to acquire metadata lock");
            meta.allocate_chunk(key.clone(), &buf)?
        };

        time!(timer, "write_page_to_cache");
        let start = std::time::Instant::now();

        // We know we can do this because we have uniquely reserved this range (see comment above
        // for how exactly that works).
        unsafe {
            let const_ptr = self
                .0
                .mmap_mut
                .as_ptr()
                .add(cache_entry.physical_range.start);

            let mut_ptr = const_ptr as *mut u8;

            std::ptr::copy_nonoverlapping(buf.as_ptr(), mut_ptr, buf.len());
        };

        CACHED_DIRECTORY_METERS.hot_writes.add(1, &[]);
        CACHED_DIRECTORY_METERS
            .hot_write_duration_ms
            .record(start.elapsed().as_millis() as u64, &[]);
        CACHED_DIRECTORY_METERS
            .hot_write_bytes
            .record(buf.len() as u64, &[]);

        let mmap_buf_result = self.get_mmap_owned_bytes(cache_entry.physical_range.clone());

        {
            let mut meta = self
                .0
                .meta
                .write()
                .expect("Failed to acquire metadata lock");

            meta.actively_writing.remove(&cache_entry.id);

            if let Ok(mmap_buf) = mmap_buf_result.as_ref() {
                // Replace the weak reference to point to the new mmap buffer
                match meta.pages.get_mut(&key) {
                    Some(page) if page.id == cache_entry.id => {
                        page.cached_buf = Arc::downgrade(mmap_buf);
                        let mut found = false;
                        for chunk in meta.chunks.iter_mut() {
                            if chunk.id == cache_entry.id {
                                chunk.cached_buf = Arc::downgrade(mmap_buf);
                                found = true;
                                break;
                            }
                        }
                        assert!(
                            found,
                            "Failed to find cache entry in chunks with id={}",
                            cache_entry.id
                        );
                    }
                    _ => {}
                }
            }
        }

        mmap_buf_result
    }
}

struct MmapPtr(Arc<MmapMut>, std::ops::Range<usize>);

impl Deref for MmapPtr {
    type Target = [u8];

    fn deref(&self) -> &[u8] {
        unsafe {
            // This pointer gets us an immutable reference to the start of the appropriate
            // physical range in the mmap. We have to use unsafe here because MmapMut is supposed
            // to only be accessed exclusively, but we tightly control concurrent access to the
            // mmap, so we can access this range knowing that others will not be writing to it.
            let ptr = self.0.as_ptr().add(self.1.start);
            std::slice::from_raw_parts(ptr, self.1.len())
        }
    }
}

unsafe impl StableDeref for MmapPtr {}
unsafe impl Send for MmapPtr {}
unsafe impl Sync for MmapPtr {}

// This is tested more heavily in cached_directory.rs. The tests here are targeted tests that are
// specific to mmap.
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_file_sanity() {
        let timer = TimerManager::new("FileCacheDirectory");
        let temp_dir = tempfile::tempdir().unwrap();
        let cache_path = temp_dir.path().join("test_cache_file");
        let cache_file: MmapCacheFile<std::ops::Range<i32>> = MmapCacheFile::new(
            &cache_path,
            Some(temp_dir.into()),
            1024 * 1024,
            0,
            true,
            Default::default(),
        )
        .unwrap();

        // Try reading empty data
        let data = cache_file.sync_get_cached_page(&(0..100), &timer).unwrap();
        assert_eq!(data, None);

        let stats = cache_file.stats().values().next().cloned().unwrap();
        assert_eq!(stats.num_disk_hits, 0);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 1);

        // Write some data
        let data = Arc::new(OwnedBytes::new(vec![1; 100]));
        cache_file
            .write_page_to_cache_blocking(0..100, data.clone(), timer.clone())
            .unwrap();

        // Read it back
        let read_data = cache_file
            .sync_get_cached_page(&(0..100), &timer)
            .unwrap()
            .unwrap();
        assert_eq!(read_data.as_slice(), data.as_slice());

        let stats = cache_file.stats().values().next().cloned().unwrap();
        // in mmap land, the first read will be a disk hit because we serve it from the mmap
        assert_eq!(stats.num_disk_hits, 1);
        assert_eq!(stats.num_mem_hits, 0);
        assert_eq!(stats.num_misses, 1);

        // Another read while the buffer is still alive will be a mem hit.
        {
            let read_data = cache_file
                .sync_get_cached_page(&(0..100), &timer)
                .unwrap()
                .unwrap();
            assert_eq!(read_data.as_slice(), data.as_slice());

            let stats = cache_file.stats().values().next().cloned().unwrap();
            assert_eq!(stats.num_disk_hits, 1);
            assert_eq!(stats.num_mem_hits, 1);
            assert_eq!(stats.num_misses, 1);
        }

        drop(read_data);

        let read_data = cache_file
            .sync_get_cached_page(&(0..100), &timer)
            .unwrap()
            .unwrap();
        assert_eq!(read_data.as_slice(), data.as_slice());

        let stats = cache_file.stats().values().next().cloned().unwrap();
        // But then we'll go back to serving it from the mmap
        assert_eq!(stats.num_disk_hits, 2);
        assert_eq!(stats.num_mem_hits, 1);
        assert_eq!(stats.num_misses, 1);

        let cache_file_path = cache_file.0.file_path.clone();
        assert!(cache_file_path.exists());
        drop(cache_file);
        assert!(!cache_file_path.exists());
    }

    #[test]
    fn test_mmap_cache_meta_next_chunk_in_use() {
        // This test ensures that when the cache is full and the first chunk is still referenced,
        // allocating a new chunk returns CacheError::NextChunkInUse. After dropping the reference
        // to the first chunk, allocation should succeed and the first chunk should be evicted.
        let timer = TimerManager::new("FileCacheDirectory");
        let temp_dir = tempfile::tempdir().unwrap();
        let cache_path = temp_dir.path().join("test_cache_file");

        let file_size = 20; // enough room for exactly two 10-byte chunks
        let cache_file: MmapCacheFile<std::ops::Range<i32>> = MmapCacheFile::new(
            &cache_path,
            Some(temp_dir.into()),
            file_size,
            0,
            true,
            Default::default(),
        )
        .unwrap();

        // Allocate the first chunk and keep a live reference to it
        let data1 = Arc::new(OwnedBytes::new(vec![0u8; 10]));
        let entry1 = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(0..10, &data1)
            .unwrap();
        assert!(entry1.physical_range.start == 0);
        cache_file
            .0
            .meta
            .write()
            .unwrap()
            .actively_writing
            .remove(&entry1.id);

        // Allocate a second chunk to fill the cache
        let data2 = Arc::new(OwnedBytes::new(vec![0u8; 10]));
        let entry2 = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(10..20, &data2)
            .unwrap();
        assert!(entry2.physical_range.start == 10);
        cache_file
            .0
            .meta
            .write()
            .unwrap()
            .actively_writing
            .remove(&entry2.id);

        // Attempt to allocate a third chunk while the first chunk is still referenced
        let data3 = Arc::new(OwnedBytes::new(vec![0u8; 10]));
        let result = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(20..30, &data3);
        assert!(matches!(result, Err(CacheError::NextChunkInUse)));

        // Drop the reference to the first chunk so it can be evicted
        assert!(Arc::strong_count(&data1) == 1);
        drop(data1);

        // Now allocation should succeed
        let _entry3 = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(20..30, &data3)
            .unwrap();

        drop(data2);

        // Ensure the first chunk was evicted and the new chunk is present
        {
            let meta = cache_file.0.meta.read().unwrap();
            assert!(!meta.pages.contains_key(&(0..10)));
            assert!(meta.pages.contains_key(&(10..20)));
            assert!(meta.pages.contains_key(&(20..30)));

            // Make sure key #2 has no active references
            let entry = meta.get_cache_entry(&(10..20));
            assert!(entry.is_some());
            assert!(entry.unwrap().cached_buf.upgrade().is_none());
        }

        // Now, get a reference to page #2, and make sure that we get a NextChunkInUse error
        let buf2 = cache_file
            .sync_get_cached_page(&(10..20), &timer)
            .unwrap()
            .unwrap();
        let result = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(30..40, &data3);
        assert!(matches!(result, Err(CacheError::NextChunkInUse)));

        drop(buf2);

        let result = cache_file
            .0
            .meta
            .write()
            .unwrap()
            .allocate_chunk(30..40, &data3);
        assert!(matches!(result, Ok(_)));
    }
}
