use std::{
    collections::{BTreeMap, HashMap},
    io::{Read, Write},
    ops::Range,
    path::{Path, PathBuf},
    pin::Pin,
    sync::{Arc, Mutex},
};

use flate2::{read::ZlibDecoder, write::<PERSON><PERSON><PERSON><PERSON>nco<PERSON>, Compression};
use tantivy::common::OwnedBytes;
use util::{async_trait::async_trait, futures::stream::BoxStream};

use crate::{
    instrumented::{CacheEntryStats, Instrumented},
    timer::TimerManager,
};

use super::{
    async_directory::{AsyncDirectory, AsyncFileHandle, AsyncWriteHandler},
    AsyncDirectoryArc,
};

pub struct TracedRange {
    pub file_len: u64,
    pub ranges: HashMap<Range<usize>, Vec<u8>>,
}

#[derive(Clone)]
pub struct TracedRangeDirectory {
    inner: AsyncDirectoryArc,
    ranges: Arc<Mutex<HashMap<std::path::<PERSON><PERSON>uf, TracedRange>>>,
}

impl std::fmt::Debug for TracedRangeDirectory {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "TracedRangeDirectory(...)")
    }
}

pub const VERSION: u32 = 1;

#[async_trait]
impl AsyncDirectory for TracedRangeDirectory {
    async fn async_get_file_handle(
        &self,
        path: &std::path::Path,
        len: Option<u64>,
    ) -> Result<Arc<dyn AsyncFileHandle>, tantivy::directory::error::OpenReadError> {
        let inner = self.inner.async_get_file_handle(path, len).await?;
        Ok(Arc::new(TracedRangeFileHandle {
            inner,
            path: Arc::new(path.to_path_buf()),
            ranges: self.ranges.clone(),
        }))
    }

    async fn async_delete(
        &self,
        path: &std::path::Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        self.inner.async_delete(path).await
    }

    async fn async_exists(
        &self,
        path: &std::path::Path,
    ) -> Result<bool, tantivy::directory::error::OpenReadError> {
        self.inner.async_exists(path).await
    }

    async fn async_open_write(
        &self,
        path: &std::path::Path,
    ) -> Result<Pin<Box<dyn AsyncWriteHandler>>, tantivy::directory::error::OpenWriteError> {
        self.inner.async_open_write(path).await
    }

    async fn async_atomic_read(
        &self,
        path: &std::path::Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        self.inner.async_atomic_read(path).await
    }

    async fn async_atomic_write(&self, path: &std::path::Path, data: &[u8]) -> std::io::Result<()> {
        self.inner.async_atomic_write(path, data).await
    }

    async fn async_sync_directory(&self) -> std::io::Result<()> {
        self.inner.async_sync_directory().await
    }

    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<PathBuf, object_store::Error>>,
    ) -> BoxStream<'a, Result<object_store::path::Path, object_store::Error>> {
        self.inner.delete_stream(locations)
    }

    async fn load_redirects(&self, path: &Path) -> util::anyhow::Result<bool> {
        self.inner.load_redirects(path).await
    }
}

impl Instrumented for TracedRangeDirectory {
    fn enable_timing(&self) {
        self.inner.enable_timing();
    }

    fn reset_timing(&self) {
        self.inner.reset_timing();
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        self.inner.timers()
    }

    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        self.inner.cache_metrics()
    }
}

struct TracedRangeFileHandle {
    inner: Arc<dyn AsyncFileHandle>,
    path: Arc<std::path::PathBuf>,
    ranges: Arc<Mutex<HashMap<std::path::PathBuf, TracedRange>>>,
}

impl std::fmt::Debug for TracedRangeFileHandle {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "TracedRangeFileHandle(...)")
    }
}

#[async_trait]
impl AsyncFileHandle for TracedRangeFileHandle {
    async fn async_read_bytes(&self, byte_range: Range<usize>) -> std::io::Result<OwnedBytes> {
        let ret = self.inner.async_read_bytes(byte_range.clone()).await?;
        let mut ranges = self.ranges.lock().expect("lock poisoned");
        ranges
            .entry(self.path.as_ref().clone())
            .or_insert_with(|| TracedRange {
                file_len: self.inner.len() as u64,
                ranges: HashMap::new(),
            })
            .ranges
            .insert(byte_range.start..byte_range.end, ret.as_slice().to_vec());
        Ok(ret)
    }
}

impl tantivy::HasLen for TracedRangeFileHandle {
    fn len(&self) -> usize {
        self.inner.len()
    }
}

type SerializedTracedRange = (u64, HashMap<Range<u64>, u64>);
type SerializedFooter = HashMap<std::path::PathBuf, SerializedTracedRange>;

impl TracedRangeDirectory {
    pub fn new(directory: AsyncDirectoryArc) -> Self {
        Self {
            inner: directory,
            ranges: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn make_traced_redirects(&self) -> std::io::Result<Vec<u8>> {
        let all_buffers = {
            let mut ranges = self.ranges.lock().expect("lock poisoned");
            std::mem::take(&mut *ranges)
        };

        let mut files: SerializedFooter = HashMap::new();
        let mut data_compressed = ZlibEncoder::new(Vec::new(), Compression::best());
        let mut start = 0;
        for (path, ranges) in all_buffers.into_iter() {
            let path_entry = files
                .entry(path)
                .or_insert_with(|| (ranges.file_len, HashMap::new()));
            for (range, range_data) in ranges.ranges.into_iter() {
                assert!(range.len() == range_data.len(), "data length mismatch");
                let range_len = range_data.len() as u64;
                data_compressed.write_all(&range_data)?;
                path_entry
                    .1
                    .insert(range.start as u64..range.end as u64, start);
                start += range_len;
            }
        }

        let footer_len = bincode::encode_into_std_write(
            &files,
            &mut data_compressed,
            bincode::config::standard(),
        )
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?
            as u64;

        let footer_u32: u32 = if footer_len > u32::MAX as u64 {
            return Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "footer too long",
            ));
        } else {
            footer_len as u32
        };

        data_compressed.write_all(&footer_u32.to_le_bytes())?;
        data_compressed.write_all(&VERSION.to_le_bytes())?;

        Ok(data_compressed.finish()?)
    }

    pub fn load_traced_redirects(
        data: Vec<u8>,
    ) -> util::anyhow::Result<HashMap<std::path::PathBuf, TracedRange>> {
        let mut data_uncompressed = Vec::new();
        let mut data_compressed = ZlibDecoder::new(data.as_slice());

        data_compressed.read_to_end(&mut data_uncompressed)?;

        if data_uncompressed.len() < 8 {
            return Err(util::anyhow::anyhow!("footer too short"));
        }

        // The last 8 bytes of the data are the footer length
        let version_start = data_uncompressed.len() - 4;
        let version_bytes: [u8; 4] =
            data_uncompressed[version_start..version_start + 4].try_into()?;
        let version = u32::from_le_bytes(version_bytes);
        if version != VERSION {
            return Err(util::anyhow::anyhow!("version mismatch"));
        }

        let footer_len_start = data_uncompressed.len() - 8;
        let footer_len_bytes: [u8; 4] =
            data_uncompressed[footer_len_start..footer_len_start + 4].try_into()?;
        let footer_len = u32::from_le_bytes(footer_len_bytes);

        let footer_bytes =
            &data_uncompressed[footer_len_start - (footer_len as usize)..footer_len_start];

        let (files, _): (SerializedFooter, usize) =
            bincode::decode_from_slice(footer_bytes, bincode::config::standard())?;

        let mut ret = HashMap::new();
        for (path, (file_len, ranges)) in files.into_iter() {
            let ret_entry = ret.entry(path).or_insert_with(|| TracedRange {
                file_len,
                ranges: HashMap::new(),
            });
            for (range, offset) in ranges.into_iter() {
                let range = range.start as usize..range.end as usize;
                let mut data = Vec::with_capacity(range.len());
                data.extend_from_slice(
                    &data_uncompressed[offset as usize..offset as usize + range.len()],
                );
                ret_entry.ranges.insert(range, data);
            }
        }

        Ok(ret)
    }
}

#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use object_store::local::LocalFileSystem;
    use tempfile::TempDir;

    use crate::directory::{
        async_directory::{AsyncDirectory, AsyncDirectoryArc},
        object_store_directory::ObjectStoreDirectory,
    };

    use super::TracedRangeDirectory;

    #[tokio::test]
    async fn test_basic_tracing() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory (using LocalFileSystem as a stand-in)
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a TracedRangeDirectory that wraps the ObjectStorage
        let traced_dir = TracedRangeDirectory::new(object_storage.clone());

        // Create a test file
        let path = Path::new("test_file");
        let data = b"Hello, World!";
        traced_dir.async_atomic_write(path, data).await.unwrap();

        // Read some bytes to generate traces
        let handle = traced_dir.async_get_file_handle(path, None).await.unwrap();
        let read_data = handle.async_read_bytes(0..5).await.unwrap();
        assert_eq!(read_data.as_ref(), b"Hello");

        // Read another range
        let read_data = handle.async_read_bytes(7..12).await.unwrap();
        assert_eq!(read_data.as_ref(), b"World");

        // Generate redirects
        let redirects = traced_dir.make_traced_redirects().await.unwrap();

        // Load the redirects
        let loaded = TracedRangeDirectory::load_traced_redirects(redirects).unwrap();

        // Verify the loaded traces
        let file_traces = loaded.get(path).unwrap();
        assert_eq!(file_traces.file_len, data.len() as u64);
        assert_eq!(file_traces.ranges.len(), 2);
        assert_eq!(file_traces.ranges.get(&(0..5)).unwrap(), b"Hello");
        assert_eq!(file_traces.ranges.get(&(7..12)).unwrap(), b"World");
    }

    #[tokio::test]
    async fn test_no_traces() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a TracedRangeDirectory
        let traced_dir = TracedRangeDirectory::new(object_storage.clone());

        // Generate redirects without any traces
        let redirects = traced_dir.make_traced_redirects().await.unwrap();
        assert!(redirects.len() >= 8); // Just the footer + some bincode stuff that's empty

        // Load the redirects
        let loaded = TracedRangeDirectory::load_traced_redirects(redirects).unwrap();
        assert!(loaded.is_empty());
    }

    #[tokio::test]
    async fn test_multiple_files() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a TracedRangeDirectory
        let traced_dir = TracedRangeDirectory::new(object_storage.clone());

        // Create multiple test files
        let paths = [Path::new("file1"), Path::new("file2"), Path::new("file3")];
        let contents = vec![
            b"First file content".to_vec(),
            b"Second file content".to_vec(),
            b"Third file content".to_vec(),
        ];

        // Write files and read from them to generate traces
        for (path, content) in paths.iter().zip(contents.iter()) {
            traced_dir.async_atomic_write(path, content).await.unwrap();
            let handle = traced_dir.async_get_file_handle(path, None).await.unwrap();

            // Read different ranges from each file
            let read_data = handle.async_read_bytes(0..5).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[0..5]);

            let read_data = handle.async_read_bytes(6..11).await.unwrap();
            assert_eq!(read_data.as_ref(), &content[6..11]);
        }

        // Generate redirects
        let redirects = traced_dir.make_traced_redirects().await.unwrap();

        // Load the redirects
        let loaded = TracedRangeDirectory::load_traced_redirects(redirects).unwrap();

        // Verify the loaded traces for each file
        for (path, content) in paths.iter().zip(contents.iter()) {
            let file_traces = loaded.get(*path).unwrap();
            assert_eq!(file_traces.file_len, content.len() as u64);
            assert_eq!(file_traces.ranges.len(), 2);
            assert_eq!(file_traces.ranges.get(&(0..5)).unwrap(), &content[0..5]);
            assert_eq!(file_traces.ranges.get(&(6..11)).unwrap(), &content[6..11]);
        }
    }

    #[tokio::test]
    async fn test_invalid_redirects() {
        // Try to load invalid redirects
        let invalid_data = vec![0, 1, 2, 3, 4, 5];
        let result = TracedRangeDirectory::load_traced_redirects(invalid_data);
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_overlapping_ranges() {
        // Create a temporary directory
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path();

        // Create an ObjectStorage directory
        let store = Arc::new(LocalFileSystem::new_with_prefix(temp_path).unwrap());
        let object_storage = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));

        // Create a TracedRangeDirectory
        let traced_dir = TracedRangeDirectory::new(object_storage.clone());

        // Create a test file
        let path = Path::new("test_file");
        let data = b"Hello, World! This is a test.";
        traced_dir.async_atomic_write(path, data).await.unwrap();

        // Read overlapping ranges
        let handle = traced_dir.async_get_file_handle(path, None).await.unwrap();
        let read_data = handle.async_read_bytes(0..10).await.unwrap();
        assert_eq!(read_data.as_ref(), b"Hello, Wor");

        let read_data = handle.async_read_bytes(5..15).await.unwrap();
        assert_eq!(read_data.as_ref(), b", World! T");

        // Generate redirects
        let redirects = traced_dir.make_traced_redirects().await.unwrap();

        // Load the redirects
        let loaded = TracedRangeDirectory::load_traced_redirects(redirects).unwrap();

        // Verify the loaded traces
        let file_traces = loaded.get(path).unwrap();
        assert_eq!(file_traces.file_len, data.len() as u64);
        assert_eq!(file_traces.ranges.len(), 2);
        assert_eq!(file_traces.ranges.get(&(0..10)).unwrap(), b"Hello, Wor");
        assert_eq!(file_traces.ranges.get(&(5..15)).unwrap(), b", World! T");
    }
}
