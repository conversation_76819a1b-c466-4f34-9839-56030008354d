use std::{
    collections::BTreeMap,
    path::{Path, PathBuf},
    sync::Arc,
};

use util::async_trait::async_trait;
use util::futures::stream::{BoxStream, StreamExt, TryStreamExt};

use super::async_directory::{
    AsyncDirectory, AsyncDirectoryArc, AsyncFileHandle, PinnedAsyncWriteHandler,
};
use crate::{
    instrumented::{CacheEntryStats, Instrumented},
    timer::TimerManager,
};

/// Wrapper around a tantivy::directory::Directory which prepends the given prefix path to any
/// input path. It can be useful for taking a single general directory object and scoping it to a
/// specific tantivy index location.
#[derive(Clone, Debug)]
pub struct PrefixDirectory {
    pub base_directory: AsyncDirectoryArc,
    pub prefix: PathBuf,
}

#[async_trait]
impl AsyncDirectory for PrefixDirectory {
    async fn async_get_file_handle(
        &self,
        path: &Path,
        len: Option<u64>,
    ) -> Result<Arc<dyn AsyncFileHandle>, tantivy::directory::error::OpenReadError> {
        self.base_directory
            .async_get_file_handle(&self.prefix.join(path), len)
            .await
    }

    async fn async_delete(
        &self,
        path: &Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        self.base_directory
            .async_delete(&self.prefix.join(path))
            .await
    }

    async fn async_exists(
        &self,
        path: &Path,
    ) -> Result<bool, tantivy::directory::error::OpenReadError> {
        self.base_directory
            .async_exists(&self.prefix.join(path))
            .await
    }

    async fn async_open_write(
        &self,
        path: &Path,
    ) -> Result<PinnedAsyncWriteHandler, tantivy::directory::error::OpenWriteError> {
        self.base_directory
            .async_open_write(&self.prefix.join(path))
            .await
    }

    async fn async_atomic_read(
        &self,
        path: &Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        self.base_directory
            .async_atomic_read(&self.prefix.join(path))
            .await
    }

    async fn async_atomic_write(&self, path: &Path, data: &[u8]) -> std::io::Result<()> {
        self.base_directory
            .async_atomic_write(&self.prefix.join(path), data)
            .await
    }

    async fn async_sync_directory(&self) -> std::io::Result<()> {
        self.base_directory.async_sync_directory().await
    }

    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<PathBuf, object_store::Error>>,
    ) -> BoxStream<'a, Result<object_store::path::Path, object_store::Error>> {
        let prefixed_locations = locations.map_ok(move |path| self.prefix.join(path)).boxed();
        self.base_directory.delete_stream(prefixed_locations)
    }

    async fn load_redirects(&self, path: &Path) -> util::anyhow::Result<bool> {
        self.base_directory
            .load_redirects(&self.prefix.join(path))
            .await
    }
}

impl Instrumented for PrefixDirectory {
    fn enable_timing(&self) {
        self.base_directory.enable_timing();
    }

    fn reset_timing(&self) {
        self.base_directory.reset_timing();
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        self.base_directory.timers()
    }

    fn cache_metrics(&self) -> BTreeMap<String, CacheEntryStats> {
        self.base_directory.cache_metrics()
    }
}
