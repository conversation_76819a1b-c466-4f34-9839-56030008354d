use util::{
    anyhow::Result,
    system_types::{FullObjectId, ObjectId, ObjectType},
};

const LEAF_OBJECT_ID_FIELDS: [&str; 5] = [
    "project_id",
    "experiment_id",
    "dataset_id",
    "prompt_session_id",
    "log_id",
];

pub fn make_object_id_column_expr(tbl: &str) -> String {
    format!(
        "make_object_id({})",
        LEAF_OBJECT_ID_FIELDS
            .map(|field| format!("\"{}\".\"{}\"", tbl, field))
            .join(", ")
    )
}

pub fn make_object_id_value_expr(
    object_id: &FullObjectId,
    object_id_placeholder: &str,
) -> Result<String> {
    Ok(match object_id.object_type {
        ObjectType::Experiment => format!(
            "make_object_id(NULL, {}, NULL, NULL, NULL)",
            object_id_placeholder
        ),
        ObjectType::Dataset => format!(
            "make_object_id(NULL, NULL, {}, NULL, NULL)",
            object_id_placeholder
        ),
        ObjectType::PromptSession => format!(
            "make_object_id(NULL, NULL, NULL, {}, NULL)",
            object_id_placeholder
        ),
        ObjectType::PlaygroundLogs => {
            // This is a hack that works around the fact that we store playground logs in this
            // format in Postgres.
            format!(
                "make_object_id(NULL, NULL, NULL, CONCAT({}, ':x'), NULL)",
                object_id_placeholder
            )
        }
        ObjectType::ProjectLogs => format!(
            "make_object_id({}, NULL, NULL, NULL, 'g')",
            object_id_placeholder
        ),
        ObjectType::ProjectPrompts | ObjectType::ProjectFunctions => {
            format!(
                "make_object_id({}, NULL, NULL, NULL, 'p')",
                object_id_placeholder
            )
        }
        ObjectType::Project => {
            return Err(util::anyhow::anyhow!(
                "Unsupported object type: {}",
                object_id.object_type
            ))
        }
    })
}

pub struct PostgresObjectIdFields<'a> {
    pub project_id: Option<&'a str>,
    pub experiment_id: Option<&'a str>,
    pub dataset_id: Option<&'a str>,
    pub prompt_session_id: Option<&'a str>,
    pub log_id: Option<&'a str>,
}

#[cfg(test)]
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct OwnedPostgresObjectIdFields {
    pub project_id: Option<String>,
    pub experiment_id: Option<String>,
    pub dataset_id: Option<String>,
    pub prompt_session_id: Option<String>,
    pub log_id: Option<String>,
}

// This function is basically a hybrid of the objectIdsUnionSchema logic defined
// in local/js/api-schema/insert_schemas.ts and the makeBrainstoreObjectId
// function defined in api-ts/src/brainstore/brainstore.ts. Keep them in sync.
pub fn make_brainstore_object_id<'a>(
    fields: PostgresObjectIdFields<'a>,
) -> Result<Option<FullObjectId<'a>>> {
    if let Some(experiment_id) = fields.experiment_id {
        assert!(fields.dataset_id.is_none());
        assert!(fields.prompt_session_id.is_none());
        assert!(fields.log_id.is_none());
        Ok(Some(FullObjectId {
            object_type: ObjectType::Experiment,
            object_id: ObjectId::new(experiment_id)?,
        }))
    } else if let Some(dataset_id) = fields.dataset_id {
        assert!(fields.experiment_id.is_none());
        assert!(fields.prompt_session_id.is_none());
        assert!(fields.log_id.is_none());
        Ok(Some(FullObjectId {
            object_type: ObjectType::Dataset,
            object_id: ObjectId::new(dataset_id)?,
        }))
    } else if let Some(prompt_session_id) = fields.prompt_session_id {
        assert!(fields.experiment_id.is_none());
        assert!(fields.dataset_id.is_none());
        assert!(fields.log_id.is_none());
        if prompt_session_id.ends_with(":x") {
            Ok(Some(FullObjectId {
                object_type: ObjectType::PromptSession,
                object_id: ObjectId::new(&prompt_session_id[..prompt_session_id.len() - 2])?,
            }))
        } else {
            Ok(None)
        }
    } else if fields.project_id.is_some() && fields.log_id == Some("g") {
        assert!(fields.experiment_id.is_none());
        assert!(fields.dataset_id.is_none());
        assert!(fields.prompt_session_id.is_none());
        Ok(Some(FullObjectId {
            object_type: ObjectType::ProjectLogs,
            object_id: ObjectId::new(fields.project_id.unwrap())?,
        }))
    } else {
        Ok(None)
    }
}

// Inverse function of make_brainstore_object_id - converts a FullObjectId back to PostgresObjectIdFields
#[cfg(test)]
pub fn make_postgres_object_id_fields(
    object_id: &FullObjectId,
) -> Option<OwnedPostgresObjectIdFields> {
    match object_id.object_type {
        ObjectType::Experiment => Some(OwnedPostgresObjectIdFields {
            project_id: None,
            experiment_id: Some(object_id.object_id.as_str().to_string()),
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        }),
        ObjectType::Dataset => Some(OwnedPostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: Some(object_id.object_id.as_str().to_string()),
            prompt_session_id: None,
            log_id: None,
        }),
        ObjectType::PromptSession => Some(OwnedPostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: Some(object_id.object_id.as_str().to_string()),
            log_id: None,
        }),
        ObjectType::PlaygroundLogs => Some(OwnedPostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: Some(format!("{}:x", object_id.object_id.as_str())),
            log_id: None,
        }),
        ObjectType::ProjectLogs => Some(OwnedPostgresObjectIdFields {
            project_id: Some(object_id.object_id.as_str().to_string()),
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: Some("g".to_string()),
        }),
        ObjectType::ProjectPrompts | ObjectType::ProjectFunctions | ObjectType::Project => None,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_make_brainstore_object_id_experiment() {
        let fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: Some("exp123"),
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::Experiment);
        assert_eq!(result.object_id.to_string(), "exp123");
    }

    #[test]
    fn test_make_brainstore_object_id_dataset() {
        let fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: Some("dataset456"),
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::Dataset);
        assert_eq!(result.object_id.to_string(), "dataset456");
    }

    #[test]
    fn test_make_brainstore_object_id_prompt_session_with_suffix() {
        let fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: Some("prompt789:x"),
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::PromptSession);
        assert_eq!(result.object_id.to_string(), "prompt789");
    }

    #[test]
    fn test_make_brainstore_object_id_prompt_session_without_suffix() {
        let fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: Some("prompt789"),
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_make_brainstore_object_id_project_logs() {
        let fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: Some("g"),
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::ProjectLogs);
        assert_eq!(result.object_id.to_string(), "project123");
    }

    #[test]
    fn test_make_brainstore_object_id_project_with_wrong_log_id() {
        let fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: Some("not_g"),
        };

        let result = make_brainstore_object_id(fields).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_make_brainstore_object_id_no_matching_fields() {
        let fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_make_brainstore_object_id_project_without_log_id() {
        let fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_make_brainstore_object_id_multiple_fields_experiment() {
        let fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: Some("exp456"),
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::Experiment);
        assert_eq!(result.object_id.to_string(), "exp456");
    }

    #[test]
    fn test_make_brainstore_object_id_multiple_fields_dataset() {
        let fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: None,
            dataset_id: Some("dataset789"),
            prompt_session_id: None,
            log_id: None,
        };

        let result = make_brainstore_object_id(fields).unwrap().unwrap();
        assert_eq!(result.object_type, ObjectType::Dataset);
        assert_eq!(result.object_id.to_string(), "dataset789");
    }

    #[test]
    fn test_make_postgres_object_id_fields_experiment() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::Experiment,
            object_id: ObjectId::new("exp123").unwrap(),
        };

        let fields = make_postgres_object_id_fields(&full_object_id).unwrap();
        assert_eq!(fields.project_id, None);
        assert_eq!(fields.experiment_id, Some("exp123".to_string()));
        assert_eq!(fields.dataset_id, None);
        assert_eq!(fields.prompt_session_id, None);
        assert_eq!(fields.log_id, None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_dataset() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::Dataset,
            object_id: ObjectId::new("dataset456").unwrap(),
        };

        let fields = make_postgres_object_id_fields(&full_object_id).unwrap();
        assert_eq!(fields.project_id, None);
        assert_eq!(fields.experiment_id, None);
        assert_eq!(fields.dataset_id, Some("dataset456".to_string()));
        assert_eq!(fields.prompt_session_id, None);
        assert_eq!(fields.log_id, None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_prompt_session() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::PromptSession,
            object_id: ObjectId::new("prompt789").unwrap(),
        };

        let fields = make_postgres_object_id_fields(&full_object_id).unwrap();
        assert_eq!(fields.project_id, None);
        assert_eq!(fields.experiment_id, None);
        assert_eq!(fields.dataset_id, None);
        assert_eq!(fields.prompt_session_id, Some("prompt789".to_string()));
        assert_eq!(fields.log_id, None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_playground_logs() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::PlaygroundLogs,
            object_id: ObjectId::new("playground123").unwrap(),
        };

        let fields = make_postgres_object_id_fields(&full_object_id).unwrap();
        assert_eq!(fields.project_id, None);
        assert_eq!(fields.experiment_id, None);
        assert_eq!(fields.dataset_id, None);
        assert_eq!(
            fields.prompt_session_id,
            Some("playground123:x".to_string())
        );
        assert_eq!(fields.log_id, None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_project_logs() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::ProjectLogs,
            object_id: ObjectId::new("project123").unwrap(),
        };

        let fields = make_postgres_object_id_fields(&full_object_id).unwrap();
        assert_eq!(fields.project_id, Some("project123".to_string()));
        assert_eq!(fields.experiment_id, None);
        assert_eq!(fields.dataset_id, None);
        assert_eq!(fields.prompt_session_id, None);
        assert_eq!(fields.log_id, Some("g".to_string()));
    }

    #[test]
    fn test_make_postgres_object_id_fields_project_prompts() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::ProjectPrompts,
            object_id: ObjectId::new("project456").unwrap(),
        };
        assert_eq!(make_postgres_object_id_fields(&full_object_id), None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_project_functions() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::ProjectFunctions,
            object_id: ObjectId::new("project789").unwrap(),
        };
        assert_eq!(make_postgres_object_id_fields(&full_object_id), None);
    }

    #[test]
    fn test_make_postgres_object_id_fields_project() {
        let full_object_id = FullObjectId {
            object_type: ObjectType::Project,
            object_id: ObjectId::new("project999").unwrap(),
        };
        assert_eq!(make_postgres_object_id_fields(&full_object_id), None);
    }

    #[test]
    fn test_roundtrip_conversion_experiment() {
        let original_fields = PostgresObjectIdFields {
            project_id: None,
            experiment_id: Some("exp123"),
            dataset_id: None,
            prompt_session_id: None,
            log_id: None,
        };

        let full_object_id = make_brainstore_object_id(original_fields).unwrap().unwrap();
        let converted_fields = make_postgres_object_id_fields(&full_object_id).unwrap();

        assert_eq!(converted_fields.experiment_id, Some("exp123".to_string()));
        assert_eq!(converted_fields.project_id, None);
        assert_eq!(converted_fields.dataset_id, None);
        assert_eq!(converted_fields.prompt_session_id, None);
        assert_eq!(converted_fields.log_id, None);
    }

    #[test]
    fn test_roundtrip_conversion_project_logs() {
        let original_fields = PostgresObjectIdFields {
            project_id: Some("project123"),
            experiment_id: None,
            dataset_id: None,
            prompt_session_id: None,
            log_id: Some("g"),
        };

        let full_object_id = make_brainstore_object_id(original_fields).unwrap().unwrap();
        let converted_fields = make_postgres_object_id_fields(&full_object_id).unwrap();

        assert_eq!(converted_fields.project_id, Some("project123".to_string()));
        assert_eq!(converted_fields.log_id, Some("g".to_string()));
        assert_eq!(converted_fields.experiment_id, None);
        assert_eq!(converted_fields.dataset_id, None);
        assert_eq!(converted_fields.prompt_session_id, None);
    }
}
