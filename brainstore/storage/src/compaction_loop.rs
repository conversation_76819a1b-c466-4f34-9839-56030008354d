use std::{
    collections::{HashSet, VecDeque},
    sync::{Arc, Mutex},
    time::Instant,
};

use otel_common::opentelemetry::{global, metrics::Histogram, KeyValue};
use tokio::sync::Notify;
use util::{system_types::ObjectType, uuid::Uuid, Result};

// After this point, just reject compactions, which could be used to signal back pressure.
pub const MAX_QUEUED_COMPACTIONS: usize = 1000;

#[derive(Clone)]
pub struct CompactionLoop {
    inner: Arc<CompactionLoopInner>,
    queue_time_histogram: Histogram<u64>,
}

#[derive(Clone, Hash, Eq, PartialEq)]
struct CompactionItem {
    segment_id: Uuid,
    object_type: Option<ObjectType>,
}

struct CompactionLoopInner {
    queue: Mutex<DedupedQueue<CompactionItem>>,
    pub ready: Notify,
}

impl Default for CompactionLoop {
    fn default() -> Self {
        let meter = global::meter("brainstore");
        let queue_time_histogram = meter
            .u64_histogram("brainstore.compaction.queue_time_ms")
            .with_description("Time spent in compaction queue before processing begins")
            .build();

        let inner = Arc::new(CompactionLoopInner {
            queue: Mutex::new(DedupedQueue::new(MAX_QUEUED_COMPACTIONS)),
            ready: Notify::new(),
        });

        Self {
            inner,
            queue_time_histogram,
        }
    }
}

impl CompactionLoop {
    pub fn add_segments(
        &self,
        segments: impl IntoIterator<Item = (Uuid, Option<ObjectType>)>,
    ) -> Result<(), DedupedQueueError> {
        let mut queue = self.inner.queue.lock().unwrap();
        for (segment_id, object_type) in segments {
            let item = CompactionItem {
                segment_id,
                object_type,
            };
            queue.push_back(item)?;
        }
        self.inner.ready.notify_one();
        Ok(())
    }

    pub fn pop_front(&self) -> Option<Uuid> {
        let mut queue = self.inner.queue.lock().unwrap();
        if let Some((item, enqueue_time)) = queue.pop_front() {
            let queue_time_ms = enqueue_time.elapsed().as_millis() as u64;

            let object_type_str = item
                .object_type
                .map(|t| t.to_string())
                .unwrap_or_else(|| "unknown".to_string());

            let attributes = vec![KeyValue::new("object_type", object_type_str)];

            self.queue_time_histogram.record(queue_time_ms, &attributes);
            Some(item.segment_id)
        } else {
            None
        }
    }

    pub async fn notified(&self) {
        self.inner.ready.notified().await;
    }

    pub fn queue_size(&self) -> usize {
        let queue = self.inner.queue.lock().unwrap();
        queue.len()
    }
}

struct DedupedQueue<T: std::hash::Hash + std::cmp::Eq + Clone> {
    queue: VecDeque<(T, Instant)>,
    set: HashSet<T>,
    max_size: usize,
}

pub enum DedupedQueueError {
    QueueFull,
}

impl<T: std::hash::Hash + std::cmp::Eq + Clone> DedupedQueue<T> {
    pub fn new(max_size: usize) -> Self {
        Self {
            queue: VecDeque::new(),
            set: HashSet::new(),
            max_size,
        }
    }

    pub fn push_back(&mut self, item: T) -> Result<(), DedupedQueueError> {
        if !self.set.contains(&item) {
            if self.queue.len() >= self.max_size {
                return Err(DedupedQueueError::QueueFull);
            }
            self.queue.push_back((item.clone(), Instant::now()));
            self.set.insert(item.clone());
        }
        Ok(())
    }

    pub fn pop_front(&mut self) -> Option<(T, Instant)> {
        let item = self.queue.pop_front()?;
        self.set.remove(&item.0);
        Some(item)
    }

    pub fn len(&self) -> usize {
        self.queue.len()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deduped_queue() {
        let mut queue = DedupedQueue::new(5);

        // Test empty queue
        assert!(queue.pop_front().is_none());

        // Test adding and removing items
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(3).is_ok());

        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(1));
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(2));
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(3));
        assert!(queue.pop_front().is_none());

        // Test deduplication
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(1).is_ok()); // Duplicate, should be ignored
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(1).is_ok()); // Duplicate, should be ignored

        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(1));
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(2));
        assert!(queue.pop_front().is_none());

        // Test that items can be re-added after being removed
        assert!(queue.push_back(1).is_ok());
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(1));
        assert!(queue.push_back(1).is_ok()); // Should be allowed now that 1 was removed
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(1));

        // Test max size behavior
        let mut queue = DedupedQueue::new(3);
        assert!(queue.push_back(1).is_ok());
        assert!(queue.push_back(2).is_ok());
        assert!(queue.push_back(3).is_ok());
        // Queue is now at max capacity
        assert!(queue.push_back(4).is_err()); // Should fail due to max size

        // After removing an item, we should be able to add again
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(1));
        assert!(queue.push_back(4).is_ok());

        // Check final queue state
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(2));
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(3));
        assert_eq!(queue.pop_front().map(|(item, _)| item), Some(4));
        assert!(queue.pop_front().is_none());
    }
}
