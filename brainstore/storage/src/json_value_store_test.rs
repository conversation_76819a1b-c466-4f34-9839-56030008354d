use serde_json::json;
use std::path::Path;

use crate::{
    directory::async_directory::AsyncDirectory,
    json_value_store::{read_json_value, write_json_value},
    test_util::TmpDirStore,
};
use util::tokio;

async fn test_json_value_store<T: AsyncDirectory + ?Sized>(directory: &T, prefix: &Path) {
    let path = prefix.join("test.json");
    let value = json!({"foo": "bar"});
    write_json_value(directory, &path, &value).await.unwrap();
    let value2 = read_json_value(directory, &path).await.unwrap();
    assert_eq!(Some(&value), value2.as_ref());
    let other_value = json!({"baz": "qux"});
    write_json_value(directory, &path, &other_value)
        .await
        .unwrap();
    let value2 = read_json_value(directory, &path).await.unwrap();
    assert_eq!(Some(&other_value), value2.as_ref());

    let nonexistentvalue = read_json_value(directory, &prefix.join("nonexistent.json"))
        .await
        .unwrap();
    assert_eq!(None, nonexistentvalue);
}

#[tokio::test]
async fn test_json_value_store_local() {
    let tmp_dir_store = TmpDirStore::new();
    test_json_value_store(
        tmp_dir_store.store_info.directory.as_ref(),
        &tmp_dir_store.store_info.prefix,
    )
    .await;
}
