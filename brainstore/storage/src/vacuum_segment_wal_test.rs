use serde_json::json;
use util::{
    anyhow::Result,
    chrono::{DateTime, Utc},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    retention_test::{run_test_with_global_stores, TestFixture},
    vacuum_segment_wal::{
        vacuum_segment_wal_single_for_testing, VacuumSegmentWalSingleInput,
        VacuumSegmentWalSingleOptions,
    },
    wal::{WALScope, Wal},
    wal_entry::WalEntry,
};

fn wal_inserts() -> Vec<Vec<WalEntry>> {
    vec![
        vec![
            WalEntry {
                _pagination_key: PaginationKey(0),
                _xact_id: TransactionId(0),
                created: DateTime::<Utc>::from_timestamp_nanos(1000),
                id: "row0".to_string(),
                data: json!({
                    "field1": "foo",
                    "field3": json!({ "input": "bar" }),
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(1),
                _xact_id: TransactionId(1),
                created: DateTime::<Utc>::from_timestamp_nanos(2000),
                _is_merge: Some(true),
                id: "row1".to_string(),
                data: json!({
                    "field1": "bar",
                    "field2": -1,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
        vec![
            WalEntry {
                _pagination_key: PaginationKey(2),
                _xact_id: TransactionId(2),
                created: DateTime::<Utc>::from_timestamp_nanos(3000),
                id: "row2".to_string(),
                data: json!({
                    "field1": "baz",
                    "field2": -2,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
            WalEntry {
                _pagination_key: PaginationKey(3),
                _xact_id: TransactionId(3),
                created: DateTime::<Utc>::from_timestamp_nanos(4000),
                id: "row3".to_string(),
                data: json!({
                    "field1": "qux",
                    "field2": -3,
                })
                .as_object()
                .unwrap()
                .clone(),
                ..Default::default()
            },
        ],
    ]
}

// Use a grace period of 0 by default in tests so we don't have to wait for
// files to get vacuumed.
fn default_vacuum_segment_wal_single_options_for_testing() -> VacuumSegmentWalSingleOptions {
    VacuumSegmentWalSingleOptions {
        deletion_grace_period_days: 0,
        ..Default::default()
    }
}

#[tokio::test]
async fn test_vacuum_grace_period() -> Result<()> {
    run_test_with_global_stores(test_vacuum_grace_period_inner).await
}

async fn test_vacuum_grace_period_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;

    // TODO: Move this manual purge call into `vacuum_segment_wal` itself.
    let num_purged = fixture
        .config()
        .global_store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_purged, 4);

    // With default grace period (10 days), vacuuming shouldn't delete any files.
    let result = vacuum_segment_wal_single_for_testing(
        VacuumSegmentWalSingleInput {
            segment_id,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
        },
        VacuumSegmentWalSingleOptions::default(),
    )
    .await?;
    assert_eq!(result.files_deleted, 0);

    // Now use a grace period of 0 and verify that the WAL files get deleted.
    let result = vacuum_segment_wal_single_for_testing(
        VacuumSegmentWalSingleInput {
            segment_id,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
        },
        default_vacuum_segment_wal_single_options_for_testing(),
    )
    .await?;
    assert_eq!(result.files_deleted, 2);

    Ok(())
}

#[tokio::test]
async fn test_vacuum_purged_wal_files() -> Result<()> {
    run_test_with_global_stores(test_vacuum_purged_wal_files_inner).await
}

async fn test_vacuum_purged_wal_files_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let segment_id = Uuid::new_v4();
    let wal = fixture.make_segment_wal();
    fixture.initialize_segment_metadata(segment_id).await;

    let entries_seq = wal_inserts();
    for entries in entries_seq.iter() {
        wal.insert(WALScope::Segment(segment_id), entries.clone())
            .await?;
    }

    let inserted_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(inserted_entries.len(), 4);

    assert_eq!(inserted_entries[0].0, TransactionId(0));
    assert_eq!(inserted_entries[0].1[0].id, "row0");
    assert_eq!(inserted_entries[1].0, TransactionId(1));
    assert_eq!(inserted_entries[1].1[0].id, "row1");
    assert_eq!(inserted_entries[2].0, TransactionId(2));
    assert_eq!(inserted_entries[2].1[0].id, "row2");
    assert_eq!(inserted_entries[3].0, TransactionId(3));
    assert_eq!(inserted_entries[3].1[0].id, "row3");

    let segment_wal_entries = wal
        .global_store
        .query_segment_wal_entries_batch(segment_id, None, None, None)
        .await?;
    assert_eq!(segment_wal_entries.len(), 4);

    let wal_filename0 = segment_wal_entries[0].wal_filename;
    let wal_filename1 = segment_wal_entries[1].wal_filename;
    assert_eq!(wal_filename0, wal_filename1);
    let wal_filename2 = segment_wal_entries[2].wal_filename;
    let wal_filename3 = segment_wal_entries[3].wal_filename;
    assert_eq!(wal_filename2, wal_filename3);
    assert_ne!(wal_filename0, wal_filename2);

    let wal_directory = wal.wal_directory(WALScope::Segment(segment_id));

    let wal_path_a = wal_directory.join(wal_filename0.to_string());
    let wal_path_b = wal_directory.join(wal_filename2.to_string());

    // Check that both WAL files exist at the expected locations.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Create additional junk files in the directory. Vacuuming should clean this up too.

    // Valid UUID not in the database.
    let fake_uuid = Uuid::new_v4();
    let fake_uuid_path = wal_directory.join(fake_uuid.to_string());
    wal.directory
        .async_atomic_write(&fake_uuid_path, b"Not in database")
        .await?;

    // Filename is not a valid UUID.
    let invalid_path = wal_directory.join("not-a-valid-uuid");
    wal.directory
        .async_atomic_write(&invalid_path, b"Invalid filename")
        .await?;

    // Verify all created files/paths exist.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(wal.directory.async_exists(&fake_uuid_path).await?);
    assert!(wal.directory.async_exists(&invalid_path).await?);

    fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(2))
        .await?;
    // TODO: Move this manual purge call into `vacuum_segment_wal` itself.
    let num_purged = fixture
        .config()
        .global_store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_purged, 2);

    // The segment files should still exist after purging but before vacuuming.
    assert!(wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Vacuuming should delete the first WAL file since all of its entries should
    // have been purged (xact_id < 2).
    let result = vacuum_segment_wal_single_for_testing(
        VacuumSegmentWalSingleInput {
            segment_id,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
        },
        default_vacuum_segment_wal_single_options_for_testing(),
    )
    .await?;

    // We should have deleted the first WAL file and the 2 junk files.
    assert_eq!(result.files_deleted, 3);
    assert!(!wal.directory.async_exists(&wal_path_a).await?);
    assert!(wal.directory.async_exists(&wal_path_b).await?);
    assert!(!wal.directory.async_exists(&fake_uuid_path).await?);
    assert!(!wal.directory.async_exists(&invalid_path).await?);

    // Now purge up to xact_id 3.
    fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(3))
        .await?;
    // TODO: Move this manual purge call into `vacuum_segment_wal` itself.
    let num_purged = fixture
        .config()
        .global_store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_purged, 1);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Vacuuming should not delete the second WAL file, since it still contains
    // a live entry (only one of its two entries was purged).
    let result = vacuum_segment_wal_single_for_testing(
        VacuumSegmentWalSingleInput {
            segment_id,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
        },
        default_vacuum_segment_wal_single_options_for_testing(),
    )
    .await?;
    assert_eq!(result.files_deleted, 0);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Now purge up to xact_id 4, which should finally purge the second entry.
    fixture
        .config()
        .global_store
        .delete_segment_wal_entries_up_to_xact_id(&[segment_id], TransactionId(4))
        .await?;
    // TODO: Move this manual purge call into `vacuum_segment_wal` itself.
    let num_purged = fixture
        .config()
        .global_store
        .purge_deleted_segment_wal_entries(&[segment_id], 0)
        .await
        .unwrap();
    assert_eq!(num_purged, 1);
    assert!(wal.directory.async_exists(&wal_path_b).await?);

    // Vacuuming should now delete the second file since all of its entries
    // have been purged.
    let result = vacuum_segment_wal_single_for_testing(
        VacuumSegmentWalSingleInput {
            segment_id,
            index_store: fixture.config().index.clone(),
            global_store: fixture.config().global_store.clone(),
            locks_manager: &*fixture.config().locks_manager,
        },
        default_vacuum_segment_wal_single_options_for_testing(),
    )
    .await?;
    assert_eq!(result.files_deleted, 1);
    assert!(!wal.directory.async_exists(&wal_path_a).await?);
    assert!(!wal.directory.async_exists(&wal_path_b).await?);

    Ok(())
}
