use std::{path::PathBuf, sync::Arc};

use clap::Parser;
use serde::{Deserialize, Serialize};
use util::{
    anyhow::{anyhow, Result},
    chrono::{Duration, Utc},
    futures::{StreamExt, TryStreamExt},
    uuid::Uuid,
};

use crate::{
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{GlobalStore, SegmentWalEntriesXactIdStatistic},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    wal::WALScope,
};

pub struct VacuumSegmentWalInput<'a> {
    pub segment_ids: Vec<Uuid>,
    pub index_store: StoreInfo,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub dry_run: bool,
}

fn default_metadata_query_batch_size() -> usize {
    1000
}

/// Default grace period in days before files can be deleted by the vacuum process.
/// Files modified within this number of days ago will be ignored.
fn default_deletion_grace_period_days() -> i64 {
    10
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Parser, Serialize, Deserialize)]
pub struct VacuumSegmentWalOptions {
    #[arg(
        long,
        default_value_t = default_metadata_query_batch_size(),
        env = "BRAINSTORE_VACUUM_METADATA_QUERY_BATCH_SIZE"
    )]
    #[serde(default = "default_metadata_query_batch_size")]
    pub metadata_query_batch_size: usize,

    #[arg(
        long,
        default_value_t = default_deletion_grace_period_days(),
        env = "BRAINSTORE_VACUUM_DELETION_GRACE_PERIOD_DAYS"
    )]
    #[serde(default = "default_deletion_grace_period_days")]
    pub deletion_grace_period_days: i64,
}

impl Default for VacuumSegmentWalOptions {
    fn default() -> Self {
        Self {
            metadata_query_batch_size: default_metadata_query_batch_size(),
            deletion_grace_period_days: default_deletion_grace_period_days(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VacuumSegmentWalOutput {
    pub segment_ids: Vec<Uuid>,
    pub files_deleted: usize,
}

pub async fn vacuum_segment_wal(
    input: VacuumSegmentWalInput<'_>,
    options: VacuumSegmentWalOptions,
) -> Result<VacuumSegmentWalOutput> {
    let min_xact_ids = input
        .global_store
        .query_segment_wal_entries_xact_id_statistic(
            &input.segment_ids,
            SegmentWalEntriesXactIdStatistic::Min,
            None,
        )
        .await?;

    let mut segment_ids_with_xact: Vec<_> = input
        .segment_ids
        .iter()
        .zip(min_xact_ids.iter())
        .map(|(segment_id, min_xact_id)| (*segment_id, min_xact_id.clone()))
        .collect();

    if input.dry_run {
        eprintln!(
            "Would vacuum WAL files for {} segment(s)",
            segment_ids_with_xact.len()
        );
        return Ok(VacuumSegmentWalOutput {
            segment_ids: input.segment_ids.clone(),
            files_deleted: 0,
        });
    }

    // Work through the segments with the earliest `min_xact_id` first. Segments
    // missing this statistic get shoved to the end.
    segment_ids_with_xact.sort_by(|a, b| match (&a.1, &b.1) {
        (None, None) => std::cmp::Ordering::Equal,
        (None, Some(_)) => std::cmp::Ordering::Greater,
        (Some(_), None) => std::cmp::Ordering::Less,
        (Some(x), Some(y)) => x.cmp(y),
    });

    let sorted_segment_ids: Vec<_> = segment_ids_with_xact
        .into_iter()
        .map(|(id, _)| id)
        .collect();

    let mut files_deleted = 0;
    let mut first_error = None;
    for &segment_id in sorted_segment_ids.iter() {
        let result = vacuum_segment_wal_single(
            VacuumSegmentWalSingleInput {
                segment_id,
                index_store: input.index_store.clone(),
                global_store: input.global_store.clone(),
                locks_manager: input.locks_manager,
            },
            VacuumSegmentWalSingleOptions {
                metadata_query_batch_size: options.metadata_query_batch_size,
                deletion_grace_period_days: options.deletion_grace_period_days,
            },
        )
        .await?;

        files_deleted += result.files_deleted;
        if first_error.is_none() {
            first_error = result.first_error;
        }
    }

    Ok(VacuumSegmentWalOutput {
        segment_ids: sorted_segment_ids,
        files_deleted,
    })
}

pub struct VacuumSegmentWalSingleInput<'a> {
    pub segment_id: Uuid,
    pub index_store: StoreInfo,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: &'a dyn GlobalLocksManager,
}

#[derive(Debug, Copy, Clone)]
pub struct VacuumSegmentWalSingleOptions {
    pub metadata_query_batch_size: usize,
    pub deletion_grace_period_days: i64,
}

impl Default for VacuumSegmentWalSingleOptions {
    fn default() -> Self {
        Self {
            metadata_query_batch_size: default_metadata_query_batch_size(),
            deletion_grace_period_days: default_deletion_grace_period_days(),
        }
    }
}

#[derive(Debug)]
pub struct VacuumSegmentWalSingleOutput {
    pub files_deleted: usize,
    pub first_error: Option<object_store::Error>,
}

/// This vacuum operation deletes segment WAL files that can be safely purged.
/// The procedure is as follows:
///
/// 1. Query the global store to find WAL files whose entries are all marked as purged.
/// 2. Delete those files (if they exist) from the index store directory.
///
/// Returns the number of files deleted.
async fn vacuum_segment_wal_single(
    input: VacuumSegmentWalSingleInput<'_>,
    options: VacuumSegmentWalSingleOptions,
) -> Result<VacuumSegmentWalSingleOutput> {
    let index_store = input.index_store;

    let segment_index_wal = ObjectAndGlobalStoreWal {
        object_store: index_store.store.clone(),
        global_store: input.global_store.clone(),
        directory: index_store.directory.clone(),
        store_prefix: index_store.prefix.clone(),
        store_type: index_store.store_type,
    };

    let wal_directory = segment_index_wal.wal_directory(WALScope::Segment(input.segment_id));
    let wal_directory_str = wal_directory
        .to_str()
        .ok_or(anyhow!("Invalid WAL directory"))?;
    let wal_prefix = object_store::path::Path::from(wal_directory_str);

    // Calculate the last_modified cutoff time using the configured grace period.
    let last_modified_cutoff_time = Utc::now() - Duration::days(options.deletion_grace_period_days);

    let paths_to_delete_stream = segment_index_wal.object_store.list(Some(&wal_prefix))
        .chunks(options.metadata_query_batch_size)
        .then(|batch| {
            let segment_index_wal = segment_index_wal.clone();
            let segment_id = input.segment_id;
            let wal_directory = wal_directory.clone();

            log::info!("Processing batch of {} WAL files", batch.len());

            async move {
                let mut valid_uuid_paths: Vec<String> = Vec::new();

                let mut paths_to_delete: Vec<Result<PathBuf, object_store::Error>> = Vec::new();
                let mut valid_filenames = Vec::new();

                for meta_result in batch.into_iter() {
                    match meta_result {
                        // Pass errors through the stream untouched.
                        Err(e) => paths_to_delete.push(Err(e)),
                        Ok(meta) => {
                            // Don't delete the file if it's within our grace period.
                            if meta.last_modified > last_modified_cutoff_time {
                                continue;
                            }

                            if let Some(filename) = meta.location.filename() {
                                match Uuid::parse_str(filename) {
                                    Ok(uuid) => {
                                        valid_filenames.push(uuid);
                                        valid_uuid_paths.push(filename.to_string());
                                    },
                                    Err(_) => {
                                        // If the filename isn't a valid UUID, it's junk. Delete it.
                                        log::warn!("Path with an invalid (non-UUID) filename will be deleted: {}", meta.location.to_string());

                                        // Assert that the joined path matches the original path.
                                        let joined_path = wal_directory.join(filename);
                                        let original_path = PathBuf::from(meta.location.to_string());
                                        assert_eq!(
                                            joined_path.to_str(),
                                            original_path.to_str(),
                                            "Joined path '{}' doesn't match original path '{}'",
                                            joined_path.display(),
                                            original_path.display()
                                        );

                                        paths_to_delete.push(Ok(joined_path));
                                    },
                                }
                            } else {
                                // If the filename is invalid, it's junk. Delete it.
                                log::warn!("Path without a filename will be deleted: {}", meta.location.to_string());

                                paths_to_delete.push(Ok(PathBuf::from(meta.location.to_string())));
                            }
                        },
                    }
                }

                // Check which valid UUIDs should be purged by checking the global store.
                if !valid_filenames.is_empty() {
                    let purged_filenames = segment_index_wal.global_store
                        .query_purged_wal_filenames(segment_id, &valid_filenames)
                        .await
                        .map_err(|e| object_store::Error::Generic {
                            store: "global_store",
                            source: Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
                        })?;

                    for uuid in purged_filenames {
                        paths_to_delete.push(Ok(wal_directory.join(uuid.to_string())));
                    }
                }

                log::info!("Found {} vacuumable files in batch", paths_to_delete.len());

                Ok::<_, object_store::Error>(util::futures::stream::iter(paths_to_delete))
            }
        })
        .try_flatten()
        .boxed();

    let mut num_files_deleted = 0;
    let mut first_error = None;
    let mut delete_stream = segment_index_wal
        .directory
        .delete_stream(paths_to_delete_stream);

    while let Some(result) = delete_stream.next().await {
        match result {
            Ok(_) => num_files_deleted += 1,
            Err(e) => {
                if !matches!(e, object_store::Error::NotFound { .. }) {
                    log::warn!("Error deleting file: {:?}", e);
                    if first_error.is_none() {
                        first_error = Some(e);
                    }
                }
            }
        }
    }

    log::info!("Vacuumed {} WAL files in total", num_files_deleted);
    log::info!("First error: {:?}", first_error);

    Ok(VacuumSegmentWalSingleOutput {
        files_deleted: num_files_deleted,
        first_error,
    })
}

#[cfg(test)]
pub async fn vacuum_segment_wal_single_for_testing(
    input: VacuumSegmentWalSingleInput<'_>,
    options: VacuumSegmentWalSingleOptions,
) -> Result<VacuumSegmentWalSingleOutput> {
    vacuum_segment_wal_single(input, options).await
}
