use std::{collections::HashMap, path::Path};

use crate::{
    directory::dynamic_readonly_directory::{DynamicIndexSpec, DynamicReadonlyDirectory},
    directory::AsyncDirectoryArc,
    tantivy_index::{
        self, make_tantivy_index_blocking, make_tantivy_schema, IndexMetaJson, TantivyIndexScope,
        TantivyIndexWriterOpts, WritableTantivySchema,
    },
};
use tantivy::TantivyDocument;
use tracing::instrument;
use util::{anyhow::Result, await_spawn_blocking, uuid::Uuid};

/// Convenience wrapper for creating a tantivy index and related structures with
/// brainstore utilities.
/// Note that this wrapper uses the index meta in the index directory. If you want
/// to perform reads using a different index meta, e.g. the global store index meta,
/// consider using a ReadonlyTantivyIndexWrapper instead.
pub struct ReadWriteTantivyIndexWrapper {
    pub index: tantivy::Index,
    pub schema: util::schema::Schema,
    pub writable_schema: WritableTantivySchema,
}

impl ReadWriteTantivyIndexWrapper {
    #[instrument(
        err,
        skip(directory, full_schema, store_prefix, index_scope),
        name = "make_tantivy_index_blocking"
    )]
    pub fn new_blocking(
        directory: AsyncDirectoryArc,
        full_schema: util::schema::Schema,
        store_prefix: &Path,
        index_scope: &TantivyIndexScope,
    ) -> Result<Self> {
        let writable_schema = make_tantivy_schema(&full_schema)?;
        let index = make_tantivy_index_blocking(
            directory,
            writable_schema.schema.clone(),
            store_prefix,
            index_scope,
        )?;
        Ok(ReadWriteTantivyIndexWrapper {
            index,
            schema: full_schema,
            writable_schema,
        })
    }

    pub async fn new(
        directory: AsyncDirectoryArc,
        full_schema: util::schema::Schema,
        store_prefix: &Path,
        index_scope: &TantivyIndexScope,
    ) -> Result<Self> {
        await_spawn_blocking!(
            move |index_scope: &TantivyIndexScope, store_prefix: &Path| {
                ReadWriteTantivyIndexWrapper::new_blocking(
                    directory,
                    full_schema,
                    store_prefix,
                    index_scope,
                )
            },
            index_scope,
            store_prefix
        )??
    }

    pub fn tantivy_schema(&self) -> &tantivy::schema::Schema {
        &self.writable_schema.schema
    }

    pub fn make_reader_blocking(&self) -> Result<tantivy::IndexReader> {
        tantivy_index::make_reader_blocking(&self.index)
    }

    pub async fn make_reader(&self) -> Result<tantivy::IndexReader> {
        let this = self;
        await_spawn_blocking!(
            move |this: &ReadWriteTantivyIndexWrapper| { this.make_reader_blocking() },
            this
        )??
    }

    pub fn make_writer_blocking(
        &self,
        opts: &TantivyIndexWriterOpts,
    ) -> Result<tantivy::IndexWriter<TantivyDocument>> {
        tantivy_index::make_writer_blocking(&self.index, opts)
    }

    pub async fn make_writer(
        &self,
        opts: &TantivyIndexWriterOpts,
    ) -> Result<tantivy::IndexWriter<TantivyDocument>> {
        let this = self;
        await_spawn_blocking!(
            move |this: &ReadWriteTantivyIndexWrapper, opts: &TantivyIndexWriterOpts| {
                this.make_writer_blocking(opts)
            },
            this,
            opts
        )??
    }
}

/// Wrapper for a readonly tantivy index that uses the provided tantivy index metas.
/// Typically, the caller should provide index metas fetched from the global store
/// rather than the `meta.json` in the index directory, which could be inconsistent
/// with the "live" state of the index in brainstore.
pub struct ReadonlyTantivyIndexWrapper {
    pub index: tantivy::Index,
    pub schema: util::schema::Schema,
    pub writable_schema: WritableTantivySchema,
}

impl ReadonlyTantivyIndexWrapper {
    pub async fn new(
        directory: AsyncDirectoryArc,
        full_schema: util::schema::Schema,
        store_prefix: &Path,
        segment_id_to_index_meta: HashMap<Uuid, IndexMetaJson>,
    ) -> Result<Self> {
        let writable_schema = make_tantivy_schema(&full_schema)?;

        let dynamic_segment_specs =
            make_dynamic_segment_specs(directory, store_prefix, segment_id_to_index_meta);
        let dynamic_readonly_directory =
            DynamicReadonlyDirectory::new(writable_schema.schema.clone(), dynamic_segment_specs)
                .await?;
        let tantivy_index = tantivy::Index::open(dynamic_readonly_directory)?;

        Ok(ReadonlyTantivyIndexWrapper {
            index: tantivy_index,
            schema: full_schema,
            writable_schema,
        })
    }

    pub fn tantivy_schema(&self) -> &tantivy::schema::Schema {
        &self.writable_schema.schema
    }

    pub fn make_reader_blocking(&self) -> Result<tantivy::IndexReader> {
        tantivy_index::make_reader_blocking(&self.index)
    }

    pub async fn make_reader(&self) -> Result<tantivy::IndexReader> {
        let this = self;
        await_spawn_blocking!(
            move |this: &ReadonlyTantivyIndexWrapper| { this.make_reader_blocking() },
            this
        )??
    }
}

/// Makes dynamic index specs for a batch of segments using the provided index metas.
fn make_dynamic_segment_specs(
    directory: AsyncDirectoryArc,
    store_prefix: &Path,
    segment_id_to_index_meta: HashMap<Uuid, IndexMetaJson>,
) -> Vec<DynamicIndexSpec> {
    segment_id_to_index_meta
        .into_iter()
        .map(|(segment_id, index_meta)| DynamicIndexSpec {
            directory: directory.clone(),
            path: TantivyIndexScope::Segment(segment_id).path(&store_prefix),
            meta: index_meta,
        })
        .collect::<Vec<_>>()
}
