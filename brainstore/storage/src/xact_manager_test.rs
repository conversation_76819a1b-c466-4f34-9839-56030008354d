use std::collections::HashSet;
use std::sync::Arc;

use crate::redis_xact_manager::RedisTransactionManager;
use crate::xact_manager::{MemoryTransactionManager, TransactionManager};

use crate::test_util::RedisContainer;

use util::{
    futures::{
        future,
        stream::{self, StreamExt},
    },
    tokio,
};

async fn test_transaction_manager_helper(xact_manager: Arc<impl TransactionManager + 'static>) {
    // Status check should succeed.
    xact_manager.status().await.unwrap();

    let xact_ids = future::join_all((1..=10).map(|_| {
        let xact_manager = xact_manager.clone();
        tokio::spawn(async move {
            stream::iter(1..=100)
                .then(|_| async {
                    tokio::task::yield_now().await;
                    xact_manager.next_xact_id().await.unwrap().to_string()
                })
                .collect::<Vec<_>>()
                .await
        })
    }))
    .await
    .into_iter()
    .map(|res| res.unwrap())
    .collect::<Vec<_>>();

    // Make sure the transaction IDs are strictly increasing within each task, and unique across
    // all tasks.
    let mut all_xact_ids = HashSet::<&str>::new();
    xact_ids.iter().for_each(|xact_id_group| {
        xact_id_group.iter().enumerate().for_each(|(idx, xact_id)| {
            assert!(
                all_xact_ids.insert(xact_id),
                "duplicate xact_id: {}",
                xact_id
            );
            if idx == 0 {
                return;
            }
            let prev = &xact_id_group[idx - 1];
            assert!(xact_id > prev, "xact_id: {}, prev: {}", xact_id, prev);
        });
    });
}

#[tokio::test]
async fn test_memory_transaction_manager() {
    test_transaction_manager_helper(Arc::new(MemoryTransactionManager::default())).await
}

#[tokio::test]
async fn test_redis_transaction_manager() {
    let container = RedisContainer::new().await;
    test_transaction_manager_helper(Arc::new(
        RedisTransactionManager::new(container.connection_url.clone()).unwrap(),
    ))
    .await;
}
