use object_store::{path::Path, ObjectStore};

use util::anyhow::{anyhow, Result};

fn generate_file_suffix() -> String {
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let uuid = util::uuid::Uuid::new_v4().to_string().replace('-', "");
    format!("{}_{}", timestamp, &uuid[..8])
}
pub async fn validate_object_store_connection(
    store: &dyn ObjectStore,
    prefix: &std::path::PathBuf,
) -> Result<()> {
    let prefix_path = Path::parse(
        prefix
            .to_str()
            .ok_or_else(|| anyhow!("Invalid UTF-8 in path"))?,
    )?;
    store.list_with_delimiter(Some(&prefix_path)).await?;

    // Test reading/writing a small object.
    let obj_name = prefix_path.child(format!(
        ".test_objects/healthcheck_object_store_{}",
        generate_file_suffix()
    ));
    let obj_data = b"Hello, World!";
    store.put(&obj_name, obj_data.to_vec().into()).await?;
    let data = store.get(&obj_name).await?.bytes().await?;
    assert_eq!(data.as_ref(), obj_data);
    store.delete(&obj_name).await?;

    Ok(())
}

pub async fn validate_redis_connection(conn: &redis::Client) -> Result<()> {
    let mut con = conn.get_multiplexed_async_connection().await?;
    let _: String = redis::cmd("PING").query_async(&mut con).await?;

    let test_key = format!("healthcheck_redis_{}", generate_file_suffix());
    let test_value = "test_value";
    redis::cmd("SET")
        .arg(&test_key)
        .arg(test_value)
        .arg("EX")
        .arg(120)
        .query_async::<()>(&mut con)
        .await?;

    let value: String = redis::cmd("GET")
        .arg(&test_key)
        .query_async(&mut con)
        .await?;
    assert_eq!(value, test_value);

    redis::cmd("DEL")
        .arg(&test_key)
        .query_async::<()>(&mut con)
        .await?;

    Ok(())
}

pub async fn validate_postgres_connection(mut conn: deadpool_postgres::Object) -> Result<()> {
    match conn.query("SELECT 1", &[]).await?.into_iter().next() {
        Some(_) => {}
        None => return Err(anyhow!("SELECT query returned no rows")),
    };

    let test_table = format!("healthcheck_postgres_{}", generate_file_suffix());

    // Create and test table within a transaction
    let tx = conn.transaction().await?;
    tx.execute(
        &format!(
            "CREATE TEMPORARY TABLE \"{}\" (id SERIAL PRIMARY KEY, value TEXT) ON COMMIT DROP",
            test_table
        ),
        &[],
    )
    .await?;

    tx.execute_raw(
        &format!("INSERT INTO \"{}\" (value) VALUES ($1)", test_table),
        &["test_value"],
    )
    .await?;

    let row = tx
        .query_one(
            &format!("SELECT value FROM \"{}\" WHERE id = 1", test_table),
            &[],
        )
        .await?;
    let value: &str = row.get(0);
    assert_eq!(value, "test_value");

    // Table will be dropped when transaction rolls back
    tx.rollback().await?;

    Ok(())
}
