// A testing-only locks manager that is useful for testing what happens when
// locks do not work as intended.
use std::collections::HashMap;
use util::anyhow::Result;
use util::async_trait::async_trait;

use crate::global_locks_manager::{
    GlobalLockReadGuard, GlobalLockWriteGuard, GlobalLocksManager, LockState,
};

#[derive(Debug, Default)]
pub struct NoopGlobalLocksManager {}
#[derive(Debug)]
struct NoopGlobalReadLockGuard {}
#[derive(Debug)]
struct NoopGlobalWriteLockGuard {}

#[async_trait]
impl GlobalLocksManager for NoopGlobalLocksManager {
    async fn read_impl(&self, _name: &str) -> Result<Box<dyn GlobalLockReadGuard>> {
        Ok(Box::new(NoopGlobalReadLockGuard {}))
    }

    async fn try_read(&self, _name: &str) -> Result<Option<Box<dyn GlobalLockReadGuard>>> {
        Ok(Some(Box::new(NoopGlobalReadLockGuard {})))
    }

    async fn write_impl(&self, _name: &str) -> Result<Box<dyn GlobalLockWriteGuard>> {
        Ok(Box::new(NoopGlobalWriteLockGuard {}))
    }

    async fn try_write(&self, _name: &str) -> Result<Option<Box<dyn GlobalLockWriteGuard>>> {
        Ok(Some(Box::new(NoopGlobalWriteLockGuard {})))
    }

    async fn snapshot_lock_state(&self) -> Result<HashMap<String, LockState>> {
        Ok(HashMap::new())
    }

    async fn status(&self) -> Result<String> {
        Ok("NoopGlobalLocksManager is ok".into())
    }
}

impl GlobalLockReadGuard for NoopGlobalReadLockGuard {}
impl GlobalLockWriteGuard for NoopGlobalWriteLockGuard {}
