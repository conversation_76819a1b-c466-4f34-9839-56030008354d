use lazy_static::lazy_static;
use object_store::{local::LocalFileSystem, path::Path as ObjectStorePath, ObjectStore};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::{<PERSON>, Mutex},
    time::Duration,
};

use util::{anyhow::Result, config::StorageConfig, url::Url, url_util::ObjectStoreType};

use crate::{
    directory::{
        async_directory::AsyncDirectoryArc,
        cached_directory::{
            CacheDirectory, CacheDirectoryKey, DirectCacheFile, FileCacheDirectory, FileCacheOpts,
        },
        mmap_directory::MmapCacheFile,
        object_store_directory::ObjectStoreDirectory,
    },
    global_locks_manager::{GlobalLocksManager, MemoryGlobalLocksManager},
    global_store::{DirectoryGlobalStore, GlobalStore},
    healthcheck_util::validate_object_store_connection,
    instrumented::Instrumented,
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    object_store_locks_manager::ObjectStoreLocksManager,
    object_store_wal::ObjectStoreWal,
    postgres_global_store::PostgresGlobalStore,
    postgres_locks_manager::{PostgresGlobalLocksManager, DEFAULT_MAX_PG_LOCKS},
    postgres_wal::{
        PostgresWAL, POSTGRES_WAL_COMMENTS_TABLE, POSTGRES_WAL_TABLE, POSTGRES_WAL_TABLE2,
    },
    redis_locks_manager::RedisLocksManager,
    redis_xact_manager::RedisTransactionManager,
    resource_limited_object_store::ResourceLimitedObjectStore,
    wal::Wal,
    xact_manager::{MemoryTransactionManager, TransactionManager},
};

#[derive(Debug, Clone)]
pub struct StoreInfo {
    pub store: Arc<dyn ObjectStore>,
    pub store_type: ObjectStoreType,
    pub directory: AsyncDirectoryArc,
    pub prefix: PathBuf,
}

impl StoreInfo {
    pub fn new(url: &Url, file_cache_opts: FileCacheOpts) -> Result<Self> {
        let (store, store_type, prefix) = url_to_store(url)?;
        let directory = Self::make_directory(store.clone(), file_cache_opts)?;

        Ok(StoreInfo {
            store,
            store_type,
            directory,
            prefix: Path::new(prefix.as_ref()).to_owned(),
        })
    }

    fn make_directory(
        store: Arc<dyn ObjectStore>,
        file_cache_opts: FileCacheOpts,
    ) -> Result<AsyncDirectoryArc> {
        let cold_directory = AsyncDirectoryArc::new(ObjectStoreDirectory::new(store));
        let directory = FileCacheDirectory::new(cold_directory, None, file_cache_opts)?;

        if log::log_enabled!(log::Level::Debug) {
            directory.enable_timing();
        }

        Ok(AsyncDirectoryArc::new(directory))
    }

    pub async fn status(&self) -> Result<String> {
        validate_object_store_connection(&self.store, &self.prefix).await?;
        Ok(format!("{:?} index is ok", self.store_type))
    }

    pub fn remove_local(&self) -> Result<()> {
        let full_path = format!("/{}", &self.prefix.to_str().unwrap());
        if std::fs::metadata(&full_path).is_ok() {
            std::fs::remove_dir_all(&full_path)?;
        }
        Ok(())
    }
}

fn url_to_wal(
    url: &Url,
    file_cache_opts: FileCacheOpts,
    directory: AsyncDirectoryArc,
) -> Result<Arc<dyn Wal>> {
    Ok(match url.scheme() {
        "postgres" | "postgresql" => Arc::new(PostgresWAL::new(
            url,
            POSTGRES_WAL_TABLE,
            POSTGRES_WAL_TABLE2,
            POSTGRES_WAL_COMMENTS_TABLE,
            directory,
        )?),
        _ => {
            let store_info = StoreInfo::new(url, file_cache_opts)?;
            Arc::new(ObjectStoreWal {
                store: store_info.store,
                store_type: store_info.store_type,
                directory: store_info.directory,
                store_prefix: store_info.prefix.clone(),
            })
        }
    })
}

pub fn url_to_global_store(
    url: &Url,
    file_cache_opts: FileCacheOpts,
    locks_manager: Arc<dyn GlobalLocksManager>,
) -> Result<(Arc<dyn GlobalStore>, Option<StoreInfo>)> {
    Ok(match url.scheme() {
        "postgres" | "postgresql" => (Arc::new(PostgresGlobalStore::new(url)?), None),
        _ => {
            let store_info = StoreInfo::new(url, file_cache_opts)?;
            (
                Arc::new(DirectoryGlobalStore::new(
                    store_info.directory.clone(),
                    locks_manager,
                    store_info.prefix.clone(),
                )),
                Some(store_info),
            )
        }
    })
}

pub fn url_to_locks_manager(
    url: &Url,
    enable_bookkeeping: bool,
) -> Result<Arc<dyn GlobalLocksManager>> {
    Ok(match url.scheme() {
        "memory" => Arc::new(MemoryGlobalLocksManager::default()),
        "postgres" | "postgresql" => {
            Arc::new(PostgresGlobalLocksManager::new(url, DEFAULT_MAX_PG_LOCKS)?)
                as Arc<dyn GlobalLocksManager>
        }
        "redis" | "rediss" => Arc::new(RedisLocksManager::new_with_opts(
            vec![url.clone()],
            crate::redis_locks_manager::DEFAULT_LOCK_PREFIX.to_string(),
            Duration::from_secs(crate::redis_locks_manager::DEFAULT_LOCK_TTL_SECONDS),
            enable_bookkeeping,
        )?),
        _ => {
            let (store, _, prefix) = url_to_store(url)?;
            Arc::new(ObjectStoreLocksManager::new_with_opts(
                Arc::from(store),
                Path::new(prefix.as_ref())
                    .join(crate::object_store_locks_manager::DEFAULT_LOCK_PREFIX)
                    .to_string_lossy()
                    .into_owned(),
                std::time::Duration::from_secs(
                    crate::object_store_locks_manager::DEFAULT_LOCK_TTL_SECONDS,
                ),
            ))
        }
    })
}

lazy_static! {
    static ref OBJECT_STORE_CACHE: Mutex<HashMap<String, Arc<dyn ObjectStore>>> =
        Mutex::new(HashMap::new());
}

pub fn url_to_store(url: &Url) -> Result<(Arc<dyn ObjectStore>, ObjectStoreType, ObjectStorePath)> {
    let path = ObjectStorePath::parse(url.path())?;

    let store_type = match url.scheme() {
        "file" => ObjectStoreType::Local,
        "s3" => ObjectStoreType::S3,
        "gs" => ObjectStoreType::GCS,
        "az" | "adl" | "azure" | "abfs" | "abfss" | "https"
            if url.host_str() == Some("blob.core.windows.net") =>
        {
            ObjectStoreType::Azure
        }
        _ => ObjectStoreType::Other,
    };

    let cache_key = if url.scheme() == "file" {
        // Don't cache local filesystem
        let store = Arc::new(ResourceLimitedObjectStore::new(Box::new(
            LocalFileSystem::new(),
        )));
        return Ok((store, store_type, path));
    } else {
        // For cloud stores, cache by scheme + host + first path segment (bucket/container)
        let bucket = url
            .path_segments()
            .and_then(|mut segments| segments.next())
            .unwrap_or("");
        format!(
            "{}://{}/{}",
            url.scheme(),
            url.host_str().unwrap_or(""),
            bucket
        )
    };

    {
        let cache = OBJECT_STORE_CACHE.lock().unwrap();
        if let Some(store) = cache.get(&cache_key) {
            return Ok((store.clone(), store_type, path));
        }
    }

    let store: Box<dyn ObjectStore> = if url.scheme() == "s3" {
        let builder = object_store::aws::AmazonS3Builder::from_env()
            .with_conditional_put(object_store::aws::S3ConditionalPut::ETagMatch)
            .with_url(url.to_string());
        Box::new(builder.build()?)
    } else if url.scheme() == "gs" {
        let builder =
            object_store::gcp::GoogleCloudStorageBuilder::from_env().with_url(url.to_string());
        Box::new(builder.build()?)
    } else if url.scheme() == "az"
        || url.scheme() == "adl"
        || url.scheme() == "azure"
        || url.scheme() == "abfs"
        || url.scheme() == "abfss"
        || (url.scheme() == "https" && url.host_str() == Some("blob.core.windows.net"))
    {
        let builder =
            object_store::azure::MicrosoftAzureBuilder::from_env().with_url(url.to_string());
        Box::new(builder.build()?)
    } else {
        let (store, _) = object_store::parse_url(url)?;
        store
    };

    let limited_store = Arc::new(ResourceLimitedObjectStore::new(store));

    // If another thread created and successfully inserted a store in the meantime, use that.
    // Otherwise, insert this into the cache.
    let final_store = {
        let mut cache = OBJECT_STORE_CACHE.lock().unwrap();
        cache
            .entry(cache_key)
            .or_insert_with(|| {
                log::info!("Caching new object store");
                limited_store
            })
            .clone()
    };

    Ok((final_store, store_type, path))
}

pub fn url_to_xact_manager(url: &Url) -> Result<Arc<dyn TransactionManager>> {
    Ok(match url.scheme() {
        "memory" => Arc::new(MemoryTransactionManager::default()),
        "redis" | "rediss" => Arc::new(RedisTransactionManager::new(url.clone())?),
        _ => Arc::new(MemoryTransactionManager::default()),
    })
}

/// A post-processed version of a Config object which has stores for the metadata, wal, and index.
#[derive(Clone)]
pub struct ConfigWithStore {
    pub locks_manager: Arc<dyn GlobalLocksManager>,
    pub global_store: Arc<dyn GlobalStore>,
    pub global_store_info: Option<StoreInfo>,
    pub wal: Arc<dyn Wal>,
    pub realtime_wal: Option<Arc<dyn Wal>>,
    pub index: StoreInfo,
    pub xact_manager: Arc<dyn TransactionManager>,
}

#[derive(Clone, Default, Debug)]
pub struct ConfigWithStoreOpts {
    pub file_cache_opts: FileCacheOpts,
}

impl ConfigWithStore {
    pub fn from_config(config: StorageConfig, opts: ConfigWithStoreOpts) -> Result<Self> {
        let locks_manager =
            url_to_locks_manager(&config.locks_uri, config.locks_manager_enable_bookkeeping)?;
        let index = StoreInfo::new(&config.index_uri, opts.file_cache_opts.clone())?;
        let wal = url_to_wal(
            &config.wal_uri,
            opts.file_cache_opts.clone(),
            index.directory.clone(),
        )?;
        let realtime_wal = config
            .realtime_wal_uri
            .as_ref()
            .map(|uri| url_to_wal(uri, opts.file_cache_opts.clone(), index.directory.clone()))
            .transpose()?;
        let (mut global_store, global_store_info) = url_to_global_store(
            &config.metadata_uri,
            opts.file_cache_opts.clone(),
            locks_manager.clone(),
        )?;
        Arc::get_mut(&mut global_store).unwrap().enable_timing();
        let xact_manager = url_to_xact_manager(&config.xact_manager_uri)?;
        Ok(ConfigWithStore {
            locks_manager,
            global_store,
            global_store_info,
            wal,
            realtime_wal,
            index,
            xact_manager,
        })
    }

    // NOTE: This is currently always an ObjectAndGlobalStoreWal WAL, but in the future it could
    // return a `dyn Wal``.
    pub fn segment_index_wal(&self) -> ObjectAndGlobalStoreWal {
        ObjectAndGlobalStoreWal {
            object_store: self.index.store.clone(),
            global_store: self.global_store.clone(),
            directory: self.index.directory.clone(),
            store_prefix: self.index.prefix.clone(),
            store_type: self.index.store_type,
        }
    }

    pub fn with_new_indexing_directory(&self, file_cache_opts: FileCacheOpts) -> Result<Self> {
        let cold_directory =
            AsyncDirectoryArc::new(ObjectStoreDirectory::new(self.index.store.clone()));
        let directory = if file_cache_opts.use_mmap_directory {
            AsyncDirectoryArc::new(CacheDirectory::<MmapCacheFile<CacheDirectoryKey>>::new(
                cold_directory,
                None,
                file_cache_opts,
            )?)
        } else {
            AsyncDirectoryArc::new(CacheDirectory::<DirectCacheFile<CacheDirectoryKey>>::new(
                cold_directory,
                None,
                file_cache_opts,
            )?)
        };

        if log::log_enabled!(log::Level::Debug) {
            directory.enable_timing();
        }

        Ok(ConfigWithStore {
            locks_manager: self.locks_manager.clone(),
            global_store: self.global_store.clone(),
            global_store_info: self.global_store_info.clone(),
            wal: self.wal.clone(),
            realtime_wal: self.realtime_wal.clone(),
            xact_manager: self.xact_manager.clone(),
            index: StoreInfo {
                store: self.index.store.clone(),
                store_type: self.index.store_type,
                prefix: self.index.prefix.clone(),
                directory,
            },
        })
    }
}
