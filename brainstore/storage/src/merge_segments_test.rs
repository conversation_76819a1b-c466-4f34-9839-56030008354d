use std::{collections::HashSet, sync::Arc};

use once_cell::sync::Lazy;
use tantivy::{doc, indexer::UserOperation, TantivyDocument, Term};
use util::{
    anyhow::Result,
    await_spawn_blocking,
    schema::{ConstructSchemaOpts, Field, Schema, TantivyField, TantivyType, TextOptions},
    system_types::{FullObjectId, ObjectId, ObjectType},
    test_util::{two_way_sync_point, TwoWaySyncPointWaitAndSend},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    global_store::{LastCompactedIndexMeta, SegmentMetadata},
    index_document::make_full_schema,
    merge_segments::{
        merge_segment_wals_unlocked_for_testing, merge_segments,
        merge_tantivy_indices_unlocked_for_testing, MergeSegmentsInput, MergeSegmentsOptionalInput,
        MergeSegmentsTestingSyncPoints, MergeSegmentsUnlockedInput,
    },
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    process_wal::{
        process_object_wal, ProcessObjectWalInput, ProcessObjectWalOptionalInput,
        ProcessObjectWalOptions, ProcessObjectWalOutput, ProcessObjectWalTestingSyncPoints,
        ProcessSegmentWalTestingSyncPoints,
    },
    tantivy_index::{collect_meta_json, TantivyIndexScope, TantivyIndexWriterOpts},
    tantivy_index_wrapper::ReadWriteTantivyIndexWrapper,
    test_util::{collect_wal_stream, TmpDirConfigWithStore},
    wal::{wal_stream, WALScope, Wal},
    wal_entry::WalEntry,
};

static OBJECT_ID0: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj0").unwrap(),
});

struct TestFixture {
    pub tmp_dir_config: TmpDirConfigWithStore,
}

impl TestFixture {
    fn new() -> Self {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        Self { tmp_dir_config }
    }

    pub async fn wal_token(&self, object_id: FullObjectId<'_>) -> Uuid {
        self.tmp_dir_config
            .config
            .global_store
            .query_object_metadatas(&[object_id])
            .await
            .unwrap()
            .remove(0)
            .wal_token
    }

    async fn index_wrapper(
        &self,
        schema: Schema,
        segment_id: Uuid,
    ) -> ReadWriteTantivyIndexWrapper {
        ReadWriteTantivyIndexWrapper::new(
            self.tmp_dir_config.config.index.directory.clone(),
            schema,
            &self.tmp_dir_config.config.index.prefix,
            &TantivyIndexScope::Segment(segment_id),
        )
        .await
        .unwrap()
    }

    fn make_segment_wal(&self) -> ObjectAndGlobalStoreWal {
        ObjectAndGlobalStoreWal {
            object_store: self.tmp_dir_config.config.index.store.clone(),
            global_store: self.tmp_dir_config.config.global_store.clone(),
            directory: self.tmp_dir_config.config.index.directory.clone(),
            store_prefix: self.tmp_dir_config.config.index.prefix.clone(),
            store_type: self.tmp_dir_config.config.index.store_type,
        }
    }

    async fn run_process_wal(&self) -> Result<ProcessObjectWalOutput> {
        self.run_process_wal_with_sync_points(Default::default())
            .await
    }

    async fn run_process_wal_with_sync_points(
        &self,
        testing_sync_points: ProcessObjectWalTestingSyncPoints,
    ) -> Result<ProcessObjectWalOutput> {
        process_object_wal(
            ProcessObjectWalInput {
                object_id: *OBJECT_ID0,
                config: &self.tmp_dir_config.config,
            },
            ProcessObjectWalOptionalInput {
                testing_sync_points,
                ..Default::default()
            },
            ProcessObjectWalOptions {
                max_rows_per_segment: 1000,
                ..Default::default()
            },
        )
        .await
    }

    async fn read_segment_wal(&self, segment_id: Uuid) -> Vec<(TransactionId, Vec<WalEntry>)> {
        let wal = self.make_segment_wal();
        collect_wal_stream(wal_stream(
            wal.wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                .await
                .unwrap(),
            Default::default(),
        ))
        .await
        .unwrap()
    }
}

fn get_all_field_values(
    reader: &tantivy::IndexReader,
    field: tantivy::schema::Field,
) -> HashSet<String> {
    let searcher = reader.searcher();
    let collector = tantivy::collector::DocSetCollector {};
    let doc_addresses = searcher
        .search(&tantivy::query::AllQuery, &collector)
        .unwrap();
    doc_addresses
        .iter()
        .filter_map(|doc_address| {
            let retrieved_doc: TantivyDocument = searcher.doc(*doc_address).unwrap();
            if let Some(field_values) = retrieved_doc.get_first(field) {
                match field_values {
                    tantivy::schema::OwnedValue::Str(text) => Some(text.clone()),
                    _ => panic!("Unexpected field type"),
                }
            } else {
                None
            }
        })
        .collect()
}

fn basic_schema() -> Schema {
    let text_options = TextOptions {
        stored: true,
        fast: false,
        tokenize: false,
    };
    Schema::new(
        "test".to_string(),
        vec![Field {
            name: "field1".to_string(),
            tantivy: vec![TantivyField {
                name: "field1".to_string(),
                field_ts: 0,
                field_type: TantivyType::Str(text_options.clone()),
                repeated: false,
                lossy_fast_field: false,
            }],
        }],
        ConstructSchemaOpts {
            auto_assign_field_ts: true,
            ..Default::default()
        },
    )
    .unwrap()
}

#[tokio::test]
async fn test_merge_tantivy_indices_unlocked() {
    let fixture = TestFixture::new();
    let schema = basic_schema();
    let writer_opts = TantivyIndexWriterOpts::default();

    // Create indices for two segments
    let segment0_id = Uuid::new_v4();
    let segment1_id = Uuid::new_v4();
    let dst_segment_id = Uuid::new_v4();

    let segment0_index = fixture.index_wrapper(schema.clone(), segment0_id).await;
    let segment1_index = fixture.index_wrapper(schema.clone(), segment1_id).await;

    let field1 = segment0_index.tantivy_schema().get_field("field1").unwrap();
    let segment0_values = (0..100)
        .map(|i| format!("segment_0_id_{}", i))
        .collect::<Vec<_>>();
    let segment1_values = (0..100)
        .map(|i| format!("segment_1_id_{}", i))
        .collect::<Vec<_>>();

    // Populate the indices.
    for (segment_values, segment_index) in [
        (&segment0_values, &segment0_index),
        (&segment1_values, &segment1_index),
    ]
    .into_iter()
    {
        let mut index_writer = segment_index.make_writer(&writer_opts).await.unwrap();
        let mut tantivy_ops: Vec<UserOperation<TantivyDocument>> = Vec::new();
        for value in segment_values {
            tantivy_ops.push(UserOperation::Add(doc!(
                field1 => value.as_str(),
            )));
        }
        index_writer.run(tantivy_ops).unwrap();
        index_writer.commit().unwrap();
    }

    // Merge the indices
    merge_tantivy_indices_unlocked_for_testing(MergeSegmentsUnlockedInput {
        src_segment_ids: &[segment0_id, segment1_id],
        dst_segment_id,
        index_store: &fixture.tmp_dir_config.config.index,
        global_store: fixture.tmp_dir_config.config.global_store.clone(),
    })
    .await
    .unwrap();

    // Verify merged index has all documents
    {
        let dst_index = fixture.index_wrapper(schema.clone(), dst_segment_id).await;
        let reader = dst_index.make_reader().await.unwrap();
        let values = await_spawn_blocking!(move || get_all_field_values(&reader, field1))
            .unwrap()
            .unwrap();
        assert_eq!(
            values,
            segment0_values
                .iter()
                .chain(segment1_values.iter())
                .cloned()
                .collect::<HashSet<_>>()
        );
    }

    for (segment_values, segment_index) in [
        (&segment0_values, &segment0_index),
        (&segment1_values, &segment1_index),
    ]
    .into_iter()
    {
        let mut index_writer = segment_index.make_writer(&writer_opts).await.unwrap();
        let term = Term::from_field_text(field1, segment_values[0].as_str());
        index_writer.delete_term(term);
        index_writer.commit().unwrap();
    }

    // Merge again
    merge_tantivy_indices_unlocked_for_testing(MergeSegmentsUnlockedInput {
        src_segment_ids: &[segment0_id, segment1_id],
        dst_segment_id,
        index_store: &fixture.tmp_dir_config.config.index,
        global_store: fixture.tmp_dir_config.config.global_store.clone(),
    })
    .await
    .unwrap();

    // Verify deletions were propagated.
    {
        let dst_index = fixture.index_wrapper(schema.clone(), dst_segment_id).await;
        let reader = dst_index.make_reader().await.unwrap();
        let values = await_spawn_blocking!(move || get_all_field_values(&reader, field1))
            .unwrap()
            .unwrap();
        assert_eq!(
            values,
            segment0_values
                .iter()
                .skip(1)
                .chain(segment1_values.iter().skip(1))
                .cloned()
                .collect::<HashSet<_>>()
        );
    }
}

fn make_wal_entry(xact_id: u64, row_id: &str) -> WalEntry {
    WalEntry {
        _xact_id: TransactionId(xact_id),
        id: row_id.to_string(),
        _object_type: OBJECT_ID0.object_type,
        _object_id: OBJECT_ID0.object_id.to_owned(),
        ..Default::default()
    }
}

#[tokio::test]
async fn test_merge_segment_wals_unlocked() {
    let fixture = TestFixture::new();

    // Create wal entries for two segments.
    let segment0_id = Uuid::new_v4();
    let segment1_id = Uuid::new_v4();
    let dst_segment_id = Uuid::new_v4();

    let wal = fixture.make_segment_wal();
    wal.insert(
        WALScope::Segment(segment0_id),
        vec![
            make_wal_entry(0, "row0"),
            make_wal_entry(0, "row1"),
            make_wal_entry(1, "row2"),
        ],
    )
    .await
    .unwrap();
    wal.insert(
        WALScope::Segment(segment0_id),
        vec![make_wal_entry(1, "row3"), make_wal_entry(2, "row4")],
    )
    .await
    .unwrap();
    wal.insert(
        WALScope::Segment(segment1_id),
        vec![make_wal_entry(1, "row5"), make_wal_entry(3, "row6")],
    )
    .await
    .unwrap();

    merge_segment_wals_unlocked_for_testing(MergeSegmentsUnlockedInput {
        src_segment_ids: &[segment0_id, segment1_id],
        dst_segment_id,
        index_store: &fixture.tmp_dir_config.config.index,
        global_store: fixture.tmp_dir_config.config.global_store.clone(),
    })
    .await
    .unwrap();

    let merged_wal = collect_wal_stream(wal_stream(
        wal.wal_metadata_stream(WALScope::Segment(dst_segment_id), Default::default())
            .await
            .unwrap(),
        Default::default(),
    ))
    .await
    .unwrap();
    assert_eq!(
        merged_wal,
        vec![
            (
                TransactionId(0),
                vec![make_wal_entry(0, "row0"), make_wal_entry(0, "row1")]
            ),
            (
                TransactionId(1),
                vec![
                    make_wal_entry(1, "row2"),
                    make_wal_entry(1, "row3"),
                    make_wal_entry(1, "row5")
                ]
            ),
            (TransactionId(2), vec![make_wal_entry(2, "row4")]),
            (TransactionId(3), vec![make_wal_entry(3, "row6")]),
        ]
    );
}

struct TestMergeSegmentsSyncPoints {
    pub before_phase_2: TwoWaySyncPointWaitAndSend,
    pub phase_2_before_update_segment_liveness: TwoWaySyncPointWaitAndSend,
}

#[tokio::test]
async fn test_merge_segments() {
    // Procedure:
    //  1. Add some object WAL entries. Process the WAL to end up with one new segment.
    //  2. Kick off a merge of the segment into a new destination segment.
    //  3. Before phase 2 starts, check that the source segment has been fully compacted and the
    //  WALs match between source and destination.
    //  4. Write some more WAL entries to the source segment.
    //  5. Start phase 2 and wait until the point where it's releasing locks.
    //  6. Check that the source segment has been fully compacted. Try writing more WAL entries
    //     into the source segment (this should be blocked).
    //  7. Finish phase 2. Check that the prior writing task failed due to the liveness check.
    //     Check that all segment metadata is up to date.
    //  8. Re-run WAL processing for the entry that failed previously. It will now end up in the
    //     destination segment.

    let fixture = Arc::new(TestFixture::new());
    let dst_segment_id = Uuid::new_v4();

    let object_wal = &fixture.tmp_dir_config.config.wal;
    let wal_token = fixture.wal_token(*OBJECT_ID0).await;
    let object_wal_scope = WALScope::ObjectId(*OBJECT_ID0, wal_token);
    object_wal
        .insert(object_wal_scope, vec![make_wal_entry(0, "row0")])
        .await
        .unwrap();
    let src_segment_id = {
        let process_wal_output = fixture.run_process_wal().await.unwrap();
        assert_eq!(process_wal_output.modified_segment_ids.len(), 1);
        process_wal_output
            .modified_segment_ids
            .iter()
            .next()
            .unwrap()
            .clone()
    };

    let (testing_sync_points, control_sync_points) = {
        let before_phase_2 = two_way_sync_point();
        let phase_2_before_update_segment_liveness = two_way_sync_point();
        (
            MergeSegmentsTestingSyncPoints {
                before_phase_2: Some(before_phase_2.0),
                phase_2_before_update_segment_liveness: Some(
                    phase_2_before_update_segment_liveness.0,
                ),
                ..Default::default()
            },
            TestMergeSegmentsSyncPoints {
                before_phase_2: before_phase_2.1,
                phase_2_before_update_segment_liveness: phase_2_before_update_segment_liveness.1,
            },
        )
    };

    let merge_handle = {
        let fixture = fixture.clone();
        let testing_sync_points = testing_sync_points;
        tokio::spawn(async move {
            let src_segment_id_arr = [src_segment_id];
            merge_segments(
                MergeSegmentsInput {
                    object_id: *OBJECT_ID0,
                    src_segment_ids: &src_segment_id_arr,
                    dst_segment_id,
                    index_store: fixture.tmp_dir_config.config.index.clone(),
                    locks_manager: &*fixture.tmp_dir_config.config.locks_manager,
                    global_store: fixture.tmp_dir_config.config.global_store.clone(),
                    compact_segment_wal_schema: make_full_schema(&basic_schema()).unwrap(),
                    compact_segment_wal_options: Default::default(),
                },
                MergeSegmentsOptionalInput {
                    testing_sync_points,
                },
            )
            .await
        })
    };

    let before_phase_2 = control_sync_points.before_phase_2.wait().await;
    assert_eq!(
        fixture.read_segment_wal(src_segment_id).await,
        vec![(TransactionId(0), vec![make_wal_entry(0, "row0")])]
    );
    assert_eq!(
        fixture
            .tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[src_segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .map(|x| x.xact_id),
        Some(TransactionId(0))
    );
    object_wal
        .insert(object_wal_scope, vec![make_wal_entry(1, "row1")])
        .await
        .unwrap();
    {
        let process_wal_output = fixture.run_process_wal().await.unwrap();
        assert_eq!(
            process_wal_output
                .modified_segment_ids
                .iter()
                .next()
                .unwrap(),
            &src_segment_id
        );
    }
    before_phase_2.send();

    let phase_2_before_update_segment_liveness = control_sync_points
        .phase_2_before_update_segment_liveness
        .wait()
        .await;
    assert_eq!(
        fixture.read_segment_wal(src_segment_id).await,
        vec![
            (TransactionId(0), vec![make_wal_entry(0, "row0")]),
            (TransactionId(1), vec![make_wal_entry(1, "row1")]),
        ]
    );
    assert_eq!(
        fixture
            .tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[src_segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .map(|x| x.xact_id),
        Some(TransactionId(1))
    );

    object_wal
        .insert(object_wal_scope, vec![make_wal_entry(2, "row2")])
        .await
        .unwrap();
    let nonlive_sync_point = two_way_sync_point();
    let nonlive_write_handle = {
        let fixture = fixture.clone();
        let testing_sync_point = ProcessObjectWalTestingSyncPoints {
            process_segment_wal: [(
                src_segment_id.clone(),
                ProcessSegmentWalTestingSyncPoints {
                    before_acquire_segment_lock: Some(nonlive_sync_point.0),
                },
            )]
            .into_iter()
            .collect(),
            ..Default::default()
        };
        tokio::spawn(async move {
            fixture
                .run_process_wal_with_sync_points(testing_sync_point)
                .await
        })
    };
    nonlive_sync_point.1.wait_and_send().await;

    phase_2_before_update_segment_liveness.send();
    assert!(nonlive_write_handle.await.unwrap().is_err());
    assert_eq!(
        fixture.read_segment_wal(src_segment_id).await,
        vec![
            (TransactionId(0), vec![make_wal_entry(0, "row0")]),
            (TransactionId(1), vec![make_wal_entry(1, "row1")]),
        ]
    );
    merge_handle.await.unwrap().unwrap();

    // After the merge has completed, we should be able to run process wal successfully again and
    // have the new WAL entry in the destination segment.
    {
        let process_wal_output = fixture.run_process_wal().await.unwrap();
        assert_eq!(
            process_wal_output
                .modified_segment_ids
                .iter()
                .next()
                .unwrap(),
            &dst_segment_id
        );
        assert_eq!(
            fixture.read_segment_wal(dst_segment_id).await,
            vec![
                (TransactionId(0), vec![make_wal_entry(0, "row0")]),
                (TransactionId(1), vec![make_wal_entry(1, "row1")]),
                (TransactionId(2), vec![make_wal_entry(2, "row2")]),
            ]
        );
    }

    let segment_meta = fixture
        .tmp_dir_config
        .config
        .global_store
        .query_segment_metadatas(&[dst_segment_id])
        .await
        .unwrap()
        .remove(0);
    assert_eq!(
        segment_meta,
        SegmentMetadata {
            last_compacted_index_meta: Some(LastCompactedIndexMeta {
                xact_id: TransactionId(1),
                tantivy_meta: segment_meta
                    .last_compacted_index_meta
                    .as_ref()
                    .unwrap()
                    .tantivy_meta
                    .clone()
            }),
            minimum_pagination_key: PaginationKey(0),
            num_rows: 3,
        },
    );

    // Check that the index meta in the destination segment matches what we have in the global
    // store.
    assert_eq!(
        collect_meta_json(
            fixture.tmp_dir_config.config.index.directory.as_ref(),
            &TantivyIndexScope::Segment(dst_segment_id)
                .path(&fixture.tmp_dir_config.config.index.prefix)
        )
        .await
        .unwrap(),
        segment_meta
            .last_compacted_index_meta
            .as_ref()
            .map(|x| x.tantivy_meta.clone())
    );
}
