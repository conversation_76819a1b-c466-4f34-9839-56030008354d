use std::collections::HashMap;

use tantivy::{directory::DirectoryClone, query::AllQuery};
use util::await_spawn_blocking;

use crate::{
    index_document::{make_full_schema, IndexDocument},
    index_wal_reader::IndexWalReader,
    tantivy_index::make_tantivy_schema,
};

pub async fn get_reader_full_docs(
    index_wal_reader: &IndexWalReader,
) -> HashMap<String, IndexDocument> {
    let full_schema = make_full_schema(index_wal_reader.schema()).unwrap();
    let tantivy_schema = make_tantivy_schema(&full_schema).unwrap();

    await_spawn_blocking!(
        move |index_wal_reader: &IndexWalReader| {
            let tantivy_index =
                tantivy::Index::open(index_wal_reader.only_directory().box_clone()).unwrap();

            let reader = tantivy_index.reader().unwrap();
            let searcher = reader.searcher();
            let collector = tantivy::collector::DocSetCollector {};

            let excluded_doc_query = index_wal_reader.wrap_exclude_query(Box::new(AllQuery {}));

            let addresses = searcher.search(&excluded_doc_query, &collector).unwrap();
            let mut row_values: HashMap<String, IndexDocument> = HashMap::new();
            for address in addresses {
                let doc: tantivy::schema::document::TantivyDocument =
                    searcher.doc(address).unwrap();
                let index_doc =
                    IndexDocument::from_tantivy_document(&doc, &tantivy_schema.invert_fields)
                        .unwrap();
                assert!(row_values
                    .insert(index_doc.wal_entry.full_row_id().to_owned().id, index_doc,)
                    .is_none());
            }

            for doc in index_wal_reader.in_memory_docs() {
                let index_doc = IndexDocument::from_json_value(doc.clone()).unwrap();
                assert!(row_values
                    .insert(index_doc.wal_entry.full_row_id().to_owned().id, index_doc,)
                    .is_none());
            }
            row_values
        },
        index_wal_reader
    )
    .unwrap()
    .unwrap()
}

pub fn sanitize_reader_doc(mut doc: IndexDocument) -> IndexDocument {
    doc.sanitize().unwrap();
    if !doc.wal_entry.data.contains_key("tags") {
        doc.wal_entry
            .data
            .insert("tags".to_string(), serde_json::Value::Array(vec![]));
    }
    doc
}
