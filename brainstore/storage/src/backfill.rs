use std::{
    collections::HashMap,
    sync::{atomic::AtomicBool, Arc},
    time::Duration,
};

use clap::Parser;
use otel_common::opentelemetry::metrics::Counter;
use rand::Rng;
use serde::{Deserialize, Serialize};
use tokio::time::Instant;
use tracing::instrument;

use crate::{
    compaction_loop::CompactionLoop,
    config_with_store::ConfigWithStore,
    global_store::{
        BackfillBrainstoreObject, BackfillTrackingEntry, BackfillTrackingEntryId,
        BackfillTrackingEntryUpdate, GlobalStore,
    },
    postgres_wal::{PostgresWAL, PostgresWalStreamBounded, PostgresWalStreamOpts},
    process_wal::{
        process_object_wal, ProcessObjectWalInput, ProcessObjectWalOptionalInput,
        ProcessObjectWalOptions,
    },
};

use util::{anyhow::Result, futures::future::join_all, system_types::ObjectType};

pub struct BackfillOneTrackingEntryInput<'a> {
    pub tracking_entry: BackfillTrackingEntry,
    pub config: &'a ConfigWithStore,
    pub process_wal_opts: &'a ProcessObjectWalOptions,
    pub compaction_loop: &'a CompactionLoop,
}

#[derive(Default, Clone)]
pub struct BackfillOneTrackingEntryOptionalInput {
    pub elapsed_time_ms_counter: Option<Counter<u64>>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackfillOneTrackingEntryOptions {
    /// The batch size of sequence IDs we query over for fetching rows belonging
    /// to the tracking entry.
    #[arg(
        long,
        default_value_t = default_sequence_id_batch_size(),
        env = "BRAINSTORE_BACKFILL_SEQUENCE_ID_BATCH_SIZE"
    )]
    #[serde(default = "default_sequence_id_batch_size")]
    pub sequence_id_batch_size: usize,

    /// The maximum number of loop iterations we spend backfilling the tracking
    /// entry. Each loop iteration attempts to advance the tracking entry by
    /// `sequence_id_batch_size`.
    #[arg(
        long,
        default_value_t = default_max_backfill_iterations(),
        env = "BRAINSTORE_BACKFILL_MAX_ITERATIONS"
    )]
    #[serde(default = "default_max_backfill_iterations")]
    pub max_backfill_iterations: usize,
}

fn default_sequence_id_batch_size() -> usize {
    20_000
}

fn default_max_backfill_iterations() -> usize {
    1000
}

impl Default for BackfillOneTrackingEntryOptions {
    fn default() -> Self {
        Self {
            sequence_id_batch_size: default_sequence_id_batch_size(),
            max_backfill_iterations: default_max_backfill_iterations(),
        }
    }
}

#[derive(Default, Debug, Clone)]
#[allow(unused)]
pub struct BackfillOneTrackingEntryOutput {
    pub acquired_lock: bool,
    pub num_iterations: usize,
}

#[instrument(err, skip(input, optional_input), fields(tracking_entry_id = input.tracking_entry.id().to_string()))]
pub async fn backfill_one_tracking_entry(
    input: BackfillOneTrackingEntryInput<'_>,
    optional_input: BackfillOneTrackingEntryOptionalInput,
    options: BackfillOneTrackingEntryOptions,
) -> Result<BackfillOneTrackingEntryOutput> {
    let _tracking_entry_lock = match input
        .config
        .locks_manager
        .try_write(&format!(
            "backfill_one_tracking_entry:{}",
            input.tracking_entry.id()
        ))
        .await?
    {
        Some(lock) => lock,
        None => {
            log::debug!("Skipping backfilling tracking entry {} because another worker is already processing it", input.tracking_entry.id());
            return Ok(BackfillOneTrackingEntryOutput::default());
        }
    };

    let start_backfill_time = Instant::now();
    let sequence_id_batch_size = i64::try_from(options.sequence_id_batch_size)?;
    let max_backfill_iterations = options.max_backfill_iterations;

    let mut tracking_entry = input.tracking_entry;
    let mut num_iterations = 0;
    while num_iterations < max_backfill_iterations {
        let tracking_entry_ids = [tracking_entry.id()];
        let (brainstore_objects, start_sequence_id, end_sequence_id, is_logs2) = if tracking_entry
            .last_processed_sequence_id
            < tracking_entry.last_encountered_sequence_id
        {
            let upper_bound_sequence_id = std::cmp::min(
                tracking_entry.last_processed_sequence_id + sequence_id_batch_size,
                tracking_entry.last_encountered_sequence_id,
            );
            let brainstore_objects = input
                .config
                .global_store
                .query_backfill_brainstore_objects(
                    &tracking_entry_ids,
                    false, /* is_logs2 */
                    tracking_entry.last_processed_sequence_id,
                    upper_bound_sequence_id,
                )
                .await?
                .remove(0);
            (
                brainstore_objects,
                tracking_entry.last_processed_sequence_id,
                upper_bound_sequence_id,
                false,
            )
        } else if tracking_entry.last_processed_sequence_id_2
            < tracking_entry.last_encountered_sequence_id_2
        {
            let upper_bound_sequence_id = std::cmp::min(
                tracking_entry.last_processed_sequence_id_2 + sequence_id_batch_size,
                tracking_entry.last_encountered_sequence_id_2,
            );
            let brainstore_objects = input
                .config
                .global_store
                .query_backfill_brainstore_objects(
                    &tracking_entry_ids,
                    true, /* is_logs2 */
                    tracking_entry.last_processed_sequence_id_2,
                    upper_bound_sequence_id,
                )
                .await?
                .remove(0);
            (
                brainstore_objects,
                tracking_entry.last_processed_sequence_id_2,
                upper_bound_sequence_id,
                true,
            )
        } else {
            // Nothing left to process here.
            break;
        };

        let iter_start_time = Instant::now();

        join_all(brainstore_objects.iter().map(|object| {
            process_one_object(
                object,
                input.config,
                input.process_wal_opts,
                input.compaction_loop,
            )
        }))
        .await
        .into_iter()
        .collect::<Result<()>>()?;

        let tracking_entry_update = if is_logs2 {
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id_2: Some(end_sequence_id),
                ..Default::default()
            }
        } else {
            BackfillTrackingEntryUpdate {
                last_processed_sequence_id: Some(end_sequence_id),
                ..Default::default()
            }
        };
        tracking_entry = input
            .config
            .global_store
            .update_backfill_tracking_entries(vec![(tracking_entry.id(), tracking_entry_update)])
            .await?
            .remove(0);

        let iter_duration_ms =
            u64::try_from(iter_start_time.elapsed().as_millis()).unwrap_or(u64::MAX);
        let total_backfill_duration_ms =
            u64::try_from((Instant::now() - start_backfill_time).as_millis()).unwrap_or(u64::MAX);

        tracing::info!(
            tracking_entry_id = tracking_entry.id().to_string(),
            start_sequence_id = start_sequence_id,
            end_sequence_id = end_sequence_id,
            is_logs2 = is_logs2,
            iter_duration_ms = iter_duration_ms,
            iter_num = num_iterations,
            total_backfill_duration_ms = total_backfill_duration_ms,
            "Processed tracking entry",
        );

        if let Some(counter) = &optional_input.elapsed_time_ms_counter {
            counter.add(
                iter_duration_ms,
                &[otel_common::opentelemetry::KeyValue::new(
                    "tracking_entry_id",
                    tracking_entry.id().to_string(),
                )],
            );
        }

        num_iterations += 1;
    }

    Ok(BackfillOneTrackingEntryOutput {
        acquired_lock: true,
        num_iterations,
    })
}

#[instrument(skip(config, process_wal_opts, compaction_loop))]
async fn process_one_object(
    object: &BackfillBrainstoreObject,
    config: &ConfigWithStore,
    process_wal_opts: &ProcessObjectWalOptions,
    compaction_loop: &CompactionLoop,
) -> Result<()> {
    // Wrap this in a bounded postgres wal stream IF it's a PostgresWAL.
    //
    // There is a general issue with backfilling, due to the fact that we
    // process in ranges of sequence ids. It's possible that an individual
    // transaction for an object, spanning multiple sequence ids, may get split
    // into separate backfill batches. This is problematic for realtime queries,
    // because say our first batch has completed and updated its
    // `last_processed_xact_id` to X. If we then make a realtime query, it will
    // think the entire transaction X has been processed and read it from the
    // segment WAL, and then miss the remaining rows for that transaction.
    //
    // This issue shows up in our unit tests, so we insert a hacky-workaround
    // for it by incrementing the max_sequence_id by a constant to help
    // guarantee we capture all logs for the transaction.
    let mut config = config.clone();
    config.wal = match config.wal.as_ref().downcast_ref::<PostgresWAL>() {
        Some(_) => Arc::new(PostgresWalStreamBounded::new(
            config.wal,
            PostgresWalStreamOpts {
                start_sequence_id: Some(u64::try_from(object.min_sequence_id)?),
                end_sequence_id: Some(u64::try_from(object.max_sequence_id + 20000)?),
                read_logs2: Some(object.is_logs2),
                ..Default::default()
            },
        )),
        None => config.wal,
    };

    let start = std::time::Instant::now();
    let result = process_object_wal(
        ProcessObjectWalInput {
            object_id: object.object_id.as_ref(),
            config: &config,
        },
        ProcessObjectWalOptionalInput {
            start_xact_id: Some(object.min_xact_id),
            end_xact_id: Some(object.max_xact_id),
            source: Some(
                (BackfillTrackingEntryId {
                    project_id: &object.project_id,
                    object_type: object.object_id.object_type,
                })
                .to_string(),
            ),
            ..Default::default()
        },
        process_wal_opts.clone(),
    )
    .await?;
    tracing::info!(
        object = format!("{:?}", object),
        duration_ms = start.elapsed().as_millis(),
        "Processed WAL",
    );
    tracing::info!(
        object_id = object.object_id.to_string(),
        modified_segment_ids = result.modified_segment_ids.len(),
        "Modified segment IDs"
    );

    if compaction_loop
        .add_segments(
            result
                .modified_segment_ids
                .into_iter()
                .map(|id| (id, Some(object.object_id.object_type))),
        )
        .is_err()
    {
        tracing::warn!(
            object_id = object.object_id.to_string(),
            "Failed to add segments to compaction queue. At capacity. Slow down!"
        );
    }

    Ok(())
}

pub struct BackfillWorkerInput {
    pub config: ConfigWithStore,
    pub process_wal_opts: ProcessObjectWalOptions,
    pub compaction_loop: CompactionLoop,
}

#[derive(Default, Clone)]
pub struct BackfillWorkerOptionalInput {
    pub terminate_signal: Option<Arc<AtomicBool>>,
    pub testing_signal_produced_entry: Option<async_channel::Sender<()>>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct BackfillWorkerOptions {
    /// The number of backfill workers to use for realtime objects (which have
    /// already completed their initial backfill). Each worker will work on a
    /// single tracking entry at a time.
    #[arg(
        long,
        default_value_t = default_num_backfill_workers_realtime(),
        env = "BRAINSTORE_NUM_BACKFILL_WORKERS_REALTIME"
    )]
    #[serde(default = "default_num_backfill_workers_realtime")]
    pub num_backfill_workers_realtime: usize,

    /// The number of backfill workers to use for historical objects (which have
    /// not already completed their initial backfill). Each worker will work on
    /// a single tracking entry at a time.
    #[arg(
        long,
        default_value_t = default_num_backfill_workers_historical(),
        env = "BRAINSTORE_NUM_BACKFILL_WORKERS_HISTORICAL"
    )]
    #[serde(default = "default_num_backfill_workers_historical")]
    pub num_backfill_workers_historical: usize,

    #[command(flatten)]
    #[serde(flatten)]
    pub backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions,
}

impl Default for BackfillWorkerOptions {
    fn default() -> Self {
        Self {
            num_backfill_workers_realtime: default_num_backfill_workers_realtime(),
            num_backfill_workers_historical: default_num_backfill_workers_historical(),
            backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions::default(),
        }
    }
}

fn default_num_backfill_workers_realtime() -> usize {
    100
}

fn default_num_backfill_workers_historical() -> usize {
    100
}

pub async fn backfill_worker(
    input: BackfillWorkerInput,
    optional_input: BackfillWorkerOptionalInput,
    options: BackfillWorkerOptions,
) {
    // The worker is an infinite loop that works as follows:
    //
    // - One background task continuously loops through the un-backfilled
    // tracking entries and enqueues them for backfilling.
    //
    // - A pool of worker tasks continuously dequeue and backfill the
    // tracking entries.
    //
    // We spawn separate producers and worker pools for processing
    // already-backfilled and not-already-backfilled entries, so that we can
    // adjust their resource allocations independently.

    let meter = otel_common::opentelemetry::global::meter("brainstore");
    let elapsed_time_ms_counter = meter
        .u64_counter("brainstore.process_wal_worker.elapsed_time_ms")
        .build();
    let bookkeeper = CurrentlyBackfillingBookkeeper::new();

    let make_futs = |has_completed_initial_backfill: bool| {
        let num_workers = if has_completed_initial_backfill {
            options.num_backfill_workers_realtime
        } else {
            options.num_backfill_workers_historical
        };
        let (sender, receiver) = if num_workers > 0 {
            let (s, r) = async_channel::bounded::<BackfillTrackingEntry>(num_workers);
            (Some(s), Some(r))
        } else {
            (None, None)
        };
        let producer_fut = backfill_producer_task(
            sender,
            has_completed_initial_backfill,
            &*input.config.global_store,
            optional_input.terminate_signal.clone(),
            optional_input.testing_signal_produced_entry.clone(),
        );
        let worker_futs = (0..num_workers)
            .map(|_| {
                backfill_worker_task(
                    receiver.clone(),
                    bookkeeper.clone(),
                    &input.config,
                    &input.process_wal_opts,
                    &input.compaction_loop,
                    BackfillOneTrackingEntryOptionalInput {
                        elapsed_time_ms_counter: Some(elapsed_time_ms_counter.clone()),
                    },
                    options.backfill_one_tracking_entry_opts.clone(),
                )
            })
            .collect::<Vec<_>>();
        (producer_fut, worker_futs)
    };

    let (producer_fut_realtime, worker_futs_realtime) = make_futs(true);
    let (producer_fut_historical, worker_futs_historical) = make_futs(false);
    tokio::join!(
        producer_fut_realtime,
        join_all(worker_futs_realtime),
        producer_fut_historical,
        join_all(worker_futs_historical)
    );
}

async fn backfill_producer_task(
    sender: Option<async_channel::Sender<BackfillTrackingEntry>>,
    has_completed_initial_backfill: bool,
    global_store: &dyn GlobalStore,
    terminate_signal: Option<Arc<AtomicBool>>,
    testing_producer_signal_produced_entry: Option<async_channel::Sender<()>>,
) {
    let error_sleep_duration = Duration::from_millis(100);
    const TRACKING_ENTRIES_BATCH_SIZE: usize = 100;

    let sender = if let Some(s) = sender {
        s
    } else {
        return;
    };

    let mut tick_manager = ProducerLoopTickManager::new();
    let mut cursor: Option<(String, ObjectType)> = None;
    loop {
        if let Some(terminate_signal) = &terminate_signal {
            if terminate_signal.load(std::sync::atomic::Ordering::Relaxed) {
                log::info!("Backfill producer task terminated");
                break;
            }
        }
        let cursor_id = cursor
            .as_ref()
            .map(|(project_id, object_type)| BackfillTrackingEntryId {
                project_id,
                object_type: *object_type,
            });
        let tracking_entries = match global_store
            .query_unbackfilled_tracking_entries_ordered(
                has_completed_initial_backfill,
                cursor_id,
                TRACKING_ENTRIES_BATCH_SIZE,
            )
            .await
        {
            Err(e) => {
                log::warn!(
                    "Failed to query unbackfilled tracking entries: {}. Sleeping for {}ms",
                    e,
                    error_sleep_duration.as_millis()
                );
                tokio::time::sleep(error_sleep_duration).await;
                continue;
            }
            Ok(x) => x,
        };
        if tracking_entries.is_empty() {
            log::debug!("Reached the end of the set of un-backfilled tracking entries");
            tick_manager.next_tick().await;
            cursor = None;
            continue;
        }
        cursor = {
            let last_entry = tracking_entries.last().unwrap();
            Some((last_entry.project_id.clone(), last_entry.object_type))
        };
        for tracking_entry in tracking_entries {
            match sender.send(tracking_entry).await {
                Ok(()) => {
                    if let Some(sender) = &testing_producer_signal_produced_entry {
                        let _ = sender.send(()).await;
                    }
                }
                Err(_) => {
                    log::info!("Tracking entry producer channel is closed. Exiting",);
                    return;
                }
            }
        }
    }
}

async fn backfill_worker_task(
    receiver: Option<async_channel::Receiver<BackfillTrackingEntry>>,
    bookkeeper: CurrentlyBackfillingBookkeeper,
    config: &ConfigWithStore,
    process_wal_opts: &ProcessObjectWalOptions,
    compaction_loop: &CompactionLoop,
    optional_input: BackfillOneTrackingEntryOptionalInput,
    options: BackfillOneTrackingEntryOptions,
) {
    let error_sleep_duration = Duration::from_millis(100);

    let receiver = if let Some(r) = receiver {
        r
    } else {
        return;
    };

    loop {
        let tracking_entry = match receiver.recv().await {
            Ok(tracking_entry) => tracking_entry,
            Err(_) => {
                log::info!("Tracking entry receiver channel is closed. Exiting",);
                return;
            }
        };
        let tracking_entry_id_str = tracking_entry.id().to_string();
        let backfill_handle = match bookkeeper
            .obtain_handle(&tracking_entry.project_id, tracking_entry.object_type)
        {
            Some(handle) => handle,
            None => {
                log::debug!(
                    "Skipping tracking entry {} because it is already being backfilled.",
                    tracking_entry_id_str
                );
                continue;
            }
        };
        let res = backfill_one_tracking_entry(
            BackfillOneTrackingEntryInput {
                tracking_entry,
                config,
                process_wal_opts,
                compaction_loop,
            },
            optional_input.clone(),
            options.clone(),
        )
        .await;
        match res {
            Ok(out) => {
                if out.acquired_lock {
                    // We actually backfilled it, so let the handle drop normally.
                } else {
                    // Failed to acquire the lock, so convert the handle into a
                    // TTL-based one to prevent future consumers from grabbing
                    // it for some time. While we don't know exactly when the
                    // lock will get released, we expect another process is
                    // backfilling this entry right now, and when finished, will
                    // release it's in-process lock. Meaning that process has
                    // has a better chance of re-acquiring the entry because it
                    // should not have any TTL-based lock.
                    backfill_handle.mark_locked();
                }
            }
            Err(err) => {
                log::error!(
                    "Failed to backfill tracking entry {}: {}. Sleeping for {}ms",
                    tracking_entry_id_str,
                    err,
                    error_sleep_duration.as_millis()
                );
                tokio::time::sleep(error_sleep_duration).await;
            }
        }
    }
}

// We explicitly limit the frequency that the producer can visit each
// tracking entry with a "tick" period. Without it, we risk tightly looping
// over the same set of entries and fanning them out to all of our worker
// tasks, all except one of them of which will fail to acquire the lock.
// Each time the loop starts, we re-initialize the period to include some
// random jitter.
struct ProducerLoopTickManager {
    tick_period: Duration,
    start_time: Instant,
}

impl ProducerLoopTickManager {
    fn new() -> Self {
        Self {
            tick_period: Self::sample_tick_duration(),
            start_time: Instant::now(),
        }
    }

    async fn next_tick(&mut self) {
        let elapsed = self.start_time.elapsed();
        if elapsed < self.tick_period {
            let remaining = self.tick_period - elapsed;
            log::debug!(
                "Producer sleeping for {}ms for remaining tick period of {}ms",
                remaining.as_millis(),
                self.tick_period.as_millis()
            );
            tokio::time::sleep(remaining).await;
        }
        self.tick_period = Self::sample_tick_duration();
        self.start_time = Instant::now();
    }

    fn sample_tick_duration() -> Duration {
        // Note: if you adjust this, make sure to run a batch CI jobs to ensure
        // it doesn't blow out Redis. Smaller wait times (e.g. <100ms) were
        // evaluated and shown to put too much load on redis, at which point it
        // would start rejecting connections, and we'd drop locks and abort
        // brainstore, etc.
        Duration::from_millis(rand::thread_rng().gen_range(500..=600))
    }
}

// In order to limit how frequently we hit redis to acquire locks for
// backfilling, we track in memory which objects we are backfilling within this
// process and which we've failed to acquire a lock on (which means another
// process is backfilling it). This will let us skip attempting to acquire the
// lock.

#[derive(Clone, Debug, Default)]
struct CurrentlyBackfillingBookkeeper(Arc<std::sync::Mutex<CurrentlyBackfillingBookkeeperInner>>);

#[derive(Debug, Default)]
struct CurrentlyBackfillingBookkeeperInner {
    currently_backfilling: HashMap<(String, ObjectType), Option<Instant>>,
}

#[derive(Debug)]
struct CurrentlyBackfillingEntryHandle {
    key: (String, ObjectType),
    bookkeeper: CurrentlyBackfillingBookkeeper,
    was_released: bool,
}

#[cfg(not(test))]
const BOOKKEEPER_EXPIRY_MIN_MS: u64 = 4000;

#[cfg(not(test))]
const BOOKKEEPER_EXPIRY_MAX_MS: u64 = 6000;

#[cfg(test)]
const BOOKKEEPER_EXPIRY_MIN_MS: u64 = 100;

#[cfg(test)]
const BOOKKEEPER_EXPIRY_MAX_MS: u64 = 200;

impl CurrentlyBackfillingBookkeeper {
    fn new() -> Self {
        Self(Arc::new(std::sync::Mutex::new(
            CurrentlyBackfillingBookkeeperInner::default(),
        )))
    }

    fn obtain_handle(
        &self,
        project_id: &str,
        object_type: ObjectType,
    ) -> Option<CurrentlyBackfillingEntryHandle> {
        use std::collections::hash_map::Entry;
        let mut guard = self.0.lock().unwrap();
        let success = match guard
            .currently_backfilling
            .entry((project_id.to_string(), object_type))
        {
            Entry::Vacant(entry) => {
                entry.insert(None);
                true
            }
            Entry::Occupied(mut entry) => {
                let val = entry.get_mut();
                if let Some(expiry_time) = val {
                    if Instant::now() > *expiry_time {
                        // There was a TTL-based entry but it expired. So clear
                        // the TTL and return a valid handle.
                        *val = None;
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
        };
        if success {
            Some(CurrentlyBackfillingEntryHandle {
                key: (project_id.to_string(), object_type),
                bookkeeper: self.clone(),
                was_released: false,
            })
        } else {
            None
        }
    }

    fn release_entry(&self, key: &(String, ObjectType)) -> bool {
        return self
            .0
            .lock()
            .unwrap()
            .currently_backfilling
            .remove(key)
            .is_some();
    }

    fn gen_expiry_time() -> Instant {
        Instant::now()
            + Duration::from_millis(
                rand::thread_rng().gen_range(BOOKKEEPER_EXPIRY_MIN_MS..=BOOKKEEPER_EXPIRY_MAX_MS),
            )
    }
}

impl CurrentlyBackfillingEntryHandle {
    fn mark_locked(mut self) {
        let mut guard = self.bookkeeper.0.lock().unwrap();
        let entry = match guard.currently_backfilling.get_mut(&self.key) {
            Some(entry) => entry,
            None => panic!("Unexpected: missing backfill entry for key {:?}", self.key),
        };
        assert!(
            entry.is_none(),
            "Backfill entry handle for key {:?} should not have a TTL",
            self.key
        );
        *entry = Some(CurrentlyBackfillingBookkeeper::gen_expiry_time());
        self.was_released = true;
    }
}

impl Drop for CurrentlyBackfillingEntryHandle {
    fn drop(&mut self) {
        if self.was_released {
            return;
        }
        if !self.bookkeeper.release_entry(&self.key) {
            log::error!("Unexpected: missing backfill entry for key {:?}", self.key);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use util::system_types::ObjectType;

    #[test]
    fn test_bookkeeper_obtain_handle_success() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Should be able to obtain handle for new entry
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle for new entry");

        assert_eq!(handle.key.0, project_id);
        assert_eq!(handle.key.1, object_type);
        assert!(!handle.was_released);
    }

    #[test]
    fn test_bookkeeper_obtain_handle_blocked() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain first handle
        let handle1 = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain first handle");

        // Second attempt should be blocked
        let handle2 = bookkeeper.obtain_handle(project_id, object_type);
        assert!(handle2.is_none(), "Second handle should be blocked");

        // Clean up first handle
        drop(handle1);

        // Now should be able to obtain handle again
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after release");
    }

    #[test]
    fn test_bookkeeper_different_keys() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id1 = "test_project1";
        let project_id2 = "test_project2";
        let object_type = ObjectType::Experiment;

        // Should be able to obtain handles for different project IDs
        bookkeeper
            .obtain_handle(project_id1, object_type)
            .expect("Should be able to obtain handle for project1");

        bookkeeper
            .obtain_handle(project_id2, object_type)
            .expect("Should be able to obtain handle for project2");

        // Should be able to obtain handles for different object types
        bookkeeper
            .obtain_handle(project_id1, ObjectType::Dataset)
            .expect("Should be able to obtain handle for different object type");
    }

    #[test]
    fn test_bookkeeper_mark_locked_and_expiry() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain handle and mark it as locked
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle");

        handle.mark_locked();

        // Should not be able to obtain handle immediately after marking as locked
        let handle2 = bookkeeper.obtain_handle(project_id, object_type);
        assert!(
            handle2.is_none(),
            "Should not be able to obtain handle when locked"
        );

        // Wait for expiry (we need to wait longer than the TTL)
        std::thread::sleep(std::time::Duration::from_millis(
            BOOKKEEPER_EXPIRY_MAX_MS + 100,
        ));

        // Should be able to obtain handle after expiry
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after expiry");
    }

    #[test]
    fn test_bookkeeper_release_entry() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        // Obtain handle
        let handle = bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle");

        let key = handle.key.clone();

        // Release should return true for existing entry
        drop(handle);

        // Releasing non-existent entry should return false
        let released = bookkeeper.release_entry(&key);
        assert!(!released, "Should return false for non-existent entry");
    }

    #[test]
    fn test_bookkeeper_handle_proper_cleanup() {
        let bookkeeper = CurrentlyBackfillingBookkeeper::new();
        let project_id = "test_project";
        let object_type = ObjectType::Experiment;

        {
            // Obtain handle and properly release it
            bookkeeper
                .obtain_handle(project_id, object_type)
                .expect("Should be able to obtain handle");
        } // Handle should drop without panic

        // Should be able to obtain handle again
        bookkeeper
            .obtain_handle(project_id, object_type)
            .expect("Should be able to obtain handle after proper cleanup");
    }

    #[test]
    fn test_bookkeeper_expiry_time_generation() {
        let start_time = Instant::now();
        let expiry_time = CurrentlyBackfillingBookkeeper::gen_expiry_time();
        let end_time = Instant::now();

        // Expiry time should be between 4-5 seconds from now
        let min_expected = start_time + Duration::from_millis(BOOKKEEPER_EXPIRY_MIN_MS);
        let max_expected = end_time + Duration::from_millis(BOOKKEEPER_EXPIRY_MAX_MS);

        assert!(
            expiry_time >= min_expected,
            "Expiry time should be at least 4 seconds from start"
        );
        assert!(
            expiry_time <= max_expected,
            "Expiry time should be at most 5 seconds from end"
        );
    }
}
