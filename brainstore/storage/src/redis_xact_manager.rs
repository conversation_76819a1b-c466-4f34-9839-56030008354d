use std::sync::Arc;

use util::async_trait::async_trait;
use util::url_util::Url;
use util::{
    anyhow::{anyhow, Result},
    xact::TransactionId,
};

use crate::healthcheck_util::validate_redis_connection;
use crate::xact_manager::TransactionManager;

#[derive(Debug)]
pub struct RedisTransactionManager {
    client: Arc<redis::Client>,
}

impl RedisTransactionManager {
    pub fn new(url: Url) -> Result<Self> {
        Ok(Self {
            client: Arc::new(redis::Client::open(url.clone())?),
        })
    }
}

#[async_trait]
impl TransactionManager for RedisTransactionManager {
    async fn next_xact_id(&self) -> Result<TransactionId> {
        let mut con = self.client.get_multiplexed_async_connection().await?;

        let ts = redis::cmd("TIME")
            .query_async::<Vec<u64>>(&mut con)
            .await?
            .into_iter()
            .next()
            .ok_or_else(|| anyhow!("TIME returned an empty array"))?;
        let xact_id = redis::cmd("INCR")
            .arg("global-xact-id")
            .query_async(&mut con)
            .await?;

        Ok(self.build_next_xact_id(xact_id, ts))
    }

    async fn status(&self) -> Result<String> {
        validate_redis_connection(&*self.client).await?;
        Ok("RedisTransactionManager is ok".into())
    }
}
