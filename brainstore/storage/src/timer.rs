use std::{
    borrow::Cow,
    collections::HashMap,
    sync::{atomic::AtomicBool, Arc, Mutex},
    time::{Duration, Instant},
};

use otel_common::opentelemetry::metrics::{Counter, Histogram, Meter};

#[macro_export]
macro_rules! time {
    ($manager:expr, $label:expr) => {
        let __bt_timer_guard = if $manager.enabled.load(std::sync::atomic::Ordering::Relaxed) {
            Some(crate::timer::TimeGuard::new(Some($manager.clone()), $label))
        } else {
            None
        };
    };
    ($label:expr) => {
        let __bt_timer_guard = if $cond {
            Some(crate::timer::TimeGuard::new(None, $label))
        } else {
            None
        };
    };
}

#[macro_export]
macro_rules! otel_time {
    ($manager:expr, $otel_timer:expr, $label:expr) => {
        let __bt_timer_guard = if $manager.enabled.load(std::sync::atomic::Ordering::Relaxed) {
            Some(crate::timer::TimeGuard::new(Some($manager.clone()), $label))
        } else {
            None
        };
        let __otel_counter_guard = crate::timer::OtelCounterGuard::new($otel_timer);
    };
}

pub struct TimerManager {
    pub name: Cow<'static, str>,
    pub enabled: AtomicBool,
    dur_map: Mutex<HashMap<Cow<'static, str>, (Duration, u64)>>,
}

impl TimerManager {
    pub fn new(name: impl Into<Cow<'static, str>>) -> Arc<Self> {
        Arc::new(Self {
            name: name.into(),
            enabled: AtomicBool::new(false),
            dur_map: Mutex::new(HashMap::new()),
        })
    }

    pub fn name(&self) -> &str {
        &self.name
    }

    pub fn enable_granular_timing(&self) {
        self.enabled
            .store(true, std::sync::atomic::Ordering::Relaxed);
    }

    pub fn reset(&self) {
        self.dur_map.lock().unwrap().clear();
    }

    pub fn log_all_durations(&self) {
        let dur_map = self.dur_map.lock().unwrap();
        let mut sorted_durations: Vec<_> = dur_map.iter().collect();
        sorted_durations.sort_by(|a, b| b.1 .0.cmp(&a.1 .0));

        let max_label_len = std::cmp::max(
            sorted_durations
                .iter()
                .map(|(label, _)| label.len())
                .max()
                .unwrap_or(30),
            30,
        );

        log::info!(
            "{:<width$} {:>15} {:>10} {:>20} {:>15}",
            "Label",
            "Total Duration",
            "Runs",
            "Total Duration",
            "Average",
            width = max_label_len,
        );
        log::info!("{:-<1$}", "", max_label_len + 15 + 10 + 20 + 15 + 4); // +4 for spaces between columns
        for (label, (total_dur, count)) in sorted_durations {
            log::info!(
                "{:<width$} {:>15?} {:>10} {:>20?} {:>15?}",
                label,
                total_dur,
                count,
                total_dur,
                *total_dur / (*count as u32),
                width = max_label_len
            );
        }
    }
}

impl std::fmt::Debug for TimerManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "TimerManager")
    }
}

pub struct TimeGuard {
    manager: Option<Arc<TimerManager>>,
    start: Instant,
    label: Option<Cow<'static, str>>,
}

impl TimeGuard {
    pub fn new(manager: Option<Arc<TimerManager>>, label: &'static str) -> Self {
        Self {
            manager,
            start: Instant::now(),
            label: Some(Cow::Borrowed(label)),
        }
    }

    pub fn new_str(manager: Option<Arc<TimerManager>>, label: &str) -> Self {
        Self {
            manager,
            start: Instant::now(),
            label: Some(Cow::Owned(label.to_string())),
        }
    }
}

impl Drop for TimeGuard {
    fn drop(&mut self) {
        let elapsed = self.start.elapsed();

        if let Some(manager) = self.manager.as_ref() {
            let label = self.label.take().unwrap();
            let mut dur_map = manager.dur_map.lock().unwrap();
            let dur_entry = dur_map.entry(label).or_insert((Duration::new(0, 0), 0));
            dur_entry.0 += elapsed;
            dur_entry.1 += 1;
        } else {
            log::debug!(
                "Timed [{}] duration: {:?}",
                self.label.as_ref().unwrap(),
                elapsed
            );
        }
    }
}

#[derive(Clone)]
pub struct OtelTimer {
    pub counter_meter: Counter<u64>,
    pub duration_meter: Histogram<u64>,
}

impl OtelTimer {
    pub fn new(meter: &Meter, label: &'static str) -> Self {
        let counter_meter = meter.u64_counter(format!("{}.count", label)).build();
        let duration_meter = meter
            .u64_histogram(format!("{}.duration_ms", label))
            .build();
        Self {
            counter_meter,
            duration_meter,
        }
    }
}

pub struct OtelCounterGuard {
    start: Instant,
    otel_timer: &'static OtelTimer,
}

impl OtelCounterGuard {
    pub fn new(otel_timer: &'static OtelTimer) -> Self {
        Self {
            start: Instant::now(),
            otel_timer,
        }
    }
}

impl Drop for OtelCounterGuard {
    fn drop(&mut self) {
        let elapsed = self.start.elapsed();
        self.otel_timer.counter_meter.add(1, &[]);
        self.otel_timer
            .duration_meter
            .record(elapsed.as_millis() as u64, &[]);
    }
}
