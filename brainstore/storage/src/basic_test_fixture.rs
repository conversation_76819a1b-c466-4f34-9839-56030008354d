use util::{schema::Schema, system_types::FullObjectId, uuid::Uuid};

use crate::{
    directory::cached_directory::FileCacheOpts,
    index_document::make_full_schema,
    process_wal::{
        compact_segment_wal, process_object_wal, CompactSegmentWalInput, CompactSegmentWalOutput,
        ProcessObjectWalInput, ProcessObjectWalOutput,
    },
    tantivy_index::IndexMetaJson,
    test_util::TmpDirConfigWithStore,
    vacuum_test_util::{
        default_vacuum_index_options_for_testing, force_vacuum_then_validate_index_wal,
        VacuumThenValidateIndexWalArgs,
    },
    wal::WALScope,
    wal_entry::WalEntry,
};

// A test fixture which abstracts common functionality for writing WAL entries, processing,
// compaction, and other operations.
pub struct BasicTestFixture {
    pub tmp_dir_config: TmpDirConfigWithStore,
}

impl BasicTestFixture {
    pub fn new() -> Self {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        Self { tmp_dir_config }
    }

    pub fn new_with_file_cache_opts(file_cache_opts: FileCacheOpts) -> Self {
        let tmp_dir_config = TmpDirConfigWithStore::new_with_file_cache_opts(file_cache_opts);
        Self { tmp_dir_config }
    }

    pub fn make_default_full_schema(&self) -> util::schema::Schema {
        make_full_schema(&Default::default()).unwrap()
    }

    pub async fn wal_token(&self) -> Uuid {
        self.wal_token_obj(FullObjectId::default()).await
    }

    pub async fn wal_token_obj(&self, object_id: FullObjectId<'_>) -> Uuid {
        self.tmp_dir_config
            .config
            .global_store
            .query_object_metadatas(&[object_id])
            .await
            .unwrap()
            .remove(0)
            .wal_token
    }

    pub async fn write_object_wal_entries(&self, entries: Vec<WalEntry>) {
        self.tmp_dir_config
            .config
            .wal
            .insert(
                WALScope::ObjectId(FullObjectId::default(), self.wal_token().await),
                entries,
            )
            .await
            .unwrap();
    }

    pub fn process_wal_input(&self) -> ProcessObjectWalInput<'_> {
        ProcessObjectWalInput {
            object_id: Default::default(),
            config: &self.tmp_dir_config.config,
        }
    }

    pub async fn run_process_wal(&self) -> ProcessObjectWalOutput {
        process_object_wal(
            self.process_wal_input(),
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap()
    }

    pub fn compact_wal_input(&self, segment_id: Uuid) -> CompactSegmentWalInput {
        CompactSegmentWalInput {
            segment_id,
            index_store: self.tmp_dir_config.config.index.clone(),
            schema: self.make_default_full_schema(),
            global_store: self.tmp_dir_config.config.global_store.clone(),
            locks_manager: &*self.tmp_dir_config.config.locks_manager,
        }
    }

    #[allow(dead_code)]
    pub async fn run_compact_wal(&self, segment_id: Uuid) -> CompactSegmentWalOutput {
        compact_segment_wal(
            self.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap()
    }

    pub async fn fetch_segment_tantivy_metadata(&self, segment_id: Uuid) -> IndexMetaJson {
        self.tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .unwrap()
            .tantivy_meta
    }

    pub async fn validate_vacuum(&self, args: ValidateVacuumArgs<'_>) {
        let full_schema = args
            .schema
            .unwrap_or_else(|| make_full_schema(&self.make_default_full_schema()).unwrap());

        for stateless in [false, true] {
            let args = VacuumThenValidateIndexWalArgs {
                config_with_store: &self.tmp_dir_config.config,
                full_schema: full_schema.clone(),
                object_ids: args.object_ids,
                stateless,
                options: default_vacuum_index_options_for_testing(),
                expected_segment_id_cursor: None,
                dry_run: false,
            };
            force_vacuum_then_validate_index_wal(args).await;
        }
    }
}

#[derive(Default)]
pub struct ValidateVacuumArgs<'a> {
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub schema: Option<Schema>,
}
