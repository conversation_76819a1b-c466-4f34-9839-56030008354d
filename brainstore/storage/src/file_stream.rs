use std::io::{BufRead, Read};

use tokio::sync::mpsc;

use crate::config_with_store::url_to_store;
use util::anyhow::Result;
use util::futures::stream::BoxStream;
use util::futures::StreamExt;
use util::spawn_blocking_util::spawn_blocking_with_async_timeout;
use util::url::Url;
use util::url_util::str_to_url;

pub async fn open(
    paths: &[String],
    queue_size: usize,
    additional_entries: Vec<util::Value>,
) -> Result<BoxStream<Result<util::Value>>> {
    let (tx, mut rx) = mpsc::channel(queue_size);
    // Convert the channels to a `Stream`.

    let mut futures = Vec::new();

    let file_urls = paths
        .iter()
        .map(|path| str_to_url(path, None))
        .collect::<Result<Vec<_>>>()?;

    for url in file_urls {
        let tx = tx.clone();
        futures.push(tokio::spawn(async move {
            let lines = match read_lines(url).await {
                Ok(lines) => lines,
                Err(e) => {
                    tx.send(Err(e)).await.unwrap();
                    return Ok(());
                }
            };

            for line in lines {
                match line {
                    Ok(line) => {
                        let value: serde_json::Value = serde_json::from_str(&line)?;
                        tx.send(Ok(value)).await.unwrap();
                    }
                    Err(e) => tx.send(Err(e.into())).await.unwrap(),
                }
            }

            Ok::<(), util::anyhow::Error>(())
        }));
    }

    Ok(async_stream::stream! {
          while let Some(item) = rx.recv().await {
              yield item;
          }
          for entry in additional_entries {
              yield Ok(entry);
          }
    }
    .boxed())
}

async fn read_lines(url: Url) -> Result<std::io::Lines<std::io::Cursor<Vec<u8>>>> {
    // This obv doesn't work on object stores, so it's just meant for local files
    let (object_store, store_type, path) = url_to_store(&url)?;
    assert_eq!(store_type, util::url_util::ObjectStoreType::Local);
    let read = object_store.get(&path).await?.bytes().await?;

    let lines = spawn_blocking_with_async_timeout(
        &tokio::runtime::Handle::current(),
        move || {
            let reader = if path.to_string().ends_with(".gz") {
                let mut gz = flate2::read::GzDecoder::new(&*read);
                let mut buffer = Vec::new();
                gz.read_to_end(&mut buffer)?;
                std::io::Cursor::new(buffer)
            } else {
                std::io::Cursor::new(read.to_vec())
            };
            Ok::<_, util::anyhow::Error>(reader.lines())
        },
        Default::default(),
        || "file_stream::read_lines".into(),
    )
    .await???;

    Ok(lines)
}
