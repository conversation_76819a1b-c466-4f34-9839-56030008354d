#[cfg(test)]
pub(crate) mod test_init {
    use std::sync::Once;

    static INIT: Once = Once::new();

    /// Initialize the test environment. This should be called at the beginning of each test.
    /// It's safe to call multiple times - initialization will only happen once.
    pub fn init() {
        INIT.call_once(|| {
            // Initialize rustls crypto provider for all tests
            // We ignore the result because it might already be initialized
            let _ = rustls::crypto::ring::default_provider()
                .install_default();

            // Set up any other global test initialization here
            // For example, setting up logging
            let _ = env_logger::builder()
                .is_test(true)
                .try_init();
        });
    }
}
