use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Utc},
    json,
    serde_json::{self, json, Map, Value},
    system_types::{FullObjectId, FullRowId, ObjectIdOwned, ObjectType},
    xact::{PaginationKey, TransactionId},
};

pub const MAX_AUDIT_VALUE_SIZE: usize = 128;

// Instead of serializing/deserializing directly, prefer to use WalEntry::new to
// convert from JSON Value -> WalEntry and WalEntry::to_value to go the other
// way around. This avoids the generic serde overhead for the struct. In fact,
// in order to enable serializing/deserializing the struct without parsing the
// data field, we have serde skip it.
//
// IMPORTANT: when adding new fields, make sure to update WalEntry::new,
// WalEntry::to_value, and, if necessary, WalEntrySystemFields.
#[derive(<PERSON><PERSON><PERSON>, <PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>Eq, Eq)]
pub struct WalEntry {
    // System fields.
    pub id: String,
    pub created: DateTime<Utc>,
    pub _pagination_key: PaginationKey,
    pub _xact_id: TransactionId,
    pub _object_type: ObjectType,
    pub _object_id: ObjectIdOwned,
    pub root_span_id: String,
    pub span_id: String,
    pub span_parents: Vec<String>,
    pub comments: WalEntryComments,
    pub audit_data: AuditDataEntries,

    // Merge control fields. These specify how data is to be merged.

    // If true, the row is expected to be a standalone comment. Only the `comments` field is merged
    // and everything else is ignored.
    pub _is_standalone_comment: Option<bool>,

    // If true, deep-merge the data field. Otherwise, replace it wholesale.
    pub _is_merge: Option<bool>,

    // Limit deep-merges beyond beyond this set of paths. Only used when `_is_merge` is true.
    pub _merge_paths: Option<Vec<Vec<String>>>,

    // If true, replace system fields wholesale. Otherwise, leave the "sticky" ones intact.
    pub _replace_sticky_system_fields: Option<bool>,

    // If true, will delete the object from the index. Subsequent merges will essentially be
    // starting from scratch, including system fields.
    pub _object_delete: Option<bool>,

    // Non-system-controlled data.
    pub data: Map<String, Value>,
}

impl WalEntry {
    pub fn new(value: Value) -> Result<Self> {
        let mut value_object = match value {
            Value::Object(map) => map,
            _ => return Err(anyhow!("Expected object for WAL entry")),
        };
        Ok(WalEntry {
            id: serde_json::from_value(
                value_object
                    .remove("id")
                    .ok_or_else(|| anyhow!("Missing id"))?,
            )?,
            created: serde_json::from_value(
                value_object
                    .remove("created")
                    .ok_or_else(|| anyhow!("Missing created"))?,
            )?,
            _pagination_key: serde_json::from_value(
                value_object
                    .remove("_pagination_key")
                    .ok_or_else(|| anyhow!("Missing _pagination_key"))?,
            )?,
            _xact_id: serde_json::from_value(
                value_object
                    .remove("_xact_id")
                    .ok_or_else(|| anyhow!("Missing _xact_id"))?,
            )?,
            _object_type: serde_json::from_value(
                value_object
                    .remove("_object_type")
                    .ok_or_else(|| anyhow!("Missing _object_type"))?,
            )?,
            _object_id: serde_json::from_value(
                value_object
                    .remove("_object_id")
                    .ok_or_else(|| anyhow!("Missing _object_id"))?,
            )?,
            _is_standalone_comment: value_object
                .remove("_is_standalone_comment")
                .map(serde_json::from_value)
                .transpose()?,
            _is_merge: value_object
                .remove("_is_merge")
                .map(serde_json::from_value)
                .transpose()?,
            _merge_paths: value_object
                .remove("_merge_paths")
                .map(serde_json::from_value)
                .transpose()?,
            _replace_sticky_system_fields: value_object
                .remove("_replace_sticky_system_fields")
                .map(serde_json::from_value)
                .transpose()?,
            _object_delete: value_object
                .remove("_object_delete")
                .map(serde_json::from_value)
                .transpose()?,
            root_span_id: serde_json::from_value(
                value_object
                    .remove("root_span_id")
                    .ok_or_else(|| anyhow!("Missing root_span_id"))?,
            )?,
            span_id: serde_json::from_value(
                value_object
                    .remove("span_id")
                    .ok_or_else(|| anyhow!("Missing span_id"))?,
            )?,
            span_parents: serde_json::from_value(
                value_object
                    .remove("span_parents")
                    .unwrap_or(Value::Array(vec![])),
            )?,
            comments: value_object
                .remove("comments")
                .map(WalEntryComments::new)
                .transpose()?
                .unwrap_or_default(),
            audit_data: value_object
                .remove("audit_data")
                .map(AuditDataEntries::new)
                .transpose()?
                .unwrap_or_default(),
            data: value_object,
        })
    }

    pub fn to_value(self) -> serde_json::Value {
        let mut out = self.data;
        out.insert("id".to_string(), serde_json::to_value(self.id).unwrap());
        out.insert(
            "created".to_string(),
            serde_json::to_value(self.created).unwrap(),
        );
        out.insert(
            "_pagination_key".to_string(),
            serde_json::to_value(self._pagination_key).unwrap(),
        );
        out.insert(
            "_xact_id".to_string(),
            serde_json::to_value(self._xact_id).unwrap(),
        );
        out.insert(
            "_object_type".to_string(),
            serde_json::to_value(self._object_type).unwrap(),
        );
        out.insert(
            "_object_id".to_string(),
            serde_json::to_value(self._object_id).unwrap(),
        );
        if let Some(v) = self._is_standalone_comment {
            out.insert(
                "_is_standalone_comment".to_string(),
                serde_json::to_value(v).unwrap(),
            );
        }
        if let Some(v) = self._is_merge {
            out.insert("_is_merge".to_string(), serde_json::to_value(v).unwrap());
        }
        if let Some(v) = self._merge_paths {
            out.insert("_merge_paths".to_string(), serde_json::to_value(v).unwrap());
        }
        if let Some(v) = self._replace_sticky_system_fields {
            out.insert(
                "_replace_sticky_system_fields".to_string(),
                serde_json::to_value(v).unwrap(),
            );
        }
        if let Some(v) = self._object_delete {
            out.insert(
                "_object_delete".to_string(),
                serde_json::to_value(v).unwrap(),
            );
        }
        out.insert(
            "root_span_id".to_string(),
            serde_json::to_value(self.root_span_id).unwrap(),
        );
        out.insert(
            "span_id".to_string(),
            serde_json::to_value(self.span_id).unwrap(),
        );
        if !self.span_parents.is_empty() {
            out.insert(
                "span_parents".to_string(),
                serde_json::to_value(self.span_parents).unwrap(),
            );
        }
        if !self.comments.0.is_empty() {
            out.insert("comments".to_string(), self.comments.to_value());
        }
        if !self.audit_data.0.is_empty() {
            out.insert("audit_data".to_string(), self.audit_data.to_value());
        }
        Value::Object(out)
    }

    pub fn merge(&mut self, mut from: WalEntry) -> Result<()> {
        if self.id != from.id {
            return Err(anyhow!("Cannot merge entries with different IDs"));
        }
        if self._object_type != from._object_type {
            return Err(anyhow!("Cannot merge entries with different object types"));
        }
        if self._object_id != from._object_id {
            return Err(anyhow!("Cannot merge entries with different object IDs"));
        }

        // Handle the merge-into or merge-from WAL entries being standalone comments.
        if self._is_standalone_comment.unwrap_or(false) {
            from.comments = {
                let mut self_comments = std::mem::take(&mut self.comments);
                self_comments.merge(from.comments);
                self_comments
            };
            *self = from;
            return Ok(());
        } else if from._is_standalone_comment.unwrap_or(false) {
            self.comments.merge(from.comments);
            return Ok(());
        }

        // If our merge-from WAL entry is a delete entry, the other contents of the entry don't
        // really matter because deletion effectively removes the row from the index. We simply
        // propagate along the delete flag and return early.
        if from._object_delete.unwrap_or(false) {
            self._object_delete = from._object_delete;
            return Ok(());
        }

        // If our merge-into WAL entry is a delete entry, the from entry will constitute a complete
        // replacement of whatever it is merged into next, including system fields. We therefore do
        // a wholesale replacement and set the `_is_merge` and `_replace_sticky_system_fields`
        // flags accordingly.
        if self._object_delete.unwrap_or(false) {
            *self = from;
            self._is_merge = None;
            self._replace_sticky_system_fields = Some(true);
            return Ok(());
        }

        // Ensure each individual entry has populated its audit data before proceeding.
        self.ensure_audit_data();
        from.ensure_audit_data();

        // Pull out the non-system fields before handling the system fields, since they are
        // processed separately.
        let mut self_data = std::mem::take(&mut self.data);
        let from_data = std::mem::take(&mut from.data);
        let mut self_comments = std::mem::take(&mut self.comments);
        let from_comments = std::mem::take(&mut from.comments);
        let mut self_audit_data = std::mem::take(&mut self.audit_data);
        let from_audit_data = std::mem::take(&mut from.audit_data);

        // Retain the is-merge label only if both entries are marked as merge entries.
        let from_is_merge = from._is_merge.unwrap_or(false);
        let from_merge_paths = std::mem::take(&mut from._merge_paths);
        let final_is_merge = self._is_merge.unwrap_or(false) && from_is_merge;

        // For system fields, we have several different cases to consider:
        //
        //  - `_replace_sticky_system_fields` is true: replace the system fields wholesale, and
        //  propagate the flag to the merged entry.
        //
        //  - `_is_merge` is false: replace most of the system fields wholesale, but retain the
        //  sticky ones.
        //
        //  - `_is_merge` is true: keep nearly all of the system fields intact.
        //
        //  If modifying this, be sure to update the `ExcludedDoc` logic in
        //  brainstore/storage/src/index_wal_reader.rs.
        if from._replace_sticky_system_fields.unwrap_or(false) {
            *self = from;
        } else if !from_is_merge {
            let self_created = self.created;
            let self_pagination_key = self._pagination_key;
            *self = from;
            self.created = self_created;
            self._pagination_key = self_pagination_key;
        } else {
            self._xact_id = from._xact_id;
        }

        self_comments.merge(from_comments);
        self_audit_data.merge(from_audit_data);

        // Process the data field according to `_is_merge`. If the from entry has `_is_merge` set,
        // we deep-merge it into the merge-into entry. If not, we replace the data field wholesale.
        if from_is_merge {
            if let Some(merge_paths) = &from_merge_paths {
                // Convert merge_paths to a HashSet of string slices for deep_merge_with_stop_paths
                let stop_paths: HashSet<Vec<&str>> = merge_paths
                    .iter()
                    .map(|path| path.iter().map(|s| s.as_str()).collect())
                    .collect();
                json::deep_merge_maps_with_stop_paths(&mut self_data, &from_data, &stop_paths);
            } else {
                json::deep_merge_maps(&mut self_data, &from_data);
            }
        } else {
            self_data = from_data;
        }
        self.data = self_data;
        self.comments = self_comments;
        self.audit_data = self_audit_data;
        self._is_merge = if final_is_merge { Some(true) } else { None };
        // Only retain merge paths if both entries are merge-type entries
        if final_is_merge {
            // Retain the merge paths for future merges.
            self._merge_paths = util::functional::merge_options(
                std::mem::take(&mut self._merge_paths),
                from_merge_paths,
                |mut a, mut b| {
                    // Create a HashSet of paths in 'a' for efficient lookup
                    let a_path: HashSet<&Vec<String>> = a.iter().collect();
                    // Only keep paths in 'b' that aren't already in 'a'
                    b.retain(|path| !a_path.contains(&path));
                    // Combine the unique paths
                    a.extend(b);
                    a
                },
            );
        } else {
            // If the result is not a merge-type entry, clear the merge paths
            self._merge_paths = None;
        }
        Ok(())
    }

    pub fn full_row_id(&self) -> FullRowId {
        FullRowId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
            id: &self.id,
        }
    }

    pub fn full_root_span_id(&self) -> FullRowId {
        FullRowId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
            id: &self.root_span_id,
        }
    }

    pub fn full_object_id(&self) -> FullObjectId {
        FullObjectId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
        }
    }

    pub fn sanitize(&mut self) {
        sanitize_system_bool(&mut self._is_standalone_comment);
        sanitize_system_bool(&mut self._is_merge);
        sanitize_system_bool(&mut self._replace_sticky_system_fields);
        sanitize_system_bool(&mut self._object_delete);

        // For deleted object entries, just replace with a default-initialized entry, since the
        // rest of the fields don't matter.
        if self._object_delete.unwrap_or(false) {
            *self = WalEntry {
                id: self.id.clone(),
                _object_type: self._object_type,
                _object_id: self._object_id.clone(),
                _object_delete: self._object_delete,
                ..Default::default()
            };
        }

        // If we are not a merge, then the merge paths are irrelevant.
        if !self._is_merge.unwrap_or(false) {
            self._merge_paths = None;
        }

        self.comments.sanitize();
        self.ensure_audit_data();
        self.audit_data.sanitize();
    }

    pub fn to_sanitized(mut self) -> Self {
        self.sanitize();
        self
    }

    fn ensure_audit_data(&mut self) {
        // If we have only audit entries on our current xact ID, add a freshly-computed one, which
        // will take precedence when we call `audit_data.sanitize()`. Once we have audit entries
        // across multiple transaction IDs, we cannot recompute the audit entry for the current
        // transaction ID because it may contain data from other transactions.
        if self
            .audit_data
            .0
            .iter()
            .all(|x| x._xact_id == self._xact_id)
        {
            self.audit_data.0.push(self.compute_self_audit_entry());
        }
    }

    fn compute_self_audit_entry(&self) -> AuditDataEntry {
        let mut data = if self._object_delete.unwrap_or(false) {
            json!({
                "action": "delete",
            })
        } else if !self._is_merge.unwrap_or(false) {
            json!({
                "action": "upsert",
            })
        } else {
            // Find the deepest path in `data`. Note that this differs from the logic in
            // api-ts/src/audit_log.ts:computeAuditMergeData, which compares values between the old
            // and new object versions. But since it is generally quite difficult to obtain a
            // fully-merged version of the row prior to this transaction, we just publish the "to"
            // part of the audit entry.
            let mut ret = std::mem::take(json!({"action": "merge"}).as_object_mut().unwrap());
            if let Some((path, value)) = json::deepest_object_value(&self.data) {
                ret.insert("path".to_string(), json!(path));
                if serde_json::to_string(value).unwrap().len() <= MAX_AUDIT_VALUE_SIZE {
                    ret.insert("to".to_string(), value.clone());
                }
            }
            serde_json::Value::Object(ret)
        };

        AuditDataEntry {
            _xact_id: self._xact_id,
            data: std::mem::take(data.as_object_mut().unwrap()),
        }
    }
}

// Instead of serializing/deserializing directly, prefer to use WalEntryComment::new to convert
// from JSON Value -> WalEntryComment and WalEntryComment::to_value to go the other way around.
// This avoids the generic serde overhead for the struct.
//
// IMPORTANT: when adding new fields, make sure to update WalEntryComment::new and
// WalEntryComment::to_value.
#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct WalEntryComment {
    // System fields.
    pub id: String,
    pub _xact_id: TransactionId,

    // Merge control fields. These specify how data is to be merged.

    // If true, deep-merge the data field. Otherwise, replace it wholesale.
    pub _is_merge: Option<bool>,

    // If true, will delete the entry from the map it is contained within.
    pub _object_delete: Option<bool>,

    // Non-system-controlled data.
    pub data: Map<String, Value>,
}

impl WalEntryComment {
    pub fn new(value: Value) -> Result<Self> {
        let mut value_object = match value {
            Value::Object(map) => map,
            _ => return Err(anyhow!("Expected object for WAL entry")),
        };
        Ok(WalEntryComment {
            id: serde_json::from_value(
                value_object
                    .remove("id")
                    .ok_or_else(|| anyhow!("Missing id"))?,
            )?,
            _xact_id: serde_json::from_value(
                value_object
                    .remove("_xact_id")
                    .ok_or_else(|| anyhow!("Missing _xact_id"))?,
            )?,
            _is_merge: value_object
                .remove("_is_merge")
                .map(serde_json::from_value)
                .transpose()?,
            _object_delete: value_object
                .remove("_object_delete")
                .map(serde_json::from_value)
                .transpose()?,
            data: value_object,
        })
    }

    pub fn to_value(self) -> serde_json::Value {
        let mut out = self.data;
        out.insert("id".to_string(), serde_json::to_value(self.id).unwrap());
        out.insert(
            "_xact_id".to_string(),
            serde_json::to_value(self._xact_id).unwrap(),
        );
        if let Some(v) = self._is_merge {
            out.insert("_is_merge".to_string(), serde_json::to_value(v).unwrap());
        }
        if let Some(v) = self._object_delete {
            out.insert(
                "_object_delete".to_string(),
                serde_json::to_value(v).unwrap(),
            );
        }
        Value::Object(out)
    }

    pub fn merge(&mut self, from: WalEntryComment) -> Result<()> {
        if self.id != from.id {
            return Err(anyhow!("Cannot merge comments with different IDs"));
        }

        // If our merge-from WAL entry is a delete entry, the other contents of the entry don't
        // really matter because deletion effectively removes the row from the index. We simply
        // propagate along the delete flag and return early.
        if from._object_delete.unwrap_or(false) {
            self._object_delete = from._object_delete;
            return Ok(());
        }

        // If our merge-into WAL entry is a delete entry, the from entry will constitute a complete
        // replacement of whatever it is merged into next, including system fields. We therefore do
        // a wholesale replacement and set the `_is_merge` flag accordingly.
        if self._object_delete.unwrap_or(false) {
            *self = from;
            self._is_merge = None;
            return Ok(());
        }

        self._xact_id = from._xact_id;

        // Retain the is-merge label only if both entries are marked as merge entries.
        let final_is_merge = self._is_merge.unwrap_or(false) && from._is_merge.unwrap_or(false);

        // Process the data field according to `_is_merge`. If the from entry has `_is_merge` set,
        // we deep-merge it into the merge-into entry. If not, we replace the data field wholesale.
        if from._is_merge.unwrap_or(false) {
            json::deep_merge_maps(&mut self.data, &from.data);
        } else {
            self.data = from.data;
        }
        self._is_merge = if final_is_merge { Some(true) } else { None };

        Ok(())
    }

    pub fn sanitize(&mut self) {
        sanitize_system_bool(&mut self._is_merge);
        sanitize_system_bool(&mut self._object_delete);

        // For deleted object entries, just replace with a default-initialized entry, since the
        // rest of the fields don't matter.
        if self._object_delete.unwrap_or(false) {
            *self = WalEntryComment {
                id: self.id.clone(),
                _object_delete: self._object_delete,
                ..Default::default()
            };
        }
    }

    pub fn to_sanitized(mut self) -> Self {
        self.sanitize();
        self
    }
}

#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct WalEntryComments(pub Vec<WalEntryComment>);

impl WalEntryComments {
    pub fn new(value: Value) -> Result<Self> {
        let value_vec = match value {
            Value::Array(a) => a,
            _ => return Err(anyhow!("Expected array for WAL entry comments")),
        };
        Ok(Self(
            value_vec
                .into_iter()
                .map(WalEntryComment::new)
                .collect::<Result<Vec<_>>>()?,
        ))
    }

    pub fn to_value(self) -> serde_json::Value {
        serde_json::to_value(
            self.0
                .into_iter()
                .map(WalEntryComment::to_value)
                .collect::<Vec<_>>(),
        )
        .unwrap()
    }

    pub fn merge(&mut self, from: WalEntryComments) {
        self.0.extend(from.0);
    }

    pub fn sanitize(&mut self) {
        // First merge all comments with the same ID.
        let (input_ind_to_output_ind, num_merged_comments) = {
            let mut out = Vec::with_capacity(self.0.len());
            let mut id_to_ind: HashMap<&str, usize> = HashMap::with_capacity(self.0.len());
            for comment in &self.0 {
                let new_ind = id_to_ind.len();
                let val = id_to_ind.entry(&comment.id).or_insert_with(|| new_ind);
                out.push(*val);
            }
            (out, id_to_ind.len())
        };
        let mut merged_comments: Vec<WalEntryComment> = Vec::with_capacity(num_merged_comments);
        for (comment, output_ind) in std::mem::take(&mut self.0)
            .into_iter()
            .zip(input_ind_to_output_ind)
        {
            assert!(output_ind <= merged_comments.len());
            if output_ind == merged_comments.len() {
                merged_comments.push(comment);
            } else {
                // Should always succeed because we're matching by ID.
                merged_comments[output_ind].merge(comment).unwrap();
            }
        }

        // Next remove all comments that are marked for deletion.
        merged_comments.retain(|x| !x._object_delete.unwrap_or(false));

        // Sort the comments by increasing transaction ID.
        merged_comments.sort_by_key(|x| x._xact_id);

        // Sanitize the comments.
        for comment in &mut merged_comments {
            comment.sanitize();
        }

        self.0 = merged_comments;
    }
}

// Instead of serializing/deserializing directly, prefer to use AuditDataEntry::new to convert
// from JSON Value -> AuditDataEntry and AuditDataEntry::to_value to go the other way around.
// This avoids the generic serde overhead for the struct.
//
// IMPORTANT: when adding new fields, make sure to update AuditDataEntry::new and
// AuditDataEntry::to_value.
#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct AuditDataEntry {
    // System fields.
    pub _xact_id: TransactionId,

    // Non-system-controlled data.
    pub data: Map<String, Value>,
}

impl AuditDataEntry {
    pub fn new(value: Value) -> Result<Self> {
        let mut value_object = match value {
            Value::Object(map) => map,
            _ => return Err(anyhow!("Expected object for audit data entry")),
        };
        Ok(AuditDataEntry {
            _xact_id: serde_json::from_value(
                value_object
                    .remove("_xact_id")
                    .ok_or_else(|| anyhow!("Missing _xact_id"))?,
            )?,
            data: value_object,
        })
    }

    pub fn to_value(self) -> serde_json::Value {
        let mut out = self.data;
        out.insert(
            "_xact_id".to_string(),
            serde_json::to_value(self._xact_id).unwrap(),
        );
        Value::Object(out)
    }

    pub fn merge(&mut self, from: AuditDataEntry) -> Result<()> {
        if self._xact_id != from._xact_id {
            return Err(anyhow!(
                "Cannot merge audit data entries with different xact IDs"
            ));
        }
        self.data = from.data;
        Ok(())
    }
}

#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct AuditDataEntries(pub Vec<AuditDataEntry>);

impl AuditDataEntries {
    pub fn new(value: Value) -> Result<Self> {
        let value_vec = match value {
            Value::Array(a) => a,
            _ => return Err(anyhow!("Expected array for audit data entries")),
        };
        Ok(Self(
            value_vec
                .into_iter()
                .map(AuditDataEntry::new)
                .collect::<Result<Vec<_>>>()?,
        ))
    }

    pub fn to_value(self) -> serde_json::Value {
        serde_json::to_value(
            self.0
                .into_iter()
                .map(AuditDataEntry::to_value)
                .collect::<Vec<_>>(),
        )
        .unwrap()
    }

    pub fn merge(&mut self, from: AuditDataEntries) {
        self.0.extend(from.0);
    }

    pub fn sanitize(&mut self) {
        // First merge all audit data entries with the same xact ID.
        let (input_ind_to_output_ind, num_merged_entries) = {
            let mut out = Vec::with_capacity(self.0.len());
            let mut id_to_ind: HashMap<TransactionId, usize> = HashMap::with_capacity(self.0.len());
            for entry in &self.0 {
                let new_ind = id_to_ind.len();
                let val = id_to_ind.entry(entry._xact_id).or_insert_with(|| new_ind);
                out.push(*val);
            }
            (out, id_to_ind.len())
        };
        let mut merged_entries: Vec<AuditDataEntry> = Vec::with_capacity(num_merged_entries);
        for (entry, output_ind) in std::mem::take(&mut self.0)
            .into_iter()
            .zip(input_ind_to_output_ind)
        {
            assert!(output_ind <= merged_entries.len());
            if output_ind == merged_entries.len() {
                merged_entries.push(entry);
            } else {
                // Should always succeed because we're matching by xact_id.
                merged_entries[output_ind].merge(entry).unwrap();
            }
        }

        // Sort the audit entries by increasing transaction ID.
        merged_entries.sort_by_key(|x| x._xact_id);

        self.0 = merged_entries;
    }
}

fn sanitize_system_bool(field: &mut Option<bool>) {
    *field = match field {
        Some(true) => Some(true),
        Some(false) => None,
        None => None,
    };
}

// In some scenarios, we only need to validate/inspect certain system fields of
// a WAL entry, and can skip deserializing everything else. To enable this, we
// have a WalEntrySystemFields which includes just the system fields we need for
// these lightweight processing operations.
//
// IMPORTANT: The set of fields needs to be kept in sync with WalEntry.
#[derive(Default, Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct WalEntrySystemFields {
    pub id: String,
    pub _pagination_key: PaginationKey,
    pub _xact_id: TransactionId,
    pub _object_type: ObjectType,
    pub _object_id: ObjectIdOwned,
    pub root_span_id: String,
    pub _is_standalone_comment: Option<bool>,
}

impl WalEntrySystemFields {
    pub fn full_row_id(&self) -> FullRowId {
        FullRowId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
            id: &self.id,
        }
    }

    pub fn full_root_span_id(&self) -> FullRowId {
        FullRowId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
            id: &self.root_span_id,
        }
    }

    pub fn full_object_id(&self) -> FullObjectId {
        FullObjectId {
            object_type: self._object_type,
            object_id: self._object_id.as_ref(),
        }
    }
}
