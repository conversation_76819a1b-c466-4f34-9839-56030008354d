use std::collections::HashMap;
use std::str::FromStr;

use serde::{Deserialize, Serialize};
use std::hash::{<PERSON>h, Hasher};
use util::anyhow::{self, bail, Result};
use util::json::PathPiece;
use util::ptree::{MakePTree, TreeBuilder};
use util::system_types::{FullObjectId, FullObjectIdOwned, ObjectIdOwned, ObjectType};

pub use super::json_ast::{Shape, UnpivotSource, UnpivotType};
use super::types::weakest_scalar_type;
pub use crate::parser::json_ast::{IntervalUnit, SortDirection};
pub use crate::schema::{LogicalSchema, ScalarType};
use crate::util::Cursor;

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize, MakePTree)]
pub enum ComparisonOp {
    Eq,
    Ne,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>ike,
    Match,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize, Copy)]
pub enum BooleanOp {
    And,
    Or,
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize, MakePTree)]
pub enum ArithmeticOp {
    Add,
    Sub,
    Mul,
    Div,
    Mod,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum UnaryOp {
    Neg,
    Not,
    IsNull,
    IsNotNull,
    Cast { cast_type: ScalarType },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TernaryCond {
    pub cond: Box<Expr>,
    pub then: Box<Expr>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Literal {
    pub value: serde_json::Value,
    pub expr_type: ScalarType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Field {
    pub name: Vec<PathPiece>,
    pub expr_type: LogicalSchema,
    pub scalar_type: ScalarType,
    pub source: Option<UnpivotSource>,
}

impl Field {
    pub fn new(
        name: Vec<PathPiece>,
        expr_type: LogicalSchema,
        source: Option<UnpivotSource>,
    ) -> Self {
        let scalar_type = weakest_scalar_type(&expr_type);
        Self {
            name,
            expr_type,
            scalar_type,
            source,
        }
    }
}

impl PartialEq for Field {
    fn eq(&self, other: &Self) -> bool {
        self.name == other.name && self.source == other.source
    }
}

impl Eq for Field {}

impl Hash for Field {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.name.hash(state);
        self.source.hash(state);
    }
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Function {
    pub name: String,
    pub args: Vec<Box<Expr>>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Expr {
    Literal(Literal),
    Interval {
        value: i64,
        unit: IntervalUnit,
    },
    Field(Field),
    Function(Function),
    Comparison {
        op: ComparisonOp,
        left: Box<Expr>,
        right: Box<Expr>,
    },
    Includes {
        haystack: Box<Expr>,
        needle: Box<Expr>,
    },
    Boolean {
        op: BooleanOp,
        children: Vec<Box<Expr>>,
    },
    Ternary {
        conds: Vec<TernaryCond>,
        else_expr: Box<Expr>,
    },
    Arithmetic {
        op: ArithmeticOp,
        left: Box<Expr>,
        right: Box<Expr>,
    },
    Unary {
        op: UnaryOp,
        expr: Box<Expr>,
    },
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Alias {
    pub expr: Box<Expr>,
    pub alias: String,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct UnpivotExpr {
    pub expr: Box<Expr>,
    pub unpivot_type: UnpivotType,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Projection {
    GroupBy {
        dimensions: Vec<Alias>,
        pivot: Vec<Alias>,
        measures: Vec<Alias>,
    },
    Flat {
        select: Vec<Alias>,
    },
    Infer {
        infer: Vec<Field>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FromObjects {
    pub objects: Vec<FullObjectIdOwned>,
    pub shape: Shape,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SortExpr {
    Alias(String),
    Expr(Box<Expr>),
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SortItem {
    pub dir: SortDirection,
    pub expr: SortExpr,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct SummaryExprs {
    pub comparison_key: Box<Expr>,
    pub weighted_scores: Vec<Alias>,
    pub custom_columns: Vec<Alias>,
    pub preview_length: Option<usize>,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct Query {
    pub projection: Projection,
    pub filter: Option<Box<Expr>>,
    pub from: FromObjects,
    pub unpivot: Vec<UnpivotExpr>,
    pub sort: Vec<SortItem>,
    pub limit: Option<usize>,
    pub cursor: Option<Cursor>,
    pub summary: Option<SummaryExprs>,
}

pub trait TransformExpr {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> Self;
}

impl TryFrom<super::json_ast::BoundExpr> for Expr {
    type Error = anyhow::Error;

    fn try_from(expr: super::json_ast::BoundExpr) -> Result<Self> {
        let boolean_op = match &expr {
            super::json_ast::BoundExpr::And { .. } => Some(BooleanOp::And),
            super::json_ast::BoundExpr::Or { .. } => Some(BooleanOp::Or),
            _ => None,
        };

        Ok(match expr {
            super::json_ast::BoundExpr::Literal { value, expr_type } => {
                Expr::Literal(Literal { value, expr_type })
            }
            super::json_ast::BoundExpr::Interval { value, unit } => Expr::Interval { value, unit },
            super::json_ast::BoundExpr::Field {
                name,
                expr_type,
                source,
            } => Expr::Field(Field::new(name, expr_type, source)),
            super::json_ast::BoundExpr::Function { name, args } => Expr::Function(Function {
                name,
                args: args
                    .into_iter()
                    .map(|e| Ok(Box::new((*e).try_into()?)))
                    .collect::<Result<Vec<_>>>()?,
            }),
            super::json_ast::BoundExpr::Eq { left, right } => Expr::Comparison {
                op: ComparisonOp::Eq,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Ne { left, right } => Expr::Comparison {
                op: ComparisonOp::Ne,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Lt { left, right } => Expr::Comparison {
                op: ComparisonOp::Lt,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Le { left, right } => Expr::Comparison {
                op: ComparisonOp::Le,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Gt { left, right } => Expr::Comparison {
                op: ComparisonOp::Gt,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Ge { left, right } => Expr::Comparison {
                op: ComparisonOp::Ge,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Like { left, right } => Expr::Comparison {
                op: ComparisonOp::Like,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Ilike { left, right } => Expr::Comparison {
                op: ComparisonOp::Ilike,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Match { left, right } => Expr::Comparison {
                op: ComparisonOp::Match,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Includes { haystack, needle } => Expr::Includes {
                haystack: Box::new((*haystack).try_into()?),
                needle: Box::new((*needle).try_into()?),
            },
            super::json_ast::BoundExpr::And {
                left,
                right,
                children,
            }
            | super::json_ast::BoundExpr::Or {
                left,
                right,
                children,
            } => {
                let bool_op = boolean_op.expect("Invalid boolean expression");
                match (left, right, children) {
                    (Some(left), Some(right), None) => flatten_boolean_expr(
                        bool_op,
                        vec![(*left).try_into()?, (*right).try_into()?],
                    )
                    .ok_or_else(|| anyhow::anyhow!("Invalid {:?} (expect 2 children)", bool_op))?,
                    (None, None, Some(children)) => flatten_boolean_expr(
                        bool_op,
                        children
                            .into_iter()
                            .map(|c| (*c).try_into())
                            .collect::<Result<Vec<_>>>()?,
                    )
                    .ok_or_else(|| {
                        anyhow::anyhow!("Invalid {:?} (expect at least 1 child)", bool_op)
                    })?,
                    _ => {
                        return Err(anyhow::anyhow!(
                            "Invalid {:?} (should have left/right or children)",
                            bool_op
                        ));
                    }
                }
            }
            super::json_ast::BoundExpr::If { conds, else_expr } => Expr::Ternary {
                conds: conds
                    .into_iter()
                    .map(|c| {
                        Ok(TernaryCond {
                            cond: Box::new((*c.cond).try_into()?),
                            then: Box::new((*c.then).try_into()?),
                        })
                    })
                    .collect::<Result<Vec<_>>>()?,
                else_expr: Box::new((*else_expr).try_into()?),
            },
            super::json_ast::BoundExpr::Add { left, right } => Expr::Arithmetic {
                op: ArithmeticOp::Add,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Sub { left, right } => Expr::Arithmetic {
                op: ArithmeticOp::Sub,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Mul { left, right } => Expr::Arithmetic {
                op: ArithmeticOp::Mul,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Div { left, right } => Expr::Arithmetic {
                op: ArithmeticOp::Div,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Mod { left, right } => Expr::Arithmetic {
                op: ArithmeticOp::Mod,
                left: Box::new((*left).try_into()?),
                right: Box::new((*right).try_into()?),
            },
            super::json_ast::BoundExpr::Neg { expr } => Expr::Unary {
                op: UnaryOp::Neg,
                expr: Box::new((*expr).try_into()?),
            },
            super::json_ast::BoundExpr::Not { expr } => Expr::Unary {
                op: UnaryOp::Not,
                expr: Box::new((*expr).try_into()?),
            },
            super::json_ast::BoundExpr::IsNull { expr } => Expr::Unary {
                op: UnaryOp::IsNull,
                expr: Box::new((*expr).try_into()?),
            },
            super::json_ast::BoundExpr::IsNotNull { expr } => Expr::Unary {
                op: UnaryOp::IsNotNull,
                expr: Box::new((*expr).try_into()?),
            },
            super::json_ast::BoundExpr::Cast { expr, cast_type } => Expr::Unary {
                op: UnaryOp::Cast { cast_type },
                expr: Box::new((*expr).try_into()?),
            },
        })
    }
}

impl TryFrom<super::json_ast::BoundAlias> for Alias {
    type Error = anyhow::Error;

    fn try_from(alias: super::json_ast::BoundAlias) -> Result<Self> {
        Ok(Alias {
            expr: Box::new((*alias.expr).try_into()?),
            alias: alias.alias,
        })
    }
}

impl TryFrom<super::json_ast::BoundQuery> for Query {
    type Error = anyhow::Error;

    fn try_from(query: super::json_ast::BoundQuery) -> Result<Self> {
        Ok(Query {
            projection: match query.projection {
                super::json_ast::BoundProjection::GroupBy {
                    dimensions,
                    pivot,
                    measures,
                } => Projection::GroupBy {
                    dimensions: dimensions
                        .into_iter()
                        .map(|a| a.try_into())
                        .collect::<Result<Vec<_>>>()?,
                    pivot: pivot
                        .into_iter()
                        .map(|a| a.try_into())
                        .collect::<Result<Vec<_>>>()?,
                    measures: measures
                        .into_iter()
                        .map(|a| a.try_into())
                        .collect::<Result<Vec<_>>>()?,
                },
                super::json_ast::BoundProjection::Flat { select } => Projection::Flat {
                    select: select
                        .into_iter()
                        .map(|a| a.try_into())
                        .collect::<Result<Vec<_>>>()?,
                },
                super::json_ast::BoundProjection::Infer { infer } => Projection::Infer {
                    infer: infer
                        .into_iter()
                        .map(|a| {
                            let expr: Expr = a.try_into()?;
                            Ok(match expr {
                                Expr::Field(field) => field,
                                _ => {
                                    return Err(anyhow::anyhow!(
                                        "Invalid infer clause: {:?}",
                                        expr
                                    ));
                                }
                            })
                        })
                        .collect::<Result<Vec<_>>>()?,
                },
            },
            filter: match query.filter {
                Some(e) => Some(Box::new((*e).try_into()?)),
                None => None,
            },
            from: query.from.try_into()?,
            unpivot: query
                .unpivot
                .into_iter()
                .map(|e| {
                    Ok(UnpivotExpr {
                        expr: Box::new((*e.expr).try_into()?),
                        unpivot_type: e.unpivot_type,
                    })
                })
                .collect::<Result<Vec<_>>>()?,
            sort: match query.sort {
                Some(s) => s
                    .into_iter()
                    .map(|i| {
                        Ok(match i {
                            super::json_ast::BoundSortItem::Alias { dir, alias } => SortItem {
                                dir,
                                expr: SortExpr::Alias(alias),
                            },
                            super::json_ast::BoundSortItem::Expr { dir, expr } => SortItem {
                                dir,
                                expr: SortExpr::Expr(Box::new((*expr).try_into()?)),
                            },
                        })
                    })
                    .collect::<Result<Vec<_>>>()?,
                None => vec![],
            },
            limit: query.limit.map(|l| l as usize),
            cursor: query.cursor.map(|c| c.as_str().try_into()).transpose()?,
            summary: match query.summary {
                Some(s) => Some(SummaryExprs {
                    comparison_key: Box::new((*s.comparison_key).try_into()?),
                    weighted_scores: s
                        .weighted_scores
                        .into_iter()
                        .map(|a| Ok(a.try_into()?))
                        .collect::<Result<Vec<_>>>()?,
                    custom_columns: s
                        .custom_columns
                        .into_iter()
                        .map(|a| a.try_into())
                        .collect::<Result<Vec<_>>>()?,
                    preview_length: s.preview_length.map(|l| l as usize),
                }),
                None => None,
            },
        })
    }
}

impl TryFrom<Option<super::json_ast::BoundFrom>> for FromObjects {
    type Error = anyhow::Error;

    fn try_from(from: Option<super::json_ast::BoundFrom>) -> Result<Self> {
        let object_ids = match from.as_ref() {
            Some(from) => {
                let object_type: Option<ObjectType> = from.name.parse().ok();
                if let Some(object_type) = object_type {
                    from.objects
                        .as_ref()
                        .unwrap_or(&vec![])
                        .iter()
                        .map(|o| {
                            Ok(FullObjectIdOwned {
                                object_type,
                                object_id: ObjectIdOwned::new(o.clone())?,
                            })
                        })
                        .collect::<Result<Vec<_>>>()?
                } else if from.name == "object" {
                    from.objects
                        .as_ref()
                        .unwrap_or(&vec![])
                        .iter()
                        .map(|o| FullObjectIdOwned::from_str(o))
                        .collect::<Result<Vec<_>>>()?
                } else {
                    if from.objects.is_some() {
                        return Err(anyhow::anyhow!("Invalid object type: {}", from.name));
                    }
                    vec![FullObjectIdOwned {
                        object_type: ObjectType::Dataset,
                        object_id: ObjectIdOwned::new(from.name.clone())?,
                    }]
                }
            }
            None => vec![FullObjectId::default().to_owned()],
        };

        Ok(FromObjects {
            objects: object_ids,
            // NOTE: This is an intentional deviation from the JS interpreter, which defaults to, and only
            // supports "traces".
            shape: from.and_then(|f| f.shape).unwrap_or(Shape::Spans),
        })
    }
}

impl Expr {
    pub fn find_fields(&self) -> HashMap<Vec<PathPiece>, ScalarType> {
        let mut fields = HashMap::new();
        let mut add_field = |expr: &Expr| {
            if let Expr::Field(field) = expr {
                if let None = field.source {
                    fields.insert(field.name.clone(), weakest_scalar_type(&field.expr_type));
                }
            }
        };
        self.traverse(&mut add_field);
        fields
    }

    pub fn traverse(&self, f: &mut impl FnMut(&Expr)) {
        f(self);
        match self {
            Expr::Literal(_) => {}
            Expr::Interval { .. } => {}
            Expr::Field(_) => {}
            Expr::Function(func) => {
                for arg in func.args.iter() {
                    arg.traverse(f);
                }
            }
            Expr::Comparison { left, right, .. } => {
                left.traverse(f);
                right.traverse(f);
            }
            Expr::Includes { haystack, needle } => {
                haystack.traverse(f);
                needle.traverse(f);
            }
            Expr::Boolean { children, .. } => {
                for child in children {
                    child.traverse(f);
                }
            }
            Expr::Ternary { conds, else_expr } => {
                for cond in conds {
                    cond.cond.traverse(f);
                    cond.then.traverse(f);
                }
                else_expr.traverse(f);
            }
            Expr::Arithmetic { left, right, .. } => {
                left.traverse(f);
                right.traverse(f);
            }
            Expr::Unary { expr, .. } => {
                expr.traverse(f);
            }
        }
    }
}

impl TransformExpr for Expr {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> Expr {
        match self {
            Expr::Literal(literal) => f(Expr::Literal(literal)),
            Expr::Interval { value, unit } => f(Expr::Interval { value, unit }),
            Expr::Field(field) => f(Expr::Field(field)),
            Expr::Function(func) => {
                let args = func
                    .args
                    .into_iter()
                    .map(|a| Box::new(a.transform(f)))
                    .collect();
                f(Expr::Function(Function {
                    name: func.name,
                    args,
                }))
            }
            Expr::Comparison { left, right, op } => {
                let left = left.transform(f);
                let right = right.transform(f);
                f(Expr::Comparison {
                    left: Box::new(left),
                    right: Box::new(right),
                    op,
                })
            }
            Expr::Includes { haystack, needle } => {
                let haystack = haystack.transform(f);
                let needle = needle.transform(f);
                f(Expr::Includes {
                    haystack: Box::new(haystack),
                    needle: Box::new(needle),
                })
            }
            Expr::Boolean { children, op } => {
                let children = children
                    .into_iter()
                    .map(|c| Box::new(c.transform(f)))
                    .collect();
                f(Expr::Boolean { children, op })
            }
            Expr::Ternary { conds, else_expr } => {
                let conds = conds
                    .into_iter()
                    .map(|c| TernaryCond {
                        cond: Box::new(c.cond.transform(f)),
                        then: Box::new(c.then.transform(f)),
                    })
                    .collect();
                let else_expr = Box::new(else_expr.transform(f));
                f(Expr::Ternary { conds, else_expr })
            }
            Expr::Arithmetic { left, right, op } => {
                let left = left.transform(f);
                let right = right.transform(f);
                f(Expr::Arithmetic {
                    left: Box::new(left),
                    right: Box::new(right),
                    op,
                })
            }
            Expr::Unary { op, expr } => {
                let expr = Box::new(expr.transform(f));
                f(Expr::Unary { op, expr })
            }
        }
    }
}

impl TransformExpr for Alias {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> Alias {
        Alias {
            expr: Box::new(self.expr.transform(f)),
            alias: self.alias,
        }
    }
}

impl TransformExpr for Projection {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> Projection {
        match self {
            Projection::Flat { select } => Projection::Flat {
                select: select.into_iter().map(|a| a.transform(f)).collect(),
            },
            Projection::Infer { infer } => Projection::Infer {
                infer: infer
                    .into_iter()
                    .map(|a| match Expr::Field(a).transform(f) {
                        Expr::Field(field) => field,
                        o => {
                            panic!("Invalid infer clause: {:?}", &o);
                        }
                    })
                    .collect(),
            },
            Projection::GroupBy {
                dimensions,
                measures,
                pivot,
            } => Projection::GroupBy {
                dimensions: dimensions.into_iter().map(|a| a.transform(f)).collect(),
                measures: measures.into_iter().map(|a| a.transform(f)).collect(),
                pivot: pivot.into_iter().map(|a| a.transform(f)).collect(),
            },
        }
    }
}

impl TransformExpr for UnpivotExpr {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> UnpivotExpr {
        UnpivotExpr {
            expr: Box::new(self.expr.transform(f)),
            unpivot_type: self.unpivot_type,
        }
    }
}

impl TransformExpr for SortItem {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> SortItem {
        SortItem {
            expr: match self.expr {
                SortExpr::Expr(e) => SortExpr::Expr(Box::new(e.transform(f))),
                SortExpr::Alias(a) => SortExpr::Alias(a),
            },
            dir: self.dir,
        }
    }
}

impl TransformExpr for Query {
    fn transform(self, f: &mut impl FnMut(Expr) -> Expr) -> Query {
        Query {
            projection: self.projection,
            filter: self.filter.map(|e| Box::new(e.transform(f))),
            unpivot: self.unpivot.into_iter().map(|e| e.transform(f)).collect(),
            sort: self.sort.into_iter().map(|s| s.transform(f)).collect(),
            // These fields aren't transformed
            from: self.from,
            limit: self.limit,
            cursor: self.cursor,
            summary: self.summary.map(|s| SummaryExprs {
                comparison_key: Box::new((*s.comparison_key).transform(f)),
                weighted_scores: s
                    .weighted_scores
                    .into_iter()
                    .map(|a| a.transform(f))
                    .collect(),
                custom_columns: s
                    .custom_columns
                    .into_iter()
                    .map(|a| a.transform(f))
                    .collect(),
                preview_length: s.preview_length,
            }),
        }
    }
}

impl MakePTree for Expr {
    fn label(&self) -> String {
        match self {
            Expr::Literal(literal) => {
                format!("{} ({:?})", literal.value.to_string(), literal.expr_type)
            }
            Expr::Interval { value, unit } => format!("Interval({value}, {unit:?})"),
            Expr::Field(field) => field.label(),
            Expr::Function(func) => func.label(),
            Expr::Comparison { op, .. } => format!("{:?}", op),
            Expr::Includes { .. } => "Includes".to_string(),
            Expr::Boolean { op, .. } => format!("{:?}", op),
            Expr::Ternary { .. } => "If".to_string(),
            Expr::Arithmetic { op, .. } => format!("{:?}", op),
            Expr::Unary { op, .. } => format!("{:?}", op),
        }
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match self {
            Expr::Literal(_) | Expr::Interval { .. } => {}
            Expr::Field(field) => field.make_ptree(builder),
            Expr::Function(func) => func.make_ptree(builder),
            Expr::Comparison { left, right, .. } => {
                self.add_child(builder, left.as_ref());
                self.add_child(builder, right.as_ref());
            }
            Expr::Includes { haystack, needle } => {
                self.add_child(builder, haystack.as_ref());
                self.add_child(builder, needle.as_ref());
            }
            Expr::Boolean { children, .. } => {
                for child in children {
                    self.add_child(builder, child.as_ref());
                }
            }
            Expr::Ternary { conds, else_expr } => {
                for cond in conds {
                    self.add_child(builder, cond.cond.as_ref());
                    self.add_child(builder, cond.then.as_ref());
                }
                builder.begin_child("else".to_string());
                self.add_child(builder, else_expr.as_ref());
                builder.end_child();
            }
            Expr::Arithmetic { left, right, .. } => {
                self.add_child(builder, left.as_ref());
                self.add_child(builder, right.as_ref());
            }
            Expr::Unary { expr, .. } => {
                self.add_child(builder, expr.as_ref());
            }
        }
    }
}

impl MakePTree for Field {
    fn label(&self) -> String {
        let serialized = serde_json::to_string(&self.expr_type)
            .unwrap_or_else(|_| "<failed to serialize>".to_string());
        let field_type = if serialized.len() > 20 {
            format!("{}...", serialized.chars().take(20).collect::<String>())
        } else {
            serialized
        };
        format!(
            "{} ({})",
            self.name
                .iter()
                .map(|c| match c {
                    PathPiece::Key(s) => s.clone(),
                    PathPiece::Index(i) => i.to_string(),
                })
                .collect::<Vec<_>>()
                .join("."),
            field_type
        )
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}
impl MakePTree for Function {
    fn label(&self) -> String {
        format!("{:?}", self.name)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        for arg in &self.args {
            self.add_child(builder, arg.as_ref());
        }
    }
}

impl MakePTree for Alias {
    fn label(&self) -> String {
        format!("Alias \"{}\"", self.alias)
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

impl MakePTree for Query {
    fn label(&self) -> String {
        "Bound query".to_string()
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        builder.begin_child("From".to_string());
        self.add_child(builder, &self.from.objects);
        builder.add_empty_child(format!("{:?}", self.from.shape));
        builder.end_child();

        if self.unpivot.len() > 0 {
            builder.begin_child("Unpivot".to_string());
            for unpivot in &self.unpivot {
                builder.begin_child(format!("{:?}", unpivot.unpivot_type));
                self.add_child(builder, unpivot.expr.as_ref());
                builder.end_child();
            }
            builder.end_child();
        }

        self.add_child(builder, &self.projection);

        if let Some(filter) = &self.filter {
            builder.begin_child("Filter".to_string());
            self.add_child(builder, filter);
            builder.end_child();
        }
        if self.sort.len() > 0 {
            builder.begin_child("Sort".to_string());
            for sort in &self.sort {
                self.add_child(builder, sort);
            }
            builder.end_child();
        }
        if let Some(limit) = &self.limit {
            builder.add_empty_child(format!("Limit {}", limit));
        }
        if let Some(cursor) = &self.cursor {
            builder.add_empty_child(format!("Cursor {}", cursor));
        }
    }
}

impl Projection {
    pub fn find_alias(&self, alias: &str) -> Option<&Alias> {
        match self {
            Projection::GroupBy {
                dimensions,
                measures,
                pivot,
            } => dimensions
                .iter()
                .find(|a| a.alias == alias)
                .or_else(|| measures.iter().find(|a| a.alias == alias))
                .or_else(|| pivot.iter().find(|a| a.alias == alias)),
            Projection::Flat { select } => select.iter().find(|a| a.alias == alias),
            Projection::Infer { .. } => None,
        }
    }
}

impl MakePTree for Projection {
    fn label(&self) -> String {
        match self {
            Projection::GroupBy { .. } => "Group by".to_string(),
            Projection::Flat { .. } => "Select".to_string(),
            Projection::Infer { .. } => "Infer".to_string(),
        }
    }
    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match self {
            Projection::GroupBy {
                dimensions,
                pivot,
                measures,
            } => {
                if dimensions.len() > 0 {
                    builder.begin_child("Dimensions".to_string());
                    for dim in dimensions {
                        self.add_child(builder, dim);
                    }
                    builder.end_child();
                }
                if pivot.len() > 0 {
                    builder.begin_child("Pivot".to_string());
                    for pivot in pivot {
                        self.add_child(builder, pivot);
                    }
                    builder.end_child();
                }
                if measures.len() > 0 {
                    builder.begin_child("Measures".to_string());
                    for measure in measures {
                        self.add_child(builder, measure);
                    }
                    builder.end_child();
                }
            }
            Projection::Flat { select } => {
                for select in select {
                    self.add_child(builder, select);
                }
            }
            Projection::Infer { infer } => {
                builder.begin_child("Infer".to_string());
                for infer in infer {
                    self.add_child(builder, infer);
                }
                builder.end_child();
            }
        }
    }
}

impl SortItem {
    pub fn find_fields(
        &self,
        projection: &Projection,
    ) -> util::Result<HashMap<Vec<PathPiece>, ScalarType>> {
        Ok(match &self.expr {
            SortExpr::Alias(alias) => {
                let projection_expr = match projection.find_alias(alias) {
                    Some(expr) => expr,
                    None => bail!("Sort alias '{alias}' not found in projection"),
                };
                projection_expr.expr.find_fields()
            }
            SortExpr::Expr(expr) => expr.find_fields(),
        })
    }
}

impl MakePTree for SortItem {
    fn label(&self) -> String {
        format!("{:?}", self.dir)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match &self.expr {
            SortExpr::Alias(alias) => {
                builder.add_empty_child(format!("Alias \"{}\"", alias));
            }
            SortExpr::Expr(expr) => {
                self.add_child(builder, expr.as_ref());
            }
        }
    }
}

impl MakePTree for UnpivotExpr {
    fn label(&self) -> String {
        format!("{:?}", self.unpivot_type)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, self.expr.as_ref());
    }
}

pub fn flatten_boolean_expr(op: BooleanOp, children: Vec<Expr>) -> Option<Expr> {
    let mut flattened = Vec::new();
    for child in children {
        match child {
            Expr::Boolean {
                op: child_op,
                children: child_children,
            } if child_op == op => {
                flattened.extend(child_children);
            }
            c => flattened.push(Box::new(c)),
        }
    }
    if flattened.is_empty() {
        None
    } else if flattened.len() == 1 {
        Some(*flattened.remove(0))
    } else {
        Some(Expr::Boolean {
            op,
            children: flattened,
        })
    }
}
