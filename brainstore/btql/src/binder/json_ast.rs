/// This file is carefully kept in sync with btql/src/binder/ast.ts
/// and is intended to be able to deserialize btql queries in their bound,
/// JSON schema format into Rust types. We do not use this format directly,
/// however, because it's pretty annoying.
use crate::parser::json_ast::{IntervalUnit, SortDirection};
use crate::schema::{LogicalSchema, ScalarType};
use serde::{Deserialize, Serialize};
use util::json::PathPiece;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "op", rename_all = "lowercase")]
pub enum BoundExpr {
    #[serde(rename = "literal")]
    Literal {
        value: serde_json::Value,
        #[serde(rename = "type")]
        expr_type: ScalarType,
    },
    #[serde(rename = "interval")]
    Interval { value: i64, unit: IntervalUnit },
    #[serde(rename = "field")]
    Field {
        name: Vec<PathPiece>,
        #[serde(rename = "type")]
        expr_type: LogicalSchema,
        source: Option<UnpivotSource>,
    },
    #[serde(rename = "function")]
    Function {
        name: String,
        args: Vec<Box<BoundExpr>>,
    },
    #[serde(rename = "eq")]
    Eq {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "ne")]
    Ne {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "lt")]
    Lt {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "le")]
    Le {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "gt")]
    Gt {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "ge")]
    Ge {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "like")]
    Like {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "ilike")]
    Ilike {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "match")]
    Match {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "includes")]
    Includes {
        haystack: Box<BoundExpr>,
        needle: Box<BoundExpr>,
    },
    #[serde(rename = "and")]
    And {
        // For back-compat, we used to use left and right. After some point,
        // maybe as early as June 1, 2025, we can remove the left and right fields
        // and use children exclusively.
        left: Option<Box<BoundExpr>>,
        right: Option<Box<BoundExpr>>,
        children: Option<Vec<Box<BoundExpr>>>,
    },
    #[serde(rename = "or")]
    Or {
        // For back-compat, we used to use left and right. After some point,
        // maybe as early as June 1, 2025, we can remove the left and right fields
        // and use children exclusively.
        left: Option<Box<BoundExpr>>,
        right: Option<Box<BoundExpr>>,
        children: Option<Vec<Box<BoundExpr>>>,
    },
    #[serde(rename = "if")]
    If {
        conds: Vec<TernaryCond>,
        #[serde(rename = "else")]
        else_expr: Box<BoundExpr>,
    },
    #[serde(rename = "add")]
    Add {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "sub")]
    Sub {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "mul")]
    Mul {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "div")]
    Div {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "mod")]
    Mod {
        left: Box<BoundExpr>,
        right: Box<BoundExpr>,
    },
    #[serde(rename = "neg")]
    Neg { expr: Box<BoundExpr> },
    #[serde(rename = "not")]
    Not { expr: Box<BoundExpr> },
    #[serde(rename = "isnull")]
    IsNull { expr: Box<BoundExpr> },
    #[serde(rename = "isnotnull")]
    IsNotNull { expr: Box<BoundExpr> },
    #[serde(rename = "cast")]
    Cast {
        expr: Box<BoundExpr>,
        #[serde(rename = "type")]
        cast_type: ScalarType,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TernaryCond {
    pub cond: Box<BoundExpr>,
    pub then: Box<BoundExpr>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Hash)]
pub struct UnpivotSource {
    pub unpivot: u32,
    #[serde(rename = "type")]
    pub source_type: UnpivotSourceType,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Hash)]
#[serde(rename_all = "lowercase")]
pub enum UnpivotSourceType {
    Key,
    Value,
    Element,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundAlias {
    pub expr: Box<BoundExpr>,
    pub alias: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundUnpivotExpr {
    pub expr: Box<BoundExpr>,
    #[serde(rename = "type")]
    pub unpivot_type: UnpivotType,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UnpivotType {
    Object,
    Array,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum BoundProjection {
    GroupBy {
        dimensions: Vec<BoundAlias>,
        pivot: Vec<BoundAlias>,
        measures: Vec<BoundAlias>,
    },
    Flat {
        select: Vec<BoundAlias>,
    },
    Infer {
        infer: Vec<BoundExpr>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum Shape {
    Spans,
    Traces,
    Summary,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct BoundFrom {
    pub name: String,
    pub objects: Option<Vec<String>>,
    pub shape: Option<Shape>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum BoundSortItem {
    Alias {
        dir: SortDirection,
        alias: String,
    },
    Expr {
        dir: SortDirection,
        expr: Box<BoundExpr>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundSummary {
    pub comparison_key: Box<BoundExpr>,
    pub weighted_scores: Vec<BoundAlias>,
    pub custom_columns: Vec<BoundAlias>,
    pub preview_length: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundQuery {
    #[serde(flatten)]
    pub projection: BoundProjection,
    pub filter: Option<Box<BoundExpr>>,
    pub from: Option<BoundFrom>,
    pub unpivot: Vec<BoundUnpivotExpr>,
    pub sort: Option<Vec<BoundSortItem>>,
    pub limit: Option<i64>,
    pub cursor: Option<String>,
    pub summary: Option<BoundSummary>,
}
