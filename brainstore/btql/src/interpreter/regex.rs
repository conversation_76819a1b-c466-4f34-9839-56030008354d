use std::error::Error;
use regex_automata::dfa::{dense::DFA, Automaton as RegexAutomaton};

// These need to be the same as in tantivy, we just can't have this crate be a dependency of tantivy.
pub const JSON_PATH_SEGMENT_SEP: u8 = 1u8;
pub const JSON_END_OF_PATH: u8 = 0u8;

pub const JSON_PATH_SEGMENT_SEP_CHAR: char = '`' as char;
pub const JSON_END_OF_PATH_CHAR: char = '~' as char;
const CONTROL_CHAR_CHAR: char = 0x7F as char;

// A regex automaton wrapper that handles control characters and extended ASCII
// by replacing them with safe alternatives to prevent DFA dead states.
//
#[derive(<PERSON><PERSON>, Debug)]
pub struct ControlCharWrapper {
    inner_dfa: DFA<Vec<u32>>,
    has_dot_star_suffix: bool,
    sanitized_pattern: String,
}

impl ControlCharWrapper {
    pub fn new(pattern: &str) -> Result<Self, Box<dyn Error>> {
        use regex_automata::dfa::dense;

        let sanitized_pattern = pattern
            .chars()
            .map(|c| {
                let byte = c as u32;
                if byte == JSON_PATH_SEGMENT_SEP as u32 {
                    JSON_PATH_SEGMENT_SEP_CHAR
                } else if byte == JSON_END_OF_PATH as u32 {
                    JSON_END_OF_PATH_CHAR
                } else if byte <= 0x1F || byte == 0x7F {
                    CONTROL_CHAR_CHAR
                } else {
                    c
                }
            })
            .collect::<String>();

        let inner_dfa = dense::Builder::new()
            .build(&sanitized_pattern)?;

        let has_dot_star_suffix = pattern.ends_with(".*");

        Ok(Self { inner_dfa, has_dot_star_suffix, sanitized_pattern })
    }

    pub fn sanitized_pattern(&self) -> &str {
        &self.sanitized_pattern
    }

    pub fn matches_text(&self, text: &str) -> bool {
        use regex_automata::util::start::Config as StartConfig;

        let start_config = StartConfig::new();
        let mut state = self.inner_dfa.start_state(&start_config)
            .unwrap_or(regex_automata::util::primitives::StateID::ZERO);

        for byte in text.bytes() {
            state = self.accept(&state, byte);

            // TODO(asteere): This optimization to end early is not done when tantivy uses this automaton. For large
            // strings this could be a significant performance win. We should consider modifying tantivy to do this as well.
            if self.inner_dfa.is_dead_state(state) {
                return false;
            }
            if self.will_always_match(&state) {
                return true;
            }
        }

        return self.is_match(&state);
    }

    pub fn accept(&self, state: &regex_automata::util::primitives::StateID, byte: u8) -> regex_automata::util::primitives::StateID {
        // The regex DFA breaks when handling control characters, so we replace them.
        // We need to handle json separators specially so we can query json paths.
        let next_state = match byte {
            b if b == JSON_PATH_SEGMENT_SEP => {
                self.inner_dfa.next_state(*state, JSON_PATH_SEGMENT_SEP_CHAR as u8)
            }
            b if b == JSON_END_OF_PATH => {
                self.inner_dfa.next_state(*state, JSON_END_OF_PATH_CHAR as u8)
            }
            b if b <= 0x1F || b == 0x7F => {
                self.inner_dfa.next_state(*state, CONTROL_CHAR_CHAR as u8)
            }
            _ => {
                // For printable ASCII, delegate to the inner DFA
                self.inner_dfa.next_state(*state, byte)
            }
        };

        next_state
    }

    pub fn is_match(&self, state: &regex_automata::util::primitives::StateID) -> bool {
        // The regex expects a null terminated string to end the pattern. We have to simulate this
        // since tantivy doesn't pass the null terminator to the regex.
        //
        let terminal_state = self.inner_dfa.next_state(*state, b'\0');
        self.inner_dfa.is_match_state(terminal_state)
    }

    pub fn can_match(&self, state: &regex_automata::util::primitives::StateID) -> bool {
        !self.inner_dfa.is_dead_state(*state)
    }

    pub fn will_always_match(&self, state: &regex_automata::util::primitives::StateID) -> bool {
        self.has_dot_star_suffix && self.inner_dfa.is_match_state(*state)
    }

    pub fn start(&self) -> regex_automata::util::primitives::StateID {
        use regex_automata::util::start::Config as StartConfig;
        let start_config = StartConfig::new();
        self.inner_dfa.start_state(&start_config).unwrap_or(regex_automata::util::primitives::StateID::ZERO)
    }
}
