use util::anyhow::Result;

use clap::{Parser, Subcommand};

pub mod create;
pub mod search;

#[cfg(test)]
pub mod json_agg_test;

#[derive(<PERSON><PERSON><PERSON>, Debug, Clone)]
pub struct Args {
    #[command(subcommand)]
    cmd: Commands,
}

#[derive(Subcommand, Debug, Clone)]
enum Commands {
    Create(create::CreateIndexArgs),
    Search(search::SearchArgs),
}

pub fn main() -> Result<()> {
    let args = Args::parse();

    match args.cmd {
        Commands::Create(args) => create::create(args),
        Commands::Search(args) => search::search(args),
    }
}
