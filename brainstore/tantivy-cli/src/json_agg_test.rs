use std::{collections::HashMap, sync::Arc};

use rand::Rng;
use serde_json::json;
use tantivy::{
    aggregation::{
        agg_req::{Aggregation, AggregationVariants, Aggregations},
        metric::StatsAggregation,
        AggregationCollector, AggregationLimits,
    },
    collector::{Collector, SegmentCollector},
    columnar::{Column, ColumnarReader, DynamicColumn},
    doc,
    fastfield::FastFieldReaders,
    query::{AllQuery},
    schema::{Schema, FAST},
    Index, IndexWriter, Score, SegmentReader,
};
use util::anyhow::Result;

// Import RegexQuery from query crate
use query::interpreter::tantivy::query::regex::RegexQuery;

#[test]
pub fn test_regex_query_functionality_from_json_index() -> Result<(), util::anyhow::Error> {
    let index_path = std::path::Path::new("src/json_index");
    let index = tantivy::Index::open_in_dir(index_path)?;

    let schema = index.schema();

    // Print all field names in the index
    println!("=== ALL FIELD NAMES IN INDEX ===");
    for field in schema.fields() {
        println!("Field: '{:?}' ", field);
    }
    println!("=== END OF FIELD NAMES ===\n");

    let json_field = schema.get_field("input_json").expect("Expected 'data' field in index");

    let reader = index.reader()?;
    let searcher = reader.searcher();

    println!("=== PRINTING ALL TERMS FROM EXISTING INDEX ===");

    for segment_reader in searcher.segment_readers() {
        let inverted_index = segment_reader.inverted_index(json_field)?;
        let terms = inverted_index.terms();
        let mut term_ord = 0u64;

        let mut term_stream = terms.stream()?;
        while term_stream.advance() {
            let term = term_stream.key();
            let mut term_display = String::new();
            for &byte in term {
                if byte.is_ascii_graphic() || byte == b' ' {
                    term_display.push(byte as char);
                } else {
                    term_display.push_str(&format!("\\x{:02x}", byte));
                }
            }
            let term_info = term_stream.value();
            println!("Term #{}: '{}' (doc_freq: {})",
                     term_ord, term_display, term_info.doc_freq);
            term_ord += 1;
        }
    }
    println!("=== END OF TERM LISTING ===\n");
    // Test cases for basic regex functionality. We automatically convert % into .*.
    // (?i) is used for case insensitive matching.
    //
    let basic_test_cases = vec![
        (".*noexist.*", false, vec![]),

        // Exact matching only works on values in the exact path
        ("Center different west community myself major travel southern consumer company together view say scene various grow very line contain political often wife determine difference much military a morning or whether tax long cause eight eat alone indicate successful take send tend issue kid nearly fill protect strong across would building trip throughout firm car his media sense amount green score project away.", true, vec![]),
        ("casual", true, vec!["metadata".to_string(), "category".to_string()]),
        ("formal", true, vec!["metadata".to_string(), "category".to_string()]),

        // Prefix matching should work only on values in the exact path
        ("You are.*", false, vec![]),
        ("You are.*", true, vec!["content".to_string()]),
        ("ou are.*", false, vec![]),
        ("(?i)you are.*", true, vec!["content".to_string()]),
        ("(?i)ou are.*", false, vec!["content".to_string()]),
        ("(?i)you are.*", true, vec!["content".to_string()]),
        ("The statement.*", true, vec!["output".to_string()]),
        ("Eng.*", true, vec!["metadata".to_string(), "language".to_string()]),

        // Suffix search only matches suffix of values in the exact path
        (".*life.", true, vec!["output".to_string()]),
        (".*world.", false, vec![]),
        (".*content", false, vec![]),
        (".*source", false, vec![]),
        (".*metadata", false, vec![]),
        (".*user", true, vec!["role".to_string()]),
        (".*role", false, vec![]),
        (".*output", false, vec![]),
        (".*ish", true, vec!["metadata".to_string(), "language".to_string()]),
        (".*ish", false, vec!["metadata".to_string()]),
        (".*ish", false, vec!["content".to_string()]),

        // Querying keys and values are both supported as long as you have a wildcard prefix and suffix
        //
        (".*comparing.*", true, vec![]),
        (".*content.*", true, vec![]),
        (".*category.*", true, vec![]),
        (".*content.*comparing.*", true, vec![]),
        (".*role.*user.*", true, vec![]),
        (".*category.*casual.*", true, vec!["metadata".to_string()]),
        (".*BEGIN DATA.*", true, vec![]),
    ];

    let mut success = true;
    for (pattern, should_match, json_path) in basic_test_cases {
        let regex_query = match RegexQuery::new(json_field, pattern, false, &json_path, true) {
            Ok(q) => q,
            Err(e) => {
                println!("ERROR: Failed to create RegexQuery: {}", e);
                continue;
            }
        };

        let count_result = searcher.search(&regex_query, &tantivy::collector::Count);
        let matched = match count_result {
            Ok(count) => count > 0,
            Err(e) => {
                println!("ERROR: Search failed: {}", e);
                continue;
            }
        };

        if matched == should_match {
            println!("✅ PASS: Pattern '{}' behaved as expected (matched: {})", pattern, matched);
        } else {
            println!("❌ FAIL: Pattern '{}' with json_path {:?} expected {}, got {}", pattern, json_path, should_match, matched);
            success = false;
        }
    }

    assert!(success);
    Ok(())
}

#[test]
pub fn test_regex_query_functionality_from_text_index() -> Result<(), util::anyhow::Error> {
    let index_path = std::path::Path::new("src/text_index");
    let index = tantivy::Index::open_in_dir(index_path)?;

    // Get the schema from the existing index
    let schema = index.schema();
    let text_field = schema.get_field("s").expect("Expected 's' field in index");

    let reader = index.reader()?;
    let searcher = reader.searcher();

    println!("=== PRINTING ALL TERMS FROM TEXT INDEX ===");

    for segment_reader in searcher.segment_readers() {
        let inverted_index = segment_reader.inverted_index(text_field)?;
        let terms = inverted_index.terms();
        let mut term_ord = 0u64;

        let mut term_stream = terms.stream()?;
        while term_stream.advance() {
            let term = term_stream.key();
            let mut term_display = String::new();
            for &byte in term {
                if byte.is_ascii_graphic() || byte == b' ' {
                    term_display.push(byte as char);
                } else {
                    term_display.push_str(&format!("\\x{:02x}", byte));
                }
            }
            let term_info = term_stream.value();
            println!("Term #{}: '{}' (doc_freq: {})",
                     term_ord, term_display, term_info.doc_freq);
            term_ord += 1;
        }
    }
    println!("=== END OF TERM LISTING ===\n");

    // Test cases for basic regex functionality on text fields
    // Terms are ["foo", "foo bar", "baz", "bar baz"]
    let basic_test_cases = vec![
        (".*foo.*", 2),
        ("foo", 1),
        ("foo.*", 2),
        (".*baz", 2),
        ("bar ", 0),
        ("bar .*", 1),
        (".*", 4),
        ("foo bar", 1),
    ];

    let mut success = true;
    for (pattern, num_matched) in basic_test_cases {
        let regex_query = match RegexQuery::new(text_field, pattern, false, &[], false) {
            Ok(q) => q,
            Err(e) => {
                println!("ERROR: Failed to create RegexQuery: {}", e);
                continue;
            }
        };

        let count_result = searcher.search(&regex_query, &tantivy::collector::Count);
        let matched = match count_result {
            Ok(count) => count,
            Err(e) => {
                println!("ERROR: Search failed: {}", e);
                continue;
            }
        };

        if matched == num_matched {
            println!("✅ PASS: Pattern '{}' behaved as expected (matched: {})", pattern, matched);
        } else {
            println!("❌ FAIL: Pattern '{}' expected {}, got {}", pattern, num_matched, matched);
            success = false;
        }
    }

    assert!(success);
    Ok(())
}

#[derive(Clone)]
pub struct FastFieldReadersImpl {
    columnar: Arc<ColumnarReader>,
    #[allow(dead_code)]
    schema: Schema,
}

#[test]
pub fn test_json_agg() -> Result<(), util::anyhow::Error> {
    let mut schema_builder = Schema::builder();
    let json = schema_builder.add_json_field("json", FAST);
    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema);
    let mut index_writer: IndexWriter = index.writer(150_000_000).unwrap();
    // => Segment with empty json
    // => Segment with json, but no field partially_empty
    index_writer
        .add_document(
            doc!(json => json!({"different_field": "blue", "foo": 2, "fee": json!({"foo": 3})})),
        )
        .unwrap();
    //// => Segment with field partially_empty
    index_writer
        .add_document(doc!(json => json!({"partially_empty": 10.0, "bar": 3})))
        .unwrap();
    index_writer
        .add_document(doc!(json => json!({"partially_full": 10.0, "bar": 3})))
        .unwrap();
    index_writer.commit().unwrap();

    // Merge all segments
    let segments = index.searchable_segment_metas()?;
    index_writer.merge(&segments.iter().map(|s| s.id()).collect::<Vec<_>>());
    index_writer.wait_merging_threads()?;

    let reader = index.reader()?;
    let searcher = reader.searcher();

    for segment_reader in searcher.segment_readers() {
        let fast_fields = segment_reader.fast_fields();

        let fast_fields_impl =
            unsafe { std::mem::transmute::<&FastFieldReaders, &FastFieldReadersImpl>(fast_fields) };

        let columnar_reader = &fast_fields_impl.columnar;
        eprintln!("columnar_reader: {:?}", columnar_reader);

        let columns = columnar_reader.list_columns()?;
        for (column_name, column_handle) in columns {
            eprintln!("column {:?}\t{:?}", column_name, column_handle);
        }

        eprintln!("\n\ndynamic_column_handles");
        let dynamic_column_handles = fast_fields.dynamic_column_handles("jso")?;
        for dynamic_column_handle in dynamic_column_handles {
            eprintln!("dynamic_column_handle: {:?}", dynamic_column_handle);
        }

        eprintln!("\n\nstream_for_column_range");

        let columns = columnar_reader
            .iter_columns()?
            .filter(|(column_name, _)| column_name.starts_with("json"));
        for (column_name, column_handle) in columns {
            eprintln!("column {:?}\t{:?}", column_name, column_handle);
        }
    }

    eprintln!("\n\nFINAL RESULTS");
    let results = searcher.search(&AllQuery, &PivotCollector::with_field("json".to_string()))?;
    for (column_name, stats) in results {
        eprintln!("column_name: {:?}\tstats: {:?}", column_name, stats);
    }
    Ok(())
}

const N_FIELD_NAMES: usize = 1;
const N_DOCS: usize = 100_000;

#[test]
fn fast_field_aggregation_benchmark() -> Result<(), util::anyhow::Error> {
    let mut rng = rand::thread_rng();

    let mut schema_builder = Schema::builder();
    let mut fields = Vec::with_capacity(N_FIELD_NAMES);
    for i in 0..N_FIELD_NAMES {
        fields.push(schema_builder.add_f64_field(&format!("score_{}", i), FAST));
    }
    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema);
    let mut index_writer: IndexWriter = index.writer(150_000_000).unwrap();

    eprintln!("[static fields] Creating {} documents", N_DOCS);
    let start = std::time::Instant::now();
    // Create documents with random subset of fields
    for _ in 0..N_DOCS {
        let mut doc = tantivy::TantivyDocument::default();
        for field in fields.iter() {
            doc.add_field_value(*field, rng.gen::<f64>());
        }
        index_writer.add_document(doc).unwrap();
    }
    eprintln!(
        "[static fields] Time to create documents: {:?}",
        start.elapsed()
    );

    let start = std::time::Instant::now();
    index_writer.commit().unwrap();
    eprintln!("[static fields] Time to commit: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let segments = index.searchable_segment_metas()?;
    index_writer.merge(&segments.iter().map(|s| s.id()).collect::<Vec<_>>());
    index_writer.wait_merging_threads()?;
    eprintln!("[static fields] Time to merge: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let reader = index.reader()?;
    let searcher = reader.searcher();
    eprintln!(
        "[static fields] Time to create reader and searcher: {:?}",
        start.elapsed()
    );

    let mut aggregations = Aggregations::new();
    for (i, _field) in fields.iter().enumerate() {
        aggregations.insert(
            format!("stats_{}", i),
            Aggregation {
                agg: AggregationVariants::Stats(StatsAggregation::from_field_name(format!(
                    "score_{}",
                    i
                ))),
                sub_aggregation: Aggregations::new(),
            },
        );
    }
    let agg_collector = AggregationCollector::from_aggs(aggregations, AggregationLimits::default());

    let start = std::time::Instant::now();
    let results = searcher.search(&AllQuery, &agg_collector)?;
    eprintln!("[static fields] Time to aggregate: {:?}", start.elapsed());

    eprintln!("[static fields] results: {:?}", results.0.len());
    eprintln!(
        "[static fields] results: {:?}",
        results.0.values().next().unwrap()
    );

    Ok(())
}

#[test]
fn static_collector_json_benchmark() -> Result<(), util::anyhow::Error> {
    let mut rng = rand::thread_rng();

    // Generate distinct random field names
    let field_names: Vec<String> = (0..N_FIELD_NAMES).map(|i| format!("field_{}", i)).collect();

    let mut schema_builder = Schema::builder();
    let json = schema_builder.add_json_field("scores", FAST);
    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema);
    let mut index_writer: IndexWriter = index.writer(150_000_000).unwrap();

    eprintln!("[dynamic fields] Creating {} documents", N_DOCS);
    let start = std::time::Instant::now();
    // Create documents with random subset of fields
    for _ in 0..N_DOCS {
        let mut doc = serde_json::value::Map::new();

        // Add random subset of fields to this doc
        for field_name in &field_names {
            if rng.gen_bool(0.5) {
                // 50% chance to include each field
                doc.insert(field_name.clone(), rng.gen::<f64>().into()); // Random value between 0 and 1
            }
        }
        index_writer
            .add_document(doc!(json => serde_json::Value::Object(doc)))
            .unwrap();
    }
    eprintln!(
        "[dynamic fields] Time to create documents: {:?}",
        start.elapsed()
    );

    let start = std::time::Instant::now();
    index_writer.commit().unwrap();
    eprintln!("[dynamic fields] Time to commit: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let segments = index.searchable_segment_metas()?;
    index_writer.merge(&segments.iter().map(|s| s.id()).collect::<Vec<_>>());
    index_writer.wait_merging_threads()?;
    eprintln!("[dynamic fields] Time to merge: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let reader = index.reader()?;
    let searcher = reader.searcher();
    eprintln!(
        "[dynamic fields] Time to create reader and searcher: {:?}",
        start.elapsed()
    );

    let mut aggregations = Aggregations::new();
    for (i, field_name) in field_names.iter().enumerate() {
        aggregations.insert(
            format!("stats_{}", i),
            Aggregation {
                agg: AggregationVariants::Stats(StatsAggregation::from_field_name(format!(
                    "scores.{}",
                    field_name
                ))),
                sub_aggregation: Aggregations::new(),
            },
        );
    }
    let agg_collector = AggregationCollector::from_aggs(aggregations, AggregationLimits::default());

    let start = std::time::Instant::now();
    let results = searcher.search(&AllQuery, &agg_collector)?;
    eprintln!("[dynamic fields] Time to aggregate: {:?}", start.elapsed());

    eprintln!("[dynamic fields] results: {:?}", results.0.len());
    eprintln!(
        "[dynamic fields] results: {:?}",
        results.0.values().next().unwrap()
    );

    Ok(())
}

#[test]
fn dynamic_collector_benchmark() -> Result<(), util::anyhow::Error> {
    let mut rng = rand::thread_rng();

    // Generate distinct random field names
    let field_names: Vec<String> = (0..N_FIELD_NAMES).map(|i| format!("field_{}", i)).collect();

    let mut schema_builder = Schema::builder();
    let json = schema_builder.add_json_field("scores", FAST);
    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema);
    let mut index_writer: IndexWriter = index.writer(150_000_000).unwrap();

    eprintln!("[dynamic fields] Creating {} documents", N_DOCS);
    let start = std::time::Instant::now();
    // Create documents with random subset of fields
    for _ in 0..N_DOCS {
        let mut doc = serde_json::value::Map::new();

        // Add random subset of fields to this doc
        for field_name in &field_names {
            if rng.gen_bool(0.5) {
                // 50% chance to include each field
                doc.insert(field_name.clone(), rng.gen::<f64>().into()); // Random value between 0 and 1
            }
        }
        index_writer
            .add_document(doc!(json => serde_json::Value::Object(doc)))
            .unwrap();
    }
    eprintln!(
        "[dynamic fields] Time to create documents: {:?}",
        start.elapsed()
    );

    let start = std::time::Instant::now();
    index_writer.commit().unwrap();
    eprintln!("[dynamic fields] Time to commit: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let segments = index.searchable_segment_metas()?;
    index_writer.merge(&segments.iter().map(|s| s.id()).collect::<Vec<_>>());
    index_writer.wait_merging_threads()?;
    eprintln!("[dynamic fields] Time to merge: {:?}", start.elapsed());

    let start = std::time::Instant::now();
    let reader = index.reader()?;
    let searcher = reader.searcher();
    eprintln!(
        "[dynamic fields] Time to create reader and searcher: {:?}",
        start.elapsed()
    );

    let start = std::time::Instant::now();
    let results = searcher.search(&AllQuery, &PivotCollector::with_field("scores".to_string()))?;
    eprintln!("[dynamic fields] Time to aggregate: {:?}", start.elapsed());

    for (column_name, stats) in results.iter().take(10) {
        eprintln!(
            "[dynamic fields] column_name: {:?}\tstats: {:?}",
            column_name, stats
        );
    }

    Ok(())
}

struct PivotCollector {
    field: String,
}

impl PivotCollector {
    fn with_field(field: String) -> PivotCollector {
        PivotCollector { field }
    }
}

impl Collector for PivotCollector {
    // That's the type of our result.
    // Our standard deviation will be a float.
    type Fruit = HashMap<String, Stats>;

    type Child = PivotSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<PivotSegmentCollector> {
        let fast_fields = segment_reader.fast_fields();

        let fast_fields_impl =
            unsafe { std::mem::transmute::<&FastFieldReaders, &FastFieldReadersImpl>(fast_fields) };

        let columnar_reader = fast_fields_impl.columnar.clone();
        let columns = columnar_reader
            .iter_columns()?
            .filter(|(column_name, _)| {
                eprintln!("column_name: {:?}", column_name);
                column_name.starts_with(&self.field)
            })
            .map(|(column_name, column_handle)| {
                Ok((
                    column_name,
                    column_handle
                        .open()?
                        .coerce_numerical(tantivy::columnar::NumericalType::F64),
                ))
            })
            .collect::<Result<Vec<_>, std::io::Error>>()
            .map_err(|e| tantivy::TantivyError::IoError(Arc::new(e)))?
            .into_iter()
            .filter_map(|(column_name, column)| match column {
                Some(DynamicColumn::F64(c)) => Some((column_name, c)),
                _ => None,
            })
            .collect::<Vec<_>>();

        let num_columns = columns.len();
        Ok(PivotSegmentCollector {
            columns,
            stats: vec![Stats::default(); num_columns],
        })
    }

    fn requires_scoring(&self) -> bool {
        // this collector does not care about score.
        false
    }

    fn merge_fruits(
        &self,
        segment_stats: Vec<HashMap<String, Stats>>,
    ) -> tantivy::Result<HashMap<String, Stats>> {
        let mut stats = HashMap::default();
        for segment_stats in segment_stats.into_iter() {
            for (column_name, stat) in segment_stats.into_iter() {
                let entry = stats.entry(column_name).or_insert(Stats::default());
                entry.count += stat.count;
                entry.sum += stat.sum;
            }
        }
        Ok(stats)
    }
}

#[derive(Default, Debug, Clone)]
pub struct Stats {
    count: usize,
    sum: f64,
}

struct PivotSegmentCollector {
    columns: Vec<(String, Column<f64>)>,
    stats: Vec<Stats>,
}

impl PivotSegmentCollector {
    #[inline(always)]
    fn collect_column_for_doc(entry: &mut Stats, column_handle: &Column<f64>, doc: u32) {
        for value in column_handle.values_for_doc(doc) {
            entry.count += 1;
            entry.sum += value;
        }
    }
}

impl SegmentCollector for PivotSegmentCollector {
    type Fruit = HashMap<String, Stats>;

    #[inline(always)]
    fn collect(&mut self, doc: u32, _score: Score) {
        // Since we know the values are single value, we could call `first_or_default_col` on the
        // column and fetch single values.
        for (i, (_, column_handle)) in self.columns.iter().enumerate() {
            PivotSegmentCollector::collect_column_for_doc(&mut self.stats[i], column_handle, doc);
        }
    }

    #[inline(always)]
    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        for (i, (_, column_handle)) in self.columns.iter().enumerate() {
            let stats = &mut self.stats[i];

            for doc in docs {
                PivotSegmentCollector::collect_column_for_doc(stats, column_handle, *doc);
            }
        }
    }

    fn harvest(self) -> <Self as SegmentCollector>::Fruit {
        self.stats
            .into_iter()
            .zip(self.columns.iter())
            .map(|(stats, (column_name, _))| (column_name.clone(), stats))
            .filter(|(_, stats)| stats.count > 0)
            .collect::<HashMap<String, Stats>>()
    }
}
