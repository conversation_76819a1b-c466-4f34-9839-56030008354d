[package]
name = "tantivy-cli"
version = "0.1.0"
edition = "2021"

[dependencies]
clap = { version = "4.5.18", features = ["derive"] }
util = { path = "../util" }
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
tantivy = { path = "../tantivy" }
rand = "0.8.5"
rustc-hash = "2.0.0"
regex-automata = "0.4.9"
tantivy-fst = "0.5.0"
btql = { version = "0.1.0", path = "../btql" }
query = { version = "0.1.0", path = "../query" }
