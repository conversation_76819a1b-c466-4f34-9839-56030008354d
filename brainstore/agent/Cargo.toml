[package]
name = "agent"
version = "0.1.0"
edition = "2024"

[dependencies]
aws-config = "1.8.0"
chrono = "0.4.38"
clap = { version = "4.5.18", features = ["derive", "env"] }
colored = "3.0.0"
log = "0.4.22"
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.128"

otel_common = { path = "../otel_common" }
util = { path = "../util" }
tracing = { path = "../tracing" }

# NOTE: These must be kept in sync with brainstore/otel_common/Cargo.toml
tracing-log = "0.2.0"
tracing-opentelemetry = "0.31.0"
opentelemetry-stdout = { version = "0.30.0", features = ["trace"] }
opentelemetry_sdk = { version = "0.30.0", features = ['rt-tokio', 'testing'] }
opentelemetry-semantic-conventions = "0.30.0"
opentelemetry-appender-tracing = "0.30.1"
tracing-subscriber = { version = "0.3.19", features = ['env-filter', 'json'] }
opentelemetry-otlp = { version = "0.30.0", features = ['http-proto', 'trace'] }
json-subscriber = { git = "https://github.com/braintrustdata/json-subscriber.git", rev = "5dae0d8384d13fed4327664a898f84257a0b6823", features = [
    "opentelemetry",
] }
opentelemetry-resource-detectors = "0.9.0"
hostname = "0.4.1"

# Memory profiling
base64 = { version = "0.21", optional = true }
# Symbolized profiles are more expensive to compute and less accurate (no line numbers).
jemalloc_pprof = { version = "0.7", features = [], optional = true }
reqwest = { version = "0.12.7", default-features = false, features = [
    "blocking",
    "json",
    "rustls-tls",
] }

[target.'cfg(target_os = "linux")'.dependencies]
procfs = { version = "0.17.0", optional = true }

[features]
enable_memprof = ["jemalloc_pprof", "procfs", "base64"]
