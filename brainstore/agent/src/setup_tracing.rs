use colored::Colorize;
use opentelemetry_otlp::{WithExportConfig, WithHttpConfig};
use opentelemetry_sdk::logs::{
    BatchConfigBuilder as LogsBatchConfigBuilder, BatchLogProcessor,
    SdkLoggerProvider as LoggerProvider,
};
use opentelemetry_sdk::metrics::{MeterProviderBuilder, SdkMeterProvider};
use opentelemetry_sdk::propagation::TraceContextPropagator;
use opentelemetry_sdk::trace::{
    BatchConfigBuilder, Sampler, SdkTracerProvider, ShouldSample, TracerProviderBuilder,
};
use opentelemetry_sdk::{trace, Resource};
use otel_common::{
    opentelemetry,
    opentelemetry::{global, KeyValue},
};
use std::collections::HashMap;
use std::time::Duration;
use tracing::subscriber::Subscriber;
use tracing_log::{Log<PERSON>race<PERSON>, NormalizeEvent};
use tracing_subscriber::Layer;
use util::url_util::{parse_headers_arg, url_join};

use clap::{Parser, ValueEnum};
use serde::{Deserialize, Serialize};
use util::anyhow::Result;

use crate::cloud::Ec2ResourceDetector;
use crate::container::DockerResourceDetector;
use crate::memprof::{MemprofHttpLabel, MemprofInput};

/// Creates a shared HTTP client with connection pooling and keep-alive configured
/// for optimal performance when sending telemetry data.
fn create_otel_http_client() -> Result<reqwest::blocking::Client> {
    let client = reqwest::blocking::Client::builder()
        .pool_max_idle_per_host(10) // Keep up to 10 idle connections per host
        .pool_idle_timeout(Duration::from_secs(60)) // Keep connections alive for 60 seconds when idle
        .connect_timeout(Duration::from_secs(10)) // 10 second connect timeout
        .timeout(Duration::from_secs(30)) // 30 second total request timeout (good default)
        .tcp_keepalive(Duration::from_secs(60)) // Enable TCP keep-alive with 60 second interval
        // .http2_keep_alive_interval(Duration::from_secs(30)) // HTTP/2 keep-alive every 30 seconds
        // .http2_keep_alive_timeout(Duration::from_secs(10)) // HTTP/2 keep-alive timeout
        // .http2_keep_alive_while_idle(true) // Send keep-alive even when no streams are active
        .build()?;

    Ok(client)
}

pub use opentelemetry_appender_tracing::layer::OpenTelemetryTracingBridge;

#[derive(Clone, Copy, Debug, Eq, PartialEq, Serialize, Deserialize, ValueEnum)]
pub enum TelemetrySignal {
    #[serde(rename = "status")]
    Status,
    #[serde(rename = "metrics")]
    Metrics,
    #[serde(rename = "traces")]
    Traces,
    #[serde(rename = "logs")]
    Logs,
    #[serde(rename = "memprof")]
    Memprof,
}

#[derive(Clone, Debug, Eq, PartialEq, Serialize, Deserialize)]
pub struct TelemetrySignalList(pub Vec<TelemetrySignal>);

impl std::str::FromStr for TelemetrySignalList {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        parse_control_plane_telemetry(s).map(TelemetrySignalList)
    }
}

impl std::ops::Deref for TelemetrySignalList {
    type Target = Vec<TelemetrySignal>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl Default for TelemetrySignalList {
    fn default() -> Self {
        TelemetrySignalList(vec![TelemetrySignal::Status, TelemetrySignal::Metrics])
    }
}

fn parse_telemetry_signal(s: &str) -> Result<TelemetrySignal, String> {
    match s.trim().to_lowercase().as_str() {
        "status" => Ok(TelemetrySignal::Status),
        "metrics" => Ok(TelemetrySignal::Metrics),
        "logs" => Ok(TelemetrySignal::Logs),
        "traces" => Ok(TelemetrySignal::Traces),
        "memprof" => Ok(TelemetrySignal::Memprof),
        _ => Err(format!("invalid telemetry signal: {}", s)),
    }
}

fn parse_control_plane_telemetry(s: &str) -> Result<Vec<TelemetrySignal>, String> {
    let trimmed = s.trim();
    if trimmed.is_empty() || trimmed.to_lowercase() == "none" {
        return Ok(Vec::new());
    }

    let mut signals = Vec::new();
    for part in trimmed.split(',') {
        let part = part.trim();
        if !part.is_empty() && part.to_lowercase() != "none" {
            signals.push(parse_telemetry_signal(part)?);
        }
    }
    Ok(signals)
}

fn parse_telemetry_signals_otlp(s: &str) -> Result<TelemetrySignal, String> {
    let signal = parse_telemetry_signal(s)?;
    match signal {
        TelemetrySignal::Status => Err("status is not allowed for OTLP telemetry".to_string()),
        TelemetrySignal::Memprof => Err("memprof is not allowed for OTLP telemetry".to_string()),
        _ => Ok(signal),
    }
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize, Default)]
pub struct TelemetryArgs {
    #[arg(
        long,
        env = "BRAINSTORE_CONTROL_PLANE_TELEMETRY",
        default_value = "status, metrics"
    )]
    pub control_plane_telemetry: TelemetrySignalList,

    #[arg(
        long,
        value_delimiter = ',',
        env = "BRAINSTORE_OTLP_TELEMETRY",
        default_value = "metrics, logs, traces",
        value_parser =parse_telemetry_signals_otlp
    )]
    pub otlp_telemetry: Vec<TelemetrySignal>,

    #[arg(
        long,
        help = "OTEL OTLP HTTP endpoint. If set, OTel data will be sent here in addition to the control plane.",
        env = "BRAINSTORE_OTLP_HTTP_ENDPOINT"
    )]
    pub otlp_http_endpoint: Option<String>,

    #[arg(
        long,
        help = "Custom headers to send with OTLP requests (format: key1=value1,key2=value2).",
        env = "BRAINSTORE_OTLP_HTTP_HEADERS"
    )]
    pub otlp_http_headers: Option<String>,

    #[arg(
        long,
        default_value_t = default_metrics_interval_seconds(),
        env = "BRAINSTORE_METRICS_INTERVAL_SECONDS"
    )]
    pub metrics_interval_seconds: u64,

    #[arg(
        long("memprof-dir"),
        help = "Directory to dump memory profiles to. This is for debugging purposes only.",
        env = "BRAINSTORE_MEMPROF_DIR"
    )]
    pub memprof_dir: Option<std::path::PathBuf>,

    #[arg(
        long,
        help = "Collect memory profiles every this many seconds",
        default_value_t = default_metrics_interval_seconds(),
        env = "BRAINSTORE_MEMPROF_INTERVAL_SECONDS"
    )]
    pub memprof_interval_seconds: u64,

    #[arg(
        long,
        help = "API URL for Continuous Profiling. Set this to send memory profiles directly to the memory profiling service.",
        env = "BRAINSTORE_PROFILING_API_URL"
    )]
    pub continuous_profiling_api_url: Option<String>,

    #[arg(
        long,
        help = "API key for Continuous Profiling. Set this to send memory profiles directly to the memory profiling service.",
        env = "BRAINSTORE_PROFILING_API_KEY"
    )]
    pub continuous_profiling_api_key: Option<String>,
}

fn default_metrics_interval_seconds() -> u64 {
    30
}

impl TelemetryArgs {
    pub fn make_memprof_input(&self, tracing_args: SetupOtelProviderArgs) -> MemprofInput {
        let SetupOtelProviderArgs {
            app_url,
            license_key,
            git_commit,
            additional_attributes,
        } = tracing_args;

        let resource =
            create_otel_resource("brainstore", git_commit, additional_attributes.clone());

        MemprofInput {
            memprof_dir: self.memprof_dir.clone(),
            memprof_interval_seconds: self.memprof_interval_seconds,
            api_url: match (self.continuous_profiling_api_url.as_ref(), app_url) {
                (Some(u), _) => Some(u.clone()),
                (None, Some(app_url)) => Some(url_join(app_url, "/api/pulse/prof/mem")),
                (None, None) => None,
            },
            api_key: match (self.continuous_profiling_api_key.as_ref(), license_key) {
                (Some(u), _) => Some(u.clone()),
                (None, Some(license_key)) => Some(license_key.to_string()),
                (None, None) => None,
            },
            resource_labels: resource
                .into_iter()
                .map(|(k, v)| MemprofHttpLabel {
                    name: k.as_str().to_string(),
                    value: v.as_str().to_string(),
                })
                .collect(),
        }
    }

    pub fn control_plane_status_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Status)
    }

    pub fn traces_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Traces)
            || self.otlp_telemetry.contains(&TelemetrySignal::Traces)
    }

    pub fn metrics_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Metrics)
            || self.otlp_telemetry.contains(&TelemetrySignal::Metrics)
    }

    pub fn logs_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Logs)
            || self.otlp_telemetry.contains(&TelemetrySignal::Logs)
    }

    pub fn control_plane_traces_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Traces)
    }

    pub fn control_plane_metrics_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Metrics)
    }

    pub fn control_plane_logs_enabled(&self) -> bool {
        self.control_plane_telemetry
            .contains(&TelemetrySignal::Logs)
    }

    pub fn otlp_traces_enabled(&self) -> bool {
        self.otlp_telemetry.contains(&TelemetrySignal::Traces)
    }

    pub fn otlp_metrics_enabled(&self) -> bool {
        self.otlp_telemetry.contains(&TelemetrySignal::Metrics)
    }

    pub fn otlp_logs_enabled(&self) -> bool {
        self.otlp_telemetry.contains(&TelemetrySignal::Logs)
    }

    pub fn disable_controlplane_telemetry(&self) -> bool {
        self.control_plane_telemetry.is_empty()
    }

    pub fn memory_profiling_enabled(&self) -> bool {
        self.memprof_dir.is_some()
            || self
                .control_plane_telemetry
                .contains(&TelemetrySignal::Memprof)
    }

    // Backward compatibility accessor
    pub fn brainstore_otlp_http_endpoint(&self) -> &Option<String> {
        &self.otlp_http_endpoint
    }
}

pub fn make_env_filter(verbose: u8) -> tracing_subscriber::filter::EnvFilter {
    tracing_subscriber::filter::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
        if verbose >= 2 {
            "debug,hyper=warn,hyper_util=warn,tower=warn,h2=warn,opentelemetry=warn"
                .parse()
                .unwrap()
        } else if verbose == 1 {
            "info".parse().unwrap()
        } else {
            "warn".parse().unwrap()
        }
    })
}

pub fn setup_log_tracer(verbose: u8) -> Result<()> {
    Ok(LogTracer::init_with_filter(if verbose >= 2 {
        log::LevelFilter::Debug
    } else if verbose == 1 {
        log::LevelFilter::Info
    } else {
        log::LevelFilter::Warn
    })?)
}

fn noop_tracer_provider() -> SdkTracerProvider {
    SdkTracerProvider::builder()
        .with_simple_exporter(opentelemetry_sdk::testing::trace::NoopSpanExporter::new())
        .build()
}

fn noop_meter_provider() -> SdkMeterProvider {
    SdkMeterProvider::builder().build()
}

fn noop_logger_provider() -> LoggerProvider {
    LoggerProvider::builder().build()
}

pub struct SetupOtelProviderArgs<'a> {
    pub app_url: Option<&'a str>,
    pub license_key: Option<&'a str>,
    pub git_commit: Option<&'a str>,
    pub additional_attributes: Vec<KeyValue>,
}

pub fn setup_otel_provider(
    telemetry_args: &TelemetryArgs,
    args: SetupOtelProviderArgs,
) -> Result<(SdkTracerProvider, SdkMeterProvider, LoggerProvider)> {
    let SetupOtelProviderArgs {
        app_url,
        license_key,
        git_commit,
        additional_attributes,
    } = args;

    // This bootstraps the opentelemetry propagator.
    global::set_text_map_propagator(TraceContextPropagator::new());

    let (control_plane_url, control_plane_headers) = match (
        telemetry_args.disable_controlplane_telemetry(),
        app_url,
        license_key,
    ) {
        (false, Some(app_url), Some(license_key)) => (
            Some(url_join(app_url, "/api/pulse/otel")),
            HashMap::from_iter(vec![(
                "Authorization".to_string(),
                format!("Bearer {}", license_key),
            )]),
        ),
        _ => (None, HashMap::new()),
    };

    let custom_endpoint_url = telemetry_args
        .otlp_http_endpoint
        .as_ref()
        .or(telemetry_args.brainstore_otlp_http_endpoint().as_ref());

    if let (None, None) = (control_plane_url.as_ref(), custom_endpoint_url.as_ref()) {
        return Ok((
            noop_tracer_provider(),
            noop_meter_provider(),
            noop_logger_provider(),
        ));
    }

    // Create a shared HTTP client for all OTEL exports to enable connection pooling
    let shared_http_client = create_otel_http_client()?;

    let custom_endpoint_headers = match telemetry_args.otlp_http_headers.as_ref() {
        Some(headers) => parse_headers_arg(headers)?,
        None => HashMap::new(),
    };

    let tracing_provider = if telemetry_args.traces_enabled() {
        let mut tracing_provider_builder = SdkTracerProvider::builder();

        if let Some(base_url) = control_plane_url.as_ref() {
            if telemetry_args.control_plane_traces_enabled() {
                tracing_provider_builder = make_trace_exporter(
                    tracing_provider_builder,
                    base_url.as_ref(),
                    &control_plane_headers,
                    &shared_http_client,
                )?;
            }
        }

        if let Some(base_url) = custom_endpoint_url.as_ref() {
            if telemetry_args.otlp_traces_enabled() {
                tracing_provider_builder = make_trace_exporter(
                    tracing_provider_builder,
                    base_url,
                    &custom_endpoint_headers,
                    &shared_http_client,
                )?;
            }
        }

        let tracing_provider = tracing_provider_builder
            .with_sampler(Sampler::ParentBased(Box::new(DropHyperSpans())))
            .with_resource(create_otel_resource(
                "brainstore",
                git_commit,
                additional_attributes.clone(),
            ))
            .build();
        global::set_tracer_provider(tracing_provider.clone());
        Some(tracing_provider)
    } else {
        None
    };

    let metrics_provider = if telemetry_args.metrics_enabled() {
        let mut builder = SdkMeterProvider::builder().with_resource(create_otel_resource(
            "brainstore",
            git_commit,
            additional_attributes.clone(),
        ));
        if let Some(base_url) = control_plane_url.as_ref() {
            if telemetry_args.control_plane_metrics_enabled() {
                builder = make_metrics_exporter(
                    builder,
                    base_url.as_ref(),
                    &control_plane_headers,
                    telemetry_args.metrics_interval_seconds,
                    &shared_http_client,
                )?;
            }
        }
        if let Some(base_url) = custom_endpoint_url.as_ref() {
            if telemetry_args.otlp_metrics_enabled() {
                builder = make_metrics_exporter(
                    builder,
                    base_url,
                    &custom_endpoint_headers,
                    telemetry_args.metrics_interval_seconds,
                    &shared_http_client,
                )?;
            }
        }

        let metrics_provider = builder.build();
        global::set_meter_provider(metrics_provider.clone());
        Some(metrics_provider)
    } else {
        None
    };

    let logs_provider = if telemetry_args.logs_enabled() {
        let mut logger_provider_builder = LoggerProvider::builder().with_resource(
            create_otel_resource("brainstore", git_commit, additional_attributes),
        );

        let mut has_exporter = false;

        // Add control plane logs exporter if enabled
        if let Some(base_url) = control_plane_url.as_ref() {
            if telemetry_args.control_plane_logs_enabled() {
                logger_provider_builder = make_logs_exporter(
                    logger_provider_builder,
                    base_url.as_ref(),
                    &control_plane_headers,
                    &shared_http_client,
                )?;
                has_exporter = true;
            }
        }

        // Add OTLP logs exporter if enabled
        if let Some(base_url) = custom_endpoint_url.as_ref() {
            if telemetry_args.otlp_logs_enabled() {
                logger_provider_builder = make_logs_exporter(
                    logger_provider_builder,
                    base_url,
                    &custom_endpoint_headers,
                    &shared_http_client,
                )?;
                has_exporter = true;
            }
        }

        if has_exporter {
            Some(logger_provider_builder.build())
        } else {
            None
        }
    } else {
        None
    };

    Ok((
        tracing_provider.unwrap_or_else(noop_tracer_provider),
        metrics_provider.unwrap_or_else(noop_meter_provider),
        logs_provider.unwrap_or_else(noop_logger_provider),
    ))
}

fn make_trace_exporter(
    builder: TracerProviderBuilder,
    base_url: &str,
    custom_headers: &HashMap<String, String>,
    http_client: &reqwest::blocking::Client,
) -> Result<TracerProviderBuilder> {
    let exporter = opentelemetry_otlp::SpanExporter::builder()
        .with_http() // Change to HTTP instead of gRPC
        .with_http_client(http_client.clone())
        .with_endpoint(url_join(base_url, "/v1/traces"))
        .with_headers(custom_headers.clone())
        .build()?;

    let batch = trace::BatchSpanProcessor::builder(exporter)
        .with_batch_config(
            BatchConfigBuilder::default()
                .with_max_queue_size(4096)
                .with_scheduled_delay(Duration::from_millis(5000)) // This is the default value.
                .build(),
        )
        .build();

    Ok(builder.with_span_processor(batch))
}

fn make_logs_exporter(
    builder: opentelemetry_sdk::logs::LoggerProviderBuilder,
    base_url: &str,
    custom_headers: &HashMap<String, String>,
    http_client: &reqwest::blocking::Client,
) -> Result<opentelemetry_sdk::logs::LoggerProviderBuilder> {
    let exporter = opentelemetry_otlp::LogExporter::builder()
        .with_http()
        .with_http_client(http_client.clone())
        .with_endpoint(url_join(base_url, "/v1/logs"))
        .with_headers(custom_headers.clone())
        .build()?;

    let processor = BatchLogProcessor::builder(exporter)
        .with_batch_config(
            LogsBatchConfigBuilder::default()
                .with_max_queue_size(2048) // Drop logs when buffer is full
                .with_max_export_batch_size(512) // Limit batch size
                .with_scheduled_delay(Duration::from_secs(5)) // Export every 5 seconds
                .build(),
        )
        .build();

    Ok(builder.with_log_processor(processor))
}

fn make_metrics_exporter(
    builder: MeterProviderBuilder,
    base_url: &str,
    custom_headers: &HashMap<String, String>,
    interval_seconds: u64,
    http_client: &reqwest::blocking::Client,
) -> Result<MeterProviderBuilder> {
    let metrics_exporter = opentelemetry_otlp::MetricExporter::builder()
        .with_http() // Use HTTP protocol
        .with_http_client(http_client.clone())
        .with_endpoint(url_join(base_url, "/v1/metrics")) // Use same endpoint as spans
        .with_headers(custom_headers.clone())
        .with_temporality(opentelemetry_sdk::metrics::Temporality::Delta) // Required for Datadog
        .build()?;

    let metrics_reader = opentelemetry_sdk::metrics::PeriodicReader::builder(metrics_exporter)
        .with_interval(Duration::from_secs(interval_seconds))
        .build();

    Ok(builder.with_reader(metrics_reader))
}

#[derive(Debug, Clone)]
pub struct DropHyperSpans();

impl ShouldSample for DropHyperSpans {
    fn should_sample(
        &self,
        _parent_context: Option<&opentelemetry::Context>,
        _trace_id: opentelemetry::trace::TraceId,
        _name: &str,
        _span_kind: &opentelemetry::trace::SpanKind,
        attributes: &[KeyValue],
        _links: &[opentelemetry::trace::Link],
    ) -> opentelemetry::trace::SamplingResult {
        for attr in attributes {
            if attr.key.as_str() == "code.namespace"
                && (attr.value.as_str().starts_with("hyper_util")
                    || attr.value.as_str().starts_with("hyper")
                    || attr.value.as_str().starts_with("tower")
                    || attr.value.as_str().starts_with("h2")
                    || attr.value.as_str().starts_with("opentelemetry"))
            {
                return opentelemetry::trace::SamplingResult {
                    decision: opentelemetry::trace::SamplingDecision::Drop,
                    attributes: vec![],
                    trace_state: opentelemetry::trace::TraceState::default(),
                };
            }
        }
        opentelemetry::trace::SamplingResult {
            decision: opentelemetry::trace::SamplingDecision::RecordAndSample,
            attributes: vec![],
            trace_state: opentelemetry::trace::TraceState::default(),
        }
    }
}

#[derive(Debug, Default)]
pub struct CustomStderrFormatter {
    pub include_parents: bool,
    pub include_thread_ids: bool,
}

// This implementation is modified from the Compact format implementation in
// tracing-subscriber-0.3.19/src/fmt/format/mod.rs.
impl<S, N> tracing_subscriber::fmt::FormatEvent<S, N> for CustomStderrFormatter
where
    S: tracing::subscriber::Subscriber + for<'a> tracing_subscriber::registry::LookupSpan<'a>,
    N: for<'a> tracing_subscriber::fmt::FormatFields<'a> + 'static,
{
    fn format_event(
        &self,
        ctx: &tracing_subscriber::fmt::FmtContext<'_, S, N>,
        mut writer: tracing_subscriber::fmt::format::Writer<'_>,
        event: &tracing::event::Event<'_>,
    ) -> std::fmt::Result {
        let normalized_meta = event.normalized_metadata();
        let meta = normalized_meta.as_ref().unwrap_or_else(|| event.metadata());

        // Write the thread ID
        if self.include_thread_ids {
            let thread_id = std::thread::current().id();
            // We use a fairly wide padding of 15 characters to accommodate large thread ID
            // numbers. This can be visually jarring when the thread ID is small.
            write!(writer, "{:>15} ", format!("{:?}", thread_id))?;
        }

        // Write the timestamp
        write!(
            writer,
            "{} ",
            chrono::Utc::now().to_rfc3339_opts(chrono::SecondsFormat::Millis, true)
        )?;

        // Write the log level
        let log_level = match *meta.level() {
            tracing::Level::ERROR => "ERROR".red(),
            tracing::Level::WARN => "WARN".yellow(),
            tracing::Level::INFO => "INFO".green(),
            tracing::Level::DEBUG => "DEBUG".blue(),
            tracing::Level::TRACE => "TRACE".purple(),
        };
        write!(writer, "{:>5} ", log_level)?;

        // Write the log message
        ctx.field_format().format_fields(writer.by_ref(), event)?;

        // Write the span data
        if self.include_parents {
            for span in ctx.event_scope().into_iter().flatten() {
                write!(writer, " [{}", span.metadata().name())?;
                let exts = span.extensions();
                if let Some(fields) = exts.get::<tracing_subscriber::fmt::FormattedFields<N>>() {
                    if !fields.is_empty() {
                        write!(writer, " {}", &fields.fields)?;
                    }
                }
                write!(writer, "]")?;
            }
        }

        writeln!(writer)
    }
}

pub fn make_stderr_layer<S>(
    pretty_logging: bool,
    verbose: u8,
    suppress_verbose_info: bool,
) -> Box<dyn Layer<S> + Send + Sync + 'static>
where
    S: Subscriber + for<'a> tracing_subscriber::registry::LookupSpan<'a>,
{
    if pretty_logging {
        tracing_subscriber::fmt::layer()
            .with_writer(std::io::stderr)
            .event_format(CustomStderrFormatter {
                include_parents: verbose >= 2 || (verbose == 1 && !suppress_verbose_info),
                include_thread_ids: verbose >= 2,
            })
            .boxed()
    } else {
        json_subscriber::layer()
            .with_current_span(false)
            .with_span_list(false)
            .with_opentelemetry_ids(true)
            .with_writer(std::io::stderr)
            .with_timer(tracing_subscriber::fmt::time::SystemTime::default())
            .with_file(false)
            .with_line_number(false)
            .boxed()
    }
}

pub fn create_otel_resource(
    service_name: &str,
    git_commit: Option<&str>,
    additional_attributes: Vec<KeyValue>,
) -> Resource {
    // Start with an empty resource
    let mut resource = Resource::builder_empty()
        .with_attribute(KeyValue::new("service.name", service_name.to_string()));

    if let Some(git_commit) = git_commit {
        resource = resource.with_attribute(KeyValue::new("service.commit", git_commit.to_string()));
    }

    // Add any additional attributes
    for attr in additional_attributes {
        resource = resource.with_attribute(attr);
    }

    resource = resource.with_detectors(&[
        Box::new(Ec2ResourceDetector),
        Box::new(DockerResourceDetector),
        // These detectors don't seem to add much useful information, and they'll explode the
        // cardinality of the metrics we capture.
        //     Box::new(opentelemetry_sdk::resource::EnvResourceDetector::new()),
        //     Box::new(opentelemetry_sdk::resource::TelemetryResourceDetector),
        //     Box::new(opentelemetry_resource_detectors::OsResourceDetector),
        //     Box::new(opentelemetry_resource_detectors::ProcessResourceDetector),
        //     Box::new(opentelemetry_resource_detectors::K8sResourceDetector),
    ]);

    // The built-in OTEL detector uses host.id rather than host.name, which Datadog resolves to the same
    // value. host.id is way less usable though.
    let host_name = hostname::get()
        .ok()
        .and_then(|h| h.to_str().and_then(|h| Some(h.to_owned())))
        .map(|h| KeyValue::new("host.name", h.to_owned()));
    if let Some(host_name) = host_name {
        resource = resource.with_attribute(host_name);
    }
    resource = resource.with_attribute(KeyValue::new("host.arch", std::env::consts::ARCH));

    resource.build()
}
