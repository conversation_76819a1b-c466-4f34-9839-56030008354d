use std::path::{Path, PathBuf};
use util::{anyhow::Result, tokio};

use base64::{Engine as Base64Engine, engine::general_purpose::STANDARD_NO_PAD as BASE64_STANDARD};

use crate::memprof::{
    MemprofFlusher, MemprofHttpLabel, MemprofHttpLabelSet, MemprofHttpRawSample, MemprofInput,
    MemprofProfileSeries, MemprofWriteRawRequest,
};
use reqwest;

#[cfg(target_os = "linux")]
fn get_rss() -> Result<u64> {
    let proc = procfs::process::Process::myself()?;
    let statm = proc.statm()?;
    let page_size = procfs::page_size();
    Ok(statm.resident * page_size)
}

#[cfg(not(target_os = "linux"))]
fn get_rss() -> Result<u64> {
    util::anyhow::bail!("RSS not supported on this platform")
}

pub async fn profile_heap() -> Result<Vec<u8>> {
    use util::anyhow::anyhow;

    let mut prof_ctl = jemalloc_pprof::PROF_CTL
        .as_ref()
        .ok_or(anyhow!("No profiler available"))?
        .lock()
        .await;
    if !prof_ctl.activated() {
        return Err(anyhow!("heap profiling not activated"));
    }
    prof_ctl.dump_pprof()
}

pub fn dump_heap_to_file(pprof: Vec<u8>, dirname: &Path) -> Result<PathBuf> {
    let rss_bytes = get_rss()?;
    let rss_mb = rss_bytes / 1024 / 1024;
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)?
        .as_secs();

    let filename = format!("heap_ts_{:010}_rss_mb_{:07}.pb.gz", timestamp, rss_mb);
    let fullpath = dirname.join(&filename);
    if !dirname.exists() {
        std::fs::create_dir_all(dirname)?;
    }
    std::fs::write(&fullpath, &pprof)?;
    Ok(fullpath)
}

pub fn run_memprof(args: MemprofInput, mut flusher: MemprofFlusher) {
    let memprof_dir = match args.memprof_dir {
        Some(dir) => Some(dir),
        None => {
            if args.api_url.is_none() {
                return;
            }
            None
        }
    };

    let interval = std::time::Duration::from_secs(args.memprof_interval_seconds);

    std::thread::spawn(move || {
        let runtime = tokio::runtime::Builder::new_current_thread()
            .enable_all()
            .build()
            .unwrap();
        runtime.block_on(async move {
            let http_client = match args.api_url {
                Some(api_url) => {
                    let client = reqwest::Client::new();
                    Some((client, api_url))
                }
                None => None,
            };

            loop {
                let should_send_flushed = tokio::select! {
                    _ = tokio::time::sleep(interval) => false,
                    _ = flusher.should_flush.recv() => true,
                };

                let heap_profile = match profile_heap().await {
                    Ok(heap_profile) => heap_profile,
                    Err(e) => {
                        log::warn!("Error dumping heap: {:?}", e);
                        continue;
                    }
                };
                match (http_client.as_ref(), memprof_dir.as_ref()) {
                    (Some((client, api_url)), _) => {
                        // Base64 encode the profile data
                        let encoded_profile = BASE64_STANDARD.encode(&heap_profile);

                        let request_body = MemprofWriteRawRequest {
                            series: vec![MemprofProfileSeries {
                                labels: MemprofHttpLabelSet {
                                    labels: [
                                        MemprofHttpLabel {
                                            name: "__name__".to_string(),
                                            value: "memory".to_string(),
                                        },
                                        MemprofHttpLabel {
                                            name: "pid".to_string(),
                                            value: format!("{}", std::process::id()),
                                        },
                                    ]
                                    .into_iter()
                                    .chain(args.resource_labels.iter().cloned())
                                    .collect(),
                                },
                                samples: vec![MemprofHttpRawSample {
                                    raw_profile: encoded_profile,
                                }],
                            }],
                        };

                        let mut request = client
                            .post(
                                api_url, // "https://api.polarsignals.com/api/parca/profilestore/profiles/writeraw",
                            )
                            .header("Content-Type", "application/json")
                            .json(&request_body);

                        if let Some(api_key) = args.api_key.as_ref() {
                            request =
                                request.header("Authorization", format!("Bearer {}", api_key));
                        }

                        let response = request.send().await;

                        match response {
                            Ok(resp) => {
                                if resp.status().is_success() {
                                    log::debug!(
                                        "Heap profile published to Continuous Profiling via HTTP"
                                    );
                                } else {
                                    log::warn!(
                                        "Error publishing heap profile: HTTP {}",
                                        resp.status()
                                    );
                                }
                            }
                            Err(e) => {
                                log::warn!("Error publishing heap profile: {:?}", e);
                            }
                        }
                    }
                    (None, Some(memprof_dir)) => {
                        if let Err(e) = dump_heap_to_file(heap_profile, memprof_dir) {
                            log::warn!("Error dumping heap to file: {:?}", e);
                        } else {
                            log::info!("Heap profile dumped to file");
                        }
                    }
                    (None, None) => {
                        log::warn!(
                            "No API key or memprof directory provided, skipping heap profile upload"
                        );
                    }
                }

                if should_send_flushed {
                    if let Err(e) = flusher.has_flushed.send(()).await {
                        log::warn!("Error sending memory profiler flush signal: {:?}", e);
                    }
                }
            }
        });
    });
}
