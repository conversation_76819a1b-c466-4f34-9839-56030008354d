[package]
name = "otel_common"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
sysinfo = "0.35"
tokio = { version = "1.40.0", features = ["rt", "time", "sync"] }
futures = "0.3"
log = "0.4"

util = { path = "../util" }

# NOTE: These must be kept in sync with brainstore/agent/Cargo.toml
opentelemetry = { version = "0.30.0", features = ["trace"] }
