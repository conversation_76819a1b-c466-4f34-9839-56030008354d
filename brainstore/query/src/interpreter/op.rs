use std::sync::Arc;

use async_trait::async_trait;
use tokio::sync::mpsc;
use tracing::Instrument;
use util::futures::stream::BoxStream;
use util::futures::StreamExt;
use util::{
    tracer::{trace_if_async, TracedNode},
    Value,
};

use crate::interpreter::context::CancellationStatus;
use crate::interpreter::error::InterpreterError;

use super::error::Result;
use super::InterpreterContext;

pub type StreamValue = Result<Value>;
pub type RowStream<'a> = BoxStream<'a, StreamValue>;

#[async_trait]
pub trait Operator: Send + 'static + Sized {
    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()>;

    fn name(&self) -> &'static str;

    fn interpret(self, ctx: Arc<InterpreterContext>) -> Result<RowStream<'static>> {
        let tracer = ctx.root_tracer.clone();
        self.interpret_node(ctx, Some(tracer))
    }

    fn interpret_node(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
    ) -> Result<RowStream<'static>> {
        let (tx, mut rx) = mpsc::channel(ctx.opts.batch_size());
        // Convert the channels to a `Stream`.
        let sub_ctx = ctx.clone();
        let producer = ctx.handle.spawn(
            async move {
                trace_if_async(log::Level::Info, &tracer, self.name(), |child| async move {
                    sub_ctx.start(); // This is a no-op if the query is already running.

                    match self.execute(sub_ctx, child, tx.clone()).await {
                        Ok(_) => (),
                        Err(e) => tx.send(Err(e)).await.unwrap(),
                    }
                })
                .await
            }
            .instrument(tracing::Span::current()),
        );

        Ok(async_stream::stream! {
              while let Some(item) = rx.recv().await {
                let cancelled = ctx.is_cancelled();
                if cancelled != CancellationStatus::Running {
                    rx.close();
                    yield Err(InterpreterError::Cancelled {
                        reason: cancelled,
                    });
                    break;
                } else {
                  yield item;
                }
              }
              // Any errors in the producer are unexpected errors and so we panic.
              producer.await.unwrap();
        }
        .boxed())
    }
}

pub enum ShouldAbort {
    Continue,
    Abort,
}

pub async fn send_row(tx: &mpsc::Sender<StreamValue>, row: Value) -> ShouldAbort {
    match tx.send(Ok(row)).await {
        Ok(_) => ShouldAbort::Continue,
        Err(_) => ShouldAbort::Abort,
    }
}
