use std::{borrow::Cow, sync::Arc};

use btql::interpreter::{
    context::ExprContext,
    expr::{interpret_expr, interpret_expr_vec},
};
use tokio::sync::mpsc;
use tokio_stream::StreamExt;
use util::{tracer::TracedNode, Value};

use crate::planner::ast::FilterQuery;
use btql::typesystem::CastInto;

use super::{
    context::InterpreterContext,
    error::Result,
    op::{send_row, Operator, ShouldAbort, StreamValue},
};

pub struct LocalOperator;

#[async_trait::async_trait]
impl Operator for FilterQuery {
    fn name(&self) -> &'static str {
        "Filter"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let FilterQuery { from, filter } = self;

        let from_stream = from.interpret_node(ctx.clone(), tracer.clone())?;

        let mut batched_stream =
            util::futures::StreamExt::ready_chunks(from_stream, ctx.opts.batch_size());
        while let Some(rows) = batched_stream.next().await {
            let rows = rows
                .into_iter()
                .map(|row| Ok(Cow::Owned(row?)))
                .collect::<Result<Vec<_>>>()?;

            let filtered_rows = perform_filter(&ctx.expr_ctx, rows, &filter)?;

            for row in filtered_rows {
                match send_row(&tx, row.into_owned()).await {
                    ShouldAbort::Continue => {}
                    ShouldAbort::Abort => {
                        log::debug!("Aborting filter search since the parent stream was cancelled");
                        break;
                    }
                }
            }
        }

        Ok(())
    }
}

const PARALLEL_BATCH_SIZE: usize = 100;

pub fn interpret_expr_parallel<'v>(
    ctx: &ExprContext,
    expr: &'v btql::binder::ast::Expr,
    rows: &'v [Cow<'v, util::Value>],
) -> Result<Vec<Cow<'v, util::Value>>> {
    if rows.len() < PARALLEL_BATCH_SIZE {
        return Ok(interpret_expr(ctx, expr, rows)?);
    }

    let batches: Vec<&[Cow<'v, util::Value>]> =
        rows.chunks(PARALLEL_BATCH_SIZE).collect::<Vec<_>>();
    let values = ctx.executor.map(
        |batch| {
            interpret_expr(ctx, expr, batch)
                .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))
        },
        batches.into_iter(),
    )?;
    Ok(values.into_iter().flat_map(|v| v).collect::<Vec<_>>())
}

pub fn interpret_expr_value<'v>(
    ctx: &ExprContext,
    expr: &'v btql::binder::ast::Expr,
    row: &'v util::Value,
) -> Result<Cow<'v, util::Value>> {
    let rows = vec![Cow::Borrowed(row)];
    let values = interpret_expr_vec(ctx, expr, rows)?;
    let first_row = values
        .into_iter()
        .next()
        .unwrap_or(Cow::Owned(util::Value::Null));
    Ok(Cow::Owned(first_row.into_owned()))
}

pub fn perform_filter<'v>(
    expr_ctx: &ExprContext,
    rows: Vec<Cow<'v, Value>>,
    filter: &Option<Box<btql::binder::ast::Expr>>,
) -> Result<Vec<Cow<'v, Value>>> {
    Ok(match &filter {
        Some(row_filter) => {
            let filtered_rows = interpret_expr_parallel(&expr_ctx, &row_filter, &rows)?;

            // These casts can technically fail, and if they do, we should just return false.
            let filtered_bools: Vec<bool> = filtered_rows
                .into_iter()
                .map(|row| row.as_ref().cast().unwrap_or(false))
                .collect::<Vec<_>>();

            rows.into_iter()
                .zip(filtered_bools.into_iter())
                .filter(|(_row, matched)| *matched)
                .map(|(row, _)| row)
                .collect()
        }
        None => rows,
    })
}
