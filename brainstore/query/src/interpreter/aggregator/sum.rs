use core::f64;
use std::borrow::Cow;
use std::marker::PhantomData;

use util::{ptree::MakePTree, xact::PaginationKey, Value};

use crate::interpreter::{
    columnar::{
        value::{Bytes<PERSON>rdinal, ColumnarExpr<PERSON><PERSON>xt, Null, StringOrdinal},
        PtrOffset,
    },
    error::Result,
};
use btql::typesystem::cast::value_is_null;

use super::{
    agg_enums::DynamicValue,
    aggregate::{Aggregator, AggregatorBase, PrimitiveAggregateValue},
    ValueAggregator,
};

pub trait AsF64 {
    fn as_f64(&self) -> f64;
}

impl AsF64 for u64 {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        *self as f64
    }
}

impl AsF64 for i64 {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        *self as f64
    }
}

impl AsF64 for f64 {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        *self
    }
}

impl AsF64 for tantivy::DateTime {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        (*self).into_timestamp_nanos() as f64
    }
}

impl AsF64 for PaginationKey {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        self.0 as f64
    }
}

impl AsF64 for bool {
    #[inline(always)]
    fn as_f64(&self) -> f64 {
        *self as u8 as f64
    }
}

macro_rules! nan_f64_impl {
    ($t:ty) => {
        impl AsF64 for $t {
            #[inline(always)]
            fn as_f64(&self) -> f64 {
                f64::NAN
            }
        }
    };
}

nan_f64_impl!(Null);
nan_f64_impl!(PtrOffset);
nan_f64_impl!(StringOrdinal);
nan_f64_impl!(BytesOrdinal);

pub trait AddValue: Aggregator {
    fn add_value(&mut self, value: Self::Item<'_>);
}

pub trait SummableInteger: Copy + PrimitiveAggregateValue {
    type SumTarget: std::ops::AddAssign
        + std::ops::Add<Output = Self::SumTarget>
        + OverflowingAdd
        + Copy
        + Into<util::Value>
        + Send
        + Sync
        + std::fmt::Debug
        + AsF64;

    fn to_sum_target(self) -> Self::SumTarget;
}

impl SummableInteger for u64 {
    type SumTarget = u64;

    #[inline(always)]
    fn to_sum_target(self) -> Self::SumTarget {
        self
    }
}

impl SummableInteger for PaginationKey {
    type SumTarget = u64;

    #[inline(always)]
    fn to_sum_target(self) -> Self::SumTarget {
        self.0
    }
}

impl SummableInteger for i64 {
    type SumTarget = i64;

    #[inline(always)]
    fn to_sum_target(self) -> Self::SumTarget {
        self
    }
}

impl SummableInteger for tantivy::DateTime {
    type SumTarget = i64;

    #[inline(always)]
    fn to_sum_target(self) -> Self::SumTarget {
        self.into_timestamp_nanos()
    }
}

impl SummableInteger for bool {
    type SumTarget = u64;

    #[inline(always)]
    fn to_sum_target(self) -> Self::SumTarget {
        self as u64
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct IntSum<I: SummableInteger> {
    sum: I::SumTarget,
    has_value: bool,
}

impl<I: SummableInteger> AddValue for IntSum<I> {
    fn add_value(&mut self, value: Option<I>) {
        if let Some(val) = value {
            self.sum = self.sum.overflowing_add(val.to_sum_target());
            self.has_value = true;
        }
    }
}

impl<I: SummableInteger> Aggregator for IntSum<I> {
    type Item<'a> = Option<I>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for val in input.iter().flatten() {
            self.sum = self.sum.overflowing_add(val.to_sum_target());
            self.has_value = true;
        }

        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        // Convert to i64 for the dynamic sum
        let sum_as_i64 = match std::any::type_name::<I::SumTarget>() {
            name if name.contains("u64") => {
                // For u64, we need to handle potential overflow
                let val = unsafe { std::mem::transmute_copy::<I::SumTarget, u64>(&self.sum) };
                val as i64
            }
            _ => {
                // For i64 and bool (which uses u64), direct cast
                unsafe { std::mem::transmute_copy::<I::SumTarget, i64>(&self.sum) }
            }
        };

        ValueAggregator::Sum(DynamicSum::Int(IntSum {
            sum: sum_as_i64,
            has_value: self.has_value,
        }))
    }
}

impl<I: SummableInteger> AggregatorBase for IntSum<I> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.sum += other.sum;
        self.has_value = self.has_value || other.has_value;
        Ok(())
    }

    fn collect(&self) -> Result<util::Value> {
        if !self.has_value {
            return Ok(util::Value::Null);
        }
        Ok(self.sum.into())
    }
}

impl<I: SummableInteger> AsF64 for IntSum<I> {
    fn as_f64(&self) -> f64 {
        self.sum.as_f64()
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct FloatSum {
    sum: f64,
    compensation: f64,
    has_value: bool,
}

impl AddValue for FloatSum {
    // Unfortunately this has to be duplicated with the logic in aggregate() below, because
    // the borrow checker
    // gets confused.
    #[inline(always)]
    fn add_value(&mut self, value: Option<f64>) {
        let f = match value {
            Some(f) if f.is_finite() => f,
            _ => return,
        };

        (self.sum, self.compensation) = add_kahan(self.sum, self.compensation, f);
        self.has_value = true;
    }
}

impl Aggregator for FloatSum {
    type Item<'a> = Option<f64>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for f in input {
            let f = match f {
                Some(f) if f.is_finite() => f,
                _ => continue,
            };

            (self.sum, self.compensation) = add_kahan(self.sum, self.compensation, *f);
            self.has_value = true;
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Sum(DynamicSum::Float(self))
    }
}

impl AggregatorBase for FloatSum {
    fn combine(&mut self, other: Self) -> Result<()> {
        (self.sum, self.compensation) = {
            let (new_sum, new_comp) = add_kahan(self.sum, self.compensation, other.sum);
            add_kahan(new_sum, new_comp, other.compensation)
        };
        self.has_value = self.has_value || other.has_value;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if !self.has_value {
            return Ok(Value::Null);
        }
        Ok(Value::Number(
            serde_json::Number::from_f64(self.as_f64()).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

pub trait OverflowingAdd {
    fn overflowing_add(self, other: Self) -> Self;
}

macro_rules! impl_overflowing_add {
    ($t:ty) => {
        impl OverflowingAdd for $t {
            #[inline(always)]
            fn overflowing_add(self, other: Self) -> Self {
                self.overflowing_add(other).0
            }
        }
    };
}

impl_overflowing_add!(i64);
impl_overflowing_add!(u64);

impl AsF64 for FloatSum {
    fn as_f64(&self) -> f64 {
        self.sum + self.compensation
    }
}

#[derive(Debug, Clone, MakePTree)]
#[ptree(label_only)]
pub struct NullSum<T>(PhantomData<T>);

impl<I> Default for NullSum<I> {
    fn default() -> Self {
        Self(PhantomData)
    }
}

impl<T: Send + Sync + std::fmt::Debug + Clone + PrimitiveAggregateValue> AddValue for NullSum<T> {
    fn add_value(&mut self, _value: Option<T>) {}
}

impl<T: Send + Sync + std::fmt::Debug + Clone + PrimitiveAggregateValue> Aggregator for NullSum<T> {
    type Item<'a> = Option<T>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, _input: &[Self::Item<'_>]) -> Result<()> {
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        // NullSum always returns null, so we can use an uninitialized DynamicSum
        ValueAggregator::Sum(DynamicSum::Uninitialized)
    }
}

impl<T> AggregatorBase for NullSum<T> {
    fn combine(&mut self, _other: Self) -> Result<()> {
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        Ok(0.into())
    }
}

impl<T> AsF64 for NullSum<T> {
    fn as_f64(&self) -> f64 {
        0.0
    }
}

// Dynamic sum for Value types
#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub enum DynamicSum {
    #[default]
    Uninitialized,
    Int(IntSum<i64>),
    Float(FloatSum),
}

impl Aggregator for DynamicSum {
    type Item<'a> = DynamicValue<'a>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            if value_is_null(value) {
                continue;
            }

            match (&mut *self, value.as_ref()) {
                (_, Value::Null) => continue,
                (_, Value::Bool(b)) => {
                    self.aggregate(ctx, &[Cow::Owned(Value::Number((*b as i64).into()))])?;
                }
                // Initialize based on first non-null value
                (DynamicSum::Uninitialized, Value::Number(n)) => {
                    if n.is_i64() {
                        *self = DynamicSum::Int(IntSum {
                            sum: n.as_i64().unwrap(),
                            has_value: true,
                        });
                    } else if n.is_u64() {
                        *self = DynamicSum::Int(IntSum {
                            sum: n.as_u64().unwrap() as i64,
                            has_value: true,
                        })
                    } else {
                        *self = DynamicSum::Float(FloatSum {
                            sum: n
                                .as_f64()
                                .ok_or_else(|| util::anyhow::anyhow!("Invalid number: {:?}", n))?,
                            compensation: 0.0,
                            has_value: true,
                        });
                    }
                }

                // Add to existing int sum
                (DynamicSum::Int(int_sum), Value::Number(n)) => {
                    if let Some(i) = n.as_i64() {
                        int_sum.aggregate(ctx, &[Some(i)])?;
                    } else if let Some(i) = n.as_u64() {
                        int_sum.aggregate(ctx, &[Some(i as i64)])?;
                    } else if let Some(f) = n.as_f64() {
                        // Promote to float
                        let mut fs = FloatSum {
                            sum: int_sum.sum as f64,
                            compensation: 0.0,
                            has_value: int_sum.has_value,
                        };
                        fs.aggregate(ctx, &[Some(f)])?;
                        *self = DynamicSum::Float(fs);
                    } else {
                        return Err(util::anyhow::anyhow!("Invalid number: {:?}", n).into());
                    }
                }

                // Add to existing float sum
                (DynamicSum::Float(float_sum), Value::Number(n)) => {
                    if let Some(f) = n.as_f64() {
                        float_sum.aggregate(ctx, &[Some(f)])?;
                    } else if let Some(i) = n.as_i64() {
                        float_sum.aggregate(ctx, &[Some(i as f64)])?;
                    } else if let Some(i) = n.as_u64() {
                        float_sum.aggregate(ctx, &[Some(i as f64)])?;
                    } else {
                        return Err(util::anyhow::anyhow!("Invalid number: {:?}", n).into());
                    }
                }

                // Skip non-numeric values
                _ => {
                    return Err(util::anyhow::anyhow!("Invalid number: {:?}", value).into());
                }
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Sum(self)
    }
}

impl AggregatorBase for DynamicSum {
    fn combine(&mut self, other: Self) -> Result<()> {
        match (&mut *self, other) {
            (DynamicSum::Uninitialized, other) => {
                *self = other;
            }
            (_, DynamicSum::Uninitialized) => {
                // Keep self as is
            }
            (DynamicSum::Int(a), DynamicSum::Int(b)) => {
                a.combine(b)?;
            }
            (DynamicSum::Float(a), DynamicSum::Float(b)) => {
                a.combine(b)?;
            }
            (DynamicSum::Int(int_sum), DynamicSum::Float(float_sum)) => {
                let mut promoted = FloatSum {
                    sum: int_sum.sum as f64,
                    compensation: 0.0,
                    has_value: int_sum.has_value,
                };
                promoted.combine(float_sum)?;
                *self = DynamicSum::Float(promoted);
            }
            (DynamicSum::Float(float_sum), DynamicSum::Int(int_sum)) => {
                let promoted = FloatSum {
                    sum: int_sum.sum as f64,
                    compensation: 0.0,
                    has_value: int_sum.has_value,
                };
                float_sum.combine(promoted)?;
            }
        }
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        match self {
            DynamicSum::Uninitialized => Ok(Value::Null),
            DynamicSum::Int(int_sum) => int_sum.collect(),
            DynamicSum::Float(float_sum) => float_sum.collect(),
        }
    }
}

impl AsF64 for DynamicSum {
    fn as_f64(&self) -> f64 {
        match self {
            DynamicSum::Uninitialized => 0.0,
            DynamicSum::Int(int_sum) => int_sum.as_f64(),
            DynamicSum::Float(float_sum) => float_sum.as_f64(),
        }
    }
}

#[inline(always)]
pub fn add_kahan(current_sum: f64, current_compensation: f64, value: f64) -> (f64, f64) {
    let y = value - current_compensation;
    let t = current_sum + y;
    let new_compensation = (t - current_sum) - y;
    (t, new_compensation)
}
