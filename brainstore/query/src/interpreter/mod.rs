use std::sync::Arc;

pub use op::{Operator, RowStream, StreamValue};
use tokio::sync::mpsc;
use util::{tracer::TracedNode, Value};

use crate::planner::ast::PlannedQuery;

pub mod aggregator;
pub mod columnar;
pub mod context;
pub mod error;
pub mod groupby;
pub mod limiter;
pub mod local;
pub mod noop;
pub mod op;
pub mod sanitize;
pub mod tantivy;
pub mod unpivot;
pub use context::InterpreterContext;

pub use tokio_stream::Stream;
pub type StreamBatch = Value;


#[cfg(test)]
mod limiter_test;

#[async_trait::async_trait]
impl Operator for Box<PlannedQuery> {
    fn name(&self) -> &'static str {
        "<op>"
    }

    fn interpret_node(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
    ) -> error::Result<RowStream<'static>> {
        match *self {
            PlannedQuery::Noop(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::TantivySearch(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::TantivyAggregate(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::TantivyExpandTraces(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::TantivySchemaInference(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::Filter(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::GroupBy(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::Project(query) => query.interpret_node(ctx, tracer),
            PlannedQuery::Unpivot(query) => query.interpret_node(ctx, tracer),
        }
    }

    async fn execute(
        self,
        _ctx: Arc<InterpreterContext>,
        _tracer: Option<Arc<TracedNode>>,
        _tx: mpsc::Sender<StreamValue>,
    ) -> error::Result<()> {
        assert!(false);
        Ok(())
    }
}
