#[cfg(test)]
mod date_trunc_tests {
    use btql::interpreter::context::ExprContext;
    use tantivy::DateTime;
    use time::format_description::well_known::Rfc3339;
    use time::OffsetDateTime;

    use crate::interpreter::columnar::{
        expr::{ColumnarExpr, ColumnarLiteral, DateTrunc, TruncInterval, BatchColumns},
        value::{ColumnarExprContext, ColumnarValueLiteralOwned, ColumnarValueType},
        OpaqueBuffer,
    };

    struct TruncateTestCase {
        timestamp: &'static str,
        interval: TruncInterval,
        tz_offset: Option<i16>,
        expected: &'static str,
        description: &'static str,
    }

    const TRUNCATE_TEST_CASES: &[TruncateTestCase] = &[
        // Year truncation
        TruncateTestCase {
            timestamp: "2024-12-31T23:58:00Z",
            interval: TruncInterval::Year,
            tz_offset: Some(-1),
            expected: "2023-12-31T23:59:00Z",
            description: "year truncation with +1 minute staying in current year",
        },
        TruncateTestCase {
            timestamp: "2024-12-31T23:58:00Z",
            interval: TruncInterval::Year,
            tz_offset: Some(-3),
            expected: "2024-12-31T23:57:00Z",
            description: "year truncation with +3 minutes crossing to next year",
        },
        TruncateTestCase {
            timestamp: "2024-01-01T00:02:00Z",
            interval: TruncInterval::Year,
            tz_offset: Some(2),
            expected: "2024-01-01T00:02:00Z",
            description: "year truncation with -2 minutes staying in current year",
        },
        TruncateTestCase {
            timestamp: "2024-01-01T00:02:00Z",
            interval: TruncInterval::Year,
            tz_offset: Some(3),
            expected: "2023-01-01T00:03:00Z",
            description: "year truncation with -3 minutes crossing to previous year",
        },
        // Month boundary cases
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:00Z",
            interval: TruncInterval::Month,
            tz_offset: Some(-1),
            expected: "2024-01-31T23:59:00Z",
            description: "month truncation with +1 minute staying in current month",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:00Z",
            interval: TruncInterval::Month,
            tz_offset: Some(-3),
            expected: "2024-02-29T23:57:00Z",
            description: "month truncation with +3 minutes crossing to previous month",
        },
        // Week truncation
        TruncateTestCase {
            timestamp: "2024-02-19T00:02:00Z", // Monday
            interval: TruncInterval::Week,
            tz_offset: Some(2),
            expected: "2024-02-19T00:02:00Z",
            description: "week truncation on Monday with -2 minutes staying in same week",
        },
        TruncateTestCase {
            timestamp: "2024-02-19T00:02:00Z", // Monday
            interval: TruncInterval::Week,
            tz_offset: Some(3),
            expected: "2024-02-12T00:03:00Z",
            description: "week truncation on Monday with -3 minutes crossing to previous week",
        },
        TruncateTestCase {
            timestamp: "2024-02-25T23:58:00Z", // Sunday
            interval: TruncInterval::Week,
            tz_offset: Some(-1),
            expected: "2024-02-18T23:59:00Z",
            description: "week truncation on Sunday with +1 minute staying in current week",
        },
        TruncateTestCase {
            timestamp: "2024-02-25T23:58:00Z", // Sunday
            interval: TruncInterval::Week,
            tz_offset: Some(-3),
            expected: "2024-02-25T23:57:00Z",
            description: "week truncation on Sunday with +3 minutes crossing to next week",
        },
        TruncateTestCase {
            timestamp: "2024-02-21T12:30:00Z", // Wednesday
            interval: TruncInterval::Week,
            tz_offset: None,
            expected: "2024-02-19T00:00:00Z",
            description: "week truncation mid-week without offset",
        },
        TruncateTestCase {
            timestamp: "2024-02-18T23:59:30-01:00", // Sunday in UTC (Monday in -01:00)
            interval: TruncInterval::Week,
            tz_offset: None,
            expected: "2024-02-19T00:00:00Z",
            description: "week truncation exactly on Monday boundary",
        },
        // Day truncation
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:11Z",
            interval: TruncInterval::Day,
            tz_offset: Some(-61),
            expected: "2024-02-29T22:59:00Z",
            description: "day truncation with +61 minutes crossing to next day",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T22:58:11Z",
            interval: TruncInterval::Day,
            tz_offset: Some(-62),
            expected: "2024-02-29T22:58:00Z",
            description: "day truncation with +62 minutes staying in same day",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T00:30:00Z",
            interval: TruncInterval::Day,
            tz_offset: None,
            expected: "2024-03-01T00:00:00Z",
            description: "day truncation without offset",
        },
        // Hour truncation
        TruncateTestCase {
            timestamp: "2024-02-29T22:58:11Z",
            interval: TruncInterval::Hour,
            tz_offset: Some(-61),
            expected: "2024-02-29T21:59:00Z",
            description: "hour truncation with +61 minutes crossing +1 hour",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T22:58:11Z",
            interval: TruncInterval::Hour,
            tz_offset: Some(-62),
            expected: "2024-02-29T22:58:00Z",
            description: "hour truncation with +62 minutes crossing +2 hours",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T12:45:30Z",
            interval: TruncInterval::Hour,
            tz_offset: None,
            expected: "2024-03-01T12:00:00Z",
            description: "hour truncation without offset",
        },
        // Minute truncation
        TruncateTestCase {
            timestamp: "2024-03-01T00:07:11Z",
            interval: TruncInterval::Minute,
            tz_offset: Some(7),
            expected: "2024-03-01T00:07:00Z",
            description: "minute truncation with -7 minutes offset",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T00:07:11Z",
            interval: TruncInterval::Minute,
            tz_offset: Some(8),
            expected: "2024-03-01T00:07:00Z",
            description: "minute truncation with -8 minutes offset",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T12:45:30Z",
            interval: TruncInterval::Minute,
            tz_offset: None,
            expected: "2024-03-01T12:45:00Z",
            description: "minute truncation without offset",
        },
        // Second truncation
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:30.500Z",
            interval: TruncInterval::Second,
            tz_offset: Some(-500),
            expected: "2024-02-29T23:58:30Z",
            description: "second truncation zeroing milliseconds",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:59.999Z",
            interval: TruncInterval::Second,
            tz_offset: Some(-1000),
            expected: "2024-02-29T23:58:59Z",
            description: "second truncation near minute boundary",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T12:45:30.123456789Z",
            interval: TruncInterval::Second,
            tz_offset: None,
            expected: "2024-03-01T12:45:30Z",
            description: "second truncation without offset, with nanoseconds",
        },
    ];

    fn parse_datetime(s: &str) -> DateTime {
        let offset_dt = OffsetDateTime::parse(s, &Rfc3339).unwrap();
        DateTime::from_utc(offset_dt)
    }

    fn datetime_to_string(dt: DateTime) -> String {
        dt.into_utc().format(&Rfc3339).unwrap()
    }

    #[test]
    fn test_date_trunc_with_timezone() {
        for test_case in TRUNCATE_TEST_CASES {
            // Set up the context with the timezone offset
            let expr_ctx = ExprContext::new(test_case.tz_offset);
            let mut ctx = ColumnarExprContext::new(expr_ctx);

            // Parse the input timestamp
            let input_dt = parse_datetime(test_case.timestamp);

            // Create a literal expression with the datetime
            let literal = ColumnarLiteral::new(ColumnarValueLiteralOwned::DateTime(input_dt));
            let literal_expr = ColumnarExpr::Literal(literal);

            // Create the DateTrunc expression
            let mut date_trunc_expr = ColumnarExpr::DateTrunc(DateTrunc {
                expr: Box::new(literal_expr),
                interval: test_case.interval,
                buf: OpaqueBuffer::new(ColumnarValueType::DateTime),
            });

            // Create dummy columns and docs (not used by literal expressions)
            let columns: &BatchColumns = &[];
            let docs = vec![0];

            // Execute the truncation
            let result = date_trunc_expr.interpret_batch(&mut ctx, &columns, &docs).unwrap();
            let result_slice = result.as_slice::<Option<DateTime>>();

            assert_eq!(result_slice.len(), 1, "Expected one result");
            let result_dt = result_slice[0].expect("Expected non-null result");
            let result_str = datetime_to_string(result_dt);

            assert_eq!(
                result_str, test_case.expected,
                "Failed test: {}\nInput: {} with offset {:?} minutes\nExpected: {}, Got: {}",
                test_case.description, test_case.timestamp, test_case.tz_offset,
                test_case.expected, result_str
            );
        }
    }

    #[test]
    fn test_date_trunc_batch() {
        // Test that DateTrunc works correctly on a batch of values
        let expr_ctx = ExprContext::new(Some(300)); // UTC-5
        let mut ctx = ColumnarExprContext::new(expr_ctx);

        // Create a batch of datetime values
        let timestamps = vec![
            "2024-03-15T10:30:45Z",
            "2024-03-15T14:45:30Z",
            "2024-03-15T23:15:00Z",
        ];

        let expected_day = vec![
            "2024-03-15T05:00:00Z", // 10:30 UTC = 05:30 UTC-5, truncated to day start, then back to UTC
            "2024-03-15T05:00:00Z", // 14:45 UTC = 09:45 UTC-5, same day
            "2024-03-15T05:00:00Z", // 23:15 UTC = 18:15 UTC-5, same day
        ];

        // Since we can't easily create a multi-value literal, we'll test the logic
        // by creating multiple single-value truncations
        for (i, (ts, expected)) in timestamps.iter().zip(expected_day.iter()).enumerate() {
            let literal = ColumnarLiteral::new(ColumnarValueLiteralOwned::DateTime(parse_datetime(ts)));
            let literal_expr = ColumnarExpr::Literal(literal);

            let mut date_trunc_expr = ColumnarExpr::DateTrunc(DateTrunc {
                expr: Box::new(literal_expr),
                interval: TruncInterval::Day,
                buf: OpaqueBuffer::new(ColumnarValueType::DateTime),
            });

            let columns: &BatchColumns = &[];
            let docs = vec![0];

            let result = date_trunc_expr.interpret_batch(&mut ctx, &columns, &docs).unwrap();
            let result_slice = result.as_slice::<Option<DateTime>>();

            let result_dt = result_slice[0].expect("Expected non-null result");
            let result_str = datetime_to_string(result_dt);

            assert_eq!(
                result_str, *expected,
                "Batch test failed for index {}: expected {}, got {}",
                i, expected, result_str
            );
        }
    }

    #[test]
    fn test_date_trunc_null_handling() {
        // Test that DateTrunc correctly handles valid DateTime values
        // (The null handling code has been commented out, so we test basic functionality)
        let expr_ctx = ExprContext::new(None);
        let mut ctx = ColumnarExprContext::new(expr_ctx);

        let literal = ColumnarLiteral::new(ColumnarValueLiteralOwned::DateTime(
            tantivy::DateTime::from_timestamp_secs(1640995200) // 2022-01-01
        ));
        let literal_expr = ColumnarExpr::Literal(literal);

        let mut date_trunc_expr = ColumnarExpr::DateTrunc(DateTrunc {
            expr: Box::new(literal_expr),
            interval: TruncInterval::Day,
            buf: OpaqueBuffer::new(ColumnarValueType::DateTime),
        });

        let columns: &BatchColumns = &[];
        let docs = vec![0];

        let result = date_trunc_expr.interpret_batch(&mut ctx, &columns, &docs).unwrap();
        let result_slice = result.as_slice::<Option<DateTime>>();

        assert_eq!(result_slice.len(), 1, "Expected one result");
        assert!(result_slice[0].is_some(), "Expected valid result for valid input");

        // Verify the result is truncated to day start (midnight)
        let result_dt = result_slice[0].unwrap();
        let result_utc = result_dt.into_utc();
        assert_eq!(result_utc.hour(), 0, "Expected hour to be 0 (midnight)");
        assert_eq!(result_utc.minute(), 0, "Expected minute to be 0");
        assert_eq!(result_utc.second(), 0, "Expected second to be 0");
    }
}
