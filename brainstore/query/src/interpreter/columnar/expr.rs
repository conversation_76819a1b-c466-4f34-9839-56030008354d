use tantivy::COLLECT_BLOCK_BUFFER_LEN;
use tantivy::{
    columnar::{Column, DynamicColumn},
    DateTime,
};
use util::ptree::MakePTree;
use util::xact::PaginationKey;

use crate::dispatch_literal_visitor;
use crate::interpreter::columnar::value::StringOrdinal;
use crate::planner::groupby::{arithmetic_value_type, coalesce_ternary_type};

use super::arithmetic::Arithmetic;
use super::cast::{CastableColumnarType, ColumnarCast};
use super::comparison::{Comparison, ComparisonOutputType};
use super::ternary::{ColumnarCond, Ternary};
use super::value::{BytesOrdinal, Null};
use super::visitor::ColumnarVisitor;
use super::PrimitiveColumnarType;
use super::{
    value::ColumnarExprContext, ColumnarValueLiteralBorrowed, ColumnarValueLiteralOwned,
    ColumnarValueType, PtrOffset,
};

#[derive(Debug, <PERSON><PERSON>, MakeP<PERSON>ree)]
pub enum ColumnarExpr {
    Column(ColumnarField),
    JSONColumnExists(JSONColumnExists),
    Cast(Cast),
    DateTrunc(DateTrunc),
    Literal(ColumnarLiteral),
    Arithmetic(Arithmetic),
    Comparison(Comparison),
    Ternary(Ternary),
}

pub type BatchColumns = [Option<DynamicColumn>];

impl ColumnarExpr {
    pub fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        Ok(match self {
            ColumnarExpr::Column(field) => field.interpret_batch(ctx, columns, docs)?,
            ColumnarExpr::JSONColumnExists(jce) => jce.interpret_batch(ctx, columns, docs)?,
            ColumnarExpr::Cast(cast) => cast.interpret_batch(ctx, columns, docs)?,
            ColumnarExpr::DateTrunc(dt) => dt.interpret_batch(ctx, columns, docs)?,
            ColumnarExpr::Literal(literal) => literal.interpret_batch(ctx, docs),
            ColumnarExpr::Arithmetic(arithmetic) => {
                arithmetic.interpret_batch(ctx, columns, docs)?
            }
            ColumnarExpr::Comparison(comparison) => {
                comparison.interpret_batch(ctx, columns, docs)?
            }
            ColumnarExpr::Ternary(ternary) => ternary.interpret_batch(ctx, columns, docs)?,
        })
    }

    pub fn columnar_type(&self) -> ColumnarValueType {
        match self {
            ColumnarExpr::Column(ColumnarField { value_type, .. }) => *value_type,
            ColumnarExpr::JSONColumnExists(JSONColumnExists { .. }) => ColumnarValueType::Bool,
            ColumnarExpr::Cast(Cast { cast_type, .. }) => *cast_type,
            ColumnarExpr::DateTrunc(DateTrunc { .. }) => ColumnarValueType::DateTime,
            ColumnarExpr::Literal(literal) => literal.literal.value_type(),
            ColumnarExpr::Arithmetic(Arithmetic { value_type, .. }) => *value_type,
            ColumnarExpr::Comparison(Comparison { .. }) => ComparisonOutputType::columnar_type(),
            ColumnarExpr::Ternary(Ternary { value_type, .. }) => *value_type,
        }
    }

    // The resulting value should not have a ColumnarValueType::Dynamic
    pub fn make_static(
        &self,
        columns: &BatchColumns,
    ) -> Result<ColumnarExpr, tantivy::TantivyError> {
        Ok(match self {
            t @ ColumnarExpr::JSONColumnExists(_) => t.clone(),
            t @ ColumnarExpr::Literal(_) => t.clone(),
            ColumnarExpr::Column(ColumnarField {
                value_type,
                column_index,
                ..
            }) => match (value_type, &columns[*column_index]) {
                (ColumnarValueType::Dynamic, Some(column)) => {
                    let specific_type = match column {
                        DynamicColumn::Bool(_) => ColumnarValueType::Bool,
                        DynamicColumn::I64(_) => ColumnarValueType::I64,
                        DynamicColumn::U64(_) => ColumnarValueType::U64,
                        DynamicColumn::F64(_) => ColumnarValueType::F64,
                        DynamicColumn::DateTime(_) => ColumnarValueType::DateTime,
                        DynamicColumn::Str(_) => ColumnarValueType::StringOrdinal,
                        DynamicColumn::Bytes(_) => ColumnarValueType::BytesOrdinal,
                        DynamicColumn::IpAddr(_) => {
                            return Err(tantivy::error::TantivyError::InternalError(
                                "IP addresses are not supported".to_string(),
                            ))
                        }
                    };
                    ColumnarExpr::Column(ColumnarField {
                        value_type: specific_type,
                        column_index: *column_index,
                        buf: OpaqueBuffer::new(specific_type),
                        str_state: StringDecompressorState::default(),
                    })
                }
                (ColumnarValueType::Dynamic, None) => ColumnarExpr::Column(ColumnarField {
                    value_type: ColumnarValueType::Null,
                    column_index: *column_index,
                    buf: OpaqueBuffer::new(ColumnarValueType::Null),
                    str_state: StringDecompressorState::default(),
                }),
                _ => self.clone(),
            },
            ColumnarExpr::DateTrunc(DateTrunc { expr, interval, .. }) => {
                let expr = expr.as_ref().make_static(columns)?;
                ColumnarExpr::DateTrunc(DateTrunc {
                    expr: Box::new(expr),
                    interval: *interval,
                    buf: OpaqueBuffer::new(ColumnarValueType::DateTime),
                })
            }
            ColumnarExpr::Cast(Cast {
                expr, cast_type, ..
            }) => {
                let expr = expr.as_ref().make_static(columns)?;
                let expr_type = expr.columnar_type();
                ColumnarExpr::Cast(Cast {
                    expr: Box::new(expr),
                    from_type: expr_type,
                    cast_type: *cast_type,
                    buf: OpaqueBuffer::new(expr_type),
                })
            }
            ColumnarExpr::Arithmetic(Arithmetic {
                left, right, op, ..
            }) => {
                let left = left.as_ref().make_static(columns)?;
                let right = right.as_ref().make_static(columns)?;
                let value_type = arithmetic_value_type(left.columnar_type(), op);
                match value_type {
                    ColumnarValueType::Null
                    | ColumnarValueType::I64
                    | ColumnarValueType::U64
                    | ColumnarValueType::F64 => {}
                    _ => {
                        return Err(tantivy::TantivyError::InternalError(format!(
                            "Arithmetic operations should result in numeric types, got: {:?}",
                            value_type
                        )));
                    }
                };
                ColumnarExpr::Arithmetic(Arithmetic::new(*op, left, right, value_type))
            }
            ColumnarExpr::Comparison(Comparison {
                left, right, op, ..
            }) => {
                let left = left.as_ref().make_static(columns)?;
                let right = right.as_ref().make_static(columns)?;

                ColumnarExpr::Comparison(Comparison::new(*op, left, right))
            }
            ColumnarExpr::Ternary(Ternary {
                conds, else_expr, ..
            }) => {
                let conds: Vec<ColumnarCond> = conds
                    .iter()
                    .map(|c| {
                        Ok(ColumnarCond {
                            cond: Box::new(c.cond.as_ref().make_static(columns)?),
                            then: Box::new(c.then.as_ref().make_static(columns)?),
                        })
                    })
                    .collect::<tantivy::Result<_>>()?;
                let else_expr = else_expr.as_ref().make_static(columns)?;
                let value_type = coalesce_ternary_type(&conds, &else_expr).ok_or_else(|| {
                    tantivy::TantivyError::InvalidArgument(format!(
                        "Cannot coalesce ternary types: {:?} and {:?}",
                        conds
                            .iter()
                            .map(|c| c.then.columnar_type())
                            .collect::<Vec<_>>(),
                        else_expr.columnar_type()
                    ))
                })?;
                ColumnarExpr::Ternary(Ternary::new(conds, else_expr, value_type))
            }
        })
    }
}

#[derive(Default, Debug, Clone)]
pub struct StringDecompressorState {
    pub rev_sorted_ords: Vec<u64>,
    pub bytes: Vec<u8>,
}

#[derive(Debug, Clone, MakePTree)]
pub struct ColumnarField {
    pub value_type: ColumnarValueType,

    #[ptree(skip)]
    pub column_index: usize,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
    #[ptree(skip)]
    pub str_state: StringDecompressorState,
}

#[derive(Debug, Clone, MakePTree)]
pub struct JSONColumnExists {
    pub prefix: String,

    #[ptree(skip)]
    pub column_index: usize,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
    #[ptree(skip)]
    pub idx_to_ord: Vec<usize>,
    #[ptree(skip)]
    pub ord_buf: Vec<StringOrdinal>,
    #[ptree(skip)]
    pub str_state: StringDecompressorState,
}

#[derive(Debug, Clone, MakePTree)]
pub struct Cast {
    pub expr: Box<ColumnarExpr>,
    pub from_type: ColumnarValueType,
    pub cast_type: ColumnarValueType,
    #[ptree(skip)]
    pub buf: OpaqueBuffer,
}

#[derive(Debug, Clone, MakePTree, Copy)]
pub enum TruncInterval {
    Second,
    Minute,
    Hour,
    Day,
    Week,
    Month,
    Year,
}

#[derive(MakePTree, Debug, Clone)]
pub struct DateTrunc {
    pub interval: TruncInterval,
    pub expr: Box<ColumnarExpr>,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
}

#[derive(Debug, Clone, MakePTree)]
pub struct ColumnarLiteral {
    pub literal: ColumnarValueLiteralOwned,

    #[ptree(skip)]
    pub buf: OpaqueBuffer,
    #[ptree(skip)]
    pub borrowed_value: Option<ColumnarValueLiteralBorrowed>,
}

impl ColumnarLiteral {
    pub fn new(literal: ColumnarValueLiteralOwned) -> Self {
        let value_type = literal.value_type();
        Self {
            literal,
            buf: OpaqueBuffer::new(value_type),
            borrowed_value: None,
        }
    }
}

#[derive(Debug, Clone)]
pub struct OpaqueBuffer {
    storage: Vec<u8>,
    columnar_type: ColumnarValueType,
}

pub struct MakeOpaqueBuffer {
    buffer_len: usize,
}

impl ColumnarVisitor<OpaqueBuffer> for MakeOpaqueBuffer {
    fn handle_default<T: PrimitiveColumnarType>(&mut self) -> OpaqueBuffer {
        OpaqueBuffer::with_capacity::<T>(T::columnar_type(), self.buffer_len)
    }

    // There's a good chance it's a string, so let's reserve that much space
    fn handle_dynamic(&mut self) -> OpaqueBuffer {
        OpaqueBuffer::with_capacity::<StringOrdinal>(
            ColumnarValueType::StringOrdinal,
            self.buffer_len,
        )
    }
}

impl OpaqueBuffer {
    pub fn new(columnar_type: ColumnarValueType) -> Self {
        dispatch_literal_visitor!(
            MakeOpaqueBuffer {
                buffer_len: COLLECT_BLOCK_BUFFER_LEN
            },
            columnar_type
        )
    }

    pub fn columnar_type(&self) -> ColumnarValueType {
        self.columnar_type
    }

    pub fn with_capacity<T: crate::interpreter::columnar::PrimitiveColumnarType>(
        columnar_type: ColumnarValueType,
        len: usize,
    ) -> Self {
        debug_assert!(T::matches_columnar_type(columnar_type));
        Self {
            storage: Vec::with_capacity(len * std::mem::size_of::<Option<T>>()),
            columnar_type,
        }
    }

    pub fn as_slice<T: crate::interpreter::columnar::PrimitiveColumnarType>(&self) -> &[T] {
        debug_assert!(
            self.storage.len() % std::mem::size_of::<T>() == 0,
            "1 storage len: {}, T size: {}",
            self.storage.len(),
            std::mem::size_of::<T>()
        );
        debug_assert!(T::matches_columnar_type(self.columnar_type));

        if self.storage.is_empty() {
            return &[];
        }

        unsafe {
            std::slice::from_raw_parts_mut(
                self.storage.as_ptr() as *mut T,
                self.storage.len() / std::mem::size_of::<T>(),
            )
        }
    }

    pub fn as_mut_slice<T: crate::interpreter::columnar::PrimitiveColumnarType>(
        &mut self,
    ) -> &mut [T] {
        debug_assert!(
            self.storage.len() % std::mem::size_of::<T>() == 0,
            "2 storage len: {}, T size: {}",
            self.storage.len(),
            std::mem::size_of::<T>()
        );
        debug_assert!(self.storage.len() % std::mem::size_of::<T>() == 0);
        debug_assert!(T::matches_columnar_type(self.columnar_type));

        if self.storage.is_empty() {
            return &mut [];
        }

        unsafe {
            std::slice::from_raw_parts_mut(
                self.storage.as_mut_ptr() as *mut T,
                self.storage.len() / std::mem::size_of::<T>(),
            )
        }
    }

    pub fn get<T: crate::interpreter::columnar::PrimitiveColumnarType>(&self, i: usize) -> T {
        debug_assert!(
            self.storage.len() % std::mem::size_of::<T>() == 0,
            "3 storage len: {}, T size: {}",
            self.storage.len(),
            std::mem::size_of::<T>()
        );
        debug_assert!(self.storage.len() % std::mem::size_of::<T>() == 0);
        debug_assert!(T::matches_columnar_type(self.columnar_type));
        debug_assert!(i < self.len::<T>());
        unsafe {
            std::ptr::read(self.storage.as_ptr().add(i * std::mem::size_of::<T>()) as *const T)
        }
    }

    pub fn len<T: crate::interpreter::columnar::PrimitiveColumnarType>(&self) -> usize {
        debug_assert!(
            self.storage.len() % std::mem::size_of::<T>() == 0,
            "4 storage len: {}, T size: {}",
            self.storage.len(),
            std::mem::size_of::<T>()
        );
        debug_assert!(self.storage.len() % std::mem::size_of::<T>() == 0);
        debug_assert!(T::matches_columnar_type(self.columnar_type));
        self.storage.len() / std::mem::size_of::<T>()
    }

    pub fn is_empty<T: crate::interpreter::columnar::PrimitiveColumnarType>(&self) -> bool {
        debug_assert!(
            self.storage.len() % std::mem::size_of::<T>() == 0,
            "5 storage len: {}, T size: {}",
            self.storage.len(),
            std::mem::size_of::<T>()
        );
        self.len::<T>() == 0
    }

    pub fn resize<T: crate::interpreter::columnar::PrimitiveColumnarType>(&mut self, len: usize) {
        debug_assert!(T::matches_columnar_type(self.columnar_type));
        self.storage.resize(len * std::mem::size_of::<T>(), 0);
    }
}

impl ColumnarField {
    fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        match (self.value_type, &columns[self.column_index]) {
            (ColumnarValueType::Bool, Some(DynamicColumn::Bool(col))) => {
                self.interpret_batch_as::<bool>(docs, col);
            }
            (ColumnarValueType::I64, Some(DynamicColumn::I64(col))) => {
                self.interpret_batch_as::<i64>(docs, col);
            }
            (ColumnarValueType::U64, Some(DynamicColumn::U64(col))) => {
                self.interpret_batch_as::<u64>(docs, col);
            }
            (ColumnarValueType::PaginationKey, Some(DynamicColumn::U64(col))) => {
                self.buf.resize::<Option<u64>>(docs.len());
                col.first_vals(docs, self.buf.as_mut_slice::<Option<u64>>());
            }
            (ColumnarValueType::F64, Some(DynamicColumn::F64(col))) => {
                self.interpret_batch_as::<f64>(docs, col);
            }
            (ColumnarValueType::DateTime, Some(DynamicColumn::DateTime(col))) => {
                self.interpret_batch_as::<tantivy::DateTime>(docs, col);
            }
            (ColumnarValueType::StringOrdinal, Some(DynamicColumn::Str(col))) => {
                self.buf.resize::<Option<StringOrdinal>>(docs.len());
                let buf = self.buf.as_mut_slice::<Option<StringOrdinal>>();

                for ptr in buf.iter_mut() {
                    *ptr = None;
                }

                for (i, doc) in docs.iter().enumerate() {
                    let mut ords = col.term_ords(*doc);
                    buf[i] = ords
                        .next()
                        .map(|o| StringOrdinal::new(self.column_index as u32, o));
                    if ords.next().is_some() {
                        return Err(tantivy::TantivyError::InvalidArgument(format!(
                            "Expected only one string value per document. Found more than one for document {}",
                            doc
                        )));
                    }
                }

                // For sufficiently dense columns, it would be good to decompress the whole
                // column's dictionary at once, but it's easier to write code that does it each
                // time.
                ctx.decompress_into_dictionary(
                    buf.iter().flatten().map(|o| o.as_ref()),
                    &mut self.str_state,
                    col.dictionary(),
                )?;
            }
            (ColumnarValueType::BytesOrdinal, Some(DynamicColumn::Bytes(col))) => {
                self.buf.resize::<Option<BytesOrdinal>>(docs.len());
                let buf = self.buf.as_mut_slice::<Option<BytesOrdinal>>();

                for ptr in buf.iter_mut() {
                    *ptr = None;
                }

                for (i, doc) in docs.iter().enumerate() {
                    let mut ords = col.term_ords(*doc);
                    buf[i] = ords
                        .next()
                        .map(|o| BytesOrdinal::new(self.column_index as u32, o));
                    if ords.next().is_some() {
                        return Err(tantivy::TantivyError::InvalidArgument(format!(
                            "Expected only one string value per document. Found more than one for document {}",
                            doc
                        )));
                    }
                }

                ctx.decompress_into_dictionary(
                    buf.iter().flatten(),
                    &mut self.str_state,
                    col.dictionary(),
                )?;
            }
            (_, Some(c)) => {
                let column_type = c.column_type();
                panic!(
                    "Mismatched columnar types: {:?} and {:?}.",
                    self.value_type, column_type
                );
            }
            (_, None) => {
                self.buf.resize::<Option<Null>>(docs.len());
                let buf = self.buf.as_mut_slice::<Option<Null>>();
                for ptr in buf.iter_mut() {
                    *ptr = None;
                }
            }
        };

        Ok(&mut self.buf)
    }

    fn interpret_batch_as<
        T: crate::interpreter::columnar::PrimitiveColumnarType + 'static + Copy,
    >(
        &mut self,
        docs: &[tantivy::DocId],
        column: &Column<T>,
    ) {
        self.buf.resize::<Option<T>>(docs.len());

        // TODO: Handle multi-valued columns. Maybe a separate columnar expression type?
        column.first_vals(docs, self.buf.as_mut_slice::<Option<T>>());
    }
}

pub type JSONColumnExistsValue = bool;
impl JSONColumnExists {
    fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        self.buf.resize::<Option<JSONColumnExistsValue>>(docs.len());
        let buf = self.buf.as_mut_slice::<Option<JSONColumnExistsValue>>();

        let col = match &columns[self.column_index] {
            Some(col) => col,
            None => {
                for ptr in buf.iter_mut() {
                    *ptr = None;
                }
                return Ok(&mut self.buf);
            }
        };

        let col = match col {
            DynamicColumn::Str(col) => col,
            c => {
                return Err(tantivy::TantivyError::InvalidArgument(format!(
                    "JSON exists field should be a string, not {}",
                    c.column_type()
                )))
            }
        };

        self.ord_buf.truncate(0);
        self.idx_to_ord.truncate(0);

        for (i, doc) in docs.iter().enumerate() {
            for ord in col.term_ords(*doc) {
                self.ord_buf
                    .push(StringOrdinal::new(self.column_index as u32, ord));
                self.idx_to_ord.push(i);
            }
        }

        ctx.decompress_into_dictionary(
            self.ord_buf.iter().map(|o| o.as_ref()),
            &mut self.str_state,
            col.dictionary(),
        )?;

        let mut curr = 0;
        for (idx, ptr) in buf.iter_mut().enumerate() {
            while curr < self.idx_to_ord.len() && self.idx_to_ord[curr] < idx {
                curr += 1;
            }

            // Look at each string. If we find a path that starts with our prefix,
            // then we say it exists and move onto the next one.
            let mut found = None;
            while curr < self.idx_to_ord.len() && self.idx_to_ord[curr] == idx {
                let value = ctx.get_string_from_ordinal(&self.ord_buf[curr]);
                if value.starts_with(&self.prefix) {
                    found = Some(true);
                    break;
                }
                curr += 1;
            }

            *ptr = found;
        }

        Ok(&mut self.buf)
    }
}

pub(crate) const NANOS_PER_SECOND: i64 = 1_000_000_000;
pub(crate) const NANOS_PER_MINUTE: i64 = 60 * NANOS_PER_SECOND;
pub(crate) const NANOS_PER_HOUR: i64 = 60 * NANOS_PER_MINUTE;
pub(crate) const NANOS_PER_DAY: i64 = 24 * NANOS_PER_HOUR;

impl DateTrunc {
    fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        let buf = self.expr.interpret_batch(ctx, columns, docs)?;
        let buf_dt = buf.as_slice::<Option<tantivy::DateTime>>();

        self.buf.resize::<Option<tantivy::DateTime>>(docs.len());

        let tz_offset = ctx.expr_ctx().tz_offset;

        match self.interval {
            TruncInterval::Second
            | TruncInterval::Minute
            | TruncInterval::Hour
            | TruncInterval::Day => {
                // Simple modulo-based truncation
                let modulo = match self.interval {
                    TruncInterval::Second => NANOS_PER_SECOND,
                    TruncInterval::Minute => NANOS_PER_MINUTE,
                    TruncInterval::Hour => NANOS_PER_HOUR,
                    TruncInterval::Day => NANOS_PER_DAY,
                    _ => unreachable!(),
                };

                if let Some(tz_offset) = tz_offset {
                    // Apply timezone offset for truncation
                    let offset_nanos = (tz_offset as i64) * NANOS_PER_MINUTE;
                    for (val, out) in buf_dt
                        .iter()
                        .zip(self.buf.as_mut_slice::<Option<tantivy::DateTime>>())
                    {
                        *out = val.map(|val| {
                            // Convert to local time: for UTC-5 offset (+300), subtract 5 hours
                            let local_nanos = val.into_timestamp_nanos() - offset_nanos;
                            // Truncate in local time
                            let truncated_local = (local_nanos / modulo) * modulo;
                            // Convert back to UTC: add back the 5 hours
                            tantivy::DateTime::from_timestamp_nanos(truncated_local + offset_nanos)
                        });
                    }
                } else {
                    // No timezone offset, truncate in UTC
                    for (val, out) in buf_dt
                        .iter()
                        .zip(self.buf.as_mut_slice::<Option<tantivy::DateTime>>())
                    {
                        *out = val.map(|val| {
                            tantivy::DateTime::from_timestamp_nanos(
                                (val.into_timestamp_nanos() / modulo) * modulo,
                            )
                        });
                    }
                }
            }
            TruncInterval::Week => {
                // Week truncation requires finding the previous Monday
                for (val, out) in buf_dt
                    .iter()
                    .zip(self.buf.as_mut_slice::<Option<tantivy::DateTime>>())
                {
                    *out = val.and_then(|val| {
                        let dt = val.into_utc();

                        // Apply timezone offset if present
                        let adjusted = if let Some(tz_offset) = tz_offset {
                            dt.saturating_sub(time::Duration::minutes(tz_offset as i64))
                        } else {
                            dt
                        };

                        // Truncate to Monday in the adjusted timezone
                        let days_since_monday = adjusted.weekday().number_days_from_monday() as i64;
                        let monday = adjusted.date() - time::Duration::days(days_since_monday);
                        let truncated = monday.midnight().assume_utc();

                        // Add timezone offset back if it was applied
                        let result = if let Some(tz_offset) = tz_offset {
                            truncated.saturating_add(time::Duration::minutes(tz_offset as i64))
                        } else {
                            truncated
                        };

                        Some(tantivy::DateTime::from_utc(result))
                    });
                }
            }
            TruncInterval::Month => {
                // Month truncation - truncate to first day of month at midnight
                for (val, out) in buf_dt
                    .iter()
                    .zip(self.buf.as_mut_slice::<Option<tantivy::DateTime>>())
                {
                    *out = val.and_then(|val| {
                        let dt = val.into_utc();

                        // Apply timezone offset if present
                        let adjusted = if let Some(tz_offset) = tz_offset {
                            dt.saturating_sub(time::Duration::minutes(tz_offset as i64))
                        } else {
                            dt
                        };

                        // Truncate in the adjusted timezone
                        let truncated = adjusted
                            .replace_day(1)
                            .ok()?
                            .replace_hour(0)
                            .ok()?
                            .replace_minute(0)
                            .ok()?
                            .replace_second(0)
                            .ok()?
                            .replace_nanosecond(0)
                            .ok()?;

                        // Add timezone offset back if it was applied
                        let result = if let Some(tz_offset) = tz_offset {
                            truncated.saturating_add(time::Duration::minutes(tz_offset as i64))
                        } else {
                            truncated
                        };

                        Some(tantivy::DateTime::from_utc(result))
                    });
                }
            }
            TruncInterval::Year => {
                // Year truncation - truncate to January 1st at midnight
                for (val, out) in buf_dt
                    .iter()
                    .zip(self.buf.as_mut_slice::<Option<tantivy::DateTime>>())
                {
                    *out = val.and_then(|val| {
                        let dt = val.into_utc();

                        // Apply timezone offset if present
                        let adjusted = if let Some(tz_offset) = tz_offset {
                            dt.saturating_sub(time::Duration::minutes(tz_offset as i64))
                        } else {
                            dt
                        };

                        // Truncate in the adjusted timezone
                        let truncated = adjusted
                            .replace_month(time::Month::January)
                            .ok()?
                            .replace_day(1)
                            .ok()?
                            .replace_hour(0)
                            .ok()?
                            .replace_minute(0)
                            .ok()?
                            .replace_second(0)
                            .ok()?
                            .replace_nanosecond(0)
                            .ok()?;

                        // Add timezone offset back if it was applied
                        let result = if let Some(tz_offset) = tz_offset {
                            truncated.saturating_add(time::Duration::minutes(tz_offset as i64))
                        } else {
                            truncated
                        };

                        Some(tantivy::DateTime::from_utc(result))
                    });
                }
            }
        }

        Ok(&mut self.buf)
    }
}

impl Cast {
    fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        let buf = self.expr.interpret_batch(ctx, columns, docs)?;
        let dst_type = self.cast_type;
        let dst = &mut self.buf;

        macro_rules! cast_match {
            ($($variant:ident => $type:ty),* $(,)?) => {
                match self.cast_type {
                    $(
                        ColumnarValueType::$variant => Self::interpret_batch_from::<$type>(
                            ctx,
                            buf.as_mut_slice::<Option<$type>>(),
                            dst,
                            dst_type,
                        ),
                    )*
                    ColumnarValueType::Dynamic => {
                        return Err(tantivy::TantivyError::InvalidArgument(
                            "Cast from Dynamic types is not supported".to_string(),
                        ))
                    }
                }
            };
        }

        cast_match! {
            Null => Null,
            Bool => bool,
            I64 => i64,
            U64 => u64,
            PaginationKey => PaginationKey,
            F64 => f64,
            DateTime => tantivy::DateTime,
            StringPtr => PtrOffset,
            StringOrdinal => StringOrdinal,
            BytesOrdinal => BytesOrdinal,
        }?;

        Ok(&mut self.buf)
    }

    fn interpret_batch_from<F: CastableColumnarType>(
        ctx: &mut ColumnarExprContext,
        buf: &[Option<F>],
        dst: &mut OpaqueBuffer,
        dst_type: ColumnarValueType,
    ) -> Result<(), tantivy::TantivyError> {
        macro_rules! cast_to_match {
            ($($variant:ident => $type:ty),* $(,)?) => {
                match dst_type {
                    $(
                        ColumnarValueType::$variant => {
                            dst.resize::<Option<$type>>(buf.len());

                            Self::interpret_batch_from_and_to::<F, $type>(
                                ctx,
                                buf,
                                dst.as_mut_slice::<Option<$type>>(),
                            )
                        },
                    )*
                    ColumnarValueType::Dynamic
                    | ColumnarValueType::StringOrdinal
                    | ColumnarValueType::BytesOrdinal => Err(tantivy::TantivyError::InvalidArgument(
                        format!("Cast to {:?} types is not supported", dst_type),
                    )),
                }
            };
        }

        cast_to_match! {
            Null => Null,
            Bool => bool,
            I64 => i64,
            U64 => u64,
            PaginationKey => PaginationKey,
            F64 => f64,
            DateTime => tantivy::DateTime,
            StringPtr => PtrOffset,
        }
    }

    fn interpret_batch_from_and_to<F, T>(
        ctx: &mut ColumnarExprContext,
        buf: &[Option<F>],
        dst: &mut [Option<T>],
    ) -> Result<(), tantivy::TantivyError>
    where
        F: ColumnarCast<T>,
    {
        for (s, d) in buf.iter().zip(dst.iter_mut()) {
            *d = s.as_ref().and_then(|s| s.columnar_cast(ctx));
        }

        Ok(())
    }
}

impl ColumnarLiteral {
    fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        docs: &[tantivy::DocId],
    ) -> &mut OpaqueBuffer {
        let value_type = self.literal.value_type();
        match value_type {
            ColumnarValueType::Null => self.interpret_batch_as::<Null>(ctx, docs),
            ColumnarValueType::Bool => self.interpret_batch_as::<bool>(ctx, docs),
            ColumnarValueType::I64 => self.interpret_batch_as::<i64>(ctx, docs),
            ColumnarValueType::U64 => self.interpret_batch_as::<u64>(ctx, docs),
            ColumnarValueType::PaginationKey => self.interpret_batch_as::<PaginationKey>(ctx, docs),
            ColumnarValueType::F64 => self.interpret_batch_as::<f64>(ctx, docs),
            ColumnarValueType::DateTime => self.interpret_batch_as::<DateTime>(ctx, docs),
            ColumnarValueType::StringPtr => self.interpret_batch_as::<PtrOffset>(ctx, docs),
            ColumnarValueType::Dynamic
            | ColumnarValueType::StringOrdinal
            | ColumnarValueType::BytesOrdinal => panic!("Invalid literal type: {:?}", value_type),
        }
    }

    fn interpret_batch_as<
        T: crate::interpreter::columnar::PrimitiveColumnarType + Copy + 'static,
    >(
        &mut self,
        ctx: &mut ColumnarExprContext,
        docs: &[tantivy::DocId],
    ) -> &mut OpaqueBuffer {
        let old_len = self.buf.len::<Option<T>>();
        self.buf.resize::<Option<T>>(docs.len());

        if old_len < docs.len() {
            let slice = self.buf.as_mut_slice::<Option<T>>();

            let borrowed_value = match self.borrowed_value {
                Some(val) => val,
                None => {
                    let borrowed_value = match &self.literal {
                        ColumnarValueLiteralOwned::Null => ColumnarValueLiteralBorrowed::Null,
                        ColumnarValueLiteralOwned::Bool(v) => {
                            ColumnarValueLiteralBorrowed::Bool(*v)
                        }
                        ColumnarValueLiteralOwned::I64(v) => ColumnarValueLiteralBorrowed::I64(*v),
                        ColumnarValueLiteralOwned::U64(v) => ColumnarValueLiteralBorrowed::U64(*v),
                        ColumnarValueLiteralOwned::PaginationKey(v) => {
                            ColumnarValueLiteralBorrowed::PaginationKey(*v)
                        }
                        ColumnarValueLiteralOwned::F64(v) => ColumnarValueLiteralBorrowed::F64(*v),
                        ColumnarValueLiteralOwned::DateTime(v) => {
                            ColumnarValueLiteralBorrowed::DateTime(*v)
                        }
                        ColumnarValueLiteralOwned::String(v) => {
                            ColumnarValueLiteralBorrowed::StringPtr(ctx.insert_expr_string(v))
                        }
                        ColumnarValueLiteralOwned::Bytes(_) => {
                            panic!("Bytes literals are not supported in columnar expressions")
                        }
                    };

                    self.borrowed_value = Some(borrowed_value);
                    borrowed_value
                }
            };

            let val = T::from_borrowed_value(borrowed_value);
            for x in slice[old_len..docs.len()].iter_mut() {
                *x = Some(val);
            }
        }

        &mut self.buf
    }
}
