use std::hash::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>};

use super::value::{ColumnarExprContext, ColumnarHashable};

pub type FastHashMap<K, V, S = gxhash::GxBuildHasher> = hashbrown::HashMap<K, V, S>;
pub type RawEntryMut<'a, K, V, S> = hashbrown::hash_map::RawEntryMut<'a, K, V, S>;
pub type Entry<'a, K, V, S> = hashbrown::hash_map::Entry<'a, K, V, S>;

#[inline]
pub fn hash_columnar_value<S: BuildHasher, D: ColumnarHashable>(
    builder: &S,
    ctx: &ColumnarExprContext,
    value: &D,
) -> u64 {
    let mut hasher = builder.build_hasher();
    value.hash_key(ctx, &mut hasher);
    hasher.finish()
}
