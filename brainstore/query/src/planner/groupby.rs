use btql::binder::ast::{<PERSON><PERSON>, <PERSON>thmet<PERSON>Op, ComparisonOp, Function, Literal, UnaryOp};
use btql::binder::Expr;
use btql::schema::ScalarType;
use btql::typesystem::CastInto;
use storage::process_wal::PAGINATION_KEY_FIELD;
use util::json::PathPiece;
use util::Value;

use super::ast::GroupByStrategy;
use super::{
    ast::GroupByQuery,
    context::PlanContext,
    error::{PlannerError, Result},
    plan,
};
use crate::interpreter::aggregator::agg_enums::AggregateFunction;
use crate::interpreter::aggregator::columnar_aggregator::ColumnarAggregator;
use crate::interpreter::aggregator::ExprAggregator;
use crate::interpreter::columnar::comparison::Comparison;
use crate::interpreter::columnar::ternary::{ColumnarCond, Ternary};
use crate::interpreter::columnar::{
    arithmetic::Arithmetic,
    expr::{Cast, JSONColumnExists, StringDecompressorState},
    ColumnarExpr, ColumnarField, ColumnarLiteral, ColumnarValueLiteralOwned, ColumnarValueType,
    DateTrunc, OpaqueBuffer, TruncInterval,
};
use crate::optimizer::ast::{GroupByPlan, TantivyProjectedField};

pub fn plan_aggregate_function(
    function: &btql::binder::ast::Function,
) -> Result<(AggregateFunction, &Expr)> {
    Ok(match function.name.as_str() {
        "count" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "count expects 1 argument".to_string(),
                });
            }
            let count_expr = function.args[0].as_ref();
            (
                match count_expr {
                    Expr::Literal(Literal { value, .. }) if !matches!(value, Value::Null) => {
                        AggregateFunction::ConstantCount
                    }
                    _ => AggregateFunction::ExprCount,
                },
                count_expr,
            )
        }
        "sum" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "sum expects 1 argument".to_string(),
                });
            }
            let inner = function.args[0].as_ref();
            (AggregateFunction::Sum, inner)
        }
        "avg" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "avg expects 1 argument".to_string(),
                });
            }
            let inner = function.args[0].as_ref();
            (AggregateFunction::Avg, inner)
        }
        "min" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "min expects 1 argument".to_string(),
                });
            }
            let inner = function.args[0].as_ref();
            (AggregateFunction::Min, inner)
        }
        "max" => {
            if function.args.len() != 1 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "max expects 1 argument".to_string(),
                });
            }
            let inner = function.args[0].as_ref();
            (AggregateFunction::Max, inner)
        }
        "percentile" => {
            if function.args.len() != 2 {
                return Err(PlannerError::InvalidOptimizedExpr {
                    msg: "percentile expects 2 arguments: expression and percentile value"
                        .to_string(),
                });
            }
            let expr = function.args[0].as_ref();
            let percentile = match function.args[1].as_ref() {
                Expr::Literal(Literal {
                    value: Value::Number(n),
                    ..
                }) => {
                    if let Some(p) = n.as_f64() {
                        if !(0.0..=100.0).contains(&p) {
                            return Err(PlannerError::InvalidOptimizedExpr {
                                msg: "percentile value must be between 0 and 100".to_string(),
                            });
                        }
                        p
                    } else {
                        return Err(PlannerError::InvalidOptimizedExpr {
                            msg: "percentile value must be a number".to_string(),
                        });
                    }
                }
                _ => {
                    return Err(PlannerError::InvalidOptimizedExpr {
                        msg: "percentile value must be a numeric literal".to_string(),
                    })
                }
            };

            (AggregateFunction::Percentile { percentile }, expr)
        }
        _ => {
            return Err(PlannerError::InvalidOptimizedExpr {
                msg: format!("unknown aggregate function: {}", function.name),
            })
        }
    })
}

pub fn expr_to_columnar_expr(
    expr: &Expr,
    projected_fields: &[TantivyProjectedField],
) -> Option<ColumnarExpr> {
    Some(match expr {
        Expr::Literal(Literal { value, expr_type }) => {
            let literal = match expr_type {
                ScalarType::Null => ColumnarValueLiteralOwned::Null,
                ScalarType::Boolean => {
                    ColumnarValueLiteralOwned::Bool(CastInto::<bool>::cast(value).ok()?)
                }
                ScalarType::Integer => {
                    ColumnarValueLiteralOwned::I64(CastInto::<i64>::cast(value).ok()?)
                }
                ScalarType::Number => {
                    ColumnarValueLiteralOwned::F64(CastInto::<f64>::cast(value).ok()?)
                }
                ScalarType::DateTime | ScalarType::Date => {
                    ColumnarValueLiteralOwned::String(CastInto::<String>::cast(value).ok()?)
                }
                ScalarType::String => {
                    ColumnarValueLiteralOwned::String(CastInto::<String>::cast(value).ok()?)
                }
                ScalarType::Array
                | ScalarType::Object
                | ScalarType::Interval
                | ScalarType::Unknown => {
                    // These types invalidate our ability to use the columnstore
                    return None;
                }
            };

            ColumnarExpr::Literal(ColumnarLiteral::new(literal))
        }
        Expr::Field(field) => {
            let (idx, field) = projected_fields.iter().enumerate().find(|(_, f)| {
                field.name.len() == 1
                    && f.field_type.fast()
                    && match &field.name[0] {
                        PathPiece::Key(s) => s.as_str() == f.alias,
                        _ => false,
                    }
            })?;

            let value_type = if field.top_level_field == PAGINATION_KEY_FIELD {
                ColumnarValueType::PaginationKey
            } else {
                match field.field_type {
                    util::schema::TantivyType::Bool(_) => ColumnarValueType::Bool,
                    util::schema::TantivyType::I64(_) => ColumnarValueType::I64,
                    util::schema::TantivyType::U64(_) => ColumnarValueType::U64,
                    util::schema::TantivyType::F64(_) => ColumnarValueType::F64,
                    util::schema::TantivyType::Date(_) => ColumnarValueType::DateTime,
                    util::schema::TantivyType::Str(_) => ColumnarValueType::StringOrdinal,
                    util::schema::TantivyType::Bytes(_) => ColumnarValueType::BytesOrdinal,
                    util::schema::TantivyType::Json(_) if field.only_exists => {
                        return Some(ColumnarExpr::JSONColumnExists(JSONColumnExists {
                            prefix: util::json::serialize_path(
                                field.json_path.iter().map(|p| p.to_string()),
                            ),
                            column_index: idx,
                            buf: OpaqueBuffer::new(ColumnarValueType::Bool),
                            str_state: StringDecompressorState::default(),
                            ord_buf: Vec::with_capacity(tantivy::COLLECT_BLOCK_BUFFER_LEN),
                            idx_to_ord: Vec::with_capacity(tantivy::COLLECT_BLOCK_BUFFER_LEN),
                        }))
                    }
                    util::schema::TantivyType::Json(_) => ColumnarValueType::Dynamic,
                    util::schema::TantivyType::Facet(_) | util::schema::TantivyType::IpAddr(_) => {
                        return None;
                    }
                }
            };

            ColumnarExpr::Column(ColumnarField {
                value_type,
                column_index: idx,
                buf: OpaqueBuffer::new(value_type),
                str_state: StringDecompressorState::default(),
            })
        }
        Expr::Function(Function { name, args })
            if name == "second"
                || name == "minute"
                || name == "hour"
                || name == "day"
                || name == "week"
                || name == "month"
                || name == "year" =>
        {
            let interval = match name.as_str() {
                "second" => TruncInterval::Second,
                "minute" => TruncInterval::Minute,
                "hour" => TruncInterval::Hour,
                "day" => TruncInterval::Day,
                "week" => TruncInterval::Week,
                "month" => TruncInterval::Month,
                "year" => TruncInterval::Year,
                _ => unreachable!(),
            };

            let inner = expr_to_columnar_expr(&args[0], projected_fields)?;
            ColumnarExpr::DateTrunc(DateTrunc {
                interval,
                expr: Box::new(inner),
                buf: OpaqueBuffer::new(ColumnarValueType::DateTime),
            })
        }
        Expr::Unary {
            op: UnaryOp::Cast { cast_type },
            expr,
        } => {
            let inner = expr_to_columnar_expr(expr, projected_fields)?;
            let cast_type = match cast_type {
                ScalarType::Null => ColumnarValueType::Null,
                ScalarType::Boolean => ColumnarValueType::Bool,
                ScalarType::Integer => ColumnarValueType::I64,
                ScalarType::Number => ColumnarValueType::F64,
                ScalarType::String => ColumnarValueType::StringOrdinal,
                ScalarType::Date | ScalarType::DateTime => ColumnarValueType::DateTime,
                ScalarType::Array
                | ScalarType::Object
                | ScalarType::Interval
                | ScalarType::Unknown => {
                    // These types invalidate our ability to use the columnstore
                    return None;
                }
            };
            let from_type = inner.columnar_type();
            ColumnarExpr::Cast(Cast {
                expr: Box::new(inner),
                from_type,
                cast_type,
                buf: OpaqueBuffer::new(cast_type),
            })
        }
        Expr::Arithmetic { op, left, right } => {
            match op {
                ArithmeticOp::Add | ArithmeticOp::Sub => {}
                _ => return None, // Unsupported arithmetic operation
            };
            let left = expr_to_columnar_expr(left, projected_fields)?;
            let right = expr_to_columnar_expr(right, projected_fields)?;
            match (left.columnar_type(), right.columnar_type()) {
                (ColumnarValueType::I64, ColumnarValueType::I64)
                | (ColumnarValueType::U64, ColumnarValueType::U64)
                | (ColumnarValueType::F64, ColumnarValueType::F64)
                | (ColumnarValueType::Dynamic, _)
                | (_, ColumnarValueType::Dynamic) => ColumnarExpr::Arithmetic(Arithmetic {
                    op: *op,
                    value_type: arithmetic_value_type(left.columnar_type(), op),
                    left: Box::new(left),
                    right: Box::new(right),
                    buf: OpaqueBuffer::new(ColumnarValueType::I64),
                }),
                _ => return None,
            }
        }
        Expr::Comparison { op, left, right } => {
            match op {
                ComparisonOp::Eq | ComparisonOp::Ne => {}
                _ => return None, // Unsupported comparison operation
            };
            let left = expr_to_columnar_expr(left, projected_fields)?;
            let right = expr_to_columnar_expr(right, projected_fields)?;
            match (left.columnar_type(), right.columnar_type()) {
                (a, b) if a == b => {}
                (ColumnarValueType::Dynamic, _) | (_, ColumnarValueType::Dynamic) => {}
                (ColumnarValueType::Null, _) | (_, ColumnarValueType::Null) => {}
                _ => {
                    log::debug!(
                        "Unsupported types for equality comparison: {:?} vs {:?}",
                        left.columnar_type(),
                        right.columnar_type()
                    );
                    return None;
                }
            };
            ColumnarExpr::Comparison(Comparison {
                op: *op,
                left: Box::new(left),
                right: Box::new(right),
                buf: OpaqueBuffer::new(ColumnarValueType::Bool),
            })
        }
        Expr::Ternary { conds, else_expr } => {
            let mut columnar_conds = Vec::with_capacity(conds.len());
            for cond in conds {
                let cond_expr = expr_to_columnar_expr(&cond.cond, projected_fields)?;
                let then_expr = expr_to_columnar_expr(&cond.then, projected_fields)?;
                if cond_expr.columnar_type() != ColumnarValueType::Bool {
                    log::debug!(
                        "Unsupported condition type for columnar grouping: {:?}",
                        cond_expr.columnar_type()
                    );
                    return None;
                }
                columnar_conds.push(ColumnarCond {
                    cond: Box::new(cond_expr),
                    then: Box::new(then_expr),
                });
            }

            let else_expr = expr_to_columnar_expr(else_expr, projected_fields)?;
            let value_type = coalesce_ternary_type(&columnar_conds, &else_expr)?;
            ColumnarExpr::Ternary(Ternary::new(columnar_conds, else_expr, value_type))
        }
        _ => {
            // Unsupported expression type for columnar grouping
            log::debug!("Unsupported expression for columnar grouping: {:?}", expr);
            return None;
        }
    })
}

pub fn arithmetic_value_type(left_type: ColumnarValueType, op: &ArithmeticOp) -> ColumnarValueType {
    match (left_type, op) {
        (ColumnarValueType::U64, ArithmeticOp::Sub) => ColumnarValueType::I64,
        (t, _) => t,
    }
}

pub fn coalesce_ternary_type(
    conds: &[ColumnarCond],
    else_expr: &ColumnarExpr,
) -> Option<ColumnarValueType> {
    let mut then_type = None;
    for cond in conds {
        match (then_type, cond.then.columnar_type()) {
            (None, t) => then_type = Some(t),
            (Some(prev), t)
                if prev == t || t == ColumnarValueType::Dynamic || t == ColumnarValueType::Null => {
            }
            (Some(prev), t) => {
                log::debug!("Mismatched then types in ternary: {:?} vs {:?}", prev, t);
                return None;
            }
        }
    }

    match (then_type, else_expr.columnar_type()) {
        (None, t) => then_type = Some(t),
        (Some(then_type), t)
            if then_type == t
                || t == ColumnarValueType::Dynamic
                || t == ColumnarValueType::Null => {}
        _ => {
            log::debug!(
                "Mismatched types in ternary expression: {:?} vs {:?}",
                then_type,
                else_expr.columnar_type()
            );
            return None;
        }
    };

    then_type
}

impl GroupByStrategy {
    pub fn from_dims_and_aggs(
        dims: &[Alias],
        aggs: &[(String, (AggregateFunction, &Expr))],
        projected_fields: &[TantivyProjectedField],
    ) -> GroupByStrategy {
        // Try to convert the dims to columnar expressions
        let mut dim_exprs = Vec::new();
        for dim in dims {
            match expr_to_columnar_expr(&dim.expr, projected_fields) {
                Some(expr) => dim_exprs.push(expr),
                None => return GroupByStrategy::Dynamic,
            }
        }

        // Try to convert the aggs to columnar expressions
        let mut columnar_exprs = Vec::new();
        for (_, (_, expr)) in aggs {
            match expr_to_columnar_expr(expr, projected_fields) {
                Some(expr) => columnar_exprs.push(expr),
                None => return GroupByStrategy::Dynamic,
            }
        }

        let aggs = aggs
            .iter()
            .zip(columnar_exprs.iter())
            .map(|((_, (agg, _)), expr)| ColumnarAggregator::new(agg.clone(), expr.clone()))
            .collect::<Option<Vec<_>>>();

        match (dim_exprs.len(), aggs) {
            (0, Some(aggs)) => GroupByStrategy::NoDims { aggs },
            (1 | 2, Some(aggs)) => GroupByStrategy::Dims {
                dims: dim_exprs,
                aggs,
            },
            // We can special case up to N dimensions and turn them into typed tuples.
            _ => GroupByStrategy::Dynamic,
        }
    }
}

pub fn plan_groupby_query(ctx: &PlanContext, query: &GroupByPlan) -> Result<GroupByQuery> {
    let aggregates = query
        .aggregates
        .iter()
        .map(|(alias, func)| Ok((alias.clone(), plan_aggregate_function(func)?)))
        .collect::<Result<Vec<_>>>()?;

    Ok(GroupByQuery {
        from: plan(ctx, &query.from)?,
        pivot: query.pivot.clone(),
        measures: query.measures.clone(),
        dimensions: query.dimensions.clone(),
        aggregates: aggregates
            .into_iter()
            .map(|(alias, (agg, expr))| (alias, ExprAggregator::new(agg, expr)))
            .collect(),
    })
}
