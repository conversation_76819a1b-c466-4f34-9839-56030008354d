[{"error": null, "next_page_using_returned_cursor": [], "query": "-- This tests that limits properly apply to traces rather than spans\nselect: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 2", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "-- This should return 3 rows (all corresponding to root_span_id='1')\nselect: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "-- This should return 1 row (corresponding to root_span_id='4')\nselect: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1 | cursor: AAAAAAADAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "-- This should return no rows, since we exhausted the first root span already\nselect: * | sort: _pagination_key desc | from: dataset('singleton') traces | filter: s MATCH 'baz' | limit: 1 | cursor: AAAAAAACAAA", "result_rows": [], "skip": false}, {"error": null, "query": "-- Even though we don't project root_span_id, this should work\nselect: span_parents | from: dataset(\"singleton\") traces", "result_rows": [null, null, null, {"span_parents": ["1"]}, {"span_parents": ["2"]}], "skip": false}, {"error": null, "query": "unpivot: span_parents as sp | select: sp | from: dataset(\"singleton\") traces", "result_rows": [{"sp": "1"}, {"sp": "2"}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "query": "/*!result -- This should return both traces (one of them has 3 spans)\nlength == 4\n*/\nfrom: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "next_page_using_returned_cursor": [], "query": "/*!result -- This should return the remaining trace\nlength == 1\n*/\nfrom: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "/*!result -- This should return both traces (one of them has 3 spans)\nlength == 0\n*/\nfrom: dataset('singleton') traces | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAI", "result_rows": [], "skip": false}]