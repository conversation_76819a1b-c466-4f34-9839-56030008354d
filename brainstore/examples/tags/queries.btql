select: *;

select: a | filter: tags includes [];
select: a | filter: tags includes null;
select: a | filter: tags includes [null];

select: a | filter: tags includes 'a';
select: a | filter: tags includes 'b';
select: a | filter: tags includes 'ab';
select: a | filter: tags includes 'ba';
select: a | filter: tags includes 'cab';
select: a | filter: not tags includes 'b';

select: a | filter: tags includes ['a'];
select: a | filter: tags includes ['b'];
select: a | filter: tags includes ['ab'];
select: a | filter: tags includes ['ba'];
select: a | filter: tags includes ['cab'];
select: a | filter: not tags includes ['b'];

select: a | filter: tags includes ['a', 'b'];
select: a | filter: tags includes ['b', 'a'];
select: a | filter: tags includes ['a', 'b', 'c'];
select: a | filter: tags includes ['a', 'b', null];

select: a | filter: [] includes tags;
select: a | filter: null includes tags;
select: a | filter: [null] includes tags;

select: a | filter: 'a' includes tags;
select: a | filter: 'b' includes tags;

select: a | filter: ['a'] includes tags;
select: a | filter: ['b'] includes tags;
select: a | filter: ['ab'] includes tags;

select: a | filter: ['a', 'b'] includes tags;
select: a | filter: ['b', 'a'] includes tags;
select: a | filter: ['a', 'b', 'c'] includes tags;
select: a | filter: ['a', 'b', null] includes tags;

select: a, tags, len(a), len(tags);
select: a | filter: len(tags) is null;
measures: count(1) | filter: len(tags) = 0;
measures: count(1) | filter: len(tags) = 1;
measures: count(1) | filter: len(tags) >= 2;
dimensions: len(tags) | measures: count(1);

-- These are currently unsupported because we can't index into fields of type `array`.
-- We'll have to update the BTQL binder to support this.
select: a, tags[0] | filter: tags[0] = 'a';
select: a, tags[-1] | filter: tags[-1] = 'b';
select: a, tags[0] | filter: tags[0] is null;
select: a, tags[0] | filter: tags[0] is not null;
select: a, tags[-2] | filter: tags[-2] is null;
select: a, tags[-2] | filter: tags[-2] is not null;
