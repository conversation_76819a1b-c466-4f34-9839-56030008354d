[{"error": null, "query": "select: *", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid4", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id as foo", "result_rows": [{"foo": "asf-dfa"}, {"foo": "dfa-asf"}, {"foo": "uuid3"}, {"foo": "uuid4"}, {"foo": "uuid5"}], "skip": false}, {"error": null, "query": "select: scores", "result_rows": [null, null, {"scores": {"bar": 0.5, "foo": 0}}, {"scores": {"foo": 0.25}}, {"scores": {"foo": 1}}], "skip": false}, {"error": null, "query": "select: metadata", "result_rows": [null, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}}, {"metadata": {"nullable": false, "split": "train"}}, {"metadata": {"split": "test"}}], "skip": false}, {"error": null, "query": "select: id | filter: id = 1", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: id = \"asf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: id match \"asf\"", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id | filter: id = \"asf-dfa\"", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id | filter: id match \"asf-dfa\"", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id as bar | filter: id match \"asf-dfa\"", "result_rows": [{"bar": "asf-dfa"}], "skip": false}, {"error": null, "query": "-- This makes sure columnar projection works\nselect: scores", "result_rows": [null, null, {"scores": {"bar": 0.5, "foo": 0}}, {"scores": {"foo": 0.25}}, {"scores": {"foo": 1}}], "skip": false}, {"error": null, "query": "select: scores.foo AS foo", "result_rows": [null, null, {"foo": 0.25}, {"foo": 0}, {"foo": 1}], "skip": false}, {"error": null, "query": "select: scores.foo AS foo, scores", "result_rows": [null, null, {"foo": 0, "scores": {"bar": 0.5, "foo": 0}}, {"foo": 0.25, "scores": {"foo": 0.25}}, {"foo": 1, "scores": {"foo": 1}}], "skip": false}, {"error": null, "query": "select: metadata", "result_rows": [null, {"metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}}, {"metadata": {"nullable": false, "split": "train"}}, {"metadata": {"split": "test"}}], "skip": false}, {"error": null, "query": "select: tags", "result_rows": [null, null, {"tags": ["a", "b"]}, {"tags": ["a", "c"]}, {"tags": ["d"]}], "skip": false}]