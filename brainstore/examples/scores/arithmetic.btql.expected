[{"error": null, "query": "-- arithmetic ops\nselect: 1 + 2", "result_rows": [{"1 + 2": 3}, {"1 + 2": 3}, {"1 + 2": 3}, {"1 + 2": 3}, {"1 + 2": 3}], "skip": false}, {"error": null, "query": "select: 5 - 3", "result_rows": [{"5 - 3": 2}, {"5 - 3": 2}, {"5 - 3": 2}, {"5 - 3": 2}, {"5 - 3": 2}], "skip": false}, {"error": null, "query": "select: 4 * 2", "result_rows": [{"4 * 2": 8}, {"4 * 2": 8}, {"4 * 2": 8}, {"4 * 2": 8}, {"4 * 2": 8}], "skip": false}, {"error": null, "query": "select: 1 + 2.5", "result_rows": [{"1 + 2.5": 3.5}, {"1 + 2.5": 3.5}, {"1 + 2.5": 3.5}, {"1 + 2.5": 3.5}, {"1 + 2.5": 3.5}], "skip": false}, {"error": null, "query": "select: 3.5 + 2", "result_rows": [{"3.5 + 2": 5.5}, {"3.5 + 2": 5.5}, {"3.5 + 2": 5.5}, {"3.5 + 2": 5.5}, {"3.5 + 2": 5.5}], "skip": false}, {"error": null, "query": "select: 5.0 - 2", "result_rows": [{"5.0 - 2": 3}, {"5.0 - 2": 3}, {"5.0 - 2": 3}, {"5.0 - 2": 3}, {"5.0 - 2": 3}], "skip": false}, {"error": null, "query": "select: 7 - 3.5", "result_rows": [{"7 - 3.5": 3.5}, {"7 - 3.5": 3.5}, {"7 - 3.5": 3.5}, {"7 - 3.5": 3.5}, {"7 - 3.5": 3.5}], "skip": false}, {"error": null, "query": "select: 2 * 3.5", "result_rows": [{"2 * 3.5": 7}, {"2 * 3.5": 7}, {"2 * 3.5": 7}, {"2 * 3.5": 7}, {"2 * 3.5": 7}], "skip": false}, {"error": null, "query": "select: 4.5 * 2", "result_rows": [{"4.5 * 2": 9}, {"4.5 * 2": 9}, {"4.5 * 2": 9}, {"4.5 * 2": 9}, {"4.5 * 2": 9}], "skip": false}, {"error": null, "query": "select: 10 / 3", "result_rows": [{"10 / 3": 3}, {"10 / 3": 3}, {"10 / 3": 3}, {"10 / 3": 3}, {"10 / 3": 3}], "skip": false}, {"error": null, "query": "select: 10.0 / 3", "result_rows": [{"10.0 / 3": 3}, {"10.0 / 3": 3}, {"10.0 / 3": 3}, {"10.0 / 3": 3}, {"10.0 / 3": 3}], "skip": false}, {"error": null, "query": "select: 4 / 2.0", "result_rows": [{"4 / 2.0": 2}, {"4 / 2.0": 2}, {"4 / 2.0": 2}, {"4 / 2.0": 2}, {"4 / 2.0": 2}], "skip": false}, {"error": null, "query": "select: scores.foo + 1", "result_rows": [null, null, {"scores.foo + 1": 1.25}, {"scores.foo + 1": 1}, {"scores.foo + 1": 2}], "skip": false}, {"error": null, "query": "select: scores.bar - 2", "result_rows": [null, null, null, null, {"scores.bar - 2": -1.5}], "skip": false}, {"error": null, "query": "select: scores.foo * 3", "result_rows": [null, null, {"scores.foo * 3": 0.75}, {"scores.foo * 3": 0}, {"scores.foo * 3": 3}], "skip": false}, {"error": null, "query": "select: scores.bar / 2", "result_rows": [null, null, null, null, {"scores.bar / 2": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo + scores.bar", "result_rows": [null, null, null, null, {"scores.foo + scores.bar": 0.5}], "skip": false}, {"error": null, "query": "select: scores.foo - scores.bar", "result_rows": [null, null, null, null, {"scores.foo - scores.bar": -0.5}], "skip": false}, {"error": null, "query": "select: scores.foo * scores.bar", "result_rows": [null, null, null, null, {"scores.foo * scores.bar": 0}], "skip": false}, {"error": null, "query": "select: scores.foo / scores.bar", "result_rows": [null, null, null, null, {"scores.foo / scores.bar": 0}], "skip": false}, {"error": null, "query": "select: counts.foo % counts.bar", "result_rows": [null, null, null, null, {"counts.foo % counts.bar": -1}], "skip": false}, {"error": null, "query": "-- data-specific arithmetic\nselect: scores.foo + 0.5", "result_rows": [null, null, {"scores.foo + 0.5": 0.5}, {"scores.foo + 0.5": 0.75}, {"scores.foo + 0.5": 1.5}], "skip": false}, {"error": null, "query": "select: scores.bar - 0.25", "result_rows": [null, null, null, null, {"scores.bar - 0.25": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo * 2", "result_rows": [null, null, {"scores.foo * 2": 0.5}, {"scores.foo * 2": 0}, {"scores.foo * 2": 2}], "skip": false}, {"error": null, "query": "select: scores.bar / 2", "result_rows": [null, null, null, null, {"scores.bar / 2": 0.25}], "skip": false}, {"error": null, "query": "select: scores.foo + scores.bar", "result_rows": [null, null, null, null, {"scores.foo + scores.bar": 0.5}], "skip": false}, {"error": null, "query": "select: scores.foo - scores.bar", "result_rows": [null, null, null, null, {"scores.foo - scores.bar": -0.5}], "skip": false}, {"error": null, "query": "select: scores.foo * scores.bar", "result_rows": [null, null, null, null, {"scores.foo * scores.bar": 0}], "skip": false}, {"error": null, "query": "select: scores.foo / scores.bar", "result_rows": [null, null, null, null, {"scores.foo / scores.bar": 0}], "skip": false}, {"error": null, "query": "select: counts.foo % counts.bar", "result_rows": [null, null, null, null, {"counts.foo % counts.bar": -1}], "skip": false}, {"error": null, "query": "-- combo ops\nselect: -(scores.bar + 0.5) as value", "result_rows": [null, null, null, null, {"value": -1}], "skip": false}, {"error": null, "query": "select: -(scores.foo * 0.5) as value", "result_rows": [null, null, {"value": -0.125}, {"value": -0.5}, {"value": 0}], "skip": false}, {"error": null, "query": "select: null + scores.foo", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: scores.bar + null", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: null * scores.foo", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: scores.foo / null", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: null % counts.foo", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: -(scores.foo + scores.bar) * 0.5 as value", "result_rows": [null, null, null, null, {"value": -0.25}], "skip": false}, {"error": null, "query": "-- edge cases\nselect: scores.foo / 0", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: counts.foo % 0", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: -(-scores.foo) as value", "result_rows": [null, null, {"value": 0.25}, {"value": 0}, {"value": 1}], "skip": false}, {"error": null, "query": "select: (null is null) + (scores.foo is not null) as value", "result_rows": [{"value": 1}, {"value": 1}, {"value": 2}, {"value": 2}, {"value": 2}], "skip": false}, {"error": null, "query": "-- filters with arithmetic ops\nselect: id, scores.foo | filter: scores.foo = 1", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 1.0", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo + scores.bar > 0.75", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo * 2 < 0.5", "result_rows": [{"foo": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar / 2 > 0.2", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, counts.foo | filter: counts.foo % 2 = 0", "result_rows": [{"foo": 10, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: -(scores.foo - scores.bar) >= 0", "result_rows": [{"foo": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: 0.5 + counts.foo", "result_rows": [{"foo": 0, "id": "dfa-asf"}, {"foo": 0.25, "id": "uuid3"}, {"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: 2 - counts.bar is not null", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "uuid3"}], "skip": false}, {"error": null, "query": "-- boolean ops\nselect: true and true", "result_rows": [{"true and true": true}, {"true and true": true}, {"true and true": true}, {"true and true": true}, {"true and true": true}], "skip": false}, {"error": null, "query": "select: true and false", "result_rows": [{"true and false": false}, {"true and false": false}, {"true and false": false}, {"true and false": false}, {"true and false": false}], "skip": false}, {"error": null, "query": "select: false and false", "result_rows": [{"false and false": false}, {"false and false": false}, {"false and false": false}, {"false and false": false}, {"false and false": false}], "skip": false}, {"error": null, "query": "select: true or true", "result_rows": [{"true or true": true}, {"true or true": true}, {"true or true": true}, {"true or true": true}, {"true or true": true}], "skip": false}, {"error": null, "query": "select: true or false", "result_rows": [{"true or false": true}, {"true or false": true}, {"true or false": true}, {"true or false": true}, {"true or false": true}], "skip": false}, {"error": null, "query": "select: false or false", "result_rows": [{"false or false": false}, {"false or false": false}, {"false or false": false}, {"false or false": false}, {"false or false": false}], "skip": false}, {"error": null, "query": "-- boolean ops with comparisons\nselect: 1 > 0 and 2 > 1", "result_rows": [{"1 > 0 and 2 > 1": true}, {"1 > 0 and 2 > 1": true}, {"1 > 0 and 2 > 1": true}, {"1 > 0 and 2 > 1": true}, {"1 > 0 and 2 > 1": true}], "skip": false}, {"error": null, "query": "select: 1 < 0 or 2 > 1", "result_rows": [{"1 < 0 or 2 > 1": true}, {"1 < 0 or 2 > 1": true}, {"1 < 0 or 2 > 1": true}, {"1 < 0 or 2 > 1": true}, {"1 < 0 or 2 > 1": true}], "skip": false}, {"error": null, "query": "select: scores.foo > 0.5 and scores.bar > 0.25", "result_rows": [{"scores.foo > 0.5 and scores.bar > 0.25": false}, {"scores.foo > 0.5 and scores.bar > 0.25": false}, {"scores.foo > 0.5 and scores.bar > 0.25": false}, {"scores.foo > 0.5 and scores.bar > 0.25": false}, {"scores.foo > 0.5 and scores.bar > 0.25": false}], "skip": false}, {"error": null, "query": "select: scores.foo < 0.3 or scores.bar > 0.4", "result_rows": [{"scores.foo < 0.3 or scores.bar > 0.4": false}, {"scores.foo < 0.3 or scores.bar > 0.4": false}, {"scores.foo < 0.3 or scores.bar > 0.4": false}, {"scores.foo < 0.3 or scores.bar > 0.4": true}, {"scores.foo < 0.3 or scores.bar > 0.4": true}], "skip": false}, {"error": null, "query": "select: scores.foo + 0.5 > 0.75 and scores.bar < 1", "result_rows": [{"scores.foo + 0.5 > 0.75 and scores.bar < 1": false}, {"scores.foo + 0.5 > 0.75 and scores.bar < 1": false}, {"scores.foo + 0.5 > 0.75 and scores.bar < 1": false}, {"scores.foo + 0.5 > 0.75 and scores.bar < 1": false}, {"scores.foo + 0.5 > 0.75 and scores.bar < 1": false}], "skip": false}, {"error": null, "query": "select: scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2", "result_rows": [{"scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2": false}, {"scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2": false}, {"scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2": false}, {"scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2": false}, {"scores.foo * 2 < 0.5 or scores.bar / 2 > 0.2": true}], "skip": false}, {"error": null, "query": "-- filters with boolean ops\nselect: id | filter: true and true", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: true or false", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: 1 > 0 and 2 < 3", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: (1 + 2) > 2 or (2 * 2) < 5", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo > 0.5 and scores.bar < 0.75", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.foo < 0.3 or scores.bar > 0.4", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "uuid3"}], "skip": false}, {"error": null, "query": "-- constant folding\nselect: id, scores.foo | filter: scores.foo = 0.05 * 5", "result_rows": [{"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: 2 - 1 = scores.foo", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 0.0 + 0.0", "result_rows": [{"foo": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 0.3 + 0.7", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 0.3 + 0.8", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 1.0 * 3.0", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: 0.5 * 1 = scores.bar", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, counts.foo | filter: counts.foo = (-7 + 0) * 1", "result_rows": [{"foo": -7, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar = 0", "result_rows": [{"bar": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar = 0.0", "result_rows": [{"bar": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar = 4 - 2", "result_rows": [{"bar": 2, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar = 1.2 + 0.8", "result_rows": [{"bar": 2, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, counts.bar | filter: counts.bar = 1.0 * 2.0", "result_rows": [{"bar": 2, "id": "uuid3"}], "skip": false}, {"error": null, "query": "-- booleans and valid strings cast to numbers\nselect: id, scores.foo | filter: scores.foo = \"0.25\"", "result_rows": [{"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = true", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = false", "result_rows": [{"foo": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = true + false", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = true * \"2\" - \"1.75\"", "result_rows": [{"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = false + 0.25", "result_rows": [{"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = \"-1\" + true + true", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "-- invalid strings treated as null in arithmetic expressions\nselect: id | filter: (\"abc\" + 1) is null", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: (\"123abc\" * 2) is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: (true + \"xyz\") is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: (\"\" - 1) is not null", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: (\" \" * 2) is null", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}]