[{"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | measures: sum(value)", "result_rows": [{"score": {"bar": {"sum(value)": 0.5}, "foo": {"sum(value)": 1.25}}, "sum(value)": 1.75}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | dimensions: id | measures: sum(value)", "result_rows": [{"id": "asf-dfa", "score": {"foo": {"sum(value)": 1}}, "sum(value)": 1}, {"id": "dfa-asf", "score": {"bar": {"sum(value)": 0.5}, "foo": {"sum(value)": 0}}, "sum(value)": 0.5}, {"id": "uuid3", "score": {"foo": {"sum(value)": 0.25}}, "sum(value)": 0.25}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | pivot: score | measures: sum(value)+1", "result_rows": [{"score": {"bar": {"sum(value)+1": 1.5}, "foo": {"sum(value)+1": 2.25}}, "sum(value)+1": 2.75}], "skip": false}]