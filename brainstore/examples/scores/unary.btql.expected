[{"error": null, "query": "-- selects\nselect: null, -null, not null", "result_rows": [null, null, null, null, null], "skip": false}, {"error": null, "query": "select: not true, not false", "result_rows": [{"not false": true, "not true": false}, {"not false": true, "not true": false}, {"not false": true, "not true": false}, {"not false": true, "not true": false}, {"not false": true, "not true": false}], "skip": false}, {"error": null, "query": "select: not scores.foo", "result_rows": [null, null, {"not scores.foo": false}, {"not scores.foo": false}, {"not scores.foo": true}], "skip": false}, {"error": null, "query": "select: -scores.bar", "result_rows": [null, null, null, null, {"-scores.bar": -0.5}], "skip": false}, {"error": null, "query": "select: -counts.foo", "result_rows": [null, null, {"-counts.foo": -10}, {"-counts.foo": -3}, {"-counts.foo": 7}], "skip": false}, {"error": null, "query": "select: scores.bar is null", "result_rows": [{"scores.bar is null": false}, {"scores.bar is null": true}, {"scores.bar is null": true}, {"scores.bar is null": true}, {"scores.bar is null": true}], "skip": false}, {"error": null, "query": "select: scores.bar is not null", "result_rows": [{"scores.bar is not null": false}, {"scores.bar is not null": false}, {"scores.bar is not null": false}, {"scores.bar is not null": false}, {"scores.bar is not null": true}], "skip": false}, {"error": null, "query": "select: counts.bar is null", "result_rows": [{"counts.bar is null": false}, {"counts.bar is null": false}, {"counts.bar is null": true}, {"counts.bar is null": true}, {"counts.bar is null": true}], "skip": false}, {"error": null, "query": "select: counts.bar is not null", "result_rows": [{"counts.bar is not null": false}, {"counts.bar is not null": false}, {"counts.bar is not null": false}, {"counts.bar is not null": true}, {"counts.bar is not null": true}], "skip": false}, {"error": null, "query": "select: scores.foo = 0.25", "result_rows": [null, null, {"scores.foo = 0.25": false}, {"scores.foo = 0.25": false}, {"scores.foo = 0.25": true}], "skip": false}, {"error": null, "query": "select: counts.foo = -7", "result_rows": [null, null, {"counts.foo = -7": false}, {"counts.foo = -7": false}, {"counts.foo = -7": true}], "skip": false}, {"error": null, "query": "select: scores.foo > -1", "result_rows": [null, null, {"scores.foo > -1": true}, {"scores.foo > -1": true}, {"scores.foo > -1": true}], "skip": false}, {"error": null, "query": "select: not (scores.foo = 0.75) as value", "result_rows": [null, null, {"value": true}, {"value": true}, {"value": true}], "skip": false}, {"error": null, "query": "select: not (counts.foo = -7) as value", "result_rows": [null, null, {"value": false}, {"value": true}, {"value": true}], "skip": false}, {"error": null, "query": "select: not (counts.foo < 10) as value", "result_rows": [null, null, {"value": false}, {"value": false}, {"value": true}], "skip": false}, {"error": null, "query": "-- filters\nselect: -2 | filter: not true", "result_rows": [], "skip": false}, {"error": null, "query": "select: -2 | filter: not false", "result_rows": [{"-2": -2}, {"-2": -2}, {"-2": -2}, {"-2": -2}, {"-2": -2}], "skip": false}, {"error": null, "query": "select: * | filter: not (scores.foo = 0)", "result_rows": [{"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}], "skip": false}, {"error": null, "query": "select: * | filter: not (scores.bar = 0.5)", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: -scores.foo", "result_rows": [{"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}], "skip": false}, {"error": null, "query": "select: * | filter: -scores.bar", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}], "skip": false}, {"error": null, "query": "-- isnull, isnotnull\nselect: * | filter: null is null", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid4", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: * | filter: scores.bar is null", "result_rows": [{"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid4", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: * | filter: scores.bar is not null", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}], "skip": false}, {"error": null, "query": "select: * | filter: counts.bar is null", "result_rows": [{"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid4", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: * | filter: counts.bar is not null", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}, {"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}], "skip": false}, {"error": null, "query": "-- combos\nselect: * | filter: not not false", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: not (scores.bar is null)", "result_rows": [{"counts": {"bar": 0, "foo": 3, "huge": 9223372036854775807}, "id": "dfa-asf", "metadata": {"nullable": false, "split": "train"}, "scores": {"bar": 0.5, "foo": 0}, "tags": ["a", "c"]}], "skip": false}, {"error": null, "query": "select: * | filter: not (scores.bar is not null)", "result_rows": [{"counts": {"bar": 2, "foo": -7, "huge": -3}, "id": "uuid3", "metadata": {"split": "test"}, "scores": {"foo": 0.25}, "tags": ["d"]}, {"counts": {"foo": 10, "huge": 5}, "id": "asf-dfa", "metadata": {"caps": "HELLO WORLD", "description": "a red car", "nullable": true, "split": "train"}, "scores": {"foo": 1}, "tags": ["a", "b"]}, {"id": "uuid4", "metadata": {"array": [1, 2, 3], "caps": "WoRLD HElLO", "split": "test"}}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "-- json nulls\nselect: metadata.nullable | filter: metadata.nullable is null", "result_rows": [null, null, null], "skip": false}, {"error": null, "query": "select: metadata.nullable | filter: metadata.nullable is not null", "result_rows": [{"nullable": false}, {"nullable": true}], "skip": false}]