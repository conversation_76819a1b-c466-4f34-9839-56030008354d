[{"error": null, "query": "select: id, tags includes 'a', not tags includes 'a', tags includes ['d'], tags includes ['a', 'b'], tags includes ['b', 'a'], tags includes ['a', 'b', 'c']", "result_rows": [{"id": "asf-dfa", "not tags includes 'a'": false, "tags includes 'a'": true, "tags includes ['a', 'b', 'c']": false, "tags includes ['a', 'b']": true, "tags includes ['b', 'a']": true, "tags includes ['d']": false}, {"id": "dfa-asf", "not tags includes 'a'": false, "tags includes 'a'": true, "tags includes ['a', 'b', 'c']": false, "tags includes ['a', 'b']": false, "tags includes ['b', 'a']": false, "tags includes ['d']": false}, {"id": "uuid3", "not tags includes 'a'": true, "tags includes 'a'": false, "tags includes ['a', 'b', 'c']": false, "tags includes ['a', 'b']": false, "tags includes ['b', 'a']": false, "tags includes ['d']": true}, {"id": "uuid4", "not tags includes 'a'": true, "tags includes 'a'": false, "tags includes ['a', 'b', 'c']": false, "tags includes ['a', 'b']": false, "tags includes ['b', 'a']": false, "tags includes ['d']": false}, {"id": "uuid5", "not tags includes 'a'": true, "tags includes 'a'": false, "tags includes ['a', 'b', 'c']": false, "tags includes ['a', 'b']": false, "tags includes ['b', 'a']": false, "tags includes ['d']": false}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes 'a'", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes 'b'", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes 'c'", "result_rows": [{"id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes ['d']", "result_rows": [{"id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes ['a', 'b']", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes ['b', 'a']", "result_rows": [{"id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id | filter: tags includes ['a', 'b', 'c']", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, metadata.array includes 1, metadata.array includes 12, metadata.array includes [2], metadata.array includes [3, 2, 1], metadata.array includes [12]", "result_rows": [{"id": "asf-dfa", "metadata.array includes 1": false, "metadata.array includes 12": false, "metadata.array includes [12]": false, "metadata.array includes [2]": false, "metadata.array includes [3, 2, 1]": false}, {"id": "dfa-asf", "metadata.array includes 1": false, "metadata.array includes 12": false, "metadata.array includes [12]": false, "metadata.array includes [2]": false, "metadata.array includes [3, 2, 1]": false}, {"id": "uuid3", "metadata.array includes 1": false, "metadata.array includes 12": false, "metadata.array includes [12]": false, "metadata.array includes [2]": false, "metadata.array includes [3, 2, 1]": false}, {"id": "uuid4", "metadata.array includes 1": true, "metadata.array includes 12": false, "metadata.array includes [12]": false, "metadata.array includes [2]": true, "metadata.array includes [3, 2, 1]": true}, {"id": "uuid5", "metadata.array includes 1": false, "metadata.array includes 12": false, "metadata.array includes [12]": false, "metadata.array includes [2]": false, "metadata.array includes [3, 2, 1]": false}], "skip": false}, {"error": null, "query": "select: id | filter: metadata.array includes 1", "result_rows": [{"id": "uuid4"}], "skip": false}, {"error": null, "query": "select: id | filter: metadata.array includes 12", "result_rows": [], "skip": false}, {"error": null, "query": "select: id | filter: metadata.array includes [2]", "result_rows": [{"id": "uuid4"}], "skip": false}, {"error": null, "query": "select: id | filter: metadata.array includes [3, 2, 1]", "result_rows": [{"id": "uuid4"}], "skip": false}, {"error": null, "query": "select: id | filter: metadata.array includes [12]", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, 'a' includes tags, not 'a' includes tags, 'd' includes tags, ['d'] includes tags, ['a', 'b'] includes tags, ['b', 'a'] includes tags, ['a', 'b', 'c'] includes tags, ['a', 'b', 'c', 'd'] includes tags", "result_rows": [{"'a' includes tags": false, "'d' includes tags": false, "['a', 'b', 'c', 'd'] includes tags": true, "['a', 'b', 'c'] includes tags": true, "['a', 'b'] includes tags": false, "['b', 'a'] includes tags": false, "['d'] includes tags": false, "id": "dfa-asf", "not 'a' includes tags": true}, {"'a' includes tags": false, "'d' includes tags": false, "['a', 'b', 'c', 'd'] includes tags": true, "['a', 'b', 'c'] includes tags": true, "['a', 'b'] includes tags": true, "['b', 'a'] includes tags": true, "['d'] includes tags": false, "id": "asf-dfa", "not 'a' includes tags": true}, {"'a' includes tags": false, "'d' includes tags": true, "['a', 'b', 'c', 'd'] includes tags": true, "['a', 'b', 'c'] includes tags": false, "['a', 'b'] includes tags": false, "['b', 'a'] includes tags": false, "['d'] includes tags": true, "id": "uuid3", "not 'a' includes tags": true}, {"'a' includes tags": true, "'d' includes tags": true, "['a', 'b', 'c', 'd'] includes tags": true, "['a', 'b', 'c'] includes tags": true, "['a', 'b'] includes tags": true, "['b', 'a'] includes tags": true, "['d'] includes tags": true, "id": "uuid4", "not 'a' includes tags": false}, {"'a' includes tags": true, "'d' includes tags": true, "['a', 'b', 'c', 'd'] includes tags": true, "['a', 'b', 'c'] includes tags": true, "['a', 'b'] includes tags": true, "['b', 'a'] includes tags": true, "['d'] includes tags": true, "id": "uuid5", "not 'a' includes tags": false}], "skip": false}, {"error": null, "query": "select: id | filter: 'a' includes tags", "result_rows": [{"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: 'd' includes tags", "result_rows": [{"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: ['d'] includes tags", "result_rows": [{"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: ['a', 'b'] includes tags", "result_rows": [{"id": "asf-dfa"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: ['b', 'a'] includes tags", "result_rows": [{"id": "asf-dfa"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: ['a', 'b', 'c'] includes tags", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: ['a', 'b', 'c', 'd'] includes tags", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id, 1 includes metadata.array, [2] includes metadata.array, [3, 2, 1] includes metadata.array, [4, 3, 2, 1] includes metadata.array", "result_rows": [{"1 includes metadata.array": false, "[2] includes metadata.array": false, "[3, 2, 1] includes metadata.array": true, "[4, 3, 2, 1] includes metadata.array": true, "id": "uuid4"}, {"1 includes metadata.array": true, "[2] includes metadata.array": true, "[3, 2, 1] includes metadata.array": true, "[4, 3, 2, 1] includes metadata.array": true, "id": "asf-dfa"}, {"1 includes metadata.array": true, "[2] includes metadata.array": true, "[3, 2, 1] includes metadata.array": true, "[4, 3, 2, 1] includes metadata.array": true, "id": "dfa-asf"}, {"1 includes metadata.array": true, "[2] includes metadata.array": true, "[3, 2, 1] includes metadata.array": true, "[4, 3, 2, 1] includes metadata.array": true, "id": "uuid3"}, {"1 includes metadata.array": true, "[2] includes metadata.array": true, "[3, 2, 1] includes metadata.array": true, "[4, 3, 2, 1] includes metadata.array": true, "id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: 1 includes metadata.array", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: [2] includes metadata.array", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: [3, 2, 1] includes metadata.array", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "select: id | filter: [4, 3, 2, 1] includes metadata.array", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}]