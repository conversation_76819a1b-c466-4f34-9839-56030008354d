[{"error": null, "query": "select: id, scores.foo | filter: scores.foo = 1", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo = 0", "result_rows": [{"foo": 0, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar = 0.5", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar = 0.25", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo >= 0.25", "result_rows": [{"foo": 0.25, "id": "uuid3"}, {"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo <= 0.25", "result_rows": [{"foo": 0, "id": "dfa-asf"}, {"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo > 0.25", "result_rows": [{"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | filter: scores.foo < 1", "result_rows": [{"foo": 0, "id": "dfa-asf"}, {"foo": 0.25, "id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar >= 0.5", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar <= 0.5", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar > 0.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.bar | filter: scores.bar < 0.5", "result_rows": [], "skip": false}, {"error": null, "query": "select: id, scores.foo | sort: scores.foo, id | limit: 3", "result_rows": [{"foo": 0, "id": "dfa-asf"}, {"foo": 0.25, "id": "uuid3"}, {"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.foo | sort: scores.foo desc, id | limit: 3", "result_rows": [{"id": "uuid4"}, {"id": "uuid5"}, {"foo": 1, "id": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | sort: scores.bar, id | limit: 3", "result_rows": [{"bar": 0.5, "id": "dfa-asf"}, {"id": "asf-dfa"}, {"id": "uuid3"}], "skip": false}, {"error": null, "query": "select: id, scores.bar | sort: scores.bar desc, id | limit: 3", "result_rows": [{"id": "asf-dfa"}, {"id": "uuid3"}, {"id": "uuid4"}], "skip": false}]