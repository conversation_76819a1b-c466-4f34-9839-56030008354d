select: coalesce(metadata.nullable, 'bar');
select: coalesce(metadata.nullable, metadata.split);
select: coalesce(metadata.nullable, scores.foo);

select: coalesce(null, 1);
select: coalesce(1, 2);
select: coalesce(null, null);
select: coalesce(1.5, 2.5);
select: coalesce(scores.bar, 0.5);
select: coalesce(counts.foo, -1);
select: coalesce(metadata.nullable, false);
select: coalesce(scores.bar, scores.foo);
select: coalesce(counts.bar, counts.foo);
select: coalesce(scores.bar > 0.5, true);
select: coalesce(scores.bar > 0.5, false);

select: coalesce();

select: coalesce(null);
select: coalesce(1);
select: coalesce('test');
select: coalesce(true);
select: coalesce(scores.foo);
select: coalesce(metadata.nullable);

select: coalesce(null, null, 1);
select: coalesce(null, 2, 3);
select: coalesce(1, 2, 3);
select: coalesce(null, null, null);
select: coalesce(null, null, 'default');
select: coalesce(metadata.nullable, scores.foo, 'fallback');
select: coalesce(null, metadata.split, scores.bar, 0.5);
select: coalesce(scores.bar, counts.foo, metadata.nullable, 'final');

select: coalesce(null, null, null, 42);
select: coalesce(null, null, null, null);
select: coalesce(null, scores.foo, null, counts.bar, 'default');
select: coalesce(metadata.nullable, null, scores.bar, null, counts.foo, 'last');

select: coalesce(null, 1, 'string', true);
select: coalesce(null, null, 1.5, 'backup', false);

select: upper(metadata.split);
select: upper(null);
select: upper();
select: metadata.nullable, upper(metadata.nullable);
select: upper(1);
select: upper(true);
select: upper(month('2024-03-01T00:00:00Z' - interval 1 day));
select: upper(metadata.split, metadata.caps);

select: lower(metadata.caps);
select: lower(null);
select: lower();
select: lower(metadata.nullable);
select: lower(1);
select: lower(true);
select: lower(month('2024-03-01T00:00:00Z' - interval 1 day));
select: lower(metadata.split, metadata.caps);

select: concat(metadata.split, '.', metadata.nullable, '.', metadata.caps);
select: concat();
select: concat(metadata.nullable);
select: concat(null, null);
select: concat(1, true, 2.5, month('2024-03-01T00:00:00Z' - interval 1 day));

select: nullif(1, 1);
select: nullif(1, 2);
select: nullif(null, 1);
select: nullif(1, null);
select: nullif(null, null);
select: nullif("test", "test");
select: nullif(scores.foo, 0.25);
select: nullif(counts.foo, -7);
select: nullif(metadata.nullable, true);
select: nullif(scores.foo, scores.bar);
select: nullif(counts.foo, counts.bar);
select: nullif(counts.foo < 0.5, true);
select: nullif(counts.foo < 0.5, false);

select: id, scores.bar | filter: coalesce(scores.bar, 0) > 0.25;
select: id, scores.foo | filter: nullif(scores.foo, 0.25) is null;
select: id, scores.foo | filter: nullif(scores.foo, 0.25) is not null;
select: id, counts.bar | filter: coalesce(counts.bar, -1) >= 0;
select: id, metadata.nullable | filter: nullif(metadata.nullable, true) is null;

select: id, scores.bar | filter: concat('hello', ' ', 'world') = 'hello world';
select: id, scores.bar | filter: lower('WORLD') = 'world';
select: id, scores.bar | filter: upper('hello') = 'HELLO';

select: greatest(1);
select: greatest(1, 2);
select: greatest(2, 1);
select: greatest(1.5, 2.5);
select: greatest(2.5, 1.5);
select: greatest(1, null);
select: greatest(null, 1);
select: greatest(null, null);
select: scores.foo, greatest(scores.foo, 0.25);
select: scores.foo, greatest(0.25, scores.foo);
select: scores.foo, greatest(scores.foo, null);
select: scores.foo, greatest(null, scores.foo);
select: counts.foo, greatest(counts.foo, -7);
select: counts.foo, greatest(-7, counts.foo);
select: counts.foo, greatest(counts.foo, null);
select: counts.foo, greatest(null, counts.foo);
select: scores.foo, scores.bar, greatest(scores.foo, scores.bar);
select: counts.foo, counts.bar, greatest(counts.foo, counts.bar);
select: greatest(1, 2, 3);
select: greatest(3, 2, 1);
select: greatest(1, null, 3);
select: greatest(null, 2, 3);
select: greatest(1, 2, null);
select: greatest(1.5, 2.5, 3.5);
select: scores.foo, scores.bar, greatest(scores.foo, scores.bar, 0.5);
select: scores.foo, scores.bar, greatest(scores.foo, null, 0.5);
select: counts.foo, counts.bar, greatest(counts.foo, counts.bar, -1);
select: counts.foo, counts.bar, greatest(null, counts.bar, -1);

select: least(1);
select: least(1, 2);
select: least(2, 1);
select: least(1.5, 2.5);
select: least(2.5, 1.5);
select: least(1, null);
select: least(null, 1);
select: least(null, null);
select: scores.foo, least(scores.foo, 0.25);
select: scores.foo, least(0.25, scores.foo);
select: scores.foo, least(scores.foo, null);
select: scores.foo, least(null, scores.foo);
select: counts.foo, least(counts.foo, -7);
select: counts.foo, least(-7, counts.foo);
select: counts.foo, least(counts.foo, null);
select: counts.foo, least(null, counts.foo);
select: scores.foo, scores.bar, least(scores.foo, scores.bar);
select: counts.foo, counts.bar, least(counts.foo, counts.bar);
select: least(1, 2, 3);
select: least(3, 2, 1);
select: least(1, null, 3);
select: least(null, 2, 3);
select: least(1, 2, null);
select: least(1.5, 2.5, 3.5);
select: scores.foo, scores.bar, least(scores.foo, scores.bar, 0.5);
select: scores.foo, scores.bar, least(scores.foo, null, 0.5);
select: counts.foo, counts.bar, least(counts.foo, counts.bar, -1);
select: counts.foo, counts.bar, least(null, counts.bar, -1);
