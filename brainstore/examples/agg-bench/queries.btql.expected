[{"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | measures: count(1)", "result_rows": [{"count(1)": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | measures: count(is_root)", "result_rows": [{"count(is_root)": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) | measures: count(1)", "result_rows": [{"count(1)": 1, "day(created)": "2025-07-22T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-25T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-27T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-28T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-24T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-26T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) | measures: count(1) | filter: created >= '2025-07-10'", "result_rows": [{"count(1)": 1, "day(created)": "2025-07-22T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-25T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-27T00:00:00Z"}, {"count(1)": 1, "day(created)": "2025-07-28T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-24T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-26T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c | sort: d", "result_rows": [{"c": 10, "d": "foo"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d", "result_rows": [{"c": 10, "d": "foo", "duration": 2.665705, "s": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: experiment_id as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d", "result_rows": [{"c": 10, "d": "singleton", "duration": 2.665705, "s": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: metrics.model as model | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: model", "result_rows": [{"c": 10, "duration": 2.665705, "s": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: metadata.model as model | measures: count(error) AS c | sort: model", "result_rows": [{"c": 0, "model": "claude-3-opus"}, {"c": 1, "model": "claude-3-sonnet"}, {"c": 0, "model": "gpt-3.5-turbo"}, {"c": 2}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metadata.model = 'claude-3-haiku' ? 1 : 0) AS haiku_spans", "result_rows": [{"haiku_spans": 0, "time": "2025-07-22T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-24T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-25T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-26T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-27T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-28T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum('claude-3-haiku' = metadata.model ? 1 : 0) AS haiku_spans", "result_rows": [{"haiku_spans": 0, "time": "2025-07-22T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-24T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-25T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-26T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-27T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-28T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test a field that doesn't exist\nfrom: experiment('singleton') | dimensions: metadata.foo as model | measures: count(1) AS c | sort: model", "result_rows": [{"c": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test a field that doesn't exist\nfrom: experiment('singleton') | measures: sum(metrics.prompt_cache_creation_tokens + metadata.foo)", "result_rows": [null], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 1\n*/\n-- Test an object field. Since this falls back to dynamic group by, it should have a dynamic node\nfrom: experiment('singleton') | dimensions: metadata as model | measures: count(1) AS c", "result_rows": [{"c": 1, "model": {"model": "claude-3-opus", "request_id": "654c9c87-5b63-48a8-afd6-ef795b41561e"}}, {"c": 1, "model": {"model": "claude-3-sonnet", "request_id": "05cc9eb3-ef4f-4202-88da-f5b492a3c7a3"}}, {"c": 1, "model": {"model": "claude-3-sonnet", "request_id": "345f2a43-885f-478a-a4de-332f69036862"}}, {"c": 1, "model": {"model": "gpt-3.5-turbo", "request_id": "b0262c0d-b0ff-4c65-9b74-c3b2915765ae"}}, {"c": 1, "model": {"request_id": "40036fe9-e081-4353-94f1-8e37f3b4808e"}}, {"c": 1, "model": {"request_id": "7f2718e7-fb4a-4be9-ab15-820573fcd840"}}, {"c": 1, "model": {"request_id": "ac9a21e5-98f6-4143-9fdb-5fdd3459f246"}}, {"c": 1, "model": {"request_id": "acb57796-fd78-49bf-8664-74a2f3237a42"}}, {"c": 1, "model": {"request_id": "b0c087cc-69c7-4c97-8fe6-0f05ece864a1"}}, {"c": 1, "model": {"request_id": "b6b6c144-162b-421b-a8da-c2b94e25c7d5"}}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test pagination key\nmeasures: max(_pagination_key) | from: experiment(\"singleton\")", "result_rows": [{"max(_pagination_key)": "p00000000000000655369"}], "skip": false}, {"error": "btql bind failed: Error: Failed to parse query (expected 'dimensions, pivot, unpivot, measures, select, infer, filter, from, sort, limit, cursor, comparison_key, weighted_scores, custom_columns, preview_length') at line 5, col 1 at dimension near '\ndimension: _paginat'", "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test pagination key\ndimension: _pagination_key | measures: count(1) | from: experiment(\"singleton\") | sort: _pagination_key", "result_rows": [], "skip": false}]