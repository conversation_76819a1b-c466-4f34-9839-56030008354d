/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | measures: count(1);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | measures: count(is_root);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) | measures: count(1);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) | measures: count(1) | filter: created >= '2025-07-10';

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c | sort: d;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: experiment_id as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: metrics.model as model | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: model;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: metadata.model as model | measures: count(error) AS c | sort: model;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metadata.model = 'claude-3-haiku' ? 1 : 0) AS haiku_spans;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
from: experiment('singleton') | dimensions: day(created) AS time | measures: sum('claude-3-haiku' = metadata.model ? 1 : 0) AS haiku_spans;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
-- Test a field that doesn't exist
from: experiment('singleton') | dimensions: metadata.foo as model | measures: count(1) AS c | sort: model;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
-- Test a field that doesn't exist
from: experiment('singleton') | measures: sum(metrics.prompt_cache_creation_tokens + metadata.foo);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 1
*/
-- Test an object field. Since this falls back to dynamic group by, it should have a dynamic node
from: experiment('singleton') | dimensions: metadata as model | measures: count(1) AS c;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
-- Test pagination key
measures: max(_pagination_key) | from: experiment("singleton");

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Dynamic segment collector")] | length == 0
*/
-- Test pagination key
dimension: _pagination_key | measures: count(1) | from: experiment("singleton") | sort: _pagination_key;
