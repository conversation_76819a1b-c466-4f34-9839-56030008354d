#!/usr/bin/env python3

import argparse
import json
import random
import shutil
import subprocess
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path

_XACT_ID = 1


def generate_row(include_xact_id):
    """Generate a single row with id, created timestamp, and is_root flag"""
    global _XACT_ID
    # Generate timestamps for metrics
    start_time = time.time() - random.uniform(0, 10)  # Start 0-10 seconds ago
    end_time = start_time + random.uniform(0.1, 5.0)  # Duration 0.1-5 seconds

    # Generate token counts
    prompt_tokens = random.randint(50, 2000)
    completion_tokens = random.randint(20, 1000)
    total_tokens = prompt_tokens + completion_tokens

    # List of popular LLM models
    models = [
        "gpt-4",
        "gpt-3.5-turbo",
        "claude-3-opus",
        "claude-3-sonnet",
        "claude-3-haiku",
        "llama-3-70b",
        "mixtral-8x7b",
    ]

    # Available tags
    all_tags = ["production", "testing", "evaluation", "experiment"]
    # Select 1-3 random tags
    num_tags = random.randint(1, 3)
    selected_tags = random.sample(all_tags, num_tags)

    error = None
    error_rand = random.random()
    if error_rand < 0.1:  # 10% chance of string error
        error = "error!"
    elif error_rand < 0.2:  # 10% chance of object error
        error = {"error": "error!", "stack": "stack!"}

    ret = {
        "id": str(uuid.uuid4()),
        "created": (datetime.now() - timedelta(days=random.uniform(0, 7))).isoformat() + "Z",
        "is_root": random.random() < 0.1,  # 1/10 chance of being True
        "experiment_id": "singleton",
        "metrics": {
            "tokens": total_tokens,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "start": start_time,
            "end": end_time,
        },
        "metadata": {
            "model": random.choice(models) if random.random() <= 0.5 else None,
            "request_id": str(uuid.uuid4()),
        },
        "tags": selected_tags,
        "error": error,
    }

    if include_xact_id:
        ret["_xact_id"] = _XACT_ID
        _XACT_ID += 1

    return ret


def generate_files(num_files, rows_per_file, output_dir="data"):
    """Generate N files with M rows each as JSONL files"""

    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    print(f"Generating {num_files} files with {rows_per_file} rows each...")

    for file_idx in range(num_files):
        filename = f"data_{file_idx:04d}.jsonl"
        filepath = output_path / filename

        with filepath.open("w") as f:
            for _ in range(rows_per_file):
                row = generate_row(include_xact_id=False)
                f.write(json.dumps(row) + "\n")

        print(f"Created {filepath}")

    print(f"Done! Generated {num_files} files with {rows_per_file} rows each in {output_dir}/")


def main():
    parser = argparse.ArgumentParser(description="Generate N JSONL files with M rows each for Braintrust data testing")
    parser.add_argument("-n", "--num-files", type=int, default=4000, help="Number of files to generate")
    parser.add_argument("-m", "--rows-per-file", type=int, default=1000, help="Number of rows per file")
    parser.add_argument("-o", "--output-dir", type=str, default="data", help="Output directory (default: data)")
    parser.add_argument("--test", action="store_true", help="Generate only 10 test events and save to events.jsonl")

    args = parser.parse_args()

    if args.test:
        # Generate 10 test events and write to events.jsonl
        with open("events.jsonl", "w") as f:
            for _ in range(10):
                row = generate_row(include_xact_id=True)
                f.write(json.dumps(row) + "\n")
        print("Generated 10 test events in events.jsonl")
        return

    try:
        shutil.rmtree(args.output_dir)
    except FileNotFoundError:
        pass

    generate_files(args.num_files, args.rows_per_file, args.output_dir)

    try:
        shutil.rmtree("index")
    except FileNotFoundError:
        pass

    # cargo run --release --bin brainstore -- wal load data/* --normalize
    subprocess.run(
        "cargo run --release --bin brainstore -- wal insert -c bench.yaml data/* --normalize --default-object-id experiment:singleton",
        shell=True,
    )
    subprocess.run(
        "cargo run --release --bin brainstore -- wal process -c bench.yaml experiment:singleton", shell=True
    )

    # Open the global store data and gut out the row id index to make the queries faster
    with open("index/bench/metadata/global_store_data", "r") as f:
        d = json.load(f)

    d["row_id_to_segment_id"] = {}
    d["root_span_id_to_segment_id"] = {}

    with open("index/bench/metadata/global_store_data", "w") as f:
        json.dump(d, f)

    subprocess.run("cargo run --release --bin brainstore -- wal compact -c bench.yaml --all", shell=True)
    subprocess.run("cargo run --release --bin brainstore -- index merge -c bench.yaml --all", shell=True)


if __name__ == "__main__":
    main()
