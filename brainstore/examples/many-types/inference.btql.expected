[{"error": null, "query": "infer: *", "result_rows": [{"name": ["arr"], "top_values": [{"count": 1, "value": "a"}, {"count": 1, "value": "b"}, {"count": 1, "value": 1}, {"count": 1, "value": 2}, {"count": 1, "value": 3}, {"count": 1, "value": false}, {"count": 1, "value": true}], "type": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, {"name": ["bool"], "top_values": [{"count": 2, "value": false}, {"count": 2, "value": true}], "type": {"type": "boolean"}}, {"name": ["obj", "flag"], "top_values": [{"count": 1, "value": true}], "type": {"type": "boolean"}}, {"name": ["float"], "top_values": [{"count": 1, "value": -1.414}, {"count": 1, "value": 0}, {"count": 1, "value": 2.718}, {"count": 1, "value": 3.14}], "type": {"type": "number"}}, {"name": ["unknown", "foo"], "top_values": [{"count": 1, "value": "bar"}], "type": {"type": "string"}}, {"name": ["id"], "top_values": [{"count": 1, "value": "row1"}, {"count": 1, "value": "row2"}, {"count": 1, "value": "row3"}, {"count": 1, "value": "row4"}, {"count": 1, "value": "row5"}, {"count": 1, "value": "row6"}, {"count": 1, "value": "row7"}, {"count": 1, "value": "row8"}], "type": {"type": "string"}}, {"name": ["int"], "top_values": [{"count": 1, "value": -7}, {"count": 1, "value": 0}, {"count": 1, "value": 1000}, {"count": 1, "value": 42}], "type": {"type": "integer"}}, {"name": ["obj", "name"], "top_values": [{"count": 1, "value": "foo"}], "type": {"type": "string"}}, {"name": ["obj", "x"], "top_values": [{"count": 1, "value": "word"}, {"count": 1, "value": 1}], "type": {"anyOf": [{"type": "string"}, {"type": "number"}]}}, {"name": ["obj", "y"], "top_values": [{"count": 1, "value": 2}], "type": {"type": "number"}}, {"name": ["str"], "top_values": [{"count": 1, "value": "alpha"}, {"count": 1, "value": "beta"}, {"count": 1, "value": "omega"}, {"count": 1, "value": "zebra"}, {"count": 1, "value": "💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩💩"}], "type": {"type": "string"}}, {"name": ["unknown"], "top_values": [{"count": 1, "value": "a"}, {"count": 1, "value": "b"}, {"count": 1, "value": "poo"}, {"count": 1, "value": 10}, {"count": 1, "value": 3.14}, {"count": 1, "value": 3}, {"count": 1, "value": true}], "type": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}], "skip": false}, {"error": "Unsupported operation: Inference with complex filter", "query": "-- Should error\ninfer: * | filter: int", "result_rows": [], "skip": true}, {"error": null, "query": "-- Should work\ninfer: * | filter: bool", "result_rows": [{"name": ["arr"], "top_values": [{"count": 1, "value": 1}, {"count": 1, "value": 2}, {"count": 1, "value": 3}, {"count": 1, "value": false}, {"count": 1, "value": true}], "type": {"anyOf": [{"type": "number"}, {"type": "boolean"}]}}, {"name": ["bool"], "top_values": [{"count": 2, "value": true}], "type": {"type": "boolean"}}, {"name": ["obj", "flag"], "top_values": [{"count": 1, "value": true}], "type": {"type": "boolean"}}, {"name": ["float"], "top_values": [{"count": 1, "value": -1.414}, {"count": 1, "value": 3.14}], "type": {"type": "number"}}, {"name": ["id"], "top_values": [{"count": 1, "value": "row2"}, {"count": 1, "value": "row4"}], "type": {"type": "string"}}, {"name": ["int"], "top_values": [{"count": 1, "value": 1000}, {"count": 1, "value": 42}], "type": {"type": "integer"}}, {"name": ["obj", "x"], "top_values": [{"count": 1, "value": 1}], "type": {"type": "number"}}, {"name": ["obj", "y"], "top_values": [{"count": 1, "value": 2}], "type": {"type": "number"}}, {"name": ["str"], "top_values": [{"count": 1, "value": "omega"}, {"count": 1, "value": "zebra"}], "type": {"type": "string"}}, {"name": ["unknown"], "top_values": [{"count": 1, "value": 3.14}, {"count": 1, "value": true}], "type": {"anyOf": [{"type": "number"}, {"type": "boolean"}]}}], "skip": false}, {"error": null, "query": "infer: * | filter: bool=false", "result_rows": [{"name": ["arr"], "top_values": [{"count": 1, "value": "a"}, {"count": 1, "value": "b"}], "type": {"type": "string"}}, {"name": ["bool"], "top_values": [{"count": 2, "value": false}], "type": {"type": "boolean"}}, {"name": ["float"], "top_values": [{"count": 1, "value": 0}, {"count": 1, "value": 2.718}], "type": {"type": "number"}}, {"name": ["id"], "top_values": [{"count": 1, "value": "row3"}, {"count": 1, "value": "row5"}], "type": {"type": "string"}}, {"name": ["int"], "top_values": [{"count": 1, "value": -7}, {"count": 1, "value": 0}], "type": {"type": "integer"}}, {"name": ["obj", "name"], "top_values": [{"count": 1, "value": "foo"}], "type": {"type": "string"}}, {"name": ["obj", "x"], "top_values": [{"count": 1, "value": "word"}], "type": {"type": "string"}}, {"name": ["str"], "top_values": [{"count": 1, "value": "alpha"}, {"count": 1, "value": "beta"}], "type": {"type": "string"}}, {"name": ["unknown"], "top_values": [{"count": 1, "value": "poo"}, {"count": 1, "value": 10}], "type": {"anyOf": [{"type": "string"}, {"type": "number"}]}}], "skip": false}]