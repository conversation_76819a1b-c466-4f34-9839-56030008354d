[{"error": null, "query": "select: * | filter: id", "result_rows": [], "skip": false}, {"error": null, "query": "/*!optimizer -- Make sure that the search pushes down an equality on the bool field\n\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.search.TermQuery | .field.name == \"bool\" and .value == true\n\n*/\nselect: * | filter: bool", "result_rows": [{"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}], "skip": false}, {"error": null, "query": "select: * | filter: int", "result_rows": [{"arr": ["a", "b"], "bool": false, "float": 2.718, "id": "row3", "int": -7, "obj": {"name": "foo", "x": "word"}, "str": "alpha", "unknown": 10}, {"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}], "skip": false}, {"error": null, "query": "select: * | filter: float", "result_rows": [{"arr": ["a", "b"], "bool": false, "float": 2.718, "id": "row3", "int": -7, "obj": {"name": "foo", "x": "word"}, "str": "alpha", "unknown": 10}, {"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}], "skip": false}, {"error": null, "query": "select: * | filter: str", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: unknown", "result_rows": [{"arr": ["a", "b"], "bool": false, "float": 2.718, "id": "row3", "int": -7, "obj": {"name": "foo", "x": "word"}, "str": "alpha", "unknown": 10}, {"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}, {"id": "row6", "unknown": ["a", "b", 3]}, {"id": "row7", "unknown": {"foo": "bar"}}], "skip": false}, {"error": null, "query": "select: * | filter: arr", "result_rows": [{"arr": ["a", "b"], "bool": false, "float": 2.718, "id": "row3", "int": -7, "obj": {"name": "foo", "x": "word"}, "str": "alpha", "unknown": 10}, {"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}], "skip": false}, {"error": null, "query": "select: * | filter: obj", "result_rows": [{"arr": ["a", "b"], "bool": false, "float": 2.718, "id": "row3", "int": -7, "obj": {"name": "foo", "x": "word"}, "str": "alpha", "unknown": 10}, {"arr": [1, 2, 3], "bool": true, "float": 3.14, "id": "row2", "int": 42, "obj": {"x": 1, "y": 2}, "str": "zebra", "unknown": true}, {"arr": [true, false], "bool": true, "float": -1.414, "id": "row4", "int": 1000, "obj": {"flag": true}, "str": "omega", "unknown": 3.14}], "skip": false}]