select: * | filter: id;

/*!optimizer -- Make sure that the search pushes down an equality on the bool field

  [.. | objects | select(has("TantivySearch"))] | .[0].TantivySearch.search.TermQuery | .field.name == "bool" and .value == true

*/
select: * | filter: bool;

select: * | filter: int;
select: * | filter: float;
select: * | filter: str;
select: * | filter: unknown;
select: * | filter: arr;
select: * | filter: obj;
