[{"error": null, "query": "select: * | filter: text match \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"hello world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"hello-world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"1\"", "result_rows": [{"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"1.0\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text match 1", "result_rows": [{"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text match 1.0", "result_rows": [{"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"asf\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"asf-dfa\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"dfa-asf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text match \"car bright\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text match \"red car\"", "result_rows": [{"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"hello\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"hello world\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"hello-world\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"1\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"1.0\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match 1", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match 1.0", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"asf\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"asf-dfa\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"dfa-asf\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"car bright\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text not match \"red car\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: NOT (text match \"hello\" AND number > 1)", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: NOT (text match \"hello\" OR number > 1)", "result_rows": [{"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text = \"hello\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text = \"hello world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text = \"hello-world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"hello world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"hello-world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"1\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"1.0\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match 1", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match 1.0", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"asf\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"asf-dfa\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"dfa-asf\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"big cat\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"yellow cat\"", "result_rows": [{"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: object.text = \"hello\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text = \"hello world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text = \"hello-world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text=3", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text=\"3\"", "result_rows": [{"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"hello\" and text match \"world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"hello\" or text match \"world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: text match \"cat\" and text match \"big\" or text match \"yellow\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: (text match \"cat\" and text match \"big\") or text match \"yellow\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"hello\" and object.text match \"world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"hello\" or object.text match \"world\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text match \"cat\" and object.text match \"big\" or object.text match \"yellow\"", "result_rows": [{"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: (object.text match \"cat\" and object.text match \"big\") or object.text match \"yellow\"", "result_rows": [{"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: text = \"hello\" and text = \"world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text = \"hello\" or text = \"world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text = \"hello\" and object.text = \"world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: object.text = \"hello\" or object.text = \"world\"", "result_rows": [], "skip": false}, {"error": null, "query": "select: * | filter: text >= \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text > \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text <= \"hello\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: text < \"hello\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text >= \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text > \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text <= \"hello\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: object.text < \"hello\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: text != \"hello\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: text != \"hello world\"", "result_rows": [{"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}, {"error": null, "query": "select: * | filter: object.text != \"asf\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 1.5, "object": {"number": 1.5, "text": "asf-dfa"}, "text": "asf-dfa"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: object.text != \"asf-dfa\"", "result_rows": [{"number": 1, "object": {"number": 1, "text": "hello world"}, "text": "hello world"}, {"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 4, "object": {"text": "a big yellow cat"}}], "skip": false}, {"error": null, "query": "select: * | filter: text != \"hello world\" and text != \"asf-dfa\"", "result_rows": [{"number": 2, "object": {"number": 2, "text": "3 3"}, "text": "a red car that is 1 bright and % wonderful"}, {"number": 3, "object": {"number": 3, "text": 3}, "text": "a red car that is 1 bright and % wonderful"}, {"text": "mountains.<!-- under the sea !-->"}], "skip": false}]