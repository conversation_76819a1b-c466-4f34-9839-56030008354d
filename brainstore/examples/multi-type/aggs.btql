measures: count(object);
measures: sum(object IS NOT NULL);
measures: sum(object IS NULL);

-- We have messed up semantics for projecting object.foo if it's in an array or not
-- because of columnstore invertibility. When we fix that, we can change events.jsonl
-- to put 'foo' in the array instead of array_foo
measures: count(object.foo);
measures: sum(object.foo IS NOT NULL);
measures: sum(object.foo IS NULL);

-- Make sure normal expressions don't get converted to expect checks
measures: count(object = 42 ? 1 : NULL);

measures: count(object.foo) + count(object.foo);
measures: count(object.foo) + sum(object.foo IS NULL);
