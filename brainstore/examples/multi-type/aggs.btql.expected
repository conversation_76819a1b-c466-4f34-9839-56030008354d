[{"error": null, "query": "measures: count(object)", "result_rows": [{"count(object)": 8}], "skip": false}, {"error": null, "query": "measures: sum(object IS NOT NULL)", "result_rows": [{"sum(object IS NOT NULL)": 8}], "skip": false}, {"error": null, "query": "measures: sum(object IS NULL)", "result_rows": [{"sum(object IS NULL)": 1}], "skip": false}, {"error": null, "query": "-- We have messed up semantics for projecting object.foo if it's in an array or not\n-- because of columnstore invertibility. When we fix that, we can change events.jsonl\n-- to put 'foo' in the array instead of array_foo\nmeasures: count(object.foo)", "result_rows": [{"count(object.foo)": 1}], "skip": false}, {"error": null, "query": "measures: sum(object.foo IS NOT NULL)", "result_rows": [{"sum(object.foo IS NOT NULL)": 1}], "skip": false}, {"error": null, "query": "measures: sum(object.foo IS NULL)", "result_rows": [{"sum(object.foo IS NULL)": 8}], "skip": false}, {"error": null, "query": "-- Make sure normal expressions don't get converted to expect checks\nmeasures: count(object = 42 ? 1 : NULL)", "result_rows": [{"count(object = 42 ? 1 : NULL)": 1}], "skip": false}, {"error": null, "query": "measures: count(object.foo) + count(object.foo)", "result_rows": [{"count(object.foo) + count(object.foo)": 2}], "skip": false}, {"error": null, "query": "measures: count(object.foo) + sum(object.foo IS NULL)", "result_rows": [{"count(object.foo) + sum(object.foo IS NULL)": 9}], "skip": false}]