/*!result --
    length == 1 and .[0].id == "babdc1ac-2c2f-4289-98ff-33549005bc58"
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key desc | limit: 1;
/*!result --
    length == 1
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key desc | limit: 1 | cursor: Z7KHbRAyADM;
/*!result --
    length == 1
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key desc | limit: 1 | cursor: Z7KHbBAwACY;

/*!result -- This actually returns the same row (b/c the root span is the max pagination key), but a different cursor
    length == 1 and .[0].id == "babdc1ac-2c2f-4289-98ff-33549005bc58"
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key asc | limit: 1;
/*!result --
    length == 1
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key asc | limit: 1 | cursor: Z7KHaBATADA;
/*!result --
    length == 1
*/
select: *  | from: experiment('singleton') summary | sort: _pagination_key asc | limit: 1 | cursor: Z7KHaBAUADE;


/*!result --
    length == 6
*/
select: id, _pagination_key | from: experiment('singleton') traces | sort: _pagination_key desc | limit: 1

/*!result --
    length == 6
*/
select: id, _pagination_key | from: experiment('singleton') traces | sort: _pagination_key asc | limit: 1
