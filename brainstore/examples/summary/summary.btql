select: * | from: experiment('singleton') summary | comparison_key: input;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: Levenshtein = 1;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality > 0.5;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality IS NULL;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Factuality IS NOT NULL;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: scores.Avg > 0.5;


select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | filter: metrics.duration > 1.5;

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | custom_columns: metadata.category as Category
    | filter: Category = "cat";

select: *
    | from: experiment('singleton') summary
    | comparison_key: input
    | weighted_scores: (Factuality+Levenshtein)/2 as Avg
    | custom_columns: metadata.model as Model
    | filter: Model = "gpt-4o";

select: * | from: experiment('singleton') summary | preview_length: 0;

select: * | from: experiment('singleton') summary | preview_length: 2000;
select: * | from: experiment('singleton') summary |  preview_length: -1;

from: experiment('singleton') summary | select: id | filter: tags includes ['greeting'];
from: experiment('singleton') summary | select: id | filter: tags not includes ['greeting'];
