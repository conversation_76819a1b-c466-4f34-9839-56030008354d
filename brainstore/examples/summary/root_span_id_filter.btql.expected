[{"error": null, "query": "-- Tests for filtering by id and root_span_id from experiment('singleton') traces\n-- Using events from brainstore/examples/summary/events.jsonl\n\n-- Valid IDs and Root Span IDs for tests:\n-- valid_id_1 = \"de4890e6-8100-422b-9793-3c0f31c4092a\" (root_span_id: \"6c4053be-dedb-4353-abb7-0fe4039090be\")\n-- valid_rsid_1 = \"6c4053be-dedb-4353-abb7-0fe4039090be\"\n-- valid_id_2 = \"b5866b68-9d13-42ac-8906-ca2db1447820\" (root_span_id: \"967a194a-6e07-4ca5-8b2d-e07771a355bf\")\n-- valid_rsid_2 = \"967a194a-6e07-4ca5-8b2d-e07771a355bf\"\n-- invalid_id = \"00000000-0000-0000-0000-000000000000\"\n-- invalid_rsid = \"11111111-1111-1111-1111-111111111111\"\n\n-- Test 1: Filter by a single valid id\n/*!optimizer -- Check for single id filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"de4890e6-8100-422b-9793-3c0f31c4092a\"] and ( .IdFilter.root_span_ids == null or (.IdFilter.root_span_ids | length == 0) ) ) | length == 1\n*/\n/*!execution -- Expect 1 segment as id exists\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a'", "result_rows": [{"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 2: Filter by a single valid root_span_id\n/*!optimizer -- Check for single root_span_id filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.root_span_ids == [\"6c4053be-dedb-4353-abb7-0fe4039090be\"] and ( .IdFilter.ids == null or (.IdFilter.ids | length == 0) ) ) | length == 1\n*/\n/*!execution -- Expect 1 segment as root_span_id exists\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be'", "result_rows": [{"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 3: OR of two valid ids\n/*!optimizer -- Check for two ids in filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids | length == 2 and contains([\"de4890e6-8100-422b-9793-3c0f31c4092a\"]) and contains([\"b5866b68-9d13-42ac-8906-ca2db1447820\"])) | length == 1\n*/\n/*!execution -- Expect 1 segment as both ids exist\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or id='b5866b68-9d13-42ac-8906-ca2db1447820'", "result_rows": [{"_pagination_key": "p07472183625663184953", "_xact_id": "1000194608726741029", "comparison_key": "165ffd77e654ffbe3e231db9acdb41d219a677ebe919215034fa11e28685484e", "created": "2025-02-17T00:48:39.688Z", "expected": "<PERSON>", "id": "fa5e1133-70c5-40cf-af5a-447b8f7279ef", "input": "Make girl memory director network positive if they anyone life appear bring almost indicate station hair author common north ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.607034, "end": 1739753323.894606, "llm_duration": 1.60023, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 68, "start": 1739753319.688477, "total_tokens": 99}, "output": "A woman reflects on life, navigating memories and experiences, as she connects with others, revealing truths about identity, ...", "root_span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "scores": {"Factuality": 1, "Levenshtein": 0.036585}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}, {"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 4: OR of two valid root_span_ids\n/*!optimizer -- Check for two root_span_ids in filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.root_span_ids | length == 2 and contains([\"6c4053be-dedb-4353-abb7-0fe4039090be\"]) and contains([\"967a194a-6e07-4ca5-8b2d-e07771a355bf\"])) | length == 1\n*/\n/*!execution -- Expect 1 segment as both root_span_ids exist\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be' or root_span_id='967a194a-6e07-4ca5-8b2d-e07771a355bf'", "result_rows": [{"_pagination_key": "p07472183625663184953", "_xact_id": "1000194608726741029", "comparison_key": "165ffd77e654ffbe3e231db9acdb41d219a677ebe919215034fa11e28685484e", "created": "2025-02-17T00:48:39.688Z", "expected": "<PERSON>", "id": "fa5e1133-70c5-40cf-af5a-447b8f7279ef", "input": "Make girl memory director network positive if they anyone life appear bring almost indicate station hair author common north ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.607034, "end": 1739753323.894606, "llm_duration": 1.60023, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 68, "start": 1739753319.688477, "total_tokens": 99}, "output": "A woman reflects on life, navigating memories and experiences, as she connects with others, revealing truths about identity, ...", "root_span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "scores": {"Factuality": 1, "Levenshtein": 0.036585}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}, {"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 5: OR of a valid id and a (different group) valid root_span_id\n/*!optimizer -- Check for id and root_span_id in the same filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"de4890e6-8100-422b-9793-3c0f31c4092a\"] and .IdFilter.root_span_ids == [\"967a194a-6e07-4ca5-8b2d-e07771a355bf\"]) | length == 1\n*/\n/*!execution -- Expect 1 segment as both conditions can find matching events\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='967a194a-6e07-4ca5-8b2d-e07771a355bf'", "result_rows": [{"_pagination_key": "p07472183625663184953", "_xact_id": "1000194608726741029", "comparison_key": "165ffd77e654ffbe3e231db9acdb41d219a677ebe919215034fa11e28685484e", "created": "2025-02-17T00:48:39.688Z", "expected": "<PERSON>", "id": "fa5e1133-70c5-40cf-af5a-447b8f7279ef", "input": "Make girl memory director network positive if they anyone life appear bring almost indicate station hair author common north ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.607034, "end": 1739753323.894606, "llm_duration": 1.60023, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 68, "start": 1739753319.688477, "total_tokens": 99}, "output": "A woman reflects on life, navigating memories and experiences, as she connects with others, revealing truths about identity, ...", "root_span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "scores": {"Factuality": 1, "Levenshtein": 0.036585}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "967a194a-6e07-4ca5-8b2d-e07771a355bf", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}, {"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 6: OR of a valid id and its own root_span_id\n/*!optimizer -- Check for id and its own root_span_id in the same filter\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"de4890e6-8100-422b-9793-3c0f31c4092a\"] and .IdFilter.root_span_ids == [\"6c4053be-dedb-4353-abb7-0fe4039090be\"]) | length == 1\n*/\n/*!execution -- Expect 1 segment\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be'", "result_rows": [{"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 7: OR of a valid id and an invalid root_span_id\n/*!optimizer -- Check for valid id and invalid root_span_id\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"de4890e6-8100-422b-9793-3c0f31c4092a\"] and .IdFilter.root_span_ids == [\"11111111-1111-1111-1111-111111111111\"]) | length == 1\n*/\n/*!execution -- Expect 1 segment due to valid id\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='de4890e6-8100-422b-9793-3c0f31c4092a' or root_span_id='11111111-1111-1111-1111-111111111111'", "result_rows": [{"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 8: OR of an invalid id and a valid root_span_id\n/*!optimizer -- Check for invalid id and valid root_span_id\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"00000000-0000-0000-0000-000000000000\"] and .IdFilter.root_span_ids == [\"6c4053be-dedb-4353-abb7-0fe4039090be\"]) | length == 1\n*/\n/*!execution -- Expect 1 segment due to valid root_span_id\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 1\n*/\nselect: * | from: experiment('singleton') summary | filter: id='00000000-0000-0000-0000-000000000000' or root_span_id='6c4053be-dedb-4353-abb7-0fe4039090be'", "result_rows": [{"_pagination_key": "p07472183629958545453", "_xact_id": "1000194608726806571", "comparison_key": "570ff19b7e51749e88f0ff43ef79e68c0ff7bd7aafabaeaf8908fdd69c94c728", "created": "2025-02-17T00:48:39.723Z", "expected": "<PERSON>", "id": "de03e5ed-f599-4eef-8e74-6bbde4c518b2", "input": "Serve because surface remain institution suffer hotel that audience vote prepare interview build our who data continue blood ...", "metadata": {"category": "formal", "language": "English", "user_type": "new"}, "metrics": {"completion_tokens": 31, "duration": 1.858434, "end": 1739753324.100647, "llm_duration": 1.856665, "prompt_cache_creation_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 84, "start": 1739753319.72396, "total_tokens": 115}, "output": "The statement reflects a complex interplay of societal issues, personal struggles, and the importance of service, community, ...", "root_span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "scores": {"Factuality": 1, "Levenshtein": 0.027624}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6c4053be-dedb-4353-abb7-0fe4039090be", "span_type_info": {"cached": 0, "has_error": false, "in_progress": false, "name": "eval", "remote": 0, "type": "eval"}, "tags": ["greeting", "english", "formal", "new_user"]}], "skip": false}, {"error": null, "query": "-- Test 9: OR of an invalid id and an invalid root_span_id\n/*!optimizer -- Check for invalid id and invalid root_span_id\n  [.. | objects | select(has(\"TantivySearch\"))] | .[0].TantivySearch.segment_filters[] | select(.IdFilter.ids == [\"00000000-0000-0000-0000-000000000000\"] and .IdFilter.root_span_ids == [\"11111111-1111-1111-1111-111111111111\"]) | length == 1\n*/\n/*!execution -- Expect 0 segments as neither exists\n  [.. | objects | select(.name == \"Compute segment batches\")][0].stats.counter_stats | .num_output_segments.total == 0\n*/\nselect: * | from: experiment('singleton') summary | filter: id='00000000-0000-0000-0000-000000000000' or root_span_id='11111111-1111-1111-1111-111111111111'", "result_rows": [], "skip": false}]