[{"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] is not null", "result_rows": [{"id": "6", "metadata.list[0]": 5}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1] | filter: metadata.list[-1] is null", "result_rows": [{"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1] | filter: metadata.list[-1] is not null", "result_rows": [{"id": "1", "metadata.list[-1]": {"b": "c", "num": 0}}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] = 5", "result_rows": [{"id": "6", "metadata.list[0]": 5}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[1] | filter: metadata.list[1] = 'a'", "result_rows": [{"id": "1", "metadata.list[1]": "a"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] = -3", "result_rows": [{"id": "1", "metadata.list[-2]": -3}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] = 'baz'", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].z | filter: metadata.list[3].z is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].z | filter: metadata.list[-1].z is null", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b is null", "result_rows": [{"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b is not null", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b is null", "result_rows": [{"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b is not null", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b = \"c\"", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b != \"c\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b = \"z\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].b | filter: metadata.list[3].b != \"z\"", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b = \"c\"", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b != \"c\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b = \"z\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].b | filter: metadata.list[-1].b != \"z\"", "result_rows": [{"b": "c", "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo ILIKE \"%red%\"", "result_rows": [{"foo": "big red car", "id": "6"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo ILIKE \"%blue%\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo ILIKE \"%red%\"", "result_rows": [{"foo": "big red car", "id": "6"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo ILIKE \"%blue%\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo match \"red\"", "result_rows": [{"foo": "big red car", "id": "6"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[1].foo | filter: metadata.list[1].foo match \"blue\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo match \"red\"", "result_rows": [{"foo": "big red car", "id": "6"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-3].foo | filter: metadata.list[-3].foo match \"blue\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] ILIKE \"%red%\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] ILIKE \"%blue%\"", "result_rows": [{"id": "6", "metadata.list[2]": "small blue truck"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] ILIKE \"%red%\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] ILIKE \"%blue%\"", "result_rows": [{"id": "6", "metadata.list[-2]": "small blue truck"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] match \"red\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[2] | filter: metadata.list[2] match \"blue\"", "result_rows": [{"id": "6", "metadata.list[2]": "small blue truck"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] match \"red\"", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-2] | filter: metadata.list[-2] match \"blue\"", "result_rows": [{"id": "6", "metadata.list[-2]": "small blue truck"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] >= 5", "result_rows": [{"id": "6", "metadata.list[0]": 5}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[0] | filter: metadata.list[0] < 4.9", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-4] | filter: metadata.list[-4] > 5.0", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-4] | filter: metadata.list[-4] >= 4.9", "result_rows": [{"id": "6", "metadata.list[-4]": 5}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].num | filter: metadata.list[3].num > 0", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[3].num | filter: metadata.list[3].num <= 1", "result_rows": [{"id": "1", "num": 0}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].num | filter: metadata.list[-1].num >= 0", "result_rows": [{"id": "1", "num": 0}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, metadata.list[-1].num | filter: metadata.list[-1].num < -1", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, coalesce(metadata.list[0], 'foo')", "result_rows": [{"coalesce(metadata.list[0], 'foo')": "foo", "id": "1"}, {"coalesce(metadata.list[0], 'foo')": "foo", "id": "2"}, {"coalesce(metadata.list[0], 'foo')": "foo", "id": "3"}, {"coalesce(metadata.list[0], 'foo')": "foo", "id": "4"}, {"coalesce(metadata.list[0], 'foo')": "foo", "id": "5"}, {"coalesce(metadata.list[0], 'foo')": "foo", "id": "7"}, {"coalesce(metadata.list[0], 'foo')": 5, "id": "6"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, coalesce(metadata.list[-1], 'foo')", "result_rows": [{"coalesce(metadata.list[-1], 'foo')": "foo", "id": "2"}, {"coalesce(metadata.list[-1], 'foo')": "foo", "id": "3"}, {"coalesce(metadata.list[-1], 'foo')": "foo", "id": "4"}, {"coalesce(metadata.list[-1], 'foo')": "foo", "id": "5"}, {"coalesce(metadata.list[-1], 'foo')": "foo", "id": "6"}, {"coalesce(metadata.list[-1], 'foo')": "foo", "id": "7"}, {"coalesce(metadata.list[-1], 'foo')": {"b": "c", "num": 0}, "id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, coalesce(metadata.list[0], 'bar') | filter: coalesce(metadata.list[0], 'bar') = 'bar'", "result_rows": [{"coalesce(metadata.list[0], 'bar')": "bar", "id": "1"}, {"coalesce(metadata.list[0], 'bar')": "bar", "id": "2"}, {"coalesce(metadata.list[0], 'bar')": "bar", "id": "3"}, {"coalesce(metadata.list[0], 'bar')": "bar", "id": "4"}, {"coalesce(metadata.list[0], 'bar')": "bar", "id": "5"}, {"coalesce(metadata.list[0], 'bar')": "bar", "id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id, coalesce(metadata.list[-1], 'bar') | filter: coalesce(metadata.list[-1], 'bar') = 'bar'", "result_rows": [{"coalesce(metadata.list[-1], 'bar')": "bar", "id": "2"}, {"coalesce(metadata.list[-1], 'bar')": "bar", "id": "3"}, {"coalesce(metadata.list[-1], 'bar')": "bar", "id": "4"}, {"coalesce(metadata.list[-1], 'bar')": "bar", "id": "5"}, {"coalesce(metadata.list[-1], 'bar')": "bar", "id": "6"}, {"coalesce(metadata.list[-1], 'bar')": "bar", "id": "7"}], "skip": false}, {"error": null, "query": "select: metadata.model | from: dataset('singleton') traces", "result_rows": [null, {"model": "gpt-3.5-turbo"}, {"model": "gpt-4"}, {"model": "gpt-4"}, {"model": "gpt-4"}, {"model": "gpt-4"}, {"model": "gpt-4"}], "skip": false}]