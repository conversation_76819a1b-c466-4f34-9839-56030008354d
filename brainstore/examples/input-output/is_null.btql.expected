[{"error": null, "query": "from: dataset('singleton') | select: id | filter: input IS NULL", "result_rows": [{"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input.field IS NULL", "result_rows": [{"id": "1"}, {"id": "3"}, {"id": "4"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input IS NOT NULL", "result_rows": [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}, {"id": "5"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input.field IS NOT NULL", "result_rows": [{"id": "2"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input", "result_rows": [{"id": "2"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input.field", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: id", "result_rows": [{"id": "1"}], "skip": false}]