{"name": "@braintrust/lambda-benchmark", "version": "1.0.0", "description": "", "main": "./dist/index.js", "binary": true, "scripts": {"build": "run-p build:*", "build:typecheck": "tsc --noEmit", "build:lambda": "esbuild --platform=node  --external:nock --external:duckdb --external:@aws-sdk/* --bundle src/index.ts --outfile=dist/benchmark/index.js --minify --sourcemap --target=es2020 --loader:.html=text", "build:quarantine": "esbuild --platform=node  --external:nock --external:duckdb --external:@aws-sdk/* --bundle src/quarantine.ts --outfile=dist/quarantine/index.js --minify --sourcemap --target=es2020 --loader:.html=text", "build:inline-runner": "esbuild --platform=node --external:nock --external:@aws-sdk/* --bundle src/inline-runner.ts --outfile=dist/inline-runner.js --sourcemap --target=es2020", "postbuild": "cd dist && cd benchmark && zip -r index.zip index.js && cd ../quarantine && zip -r index.zip index.js", "deploy": "run-s build deploy:*", "deploy:benchmark": "AWS_DEFAULT_REGION=us-west-1 aws lambda update-function-code --function-name arn:aws:lambda:us-west-1:872608195481:function:ResponseStreamingBenchmark --zip-file fileb://$PWD/dist/benchmark/index.zip", "deploy:quarantine": "AWS_DEFAULT_REGION=us-west-1 aws lambda update-function-code --function-name arn:aws:lambda:us-west-1:872608195481:function:Quarantine --zip-file fileb://$PWD/dist/quarantine/index.zip", "test": "time curl -i -N 'https://lrligvyxjjdh4udplxz6q6y7we0tvylk.lambda-url.us-west-1.on.aws/'"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-lambda": "^3.549.0", "@aws-sdk/client-s3": "^3.565.0", "@aws-sdk/client-sts": "^3.590.0", "@aws-sdk/lib-storage": "^3.565.0", "@braintrust/btql": "workspace:*", "@braintrust/core": "workspace:*", "@braintrust/local": "workspace:*", "@braintrust/proxy": "workspace:*", "@e2b/code-interpreter": "^0.0.8", "archiver": "^7.0.1", "aws-lambda": "^1.0.7", "body-parser": "^1.20.3", "braintrust": "workspace:*", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.19.2", "express-async-errors": "^3.1.1", "lambda-stream": "^0.5.0", "pg": "^8.11.3", "pg-query-stream": "^4.5.5", "redis": "^4.6.8", "serverless-http": "^3.2.0", "sucrase": "^3.35.0", "tmp": "^0.2.3", "undici": "^6.21.1", "uuid": "^9.0.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}, "devDependencies": {"@aws-sdk/s3-request-presigner": "^3.565.0", "@types/archiver": "^6.0.2", "@types/aws-lambda": "^8.10.130", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/pg": "^8.11.14", "@types/tmp": "^0.2.6", "@typescript-eslint/eslint-plugin": "^8.11.0", "argparse": "^2.0.1", "client-sts": "link:@types/aws-sdk/client-sts", "esbuild": "^0.19.9", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "mock-aws-s3": "^4.0.2", "nock": "^13.5.4", "nodemon": "^3.0.1", "npm-run-all": "^4.1.5", "typescript": "^5.0.4", "vitest": "^2.1.9"}}