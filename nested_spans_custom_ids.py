#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a log with deeply nested spans using custom span IDs for all spans.

This demonstrates multi-level hierarchical logging with custom span IDs:
- Level 1: Main task span (custom root span ID)
- Level 2: Major sub-tasks (custom span IDs)
- Level 3: Detailed steps within sub-tasks (custom span IDs)
- Level 4: Individual operations within steps (custom span IDs)

Usage:
    python nested_spans_custom_ids.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import time
import braintrust
from braintrust.logger import SpanImpl, ParentSpanIds
from braintrust.util import LazyValue


def create_custom_span(parent_span, name, custom_span_id, event_data=None):
    """Helper function to create a span with custom span ID"""
    if event_data is None:
        event_data = {}
    
    if parent_span is None:
        # Root span
        logger = braintrust.current_logger()
        return SpanImpl(
            parent_object_type=logger._parent_object_type(),
            parent_object_id=logger._lazy_id,
            parent_compute_object_metadata_args=None,
            parent_span_ids=None,
            name=name,
            span_id=custom_span_id,
            root_span_id=custom_span_id,
            event=event_data
        )
    else:
        # Child span
        parent_span_ids = ParentSpanIds(
            span_id=parent_span.span_id,
            root_span_id=parent_span.root_span_id
        )
        return SpanImpl(
            parent_object_type=parent_span.parent_object_type,
            parent_object_id=parent_span.parent_object_id,
            parent_compute_object_metadata_args=parent_span.parent_compute_object_metadata_args,
            parent_span_ids=parent_span_ids,
            name=name,
            span_id=custom_span_id,
            event=event_data
        )


def main():
    # Initialize logger for a project
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Level 1: Main task span with custom root span ID
    main_span = create_custom_span(
        parent_span=None,
        name="document_analysis_pipeline",
        custom_span_id="root-doc-analysis-2024-01-15",
        event_data={
            "input": "Analyze the quarterly financial report document",
            "metadata": {
                "document_id": "doc_q4_2023_financial",
                "document_type": "financial_report",
                "pages": 45,
                "custom_span_id": "root-doc-analysis-2024-01-15"
            }
        }
    )
    
    print(f"Created main span with custom ID: {main_span.span_id}")
    print(f"Root span ID: {main_span.root_span_id}")
    
    try:
        # Level 2: Document preprocessing
        preprocess_span = create_custom_span(
            parent_span=main_span,
            name="document_preprocessing",
            custom_span_id="preprocess-stage-001",
            event_data={
                "input": "Raw PDF document with 45 pages",
                "metadata": {"stage": "preprocessing", "custom_span_id": "preprocess-stage-001"}
            }
        )
        
        try:
            # Level 3: Text extraction from preprocessing
            extract_span = create_custom_span(
                parent_span=preprocess_span,
                name="text_extraction",
                custom_span_id="extract-text-ocr-001",
                event_data={
                    "input": "PDF file with mixed content",
                    "metadata": {"extraction_method": "OCR + text_layer", "custom_span_id": "extract-text-ocr-001"}
                }
            )
            
            try:
                # Level 4: Individual page processing
                page1_span = create_custom_span(
                    parent_span=extract_span,
                    name="page_1_extraction",
                    custom_span_id="page-001-exec-summary",
                    event_data={
                        "input": "Page 1: Executive Summary",
                        "output": "Extracted 1,247 characters",
                        "scores": {"text_quality": 0.98, "confidence": 0.95},
                        "metadata": {"custom_span_id": "page-001-exec-summary"}
                    }
                )
                page1_span.end()
                
                page2_span = create_custom_span(
                    parent_span=extract_span,
                    name="page_2_extraction",
                    custom_span_id="page-002-financial-overview",
                    event_data={
                        "input": "Page 2: Financial Overview",
                        "output": "Extracted 1,456 characters",
                        "scores": {"text_quality": 0.94, "confidence": 0.92},
                        "metadata": {"custom_span_id": "page-002-financial-overview"}
                    }
                )
                page2_span.end()
                
                # Log extraction summary
                extract_span.log(
                    output="Successfully extracted text from 45 pages",
                    scores={"overall_quality": 0.96, "pages_processed": 0.45}
                )
            finally:
                extract_span.end()
            
            # Level 3: Text cleaning from preprocessing
            clean_span = create_custom_span(
                parent_span=preprocess_span,
                name="text_cleaning",
                custom_span_id="clean-normalize-001",
                event_data={
                    "input": "Raw extracted text with formatting artifacts",
                    "metadata": {
                        "cleaning_steps": ["remove_headers", "fix_spacing", "normalize_encoding"],
                        "custom_span_id": "clean-normalize-001"
                    }
                }
            )
            
            try:
                # Level 4: Individual cleaning steps
                headers_span = create_custom_span(
                    parent_span=clean_span,
                    name="remove_headers_footers",
                    custom_span_id="remove-headers-footers-001",
                    event_data={
                        "input": "Text with page headers and footers",
                        "output": "Clean text without headers/footers",
                        "scores": {"artifacts_removed": 0.99},
                        "metadata": {"custom_span_id": "remove-headers-footers-001"}
                    }
                )
                headers_span.end()
                
                whitespace_span = create_custom_span(
                    parent_span=clean_span,
                    name="normalize_whitespace",
                    custom_span_id="normalize-whitespace-001",
                    event_data={
                        "input": "Text with irregular spacing",
                        "output": "Text with normalized spacing",
                        "scores": {"formatting_quality": 0.97},
                        "metadata": {"custom_span_id": "normalize-whitespace-001"}
                    }
                )
                whitespace_span.end()
                
                clean_span.log(
                    output="Cleaned and normalized text ready for analysis",
                    scores={"cleaning_quality": 0.95}
                )
            finally:
                clean_span.end()
            
            preprocess_span.log(
                output="Document preprocessing completed",
                scores={"preprocessing_success": 1.0}
            )
        finally:
            preprocess_span.end()
        
        # Level 2: Content analysis
        analysis_span = create_custom_span(
            parent_span=main_span,
            name="content_analysis",
            custom_span_id="analysis-financial-metrics-001",
            event_data={
                "input": "Cleaned document text",
                "metadata": {"analysis_type": "financial_metrics_extraction", "custom_span_id": "analysis-financial-metrics-001"}
            }
        )

        try:
            # Level 3: Financial metrics extraction
            metrics_span = create_custom_span(
                parent_span=analysis_span,
                name="financial_metrics_extraction",
                custom_span_id="extract-metrics-revenue-profit-001",
                event_data={
                    "input": "Financial report text",
                    "metadata": {
                        "target_metrics": ["revenue", "profit", "expenses", "growth_rate"],
                        "custom_span_id": "extract-metrics-revenue-profit-001"
                    }
                }
            )

            try:
                # Level 4: Individual metric extraction
                revenue_span = create_custom_span(
                    parent_span=metrics_span,
                    name="revenue_extraction",
                    custom_span_id="revenue-q4-yoy-growth-001",
                    event_data={
                        "input": "Text sections mentioning revenue",
                        "output": {"q4_revenue": "$2.4M", "yoy_growth": "15%"},
                        "scores": {"extraction_confidence": 0.94},
                        "metadata": {"custom_span_id": "revenue-q4-yoy-growth-001"}
                    }
                )
                revenue_span.end()

                profit_span = create_custom_span(
                    parent_span=metrics_span,
                    name="profit_extraction",
                    custom_span_id="profit-margin-net-income-001",
                    event_data={
                        "input": "Text sections mentioning profit/loss",
                        "output": {"net_profit": "$340K", "profit_margin": "14.2%"},
                        "scores": {"extraction_confidence": 0.91},
                        "metadata": {"custom_span_id": "profit-margin-net-income-001"}
                    }
                )
                profit_span.end()

                metrics_span.log(
                    output="Successfully extracted key financial metrics",
                    scores={"metrics_completeness": 0.88}
                )
            finally:
                metrics_span.end()

            # Level 3: Sentiment analysis
            sentiment_span = create_custom_span(
                parent_span=analysis_span,
                name="sentiment_analysis",
                custom_span_id="sentiment-exec-outlook-risks-001",
                event_data={
                    "input": "Financial report narrative sections",
                    "metadata": {
                        "analysis_sections": ["executive_summary", "outlook", "risks"],
                        "custom_span_id": "sentiment-exec-outlook-risks-001"
                    }
                }
            )

            try:
                # Level 4: Section-specific sentiment
                exec_sentiment_span = create_custom_span(
                    parent_span=sentiment_span,
                    name="executive_summary_sentiment",
                    custom_span_id="exec-summary-positive-sentiment-001",
                    event_data={
                        "input": "Executive summary text",
                        "output": {"sentiment": "positive", "confidence": 0.82},
                        "scores": {"positivity": 0.7},
                        "metadata": {"custom_span_id": "exec-summary-positive-sentiment-001"}
                    }
                )
                exec_sentiment_span.end()

                outlook_sentiment_span = create_custom_span(
                    parent_span=sentiment_span,
                    name="outlook_sentiment",
                    custom_span_id="outlook-cautious-optimistic-001",
                    event_data={
                        "input": "Future outlook section",
                        "output": {"sentiment": "cautiously_optimistic", "confidence": 0.76},
                        "scores": {"positivity": 0.6},
                        "metadata": {"custom_span_id": "outlook-cautious-optimistic-001"}
                    }
                )
                outlook_sentiment_span.end()

                sentiment_span.log(
                    output="Overall document sentiment: positive with caution",
                    scores={"overall_sentiment": 0.65}
                )
            finally:
                sentiment_span.end()

            analysis_span.log(
                output="Content analysis completed",
                scores={"analysis_quality": 0.89}
            )
        finally:
            analysis_span.end()

        # Level 2: Report generation
        report_span = create_custom_span(
            parent_span=main_span,
            name="report_generation",
            custom_span_id="generate-executive-summary-001",
            event_data={
                "input": "Extracted metrics and sentiment analysis",
                "metadata": {"report_format": "executive_summary", "custom_span_id": "generate-executive-summary-001"}
            }
        )

        try:
            # Level 3: Summary creation
            summary_span = create_custom_span(
                parent_span=report_span,
                name="summary_creation",
                custom_span_id="create-q4-summary-report-001",
                event_data={
                    "input": "Financial metrics and sentiment data",
                    "output": "Q4 2023 showed strong performance with $2.4M revenue (15% YoY growth) and healthy 14.2% profit margin. Overall sentiment is positive with cautious optimism for future quarters.",
                    "scores": {"summary_quality": 0.92, "conciseness": 0.88},
                    "metadata": {"custom_span_id": "create-q4-summary-report-001"}
                }
            )
            summary_span.end()

            report_span.log(
                output="Executive summary report generated",
                scores={"report_completeness": 0.94}
            )
        finally:
            report_span.end()

        print("Created all spans with custom IDs:")
        print("Preprocessing spans:")
        print(f"  - Preprocessing: preprocess-stage-001")
        print(f"  - Text extraction: extract-text-ocr-001")
        print(f"  - Page 1: page-001-exec-summary")
        print(f"  - Page 2: page-002-financial-overview")
        print(f"  - Text cleaning: clean-normalize-001")
        print(f"  - Headers removal: remove-headers-footers-001")
        print(f"  - Whitespace normalization: normalize-whitespace-001")
        print("Analysis spans:")
        print(f"  - Content analysis: analysis-financial-metrics-001")
        print(f"  - Metrics extraction: extract-metrics-revenue-profit-001")
        print(f"  - Revenue extraction: revenue-q4-yoy-growth-001")
        print(f"  - Profit extraction: profit-margin-net-income-001")
        print(f"  - Sentiment analysis: sentiment-exec-outlook-risks-001")
        print(f"  - Executive sentiment: exec-summary-positive-sentiment-001")
        print(f"  - Outlook sentiment: outlook-cautious-optimistic-001")
        print("Report generation spans:")
        print(f"  - Report generation: generate-executive-summary-001")
        print(f"  - Summary creation: create-q4-summary-report-001")

    finally:
        # Log final result on main span
        main_span.log(
            output="Document analysis pipeline completed successfully",
            expected="Comprehensive financial document analysis with extracted metrics and insights",
            scores={
                "pipeline_success": 1.0,
                "overall_quality": 0.91,
                "processing_efficiency": 0.87
            },
            metadata={
                "total_spans_created": 15,
                "max_nesting_depth": 4,
                "pipeline_completed": True,
                "all_spans_have_custom_ids": True
            },
            tags=["document_analysis", "financial_report", "multi_level_processing", "custom_span_ids"]
        )
        main_span.end()

    print("\nCreated deeply nested trace structure with custom span IDs:")
    print("Level 1: document_analysis_pipeline (root-doc-analysis-2024-01-15)")
    print("├── Level 2: document_preprocessing (preprocess-stage-001)")
    print("│   ├── Level 3: text_extraction (extract-text-ocr-001)")
    print("│   │   ├── Level 4: page_1_extraction (page-001-exec-summary)")
    print("│   │   └── Level 4: page_2_extraction (page-002-financial-overview)")
    print("│   └── Level 3: text_cleaning (clean-normalize-001)")
    print("│       ├── Level 4: remove_headers_footers (remove-headers-footers-001)")
    print("│       └── Level 4: normalize_whitespace (normalize-whitespace-001)")
    print("├── Level 2: content_analysis (analysis-financial-metrics-001)")
    print("│   ├── Level 3: financial_metrics_extraction (extract-metrics-revenue-profit-001)")
    print("│   │   ├── Level 4: revenue_extraction (revenue-q4-yoy-growth-001)")
    print("│   │   └── Level 4: profit_extraction (profit-margin-net-income-001)")
    print("│   └── Level 3: sentiment_analysis (sentiment-exec-outlook-risks-001)")
    print("│       ├── Level 4: executive_summary_sentiment (exec-summary-positive-sentiment-001)")
    print("│       └── Level 4: outlook_sentiment (outlook-cautious-optimistic-001)")
    print("└── Level 2: report_generation (generate-executive-summary-001)")
    print("    └── Level 3: summary_creation (create-q4-summary-report-001)")
    
    # Flush to ensure all logs are sent to the server
    logger.flush()
    print("\nAll nested spans with custom IDs have been sent to Braintrust server")
    print("Check your Braintrust dashboard to explore the hierarchical trace structure")
    print("All spans will show their custom span IDs in the trace view")


if __name__ == "__main__":
    main()
