#!/usr/bin/env python3
"""
Simple script to insert a log into a Braintrust project using the SDK with a custom root span ID.

This example shows how to:
1. Initialize a logger for a project
2. Create a span with a custom root span ID
3. Insert a log entry into that span
4. Flush the log to ensure it's sent to the server

Usage:
    python simple_log_insert_custom_root_span.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import braintrust
from braintrust.logger import SpanImpl, SpanObjectTypeV3
from braintrust.util import LazyValue


def main():
    # Initialize logger for a project (creates project if it doesn't exist)
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Define our custom root span ID
    custom_root_span_id = "my-custom-trace123"
    
    print(f"Creating span with custom root span ID: {custom_root_span_id}")
    
    # Create a span with custom root span ID using the lower-level SpanImpl constructor
    # Since the public API doesn't directly expose root_span_id parameter, we need to use SpanImpl directly
    span = SpanImpl(
        parent_object_type=logger._parent_object_type(),
        parent_object_id=logger._lazy_id,
        parent_compute_object_metadata_args=None,
        parent_span_ids=None,  # No parent means this is a root span
        name="custom_root_span",
        span_id=custom_root_span_id,  # This becomes the root span ID for root spans
        root_span_id=custom_root_span_id,  # Explicitly set the root span ID
        event={
            "input": "What is the weather like today?",
            "output": "I don't have access to real-time weather data.",
            "scores": {"helpfulness": 0.3, "accuracy": 1.0},
            "metadata": {
                "model": "example-model",
                "timestamp": "2024-01-15T10:30:00Z",
                "user_id": "user123",
                "custom_root_span": True
            },
            "tags": ["weather", "information_request", "custom_root_span"]
        }
    )
    
    print(f"Successfully created span with:")
    print(f"  - Span ID: {span.span_id}")
    print(f"  - Root Span ID: {span.root_span_id}")
    print(f"  - Row ID: {span.id}")
    
    # End the span to finalize it
    span.end()
    
    # Alternative approach: Use logger.start_span with spanId parameter
    # Note: This approach uses the spanId as both span ID and root span ID for root spans
    print(f"\nAlternative approach with logger.start_span:")


if __name__ == "__main__":
    main()
