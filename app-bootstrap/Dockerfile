# The build context of this Dockerfile is assumed to be the root of the
# repository.

FROM node:22-bookworm-slim AS base

FROM base AS runner

# install runtime and debugging tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    python3 python3-venv \
    curl vim dstat procps net-tools ripgrep

WORKDIR /braintrust

# Create venv and root environment.
RUN python3 -m venv venv
RUN . venv/bin/activate && export > /root/env

COPY app-bootstrap/requirements.txt .
RUN . /root/env && pip install -r requirements.txt

COPY scripts/docker_entrypoint_loader.sh app-bootstrap/docker_entrypoint.py .

EXPOSE 8100
ENTRYPOINT ["./docker_entrypoint_loader.sh", "python", "docker_entrypoint.py"]

HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8100/ || exit 1
