import json
import os
import signal
import sys
import traceback
from functools import cache
from uuid import uuid4
from wsgiref.simple_server import make_server

import falcon
import psycopg2


@cache
def get_app_db_url():
    return os.environ["SUPABASE_PG_URL"]


# Returns the org ID and a boolean indicating whether it was newly-created.
def get_or_create_org(org_name):
    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT id FROM organizations WHERE name = %s", (org_name,))
            res = cursor.fetchone()
            if res is not None:
                return res[0], False
            cursor.execute("INSERT INTO organizations(name) VALUES (%s) RETURNING id", (org_name,))
            org_id = cursor.fetchone()[0]
            cursor.execute("SELECT insert_resource_definition(%s, %s)", (org_id, "unlimited"))
            return org_id, True


# Returns the user ID and a boolean indicating whether it was newly-created.
def get_or_create_user(user_email):
    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT id FROM users WHERE email = %s", (user_email,))
            res = cursor.fetchone()
            if res is not None:
                return res[0], False
            cursor.execute(
                "INSERT INTO users(auth_id, email) VALUES (%s, %s) RETURNING id",
                (
                    str(uuid4()),
                    user_email,
                ),
            )
            return cursor.fetchone()[0], True


def add_membership(org_id, user_id):
    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT add_member_to_org_unchecked(%s, %s, array [get_group_id(%s, 'Owners')])",
                (
                    user_id,
                    org_id,
                    org_id,
                ),
            )


def create_api_key(org_id, user_email):
    with psycopg2.connect(get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT auth_id FROM users WHERE email = %s", (user_email,))
            auth_id = cursor.fetchone()[0]
            cursor.execute("SELECT create_api_key(%s, %s, %s)", (auth_id, org_id, str(uuid4())))
            return cursor.fetchone()[0]


class AuthMiddleware:
    def __init__(self):
        self.secret = os.environ["CONTAINER_SERVER_SECRET"]

    def process_request(self, req, resp):
        if req.path in ["/"]:
            return

        authorization = req.get_header("Authorization", required=True)
        try:
            parts = authorization.split()
            assert parts[0] == "Bearer"
            if parts[1] != self.secret:
                raise falcon.HTTPBadRequest("Access denied")
        except Exception as e:
            if not isinstance(e, falcon.http_error.HTTPError):
                raise falcon.HTTPBadRequest(f"Must include 'Authorization' header with value 'Bearer [secret]'")


class MaxContentLengthMiddleware:
    def __init__(self, max_content_length):
        self.max_content_length = max_content_length

    def process_request(self, req, resp):
        if req.content_length and req.content_length > self.max_content_length:
            raise falcon.HTTPBadRequest(
                f"Body of length {req.content_length} is too large. Must be at most {self.max_content_length} bytes"
            )


class IndexResource:
    def on_get(self, req, resp):
        del req
        resp.text = "Up and running!"


class CreateApiKeyResource:
    def on_post(self, req, resp):
        try:
            args = json.load(req.bounded_stream)
        except Exception:
            traceback.print_exc()
            raise falcon.HTTPBadRequest("Body is not valid JSON")

        if not (isinstance(args, dict) and "org_name" in args and "user_email" in args):
            raise falcon.HTTPBadRequest(
                """Body must be an object of the form {"org_name": [org_name], "user_email": [user_email]}"""
            )

        org_name = args["org_name"]
        user_email = args["user_email"]
        try:
            org_id, is_new_org = get_or_create_org(org_name)
            user_id, is_new_user = get_or_create_user(user_email)
            if is_new_org or is_new_user:
                add_membership(org_id, user_id)
            api_key = create_api_key(org_id, user_email)
            resp.text = api_key
        except Exception:
            traceback.print_exc()
            raise falcon.HTTPInternalServerError("Failed to create API key")


def sigterm_handler(signum, frame):
    del signum, frame
    print(f"Captured sigterm. Exiting container.", file=sys.stderr)
    sys.exit(0)


if __name__ == "__main__":
    signal.signal(signal.SIGTERM, sigterm_handler)

    app = falcon.App(middleware=[AuthMiddleware(), MaxContentLengthMiddleware(64 * 1024)])
    app.add_route("/", IndexResource())
    app.add_route("/create-api-key", CreateApiKeyResource())
    with make_server("0.0.0.0", 8100, app) as httpd:
        httpd.serve_forever()
