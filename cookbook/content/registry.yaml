# yaml-language-server: $schema=./.github/registry_schema.json

- title: Text-to-SQL
  path: examples/Text2SQL/Text2SQL.ipynb
  date: 2023-08-12
  authors:
    - ankrgyl
  tags:
    - evals
    - sql
- title: Classifying news articles
  path: examples/ClassifyingNewsArticles/ClassifyingNewsArticles.ipynb
  date: 2023-09-01
  authors:
    - da<PERSON><PERSON><PERSON>
  tags:
    - evals
    - classification
- title: Improving Github issue titles using their contents
  path: examples/Github-Issues/Github-Issues.ipynb
  date: 2023-10-29
  authors:
    - ankrgyl
  tags:
    - evals
    - summarization
- title: Coda's Help Desk with and without RAG
  path: examples/CodaHelpDesk/CodaHelpDesk.ipynb
  date: 2023-12-21
  authors:
    - austinmxx
    - wong
  tags:
    - evals
    - rag
- title: Generating beautiful HTML components
  path: examples/HTMLGenerator/HTMLGenerator.ipynb
  date: 2024-01-29
  authors:
    - ankrgyl
  tags:
    - logging
    - datasets
    - evals
- title: Generating release notes and hill-climbing to improve them
  path: examples/ReleaseNotes/ReleaseNotes.ipynb
  date: 2024-02-02
  authors:
    - ankrgyl
  tags:
    - evals
    - hill-climbing
- title: How Zapier uses assertions to evaluate tool usage in chatbots
  path: examples/Assertions/Assertions.ipynb
  date: 2024-02-13
  authors:
    - vitorbal
  tags:
    - evals
    - assertions
    - tools
  logo: https://cdn.zapier.com/zapier/images/favicon.ico
  banner: /docs/cookbook-banners/Zapier.png
- title: AI Search Bar
  path: examples/AISearch/ai_search_evals.ipynb
  date: 2024-03-04
  authors:
    - austinmxx
  tags:
    - evals
    - sql
- title: Detecting Prompt Injections
  path: examples/PromptInjectionDetector/PromptInjectionGPT4o.ipynb
  date: 2024-05-20
  authors:
    - nelsonauner
  tags:
    - evals
    - classification
- title: Comparing evals across multiple AI models
  path: examples/ModelComparison/ModelComparison.ipynb
  date: 2024-05-22
  authors:
    - j13huang
  tags:
    - evals
    - charts
- title: Optimizing Ragas to evaluate a RAG pipeline
  path: examples/SimpleRagas/SimpleRagas.ipynb
  date: 2024-05-27
  authors:
    - ankrgyl
    - nelsonauner
  tags:
    - evals
    - rag
- title: LLM Eval For Text2SQL
  path: examples/Text2SQL-Data/Text2SQL-Data.ipynb
  date: 2024-05-29
  authors:
    - ankrgyl
  tags:
    - evals
    - datasets
    - text2sql
- title: Evaluating a chat assistant
  path: examples/EvaluatingChatAssistant/EvaluatingChatAssistant.ipynb
  date: 2024-07-16
  authors:
    - tara-nagar
  tags:
    - evals
    - chat
- title: Tool calls in LLaMa 3.1
  path: examples/LLaMa-3_1-Tools/LLaMa-3_1-Tools.ipynb
  date: 2024-07-26
  authors:
    - ankrgyl
  tags:
    - evals
    - llama-3.1
    - tools
- title: Benchmarking inference providers
  path: examples/ProviderBenchmark/ProviderBenchmark.ipynb
  date: 2024-07-29
  authors:
    - ankrgyl
  tags:
    - evals
    - llama-3.1
    - providers
- title: An agent that runs OpenAPI commands
  path: examples/APIAgent-Py/APIAgent.ipynb
  date: 2024-08-12
  authors:
    - ankrgyl
  tags:
    - agent
    - rag
    - evals
- title: "Unreleased AI: A full stack Next.js app for generating changelogs"
  path: examples/UnreleasedAI/UnreleasedAI.mdx
  date: 2024-08-28
  authors:
    - ornellaaltunyan
  banner: /docs/cookbook-banners/AI-app.png
  tags:
    - evals
    - logging
    - next.js
- title: "Evaluating multimodal receipt extraction"
  path: examples/ReceiptExtraction/ReceiptExtraction.ipynb
  date: 2024-09-30
  authors:
    - ankrgyl
  tags:
    - evals
    - multimodal
    - receipts
- title: Using functions to build a RAG agent
  path: examples/ToolRAG/ToolRAG.mdx
  date: 2024-10-08
  authors:
    - ornellaaltunyan
    - ankrgyl
  tags:
    - functions
    - rag
    - tools
- title: Using OpenTelemetry for LLM observability
  path: examples/OTEL-logging/OTEL-logging.mdx
  date: 2024-10-31
  authors:
    - ornellaaltunyan
  tags:
    - evals
    - tools
- title: Using Python functions to extract text from images
  path: examples/ToolOCR/ToolOCR.mdx
  date: 2024-11-22
  authors:
    - ornellaaltunyan
  tags:
    - python
    - tools
    - ocr
    - functions
- title: Evaluating SimpleQA
  path: examples/SimpleQA/SimpleQA.ipynb
  date: 2024-12-06
  authors:
    - ankrgyl
    - ornellaaltunyan
  tags:
    - datasets
    - evals
- title: Evaluating audio with the OpenAI Realtime API
  path: examples/Realtime/Realtime.mdx
  date: 2024-12-14
  authors:
    - ornellaaltunyan
  tags:
    - evals
    - tools
    - audio
- title: Evaluating the precision and recall of an emotion classifier
  path: examples/PrecisionRecall/PrecisionRecall.ipynb
  date: 2025-01-17
  authors:
    - abarbir12
  tags:
    - recall
    - precision
    - evals
    - classifier
    - python
- title: Evaluating a prompt chaining agent
  path: examples/PromptChaining/prompt-chaining.ipynb
  date: 2025-01-30
  authors:
    - abarbir12
  tags:
    - agent
    - evals
    - python
- title: Classifying spam using structured outputs
  path: examples/SpamClassifier/SpamClassifier.mdx
  date: 2025-02-08
  authors:
    - ornellaaltunyan
  tags:
    - classifier
    - structured outputs
    - playground
- title: Evaluating a voice agent
  path: examples/VoiceAgent/voiceagent.ipynb
  date: 2025-02-13
  authors:
    - abarbir12
  tags:
    - agent
    - evals
    - voice
- title: Evaluating video QA
  path: examples/VideoQA/VideoQA.ipynb
  date: 2025-02-18
  authors:
    - abarbir12
  tags:
    - evals
    - video
    - datasets
- title: Prompt versioning and deployment
  path: examples/PromptVersioning/PromptVersioning.ipynb
  date: 2025-02-24
  authors:
    - abarbir12
  tags:
    - evals
    - prompting
    - functions
- title: Evaluating a web agent
  path: examples/WebAgent/WebAgent.ipynb
  date: 2025-03-08
  authors:
    - ornellaaltunyan
    - abarbir12
  tags:
    - eval
    - agent
    - multimodal
- title: Tracing Vercel AI SDK applications
  path: examples/VercelAISDKTracing/vercel-ai-sdk-tracing.mdx
  date: 2025-05-15
  authors:
    - philhetzel
  tags:
    - logging
    - Next.js
- title: Evaluating video QA with Twelve Labs
  path: examples/VideoQATwelveLabs/VideoQATwelveLabs.ipynb
  date: 2025-05-14
  authors:
    - jamesle
    - ornellaaltunyan
  tags:
    - eval
    - video
    - multimodal
- title: Using PDF attachments in playgrounds
  path: examples/PDFPlayground/PDFPlayground.mdx
  date: 2025-05-22
  authors:
    - carlosesteban
    - ornellaaltunyan
  tags:
    - logging
    - multimodal
    - playground
    - typescript
