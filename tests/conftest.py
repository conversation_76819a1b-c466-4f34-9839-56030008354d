import datetime

import pytest
from braintrust_local.flaky_tests import FLAKY_TESTS_EXACT, FLAKY_TESTS_REGEX


@pytest.fixture(autouse=True)
def print_start_and_end_times(request):
    start = datetime.datetime.now()
    print(f"START TEST {request.node.name}: {start.strftime('%s')} ({start})")

    yield

    end = datetime.datetime.now()
    print(f"END TEST {request.node.name}: {end.strftime('%s')} ({end})")


def pytest_collection_modifyitems(items):
    for item in items:
        found_flake_info = FLAKY_TESTS_EXACT.get(item.nodeid)
        if found_flake_info is None:
            for regex, flake_info in FLAKY_TESTS_REGEX:
                if regex.search(item.nodeid):
                    found_flake_info = flake_info
                    break
        if found_flake_info:
            item.add_marker(pytest.mark.flaky(reruns=3, reruns_delay=1))
