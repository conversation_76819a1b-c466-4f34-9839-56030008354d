import braintrust
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class AuditHeadersTest(BraintrustAppTestBase):
    def _run(self, verb, url, *, want_resources=None, want_normalized_url=None, audit_headers=None, body=None):
        resp = None
        if audit_headers is None or not audit_headers:
            # Audit header not set.
            resp = self.run_request(verb, url, json=body, skip_enable_audit=True)
            for k in resp.headers:
                self.assertNotIn("x-bt-audit", k)

        if audit_headers is None or audit_headers:
            resp = self.run_request(
                verb,
                url,
                headers={
                    "Authorization": f"Bearer {self.org_api_key}",
                    "x-bt-enable-audit": "true",
                },
                json=body,
            )
            self.assertEqual(resp.headers["x-bt-audit-user-id"], self.user_id)
            self.assertEqual(resp.headers["x-bt-audit-user-email"], self.user_email)
            self.assertEqual(resp.headers["x-bt-audit-normalized-url"], want_normalized_url)
            if want_resources is not None and not isinstance(want_resources, list):
                body = resp.json()
                if "objects" in body:
                    want_resources = want_resources(body["objects"])
                else:
                    want_resources = want_resources([body])
            if want_resources is not None:
                actual_resources = sorted(
                    braintrust.parse_audit_resources(resp.headers["x-bt-audit-resources"]),
                    key=lambda r: r["id"],
                )
                self.assertEqual(
                    actual_resources,
                    sorted(want_resources, key=lambda r: r["id"]),
                )

        assert resp is not None
        return resp

    def _run_rest(
        self,
        object_type,
        *,
        post_body=None,
        put_body=None,
        patch_body=None,
        list_params=None,
        get_params=None,
        delete_body=None,
        want_resources=None,
        skip_verbs=None,
    ):
        if get_params is None:
            get_params = ""
        if skip_verbs is None:
            skip_verbs = []

        # post can never be skipped.
        resp = self._run(
            "post",
            f"{LOCAL_API_URL}/v1/{object_type}",
            body=post_body,
            want_resources=want_resources,
            want_normalized_url=f"/v1/{object_type}",
        )
        id_1 = resp.json()["id"]
        if "put" not in skip_verbs:
            resp = self._run(
                "put",
                f"{LOCAL_API_URL}/v1/{object_type}",
                body=put_body,
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}",
            )
            id_2 = resp.json()["id"]
        else:
            resp = self._run(
                "post",
                f"{LOCAL_API_URL}/v1/{object_type}",
                body=put_body,
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}",
            )
            id_2 = resp.json()["id"]
        if "patch" not in skip_verbs:
            self._run(
                "patch",
                f"{LOCAL_API_URL}/v1/{object_type}/{id_2}",
                body=patch_body,
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}/[id]",
            )
        if "list" not in skip_verbs:
            self._run(
                "get",
                f"{LOCAL_API_URL}/v1/{object_type}{list_params}",
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}",
            )
        if "get" not in skip_verbs:
            self._run(
                "get",
                f"{LOCAL_API_URL}/v1/{object_type}/{id_1}{get_params}",
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}/[id]",
            )
        if "delete" not in skip_verbs:
            self._run(
                "delete",
                f"{LOCAL_API_URL}/v1/{object_type}/{id_1}",
                body=delete_body,
                audit_headers=True,
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}/[id]",
            )
            self._run(
                "delete",
                f"{LOCAL_API_URL}/v1/{object_type}/{id_2}",
                body=delete_body,
                audit_headers=True,
                want_resources=want_resources,
                want_normalized_url=f"/v1/{object_type}/[id]",
            )

    def test_logs3_btql_audit_headers(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]

        self._run(
            "post",
            f"{LOCAL_API_URL}/logs3",
            body={
                "api_version": 2,
                "rows": [
                    {"dataset_id": dataset_id},
                ],
            },
            audit_headers=False,
        )
        self._run(
            "post",
            f"{LOCAL_API_URL}/logs3",
            body={
                "api_version": 2,
                "rows": [
                    {"dataset_id": dataset_id},
                    {
                        "project_id": project_id,
                        "log_id": "p",
                        "name": "Calculator",
                        "slug": "calculator-0",
                        "function_data": {"type": "prompt"},
                        "prompt_data": {
                            "options": {"model": "gpt-4o"},
                            "prompt": {
                                "type": "chat",
                                "messages": [
                                    {"role": "user", "content": "What is 2 + 2?"},
                                ],
                            },
                        },
                    },
                ],
            },
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url="/logs3",
        )

        self._run(
            "post",
            f"{LOCAL_API_URL}/btql",
            body={
                "query": f"""
                    from: dataset('{dataset_id}')
                | select: * """
            },
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url="/btql",
        )
        self._run(
            "post",
            f"{LOCAL_API_URL}/btql",
            body={
                "query": f"""
                    from: project_functions('{project_id}')
                | select: * """
            },
            audit_headers=False,
        )

    def test_logs3_audit_headers_invalid_name(self):
        resp = self.registerProject({"project_name": "x — y"})
        project_id = resp["project"]["id"]

        self._run(
            "post",
            f"{LOCAL_API_URL}/logs3",
            body={
                "api_version": 2,
                "rows": [
                    {
                        "project_id": project_id,
                        "log_id": "p",
                        "name": "Calculator",
                        "slug": "calculator-0",
                        "function_data": {"type": "prompt"},
                        "prompt_data": {
                            "options": {"model": "gpt-4o"},
                            "prompt": {
                                "type": "chat",
                                "messages": [
                                    {"role": "user", "content": "What is 2 + 2?"},
                                ],
                            },
                        },
                    },
                ],
            },
            want_resources=[
                {
                    "type": "project",
                    "id": project_id,
                    "name": "x — y",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url="/logs3",
        )

    def test_organization_members_patch_audit_headers(self):
        resp = self._run(
            "patch",
            f"{LOCAL_API_URL}/v1/organization/members",
            body={
                "org_id": self.org_id,
                "invite_users": {"emails": [self.user_email]},
            },
            want_resources=[
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url="/v1/organization/members",
        )

    def test_insert_audit_headers(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]

        self._run(
            "post",
            f"{LOCAL_API_URL}/v1/insert",
            body={
                "dataset": {
                    dataset_id: {
                        "events": [{}],
                    },
                },
            },
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url="/v1/insert",
        )

    def test_rest_env_var_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/function",
            json={
                "project_id": project_id,
                "function_data": {
                    "type": "code",
                    "data": {
                        "type": "inline",
                        "runtime_context": {"runtime": "python", "version": "3.12"},
                        "code": """\
async function handler() {
if (process.env.SECRET) {
    return process.env.SECRET;
} else {
    throw new Error("SECRET environment variable not set");
}
}""",
                    },
                },
                "name": "Secret checker",
                "slug": "secret-checker",
            },
        )
        function_id = resp.json()["id"]

        self._run_rest(
            "env_var",
            post_body={
                "object_type": "function",
                "object_id": function_id,
                "name": "SECRET",
                "value": "hello",
            },
            put_body={
                "object_type": "function",
                "object_id": function_id,
                "name": "SECRET2",
                "value": "world",
            },
            patch_body={
                "name": "SECRET2",
                "value": "foo",
            },
            list_params=f"?object_type=function&object_id={function_id}",
            want_resources=[
                {
                    "type": "prompt",
                    "id": function_id,
                    "name": "secret-checker",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_column_audit_headers(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]

        self._run_rest(
            "column",
            post_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "subtype": None,
                "name": "my_column",
                "expr": "input.my_path",
            },
            put_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "subtype": None,
                "name": "my_other_column",
                "expr": "input.my_path",
            },
            patch_body={
                "name": "my_other_column",
                "expr": "input.my_other_path",
            },
            list_params=f"?object_type=dataset&object_id={dataset_id}",
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            skip_verbs=["get"],
        )

    def test_rest_prompt_function_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        for object_type in ["prompt", "function"]:
            self._run_rest(
                object_type,
                post_body={
                    "project_id": project_id,
                    "name": "Scorer",
                    "slug": "scorer-0",
                    "prompt_data": {
                        "prompt": {
                            "type": "chat",
                            "messages": [
                                {"role": "user", "content": "Return 'A'."},
                            ],
                        },
                        "options": {"model": "gpt-4o"},
                        "parser": {
                            "type": "llm_classifier",
                            "use_cot": True,
                            "choice_scores": {"A": 1, "B": 0},
                        },
                    },
                    "function_type": "scorer",
                },
                put_body={
                    "project_id": project_id,
                    "name": "Scorer 2",
                    "slug": "scorer-2",
                    "prompt_data": {
                        "prompt": {
                            "type": "chat",
                            "messages": [
                                {"role": "user", "content": "Return 'B'."},
                            ],
                        },
                        "options": {"model": "gpt-4o"},
                        "parser": {
                            "type": "llm_classifier",
                            "use_cot": True,
                            "choice_scores": {"A": 1, "B": 0},
                        },
                    },
                    "function_type": "scorer",
                },
                patch_body={"name": "Scorer 3"},
                list_params=f"?project_id={project_id}",
                want_resources=[
                    {
                        "type": "project",
                        "id": project_id,
                        "name": "p",
                    },
                    {
                        "type": "organization",
                        "id": self.org_id,
                        "name": self.org_name,
                    },
                ],
            )

    def test_rest_dataset_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]
        self._run_rest(
            object_type="dataset",
            post_body={
                "project_id": project_id,
                "name": "Dataset 1",
            },
            put_body={
                "project_id": project_id,
                "name": "Dataset 2",
            },
            patch_body={"description": "My dataset"},
            list_params=f"?project_id={project_id}",
            want_resources=lambda datasets: [
                *[{"type": "dataset", "id": d["id"], "name": d["name"]} for d in datasets],
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_acl_audit_headers(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]

        self._run_rest(
            object_type="acl",
            post_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "permission": "read",
                "user_id": self.user_id,
            },
            put_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "permission": "update",
                "user_id": self.user_id,
            },
            patch_body=None,
            list_params=f"?object_type=dataset&object_id={dataset_id}",
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            skip_verbs=["patch"],
        )

    def test_rest_project_audit_headers(self):
        orig_names = {"project3": "project2"}
        self._run_rest(
            object_type="project",
            post_body={
                "name": "project1",
            },
            put_body={
                "name": "project2",
            },
            patch_body={
                "settings": {
                    "comparison_key": "output",
                },
            },
            list_params=f"?org_name={self.org_name}",
            want_resources=lambda projects: [
                *[
                    {
                        "type": "project",
                        "id": p["id"],
                        "name": p["name"],
                    }
                    for p in projects
                ],
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_experiment_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        self._run_rest(
            object_type="experiment",
            post_body={
                "project_id": project_id,
                "name": "experiment1",
            },
            put_body={
                "project_id": project_id,
                "name": "experiment2",
            },
            patch_body={"name": "experiment3"},
            list_params=f"?project_id={project_id}",
            want_resources=lambda experiments: [
                *[{"type": "experiment", "id": e["id"], "name": e["name"]} for e in experiments],
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_role_audit_headers(self):
        self._run_rest(
            object_type="role",
            post_body={
                "org_name": self.org_name,
                "name": "role1",
            },
            put_body={
                "org_name": self.org_name,
                "name": "role2",
            },
            patch_body={"name": "role3"},
            list_params=f"?org_name={self.org_name}&limit=2",
            want_resources=lambda roles: [
                *[{"type": "role", "id": r["id"], "name": r["name"]} for r in roles],
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_group_audit_headers(self):
        self._run_rest(
            object_type="group",
            post_body={
                "org_name": self.org_name,
                "name": "group1",
            },
            put_body={
                "org_name": self.org_name,
                "name": "group2",
            },
            patch_body={"name": "group3"},
            list_params=f"?org_name={self.org_name}",
            want_resources=lambda groups: [
                *[{"type": "group", "id": g["id"], "name": g["name"]} for g in groups],
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_project_score_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        self._run_rest(
            object_type="project_score",
            post_body={
                "project_id": project_id,
                "name": "project_score1",
                "score_type": "slider",
            },
            put_body={
                "project_id": project_id,
                "name": "project_score2",
                "score_type": "slider",
            },
            patch_body={"name": "project_score3"},
            list_params=f"?project_id={project_id}",
            want_resources=[
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_project_tag_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        self._run_rest(
            object_type="project_tag",
            post_body={
                "project_id": project_id,
                "name": "project_tag1",
            },
            put_body={
                "project_id": project_id,
                "name": "project_tag2",
            },
            patch_body={"name": "project_tag3"},
            list_params=f"?project_id={project_id}",
            want_resources=[
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_span_iframe_audit_headers(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        self._run_rest(
            object_type="span_iframe",
            post_body={
                "project_id": project_id,
                "name": "span_iframe1",
                "url": "https://example.com",
            },
            put_body={
                "project_id": project_id,
                "name": "span_iframe2",
                "url": "https://example2.com",
            },
            patch_body={"name": "span_iframe3"},
            list_params=f"?project_id={project_id}",
            want_resources=[
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            skip_verbs=["put"],
        )

    def test_rest_view_audit_headers(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]

        self._run_rest(
            object_type="view",
            post_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "view_type": "dataset",
                "name": "view1",
            },
            put_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "view_type": "dataset",
                "name": "view2",
            },
            patch_body={
                "object_type": "dataset",
                "object_id": dataset_id,
                "name": "view3",
            },
            list_params=f"?object_type=dataset&object_id={dataset_id}",
            get_params=f"?object_type=dataset&object_id={dataset_id}",
            delete_body={
                "object_type": "dataset",
                "object_id": dataset_id,
            },
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    def test_rest_api_key_audit_headers(self):
        self._run_rest(
            object_type="api_key",
            post_body={
                "org_name": self.org_name,
                "name": "api_key1",
            },
            put_body={
                "org_name": self.org_name,
                "name": "api_key2",
            },
            patch_body={"name": "api_key3"},
            list_params=f"?org_name={self.org_name}",
            want_resources=[
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            skip_verbs=["put", "patch"],
        )

    def test_rest_ai_secret_audit_headers(self):
        self._run_rest(
            object_type="ai_secret",
            post_body={
                "org_name": self.org_name,
                "name": "ai_secret1",
            },
            put_body={
                "org_name": self.org_name,
                "name": "ai_secret2",
            },
            patch_body={"name": "ai_secret3"},
            list_params=f"?org_name={self.org_name}",
            want_resources=[
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
        )

    @parameterized.expand(["get", "post"])
    def test_id_fetch(self, method):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]
        self._run(
            method,
            f"{LOCAL_API_URL}/v1/dataset/{dataset_id}/fetch",
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/dataset/[id]/fetch",
        )

    def test_object_type_id_summarize_dataset(self):
        resp = self.registerDataset({"project_name": "p", "dataset_name": "dataset"})
        project_id = resp["project"]["id"]
        dataset_id = resp["dataset"]["id"]
        self._run(
            "get",
            f"{LOCAL_API_URL}/v1/dataset/{dataset_id}/summarize",
            want_resources=[
                {
                    "type": "dataset",
                    "id": dataset_id,
                    "name": "dataset",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/dataset/[id]/summarize",
        )

    def test_object_type_id_summarize_experiment(self):
        resp = self.registerExperiment({"project_name": "p", "experiment_name": "experiment1"})
        project_id = resp["project"]["id"]
        experiment1_id = resp["experiment"]["id"]
        self._run(
            "get",
            f"{LOCAL_API_URL}/v1/experiment/{experiment1_id}/summarize",
            want_resources=[
                {
                    "type": "experiment",
                    "id": experiment1_id,
                    "name": "experiment1",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/experiment/[id]/summarize",
        )

        resp = self.registerExperiment({"project_name": "p", "experiment_name": "experiment2"})
        experiment2_id = resp["experiment"]["id"]
        self._run(
            "get",
            f"{LOCAL_API_URL}/v1/experiment/{experiment1_id}/summarize?summarize_scores=true&comparison_experiment_id={experiment2_id}",
            want_resources=[
                {
                    "type": "experiment",
                    "id": experiment1_id,
                    "name": "experiment1",
                },
                {
                    "type": "experiment",
                    "id": experiment2_id,
                    "name": "experiment2",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/experiment/[id]/summarize",
        )

    def test_object_type_id_insert_feedback(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]

        self._run(
            "post",
            f"{LOCAL_API_URL}/v1/project_logs/{project_id}/insert",
            body={"events": [{"id": "row0", "input": "foo", "output": "bar", "scores": {"x": 0.1}}]},
            want_resources=[
                {
                    "type": "project_log",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/project_logs/[id]/insert",
        )

        self._run(
            "post",
            f"{LOCAL_API_URL}/v1/project_logs/{project_id}/feedback",
            body={"feedback": [{"id": "row0", "scores": {"x": 0.2}, "tags": ["goop"]}]},
            want_resources=[
                {
                    "type": "project_log",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "project",
                    "id": project_id,
                    "name": "p",
                },
                {
                    "type": "organization",
                    "id": self.org_id,
                    "name": self.org_name,
                },
            ],
            want_normalized_url=f"/v1/project_logs/[id]/feedback",
        )
