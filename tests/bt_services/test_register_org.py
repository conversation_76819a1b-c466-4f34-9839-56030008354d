from importlib import reload
from uuid import uuid4

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class RegisterOrgTest(BraintrustAppTestBase):
    def test_basic(self):
        import braintrust

        org_name = str(uuid4())
        resp = braintrust.app_conn().post_json("api/organization/register", dict(org_name=org_name))
        org_id = resp
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT create_api_key(users.auth_id, %s, 'test')
                    FROM users
                    WHERE id=%s""",
                    (org_id, self.user_id),
                )
                org_api_key = cursor.fetchone()[0]
        try:
            braintrust.login(org_name=org_name, app_url=LOCAL_APP_URL, api_key=org_api_key, force_login=True)
            braintrust = reload(braintrust)
            self.assertEqual(braintrust.org_id(), org_id)
        finally:
            BraintrustAppTestBase.tearDownDb(org_id)

    def test_determine_resource_tier(self):
        users = (
            ["free", dict(id=str(uuid4()), email="testuser.com")],
            ["free", dict(id=str(uuid4()), email="testuser.edu")],
        )
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                for _, params in users:
                    cursor.execute(
                        """
                        insert into users(id, email) values (%(id)s, %(email)s)
                    """,
                        params,
                    )
        try:
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    for expected_tier, params in users:
                        cursor.execute(
                            """
                            select determine_resource_tier(%s)
                        """,
                            [params["id"]],
                        )
                        actual_tier = cursor.fetchone()[0]
                        self.assertEqual(expected_tier, actual_tier)
        finally:
            with BraintrustAppTestBase.connect_app_db() as conn:
                with conn.cursor() as cursor:
                    for [_, params] in users:
                        cursor.execute(
                            """
                            delete from users where id = %s
                        """,
                            [params["id"]],
                        )
