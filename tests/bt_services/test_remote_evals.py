import os
import subprocess
import time

import braintrust
import requests

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.bt_services.test_bundled_code import TEST_DIR

PORT = 8301
REMOTE_SERVER_URL = f"http://localhost:{PORT}"


class RemoteEvalsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        remote_evals_file = os.path.join(TEST_DIR, "remote.eval.ts")
        self.process = subprocess.Popen(
            ["npx", "braintrust", "eval", remote_evals_file, "--dev", "--dev-port", str(PORT)],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )

        # For up to 2 minutes, try to connect to the server.
        start = time.time()
        print("Waiting for server to start...")
        started = False
        while time.time() - start < 120:
            try:
                response = requests.get(REMOTE_SERVER_URL + "/")
                if response.ok:
                    started = True
                    break
            except Exception:
                time.sleep(0.5)
        if not started:
            raise Exception("Server failed to start")

    def tearDown(self):
        super().tearDown()
        self.process.terminate()

    # I think we can only have one test, because each setUp() will create a conflicting
    # instance of the dev server.
    def test_remote_eval(self):
        result = self.run_request("get", REMOTE_SERVER_URL + "/list").json()
        self.assertTrue("Simple eval" in result)

        # Try running the eval with invalid array of objects
        resp = self.run_request(
            "post",
            REMOTE_SERVER_URL + "/eval",
            json={
                "name": "Simple eval",
                "parameters": {"array_of_objects": "not an array"},
                "data": {"data": []},
            },
            expect_error=True,
        )
        self.assertEqual(resp.status_code, 400)

        # Now try running an actual eval. We should get the experiment id back.
        resp = self.run_request(
            "post",
            REMOTE_SERVER_URL + "/eval",
            headers={"Authorization": f"Bearer {self.org_api_key}"},
            json={
                "name": "Simple eval",
                "parameters": {"prefix": "Calculator"},
                "data": {"data": [{"input": "1 + 1", "expected": 2}]},
                "scores": [{"name": "foo", "function_id": {"global_function": "ExactMatch"}}],
            },
        )
        self.assertEqual(resp.status_code, 200)
        summary = resp.json()
        self.assertTrue("experimentId" in summary)
        experiment_id = summary["experimentId"]

        # Fetch the experiment logs
        resp = self.run_request(
            "post",
            LOCAL_API_URL + "/btql",
            json={"query": f"select: * | from: experiment('{experiment_id}')"},
        )
        self.assertEqual(resp.status_code, 200)
        logs = resp.json()["data"]

        task_log = next((log for log in logs if log["is_root"]), None)
        self.assertIsNotNone(task_log)
        self.assertEqual(task_log["input"], "1 + 1")
        self.assertEqual(task_log["output"], "Calculator: 1 + 1")

        # Collect the scores
        scores = set()
        for log in logs:
            if "scores" in log and log["scores"]:
                print(log["scores"])
                for score in log["scores"].keys():
                    scores.add(score)
        self.assertEqual(scores, {"ExactMatch", "Levenshtein"})
