import time

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


class EnvVarTest(BraintrustAppTestBase):
    def test_update_secret(self):
        secret = self.run_request(
            "post",
            make_v1_url("env_var"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                name="SECRET",
                value="VALUE",
            ),
        ).json()
        self.assertFalse("value" in secret)

        # Validate the original value
        value = self.run_request(
            "get",
            f"{LOCAL_API_URL}/function-env-decrypt/{secret['id']}",
        ).json()
        self.assertEqual(value["value"], "VALUE")
        use_time = value["used"]

        # Let's make sure the use time is updated too
        time.sleep(1)

        # Fetch the secret, and make sure the use time is not updated
        fetched_value = self.run_request(
            "get",
            make_v1_url("env_var", secret["id"]),
        ).json()
        self.assertEqual(fetched_value["used"], use_time)

        # Update the secret
        secret2 = self.run_request(
            "patch",
            make_v1_url("env_var", secret["id"]),
            json=dict(
                name="SECRET",
                value="VALUE2",
            ),
        ).json()
        self.assertFalse("value" in secret2)

        # Make sure the use time is updated too
        value2 = self.run_request(
            "get",
            f"{LOCAL_API_URL}/function-env-decrypt/{secret['id']}",
        ).json()
        self.assertEqual(value2["value"], "VALUE2")
        self.assertGreater(value2["used"], use_time)

        # add a non-owner user and make sure they can't decrypt
        other_user_id, _, other_user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute("select id from roles where org_id is null and name='Engineer'")
                engineer_role_id = cursor.fetchone()[0]
        self.grant_acl(
            dict(object_type="organization", object_id=self.org_id, user_id=other_user_id, role_id=engineer_role_id),
        )

        other_user_override_headers = {"Authorization": f"Bearer {other_user_api_key}"}
        with self.assertRaises(Exception):
            self.run_request(
                "get",
                f"{LOCAL_API_URL}/function-env-decrypt/{secret['id']}",
                headers=other_user_override_headers,
            )

    def test_permissions(self):
        org_secret = self.run_request(
            "post",
            make_v1_url("env_var"),
            json=dict(
                object_type="organization",
                object_id=self.org_id,
                name="SECRET",
                value="VALUE",
            ),
        ).json()

        project = self.run_request("post", make_v1_url("project"), json=dict(name="project")).json()
        project_secret = self.run_request(
            "post",
            make_v1_url("env_var"),
            json=dict(
                object_type="project",
                object_id=project["id"],
                name="SECRET",
                value="VALUE",
            ),
        ).json()

        function = self.run_request(
            "post",
            make_v1_url("function"),
            json=dict(
                project_id=project["id"],
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [{"role": "user", "content": "Hello"}],
                    },
                },
                function_data={
                    "type": "prompt",
                },
                name="prompt",
                slug="prompt",
            ),
        ).json()
        function_secret = self.run_request(
            "post",
            make_v1_url("env_var"),
            json=dict(
                object_type="function",
                object_id=function["id"],
                name="SECRET",
                value="VALUE",
            ),
        ).json()

        # The org owner should be able to fetch all secrets.
        for secret in (org_secret, project_secret, function_secret):
            self.run_request("get", make_v1_url("env_var", secret["id"]))

        # An unauthorized user who is not part of the org should not be able to
        # fetch any secrets.
        other_org_id, _ = self.createOrg()
        other_user_id, _, other_user_other_org_api_key = self.createUserInOrg(other_org_id)
        for secret in (org_secret, project_secret, function_secret):
            self.run_request(
                "get",
                make_v1_url("env_var", secret["id"]),
                headers=dict(Authorization=f"Bearer {other_user_other_org_api_key}"),
                expect_error=True,
            )

        # If we add them to the org as a non-owner, they should be able to fetch
        # the org secret, but not project or function.
        self.addUserToOrg(user_id=other_user_id, org_id=self.org_id, remove_from_org_owners=True)
        other_user_org_api_key = self.createUserOrgApiKey(user_id=other_user_id, org_id=self.org_id)
        other_user_org_headers = dict(Authorization=f"Bearer {other_user_org_api_key}")
        for secret in (project_secret, function_secret):
            self.run_request(
                "get", make_v1_url("env_var", secret["id"]), headers=other_user_org_headers, expect_error=True
            )
        resp = self.run_request("get", make_v1_url("env_var", org_secret["id"]), headers=other_user_org_headers).json()
        self.assertEqual(resp["id"], org_secret["id"])

        # Grant them project read access (restricted to the project data only),
        # and they should be able to read the project secret, but not the
        # function secret.
        self.grant_acl(
            body=dict(
                object_type="project",
                object_id=project["id"],
                user_id=other_user_id,
                permission="read",
                restrict_object_type="project",
            ),
        )
        resp = self.run_request(
            "get", make_v1_url("env_var", project_secret["id"]), headers=other_user_org_headers
        ).json()
        self.assertEqual(resp["id"], project_secret["id"])
        self.run_request(
            "get", make_v1_url("env_var", function_secret["id"]), headers=other_user_org_headers, expect_error=True
        )

        # Grant them project read access (restricted to prompts) and they should
        # now be able to read the function secret.
        self.grant_acl(
            body=dict(
                object_type="project",
                object_id=project["id"],
                user_id=other_user_id,
                permission="read",
                restrict_object_type="prompt",
            ),
        )
        resp = self.run_request(
            "get", make_v1_url("env_var", function_secret["id"]), headers=other_user_org_headers
        ).json()
        self.assertEqual(resp["id"], function_secret["id"])

        # They should not be able to update or delete secrets.
        self.run_request(
            "patch",
            make_v1_url("env_var", function_secret["id"]),
            headers=other_user_org_headers,
            json=dict(value="NEW_VALUE"),
            expect_error=True,
        )
        self.run_request(
            "delete", make_v1_url("env_var", function_secret["id"]), headers=other_user_org_headers, expect_error=True
        )

        # Granting them update permission should enable them to do both.
        self.grant_acl(
            body=dict(
                object_type="project",
                object_id=project["id"],
                user_id=other_user_id,
                permission="update",
                restrict_object_type="prompt",
            ),
        )
        resp = self.run_request(
            "patch",
            make_v1_url("env_var", function_secret["id"]),
            headers=other_user_org_headers,
            json=dict(name="NEW_SECRET"),
        ).json()
        self.assertEqual(resp["id"], function_secret["id"])
        self.assertEqual(resp["name"], "NEW_SECRET")
        resp = self.run_request(
            "delete", make_v1_url("env_var", function_secret["id"]), headers=other_user_org_headers
        ).json()
        self.assertEqual(resp["id"], function_secret["id"])
