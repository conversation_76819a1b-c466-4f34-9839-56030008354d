from braintrust import Eva<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Reporter
from braintrust_core.score import Score

from autoevals import NumericDiff

Eval(
    "sync",
    data=lambda: [
        EvalCase(input=1, expected=2),
    ],
    task=lambda input, hooks: input * 2,
    scores=[NumericDiff],
)


async def abasic_data():
    yield EvalCase(input=1, expected=2)


async def abasic_task(input, hooks):
    return input * 2


Eval(
    "async",
    data=abasic_data,
    task=abasic_task,
    scores=[NumericDiff],
)

Eval("mixnmatch", data=abasic_data, task=lambda input, hooks: input * 2, scores=[NumericDiff()])
Eval("mixnmatch2", data=lambda: [EvalCase(input=1, expected=2)], task=abasic_task, scores=[NumericDiff()])


def sync_score(input, output, expected):
    return Score(name="sync_score", score=output == expected)


def anon_score(input, output, expected):
    return output == expected


Eval(
    "embedded data",
    data=[EvalCase(input=1, expected=2), {"input": 3}],
    task=lambda input, hooks: input * 2,
    scores=[NumericDiff, sync_score, anon_score],
)

Eval(
    "simple task",
    data=[EvalCase(input=1, expected=2), {"input": 3}],
    task=lambda input: input * 2,
    scores=[NumericDiff, sync_score, anon_score],
)

Reporter(
    "allow anything",
    report_eval=lambda: True,
    report_run=lambda: True,
)
