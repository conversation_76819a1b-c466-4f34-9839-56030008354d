[{"context": {}, "expected": null, "id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "input": [{"content": [{"text": "What is the capital of France?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "messages", "model": "gpt-3.5-turbo"}, "metrics": {"completion_tokens": 7, "prompt_tokens": 14, "tokens": 21}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "message": {"content": "The capital of France is Paris.", "role": "assistant"}}, "root_span_id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "input": [{"content": [{"text": "What is the capital of France?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "messages", "model": "gpt-3.5-turbo"}, "metrics": {"completion_tokens": 7, "prompt_tokens": 14, "tokens": 21}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "message": {"content": "The capital of France is Paris.", "role": "assistant"}}, "root_span_id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "a5587c2ae8fa054cb28276a03e2b971b1c2a5daad8482c1a8aa311e5f3ee93b5", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "61dd362f8fd486bef7dc4c54fe857cf4a88dff063f0d494e8380d3233a02b5bb", "input": [{"content": [{"text": "What is the weather in San Francisco and what attractions should I visit?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "prompt", "model": "gpt-3.5-turbo", "temperature": 0, "tools": [{"function": {"description": "Get the weather in a location", "name": "weather", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"location": {"description": "The location to get the weather for", "type": "string"}}, "required": ["location"], "type": "object"}}, "type": "function"}, {"function": {"name": "cityAttractions", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"city": {"type": "string"}}, "required": ["city"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 45, "prompt_tokens": 83, "tokens": 128}, "origin": null, "output": {"finish_reason": "tool-calls", "index": 0, "message": {"content": "[{\"toolCallType\":\"function\",\"toolCallId\":\"call_0U2XcM4r5LJrsdcGoDtH2Acb\",\"toolName\":\"weather\",\"args\":\"{\\\"location\\\": \\\"San Francisco\\\"}\"},{\"toolCallType\":\"function\",\"toolCallId\":\"call_EZqQYgvGX4pkrxyOMvQ2aSXF\",\"toolName\":\"cityAttractions\",\"args\":\"{\\\"city\\\": \\\"San Francisco\\\"}\"}]", "role": "assistant"}}, "root_span_id": "61dd362f8fd486bef7dc4c54fe857cf4a88dff063f0d494e8380d3233a02b5bb", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "61dd362f8fd486bef7dc4c54fe857cf4a88dff063f0d494e8380d3233a02b5bb", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8fc9df585c310873e8ad7542e83e4e0d4106d119764698d395431ee9522fffcf", "input": [{"content": [{"text": "What is the weather in San Francisco and what attractions should I visit?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "prompt", "model": "gpt-3.5-turbo", "temperature": 0, "tools": [{"function": {"description": "Get the weather in a location", "name": "weather", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"location": {"description": "The location to get the weather for", "type": "string"}}, "required": ["location"], "type": "object"}}, "type": "function"}, {"function": {"name": "cityAttractions", "parameters": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"city": {"type": "string"}}, "required": ["city"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 45, "prompt_tokens": 83, "tokens": 128}, "origin": null, "output": {"finish_reason": "tool-calls", "index": 0, "message": {"content": "[{\"toolCallType\":\"function\",\"toolCallId\":\"call_qvCK5KWgqaomAIT8uIHsloCM\",\"toolName\":\"weather\",\"args\":\"{\\\"location\\\": \\\"San Francisco\\\"}\"},{\"toolCallType\":\"function\",\"toolCallId\":\"call_wchq5kXITLOItDGGLkj38w10\",\"toolName\":\"cityAttractions\",\"args\":\"{\\\"city\\\": \\\"San Francisco\\\"}\"}]", "role": "assistant"}}, "root_span_id": "8fc9df585c310873e8ad7542e83e4e0d4106d119764698d395431ee9522fffcf", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "8fc9df585c310873e8ad7542e83e4e0d4106d119764698d395431ee9522fffcf", "span_parents": null, "tags": null}]