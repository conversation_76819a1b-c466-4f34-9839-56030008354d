// Mirror of tests/test_span_identifier_v2.py.

import { v4 as uuidv4 } from "uuid";

import {
  SpanObjectTypeV2,
  SpanComponentsV2,
  SpanRowIdsV2,
  SpanComponentsV1,
  SpanObjectTypeV1,
  SpanRowIdsV1,
} from "@braintrust/core";

function assertThrows(f: () => void) {
  let didThrow = false;
  try {
    f();
  } catch {
    didThrow = true;
  }
  if (!didThrow) {
    throw new Error("Function did not throw");
  }
}

function assertEquals(lhs: SpanComponentsV2, rhs: SpanComponentsV2) {
  const lhsStr = JSON.stringify(lhs.toObject());
  const rhsStr = JSON.stringify(rhs.toObject());
  if (lhsStr !== rhsStr) {
    throw new Error(`lhs != rhs:\nlhs = ${lhsStr}\nrhs = ${rhsStr}`);
  }
}

function testSpanRowIdsV2() {
  new SpanRowIdsV2({ rowId: "x", spanId: "x", rootSpanId: "x" });
  assertThrows(() => {
    new SpanRowIdsV2({ rowId: "", spanId: "x", rootSpanId: "x" });
  });
  assertThrows(() => {
    new SpanRowIdsV2({ rowId: "x", spanId: "", rootSpanId: "x" });
  });
  assertThrows(() => {
    new SpanRowIdsV2({ rowId: "x", spanId: "x", rootSpanId: "" });
  });
}

function testNoRowId() {
  for (const objectType of [
    SpanObjectTypeV2.EXPERIMENT,
    SpanObjectTypeV2.PROJECT_LOGS,
  ]) {
    const objectId = uuidv4();
    const computeObjectMetadataArgs = { foo: "bar", hello: { goodbye: "yes" } };
    let components = new SpanComponentsV2({ objectType, objectId });
    assertEquals(SpanComponentsV2.fromStr(components.toStr()), components);
    components = new SpanComponentsV2({
      objectType,
      computeObjectMetadataArgs,
    });
    assertEquals(SpanComponentsV2.fromStr(components.toStr()), components);
    components = new SpanComponentsV2({
      objectType,
      objectId,
      computeObjectMetadataArgs,
    });
    assertEquals(SpanComponentsV2.fromStr(components.toStr()), components);
  }
}

function testWithRowId() {
  const [objectId, rowId, spanId, rootSpanId] = Array.from({ length: 4 }).map(
    () => uuidv4(),
  );
  const computeObjectMetadataArgs = { foo: "bar", hello: { goodbye: "yes" } };
  let components = new SpanComponentsV2({
    objectType: SpanObjectTypeV2.EXPERIMENT,
    objectId,
    computeObjectMetadataArgs,
    rowIds: new SpanRowIdsV2({ rowId, spanId, rootSpanId }),
  });
  assertEquals(SpanComponentsV2.fromStr(components.toStr()), components);

  components = new SpanComponentsV2({
    objectType: SpanObjectTypeV2.EXPERIMENT,
    objectId,
    computeObjectMetadataArgs,
    rowIds: new SpanRowIdsV2({ rowId: "hello world", spanId, rootSpanId }),
  });
  assertEquals(SpanComponentsV2.fromStr(components.toStr()), components);

  function roundtripBadComponent(args?: {
    objectId?: string;
    rowId?: string;
    spanId?: string;
    rootSpanId?: string;
  }) {
    const x = uuidv4();
    const { objectId: _, ...rowIdKwargs } = args ?? {};
    const objectId = args?.objectId ?? x;
    const components = new SpanComponentsV2({
      objectType: SpanObjectTypeV2.EXPERIMENT,
      objectId,
      computeObjectMetadataArgs,
      rowIds: new SpanRowIdsV2({
        ...{ rowId: x, spanId: x, rootSpanId: x },
        ...rowIdKwargs,
      }),
    });
    SpanComponentsV2.fromStr(components.toStr());
  }

  assertThrows(() => roundtripBadComponent({ objectId: "hello" }));
  assertThrows(() => roundtripBadComponent({ spanId: "hello" }));
  assertThrows(() => roundtripBadComponent({ rootSpanId: "hello" }));
}

function testBackwardsCompatible() {
  const [objectId, rowId, spanId, rootSpanId] = Array.from({ length: 4 }).map(
    () => uuidv4(),
  );
  const spanComponentsOld = new SpanComponentsV1({
    objectType: SpanObjectTypeV1.EXPERIMENT,
    objectId,
    rowIds: new SpanRowIdsV1({ rowId, spanId, rootSpanId }),
  });
  const spanComponentsNew = SpanComponentsV2.fromStr(spanComponentsOld.toStr());
  const newObjectType: number = spanComponentsNew.objectType;
  const oldObjectType: number = spanComponentsOld.objectType;
  if (!(newObjectType === oldObjectType)) {
    throw new Error();
  }
  if (!(spanComponentsNew.objectId === spanComponentsOld.objectId)) {
    throw new Error();
  }
  if (
    !(
      spanComponentsNew.rowIds &&
      spanComponentsOld.rowIds &&
      JSON.stringify(spanComponentsNew.rowIds.toObject()) ===
        JSON.stringify(spanComponentsOld.rowIds.toObject())
    )
  ) {
    throw new Error();
  }
  if (spanComponentsNew.computeObjectMetadataArgs) {
    throw new Error();
  }
}

async function main() {
  testSpanRowIdsV2();
  testNoRowId();
  testWithRowId();
  testBackwardsCompatible();
}

main();
