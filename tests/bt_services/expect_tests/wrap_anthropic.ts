import { Anthropic } from "@anthropic-ai/sdk";
import { Message } from "@braintrust/core/typespecs";
import { deepEqual } from "assert";
import { initExperiment, wrapAnthropic, wrapOpenAI } from "braintrust";
import OpenAI from "openai";
import { CompletionUsage } from "openai/resources";
import { z } from "zod";

// Since this test involves the prompt cache, change this NONCE to a new subject when updating.
const NONCE1 = "scallop";
const NONCE2 = "cheese";

async function getExperimentMetrics(experimentId: string) {
  const results = await fetch("http://localhost:8000/btql", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
    },
    body: JSON.stringify({
      query: `select: metrics | from: experiment('${experimentId}') spans | filter: span_attributes.type='llm' | sort: created`,
    }),
  });
  if (!results.ok) {
    throw new Error(`Failed to get experiment metrics: ${results.statusText}`);
  }
  const data = await results.json();
  return data.data.map((d: Record<string, unknown>) =>
    btMetricsSchema.parse(d.metrics),
  );
}

export const btMetricsSchema = z.object({
  completion_tokens: z.number().optional(),
  prompt_cache_creation_tokens: z.number().optional(),
  prompt_cached_tokens: z.number().optional(),
  prompt_tokens: z.number().optional(),
  tokens: z.number().optional(),
});
type BraintrustMetrics = z.infer<typeof btMetricsSchema>;

function anthropicUsageEqualsBraintrustMetrics(
  anthropicUsage: Anthropic.Messages.Usage,
  btMetrics: BraintrustMetrics,
) {
  if (anthropicUsage.output_tokens !== btMetrics.completion_tokens) {
    throw new Error(
      `Output tokens do not match: ${anthropicUsage.output_tokens} !== ${btMetrics.completion_tokens}`,
    );
  }
  if (
    anthropicUsage.input_tokens +
      (anthropicUsage.cache_read_input_tokens ?? 0) +
      (anthropicUsage.cache_creation_input_tokens ?? 0) !==
    btMetrics.prompt_tokens
  ) {
    throw new Error(
      `Prompt tokens do not match: ${anthropicUsage.input_tokens} + ${anthropicUsage.cache_read_input_tokens} + ${anthropicUsage.cache_creation_input_tokens} !== ${btMetrics.prompt_tokens}`,
    );
  }
  if (
    (anthropicUsage.cache_read_input_tokens ?? 0) !==
    btMetrics.prompt_cached_tokens
  ) {
    throw new Error(
      `Cache read output tokens do not match: ${anthropicUsage.cache_read_input_tokens} + ${anthropicUsage.cache_creation_input_tokens} !== ${btMetrics.prompt_cached_tokens}`,
    );
  }
  if (
    (anthropicUsage.cache_creation_input_tokens ?? 0) !==
    btMetrics.prompt_cache_creation_tokens
  ) {
    throw new Error(
      `Cache creation output tokens do not match: ${anthropicUsage.cache_read_input_tokens} + ${anthropicUsage.cache_creation_input_tokens} !== ${btMetrics.prompt_cache_creation_tokens}`,
    );
  }
  if (
    (anthropicUsage.cache_read_input_tokens ?? 0) +
      (anthropicUsage.cache_creation_input_tokens ?? 0) +
      anthropicUsage.input_tokens +
      anthropicUsage.output_tokens !==
    btMetrics.tokens
  ) {
    throw new Error(
      `Total tokens do not match: ${anthropicUsage.cache_read_input_tokens} + ${anthropicUsage.cache_creation_input_tokens} + ${anthropicUsage.input_tokens} + ${anthropicUsage.output_tokens} !== ${btMetrics.tokens}`,
    );
  }
  return true;
}

async function runAnthropicMessages(
  anthropic: Anthropic,
  messages: Anthropic.Messages.MessageParam[],
): Promise<{
  nonStreamingUsage: Anthropic.Messages.Usage;
  streamingUsage: Anthropic.Messages.Usage;
  nonStreamingBtMetrics: BraintrustMetrics;
  streamingBtMetrics: BraintrustMetrics;
}> {
  const experiment = initExperiment("wrap_anthropic");
  const nonStreamingUsage = await experiment.traced(async () => {
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-latest",
      messages,
      max_tokens: 100,
      stream: false,
      temperature: 0,
    });
    return response.usage;
  });

  // Just to make sure the created timestamps line up
  await new Promise((resolve) => setTimeout(resolve, 50));

  const streamingUsage = await experiment.traced(async () => {
    const response = await anthropic.messages.create({
      model: "claude-3-5-sonnet-latest",
      messages,
      max_tokens: 100,
      stream: true,
      temperature: 0,
    });
    let allUsage = {};
    for await (const chunk of response) {
      if (chunk.type === "message_start") {
        allUsage = {
          ...allUsage,
          ...chunk.message.usage,
        };
      } else if (chunk.type === "message_delta") {
        allUsage = {
          ...allUsage,
          ...chunk.usage,
        };
      }
    }
    return allUsage;
  });

  await experiment.flush();

  const [nonStreamingBtMetrics, streamingBtMetrics] =
    await getExperimentMetrics(await experiment.id);

  return {
    nonStreamingUsage,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    streamingUsage: streamingUsage as Anthropic.Messages.Usage,
    nonStreamingBtMetrics,
    streamingBtMetrics,
  };
}

async function sdkTest() {
  const anthropic = wrapAnthropic(new Anthropic({}));

  {
    const {
      nonStreamingUsage,
      streamingUsage,
      nonStreamingBtMetrics,
      streamingBtMetrics,
    } = await runAnthropicMessages(anthropic, [
      { role: "user", content: `Repeat the following word: ${NONCE1}` },
    ]);

    deepEqual(nonStreamingUsage, streamingUsage);

    anthropicUsageEqualsBraintrustMetrics(
      nonStreamingUsage,
      nonStreamingBtMetrics,
    );
    anthropicUsageEqualsBraintrustMetrics(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      streamingUsage as Anthropic.Messages.Usage,
      streamingBtMetrics,
    );
  }

  {
    const baseText = `Yo yo please echo the following word: ${NONCE2}. `.repeat(
      100,
    );

    // Next, try to create cache breakpoints
    await runAnthropicMessages(anthropic, [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: baseText,
            cache_control: {
              type: "ephemeral",
            },
          },
        ],
      },
    ]);

    await new Promise((resolve) => setTimeout(resolve, 100));

    // And run again with a follow up message
    const {
      nonStreamingUsage,
      streamingUsage,
      nonStreamingBtMetrics,
      streamingBtMetrics,
    } = await runAnthropicMessages(anthropic, [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: baseText,
            cache_control: {
              type: "ephemeral",
            },
          },
        ],
      },
      {
        role: "assistant",
        content: NONCE2,
      },
      {
        role: "user",
        content: `Repeat the following word: hi`,
      },
    ]);

    deepEqual(nonStreamingUsage, streamingUsage);

    anthropicUsageEqualsBraintrustMetrics(
      nonStreamingUsage,
      nonStreamingBtMetrics,
    );
    anthropicUsageEqualsBraintrustMetrics(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      streamingUsage as Anthropic.Messages.Usage,
      streamingBtMetrics,
    );

    if (nonStreamingUsage.cache_read_input_tokens === 0) {
      throw new Error("Cache read input tokens should not be 0");
    }
  }
}

export type ExtendedUsage = CompletionUsage & {
  prompt_tokens_details: {
    cached_tokens?: number;
    cache_creation_tokens?: number;
  };
};

async function runOpenAIMessages(
  openai: OpenAI,
  messages: Message[],
): Promise<{
  nonStreamingUsage: ExtendedUsage;
  streamingUsage: ExtendedUsage;
  nonStreamingBtMetrics: BraintrustMetrics;
  streamingBtMetrics: BraintrustMetrics;
}> {
  const experiment = initExperiment("wrap_anthropic");
  const nonStreamingUsage = await experiment.traced(async () => {
    const response = await openai.chat.completions.create({
      model: "claude-3-5-sonnet-latest",
      // @ts-ignore Ideally we have a nice import to monkey-patch the OpenAI client here
      messages,
      max_tokens: 100,
      stream: false,
      temperature: 0,
    });
    return response.usage;
  });

  // Just to make sure the created timestamps line up
  await new Promise((resolve) => setTimeout(resolve, 50));

  const streamingUsage = await experiment.traced(async () => {
    const response = await openai.chat.completions.create({
      model: "claude-3-5-sonnet-latest",
      // @ts-ignore Ideally we have a nice import to monkey-patch the OpenAI client here
      messages,
      max_tokens: 100,
      stream: true,
      temperature: 0,
    });
    let allUsage = {};
    for await (const chunk of response) {
      if ("usage" in chunk) {
        allUsage = {
          ...allUsage,
          ...chunk.usage,
        };
      }
    }
    return allUsage;
  });

  await experiment.flush();

  const [nonStreamingBtMetrics, streamingBtMetrics] =
    await getExperimentMetrics(await experiment.id);

  return {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    nonStreamingUsage: nonStreamingUsage as ExtendedUsage,
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    streamingUsage: streamingUsage as ExtendedUsage,
    nonStreamingBtMetrics,
    streamingBtMetrics,
  };
}

function openaiUsageEqualsBraintrustMetrics(
  openaiUsage: ExtendedUsage,
  btMetrics: BraintrustMetrics,
) {
  if (openaiUsage.completion_tokens !== btMetrics.completion_tokens) {
    throw new Error(
      `Output tokens do not match: ${openaiUsage.completion_tokens} !== ${btMetrics.completion_tokens}`,
    );
  }
  if (openaiUsage.prompt_tokens !== btMetrics.prompt_tokens) {
    throw new Error(
      `Prompt tokens do not match: ${openaiUsage.prompt_tokens} !== ${btMetrics.prompt_tokens}`,
    );
  }
  if (
    (openaiUsage.prompt_tokens_details?.cached_tokens ?? 0) !==
    btMetrics.prompt_cached_tokens
  ) {
    throw new Error(
      `Cache read output tokens do not match: ${openaiUsage.prompt_tokens_details?.cached_tokens} + ${openaiUsage.prompt_tokens_details?.cache_creation_tokens} !== ${btMetrics.prompt_cached_tokens}`,
    );
  }
  if (
    (openaiUsage.prompt_tokens_details?.cache_creation_tokens ?? 0) !==
    btMetrics.prompt_cache_creation_tokens
  ) {
    throw new Error(
      `Cache creation output tokens do not match: ${openaiUsage.prompt_tokens_details?.cached_tokens} + ${openaiUsage.prompt_tokens_details?.cache_creation_tokens} !== ${btMetrics.prompt_cache_creation_tokens}`,
    );
  }
  if (
    openaiUsage.prompt_tokens + openaiUsage.completion_tokens !==
    btMetrics.tokens
  ) {
    throw new Error(
      `Total tokens do not match: ${openaiUsage.prompt_tokens} + ${openaiUsage.completion_tokens} !== ${btMetrics.tokens}`,
    );
  }
  return true;
}

async function openaiTest() {
  const openai = wrapOpenAI(new OpenAI({}));

  {
    const {
      nonStreamingUsage,
      streamingUsage,
      nonStreamingBtMetrics,
      streamingBtMetrics,
    } = await runOpenAIMessages(openai, [
      { role: "user", content: `Repeat the following word: ${NONCE1}` },
    ]);

    console.log(nonStreamingUsage);
    console.log(streamingUsage);

    deepEqual(nonStreamingUsage, streamingUsage);

    openaiUsageEqualsBraintrustMetrics(
      nonStreamingUsage,
      nonStreamingBtMetrics,
    );
    openaiUsageEqualsBraintrustMetrics(streamingUsage, streamingBtMetrics);
  }

  {
    const baseText = `Yo yo please echo the following word: ${NONCE2}. `.repeat(
      100,
    );

    // Next, try to create cache breakpoints
    await runOpenAIMessages(openai, [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: baseText,
            cache_control: {
              type: "ephemeral",
            },
          },
        ],
      },
    ]);

    await new Promise((resolve) => setTimeout(resolve, 100));

    // And run again with a follow up message
    const {
      nonStreamingUsage,
      streamingUsage,
      nonStreamingBtMetrics,
      streamingBtMetrics,
    } = await runOpenAIMessages(openai, [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: baseText,
            cache_control: {
              type: "ephemeral",
            },
          },
        ],
      },
      {
        role: "assistant",
        content: NONCE2,
      },
      {
        role: "user",
        content: `Repeat the following word: hi`,
      },
    ]);

    deepEqual(nonStreamingUsage, streamingUsage);

    openaiUsageEqualsBraintrustMetrics(
      nonStreamingUsage,
      nonStreamingBtMetrics,
    );
    openaiUsageEqualsBraintrustMetrics(streamingUsage, streamingBtMetrics);

    if (nonStreamingUsage.prompt_tokens_details?.cached_tokens === 0) {
      throw new Error("Cache read input tokens should not be 0");
    }
  }
}

async function main() {
  await sdkTest();
  await openaiTest();
}

main().catch(console.error);
