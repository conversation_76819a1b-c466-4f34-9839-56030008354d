[{"context": {}, "expected": null, "id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"model": "gpt-4o", "provider": "openai", "response_format": {"json_schema": {"name": "CalendarEvent", "schema": {"properties": {"date": {"title": "Date", "type": "string"}, "name": {"title": "Name", "type": "string"}, "participants": {"items": {"type": "string"}, "title": "Participants", "type": "array"}}, "required": ["name", "date", "participants"], "title": "CalendarEvent", "type": "object"}}, "type": "json_schema"}, "temperature": 0, "tools": [{"function": {"name": "QueryCalendarEvent", "parameters": {"additionalProperties": false, "properties": {"date": {"title": "Date", "type": "string"}, "name": {"title": "Name", "type": "string"}}, "required": ["name", "date"], "title": "QueryCalendarEvent", "type": "object"}, "strict": true}, "type": "function"}]}, "metrics": {"completion_accepted_prediction_tokens": 0, "completion_audio_tokens": 0, "completion_reasoning_tokens": 0, "completion_rejected_prediction_tokens": 0, "completion_tokens": 30, "prompt_audio_tokens": 0, "prompt_cached_tokens": 0, "prompt_tokens": 113, "tokens": 143}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": [], "audio": null, "content": "{\"name\":\"Team Meeting\",\"date\":\"2023-11-10\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "function_call": null, "parsed": {"date": "2023-11-10", "name": "Team Meeting", "participants": ["<PERSON>", "<PERSON>", "<PERSON>"]}, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "279089ebe631674e301cf4c04806d006995a5188b2b019a58c5324d39a7a649b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"model": "gpt-4o", "provider": "openai", "text": {"format": {"name": "calendar_event", "schema": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "strict": true, "type": "json_schema"}}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 35, "prompt_cached_tokens": 0, "prompt_tokens": 53, "tokens": 88}, "origin": null, "output": [{"content": [{"annotations": [], "logprobs": null, "text": "{\"name\":\"Annual Technology Conference\",\"date\":\"2023-12-05\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "type": "output_text"}], "id": "msg_680ac0c125f88192b2fc63fa0a9259d60e9f80e36095dcc8", "role": "assistant", "status": "completed", "type": "message"}], "root_span_id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "scores": null, "span_attributes": {"name": "Response", "type": "llm"}, "span_id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "input": [{"content": "say 10", "role": "system"}], "is_root": true, "metadata": {"model": "gpt-4o", "provider": "openai", "text": {"format": {"name": "calendar_event", "schema": {"additionalProperties": false, "properties": {"date": {"type": "string"}, "name": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}}, "required": ["name", "date", "participants"], "type": "object"}, "strict": true, "type": "json_schema"}}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 35, "prompt_cached_tokens": 0, "prompt_tokens": 53, "tokens": 88}, "origin": null, "output": [{"content": [{"annotations": [], "logprobs": null, "text": "{\"name\":\"Annual Technology Conference\",\"date\":\"2023-12-05\",\"participants\":[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]}", "type": "output_text"}], "id": "msg_680ac0c125f88192b2fc63fa0a9259d60e9f80e36095dcc8", "role": "assistant", "status": "completed", "type": "message"}], "root_span_id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "scores": null, "span_attributes": {"name": "Response", "type": "llm"}, "span_id": "7603deb680a0172594475b77f2301c26353f9aaace408e9031a6f98e74b5cb84", "span_parents": null, "tags": null}]