// Mirror of tests/test_span_identifier_v3.py.

import { v4 as uuidv4 } from "uuid";

import {
  SpanComponentsV2,
  SpanObjectTypeV2,
  SpanRowIdsV2,
  SpanObjectTypeV3,
  SpanComponentsV3,
  deterministicReplacer,
} from "@braintrust/core";

function assertEquals(lhs: SpanComponentsV3, rhs: SpanComponentsV3) {
  const lhsStr = JSON.stringify(lhs.data, deterministicReplacer);
  const rhsStr = JSON.stringify(rhs.data, deterministicReplacer);
  if (lhsStr !== rhsStr) {
    throw new Error(`lhs != rhs:\nlhs = ${lhsStr}\nrhs = ${rhsStr}`);
  }
}

function testNoRowId() {
  for (const object_type of [
    SpanObjectTypeV3.EXPERIMENT,
    SpanObjectTypeV3.PROJECT_LOGS,
  ]) {
    const object_id = uuidv4();
    const compute_object_metadata_args = {
      foo: "bar",
      hello: { goodbye: "yes" },
    };
    let components = new SpanComponentsV3({ object_type, object_id });
    assertEquals(SpanComponentsV3.fromStr(components.toStr()), components);
    components = new SpanComponentsV3({
      object_type,
      compute_object_metadata_args,
    });
    assertEquals(SpanComponentsV3.fromStr(components.toStr()), components);

    // This should fail to compile.
    //
    // new SpanComponentsV3({
    //   object_type,
    //   object_id,
    //   compute_object_metadata_args,
    // });
  }
}

function testWithRowId() {
  const [object_id, row_id, span_id, root_span_id] = [
    "a",
    uuidv4(),
    "c",
    uuidv4(),
  ];
  const components = new SpanComponentsV3({
    object_type: SpanObjectTypeV3.EXPERIMENT,
    object_id,
    row_id,
    span_id,
    root_span_id,
  });
  assertEquals(SpanComponentsV3.fromStr(components.toStr()), components);

  // Must provide all or none of the row IDs. So these will fail to compile.
  //
  //
  // new SpanComponentsV3({
  //   object_type: SpanObjectTypeV3.EXPERIMENT,
  //   object_id,
  //   row_id,
  //   span_id,
  // });
  // new SpanComponentsV3({
  //   object_type: SpanObjectTypeV3.EXPERIMENT,
  //   object_id,
  //   row_id,
  //   root_span_id,
  // });
  // new SpanComponentsV3({
  //   object_type: SpanObjectTypeV3.EXPERIMENT,
  //   object_id,
  //   span_id,
  //   root_span_id,
  // });
}

function testBackwardsCompatible() {
  const [object_id, row_id, span_id, root_span_id] = Array.from({
    length: 4,
  }).map(() => uuidv4());
  const spanComponentsOld = new SpanComponentsV2({
    objectType: SpanObjectTypeV2.EXPERIMENT,
    objectId: object_id,
    rowIds: new SpanRowIdsV2({
      rowId: row_id,
      spanId: span_id,
      rootSpanId: root_span_id,
    }),
  });
  const spanComponentsNew = SpanComponentsV3.fromStr(spanComponentsOld.toStr());
  if (
    !(
      Number(spanComponentsNew.data.object_type) ===
      Number(spanComponentsOld.objectType)
    )
  ) {
    throw new Error();
  }
  if (!(spanComponentsNew.data.object_id === spanComponentsOld.objectId)) {
    throw new Error();
  }
  if (!(spanComponentsNew.data.row_id === spanComponentsOld.rowIds?.rowId)) {
    throw new Error();
  }
  if (!(spanComponentsNew.data.span_id === spanComponentsOld.rowIds?.spanId)) {
    throw new Error();
  }
  if (
    !(
      spanComponentsNew.data.root_span_id ===
      spanComponentsOld.rowIds?.rootSpanId
    )
  ) {
    throw new Error();
  }
  if (spanComponentsNew.data.compute_object_metadata_args) {
    throw new Error();
  }
}

async function main() {
  testNoRowId();
  testWithRowId();
  testBackwardsCompatible();
}

main();
