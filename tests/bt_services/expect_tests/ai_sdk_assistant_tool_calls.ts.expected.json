[{"context": {}, "expected": null, "id": "5f0a3e66a9d5f81952312751124bc8f7a8e448a41629ef130fe52475e368fec0", "input": [{"content": "Hi!", "role": "system"}, {"content": "Hello, how can I help?", "role": "assistant"}, {"content": [{"text": "What is the capital of France?", "type": "text"}], "role": "user"}], "is_root": true, "metadata": {"inputFormat": "messages", "model": "gpt-4o", "temperature": 0}, "metrics": {"completion_tokens": 8, "prompt_tokens": 31, "tokens": 39}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "message": {"content": "The capital of France is Paris.", "role": "assistant"}}, "root_span_id": "5f0a3e66a9d5f81952312751124bc8f7a8e448a41629ef130fe52475e368fec0", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "5f0a3e66a9d5f81952312751124bc8f7a8e448a41629ef130fe52475e368fec0", "span_parents": null, "tags": null}]