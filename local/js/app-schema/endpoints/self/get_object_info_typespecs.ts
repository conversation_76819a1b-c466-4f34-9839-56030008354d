import { z } from "zod";
import { aclObjectTypeEnum, permissionEnum } from "@braintrust/core/typespecs";

export const inputSchema = z.object({
  object_type: aclObjectTypeEnum,
  override_restrict_object_type: aclObjectTypeEnum.nullish(),
  object_ids: z.string().array(),
  // Old API backends were written in a way that over-strictly checked that
  // the AclObjectTypes we returned fit inside their definition of the
  // AclObjectType schema. This is problematic new AclObjectTypes would be
  // rejected by the backend. To work around this, new backends will
  // explicitly signal that they accept an arbitrary set of AclObjectTypes.
  //
  // DEPRECATION_NOTICE: This flag can be deprecated once users have migrated
  // to API version >= 0.0.45.
  accept_arbitrary_acl_object_types: z.boolean().nullish(),
  allow_sysadmin_roles: z.string().array().nullish(),
  include_deleted_objects: z.boolean().nullish(),
});

export const outputSchema = z
  .object({
    object_id: z.string(),
    object_name: z.string(),
    parent_cols: z.record(
      // We decay the type to string here, so that old API backends do not try
      // to parse the output using a potentially-outdated version of the
      // `aclObjectTypeEnum` schema.
      z.string(),
      z.strictObject({
        id: z.string(),
        name: z.string(),
      }),
    ),
    permissions: permissionEnum.array(),
    // Only populated if the 'allow_sysadmin_roles' input is set.
    is_allowed_sysadmin: z.boolean().nullish(),
  })
  .array();
