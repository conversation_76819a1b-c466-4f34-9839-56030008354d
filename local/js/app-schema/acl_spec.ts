import { AclObjectType } from "@braintrust/core/typespecs";

export interface AclSpec {
  // A sequence of object types higher up in the logical object hierarchy, which
  // this object inherits permissions from. Conventionally we list these in
  // order of leaf to root, in case people care about the ordering.
  //
  // Note that the "physical" table information is defined in
  // local/js/app-schema/acl_object_table_info.ts:AclObjectToTableInfo. So
  // making adjustments here may necessitate corresponding adjustments there.
  parentAclObjectTypes: AclObjectType[];
}

export const aclSpecs: Record<AclObjectType, AclSpec> = {
  organization: {
    parentAclObjectTypes: [],
  },
  project: {
    parentAclObjectTypes: ["org_project", "organization"],
  },
  experiment: {
    parentAclObjectTypes: ["project", "org_project", "organization"],
  },
  dataset: {
    parentAclObjectTypes: ["project", "org_project", "organization"],
  },
  prompt: {
    parentAclObjectTypes: ["project", "org_project", "organization"],
  },
  prompt_session: {
    parentAclObjectTypes: ["project", "org_project", "organization"],
  },
  group: {
    parentAclObjectTypes: ["organization"],
  },
  role: {
    parentAclObjectTypes: ["organization"],
  },
  org_member: {
    parentAclObjectTypes: ["organization"],
  },
  project_log: {
    parentAclObjectTypes: ["project", "org_project", "organization"],
  },
  org_project: {
    parentAclObjectTypes: ["organization"],
  },
};
