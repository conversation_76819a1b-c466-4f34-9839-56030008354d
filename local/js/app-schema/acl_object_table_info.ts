// The AclObjectTableInfos structure aims to map "physical" SQL tables to
// conceptual RBAC "nouns", to enable easy RBAC-safe query construction. The
// AclObjectType enum covers all nouns in the RBAC hierarchy, onto which we may
// attach permissions. However, there are two caveats:
//
//  1. Not all RBAC nouns correspond to physical tables, e.g. "org_project", but
//  we still want to check for ACLs against this noun. For this, we allow
//  mapping an RBAC noun to a "virtual" table, which describes a physical table
//  that contains the ids and other info to use for checking that noun.
//
//  2. Not all physical tables correspond to AclObjectTypes, e.g.
//  "project_scores". For this, we extend the AclObjectType enum to include
//  these purely-physical tables. These tables can provide data and join to
//  other tables, but they will not be checked for ACLs.

import { aclObjectTypeEnum, AclObjectType } from "@braintrust/core/typespecs";
import { z } from "zod";

const tableInfoObjectTypeEnum = z.enum([
  ...aclObjectTypeEnum.options,
  "project_automation",
  "project_score",
  "project_tag",
  "span_iframe",
  "view",
  "api_key",
  "user",
  "ai_secret",
]);

export type TableInfoObjectType = z.infer<typeof tableInfoObjectTypeEnum>;

type ForeignKeyIdsType = { [K in TableInfoObjectType]?: string };

// Denotes whether a table's linkage to a particular parent table is optional.
// We can infer that optional linkage to org implies optional linkage to
// project, but not the other way around.
export type OptionalLinkageSpec = "org" | "project";

export function isOrgLinkageOptional(spec: OptionalLinkageSpec | undefined) {
  return spec === "org";
}

export function isProjectLinkageOptional(
  spec: OptionalLinkageSpec | undefined,
) {
  return spec === "org" || spec == "project";
}

export type AclObjectTableInfo = {
  tableSchema?: string;
  tableName: string;
  nameCol: string;
  deletedAtCol?: string;
  foreignKeyIds?: ForeignKeyIdsType;
  optionalLinkageSpec?: OptionalLinkageSpec;
};

const DefaultAclObjectTableInfo = {
  nameCol: "name",
  deletedAtCol: "deleted_at",
} as const;

export type AclObjectTableInfoEntry =
  | { kind: "table"; data: AclObjectTableInfo }
  | { kind: "virtual"; data: AclObjectType };

export const aclObjectTableInfos: Record<
  TableInfoObjectType,
  AclObjectTableInfoEntry
> = {
  organization: {
    kind: "table",
    data: {
      tableName: "organizations",
      nameCol: "name",
      optionalLinkageSpec: "project",
    },
  },
  project: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "projects",
      foreignKeyIds: {
        organization: "org_id",
      },
    },
  },
  experiment: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "experiments",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  dataset: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "datasets",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  prompt: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "prompts",
      nameCol: "slug",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  prompt_session: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "prompt_sessions",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  project_automation: {
    kind: "table",
    data: {
      tableName: "project_automations",
      nameCol: "name",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  project_score: {
    kind: "table",
    data: {
      tableName: "project_scores",
      nameCol: "name",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  project_tag: {
    kind: "table",
    data: {
      tableName: "project_tags",
      nameCol: "name",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  span_iframe: {
    kind: "table",
    data: {
      tableName: "span_iframes",
      nameCol: "name",
      foreignKeyIds: {
        project: "project_id",
      },
    },
  },
  group: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "groups",
      foreignKeyIds: {
        organization: "org_id",
      },
      optionalLinkageSpec: "project",
    },
  },
  role: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "roles",
      foreignKeyIds: {
        organization: "org_id",
      },
      optionalLinkageSpec: "org",
    },
  },
  org_member: { kind: "virtual", data: "organization" },
  project_log: { kind: "virtual", data: "project" },
  org_project: { kind: "virtual", data: "organization" },
  view: {
    kind: "table",
    data: {
      ...DefaultAclObjectTableInfo,
      tableName: "views",
      optionalLinkageSpec: "org",
    },
  },
  user: {
    kind: "table",
    data: {
      tableName: "users",
      nameCol: "email",
      optionalLinkageSpec: "org",
    },
  },
  api_key: {
    kind: "table",
    data: {
      tableName: "api_keys",
      nameCol: "name",
      foreignKeyIds: {
        user: "user_id",
        organization: "org_id",
      },
      optionalLinkageSpec: "org",
    },
  },
  ai_secret: {
    kind: "table",
    data: {
      tableSchema: "secrets",
      tableName: "org_secrets",
      nameCol: "name",
      foreignKeyIds: {
        organization: "org_id",
      },
      optionalLinkageSpec: "project",
    },
  },
};

// Make sure none of the virtual tables point to other virtual tables.
for (const key of tableInfoObjectTypeEnum.options) {
  const tableInfo = aclObjectTableInfos[key];
  if (tableInfo.kind === "virtual") {
    const otherTableInfo = aclObjectTableInfos[tableInfo.data];
    if (!(otherTableInfo.kind === "table")) {
      throw new Error(
        `Virtual table ${key} refers to non-physical table ${tableInfo.data}`,
      );
    }
  }
}
