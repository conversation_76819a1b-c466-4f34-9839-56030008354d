import { _urljoin } from "@braintrust/core";
import {
  <PERSON>,
  Logger,
  NOOP_SPAN,
  parse<PERSON><PERSON>d<PERSON><PERSON><PERSON>,
  Span,
  traced,
  X_CACHED_HEADER,
} from "braintrust";
import {
  ChatCompletion,
  ChatCompletionCreateParamsNonStreaming,
  ChatCompletionMessageParam,
  FunctionDefinition,
} from "openai/resources";
import zodToJsonSchema from "zod-to-json-schema";
import mustache from "mustache";
import { fewShots } from "./few-shots";
import {
  AutoCompleteArgs,
  AutoCompleteCredentials,
  AutocompleteResponse,
  completionToolArgs,
} from "./types";
import { makeCopilotEditorSnippets } from "./context";

const SYSTEM_PROMPT = `You are a helpful assistant that can autocomplete code, data, and prompts.

Your job is to fill-in-the-middle between the prefix and the suffix. I'll give you the prefix between <prefix>...</prefix> and suffix between <suffix>...</suffix>.

Return 1-2 lines of text that you would replace in between the prefix and suffix. Do not repeat any text from either the prefix or the suffix. Only return the text in between.

I will provide you a series of examples, between <example>...</example>, where each example contains the prefix and suffix, followed by your response.

{{#fewShots}}
<example>
<prefix>{{{context.prefix}}}</prefix>
<suffix>{{{context.suffix}}}</suffix>
<completion>{{{completion}}}</completion>
<note>{{{note}}}</note>
</example>
{{/fewShots}}

Next, I will provide you with some context, followed by the prefix and suffix.`;

const DEFAULT_MODEL = "gpt-4o-2024-08-06";
// const DEFAULT_MODEL = "claude-3-5-sonnet-20240620";

export const tools: Record<string, Omit<FunctionDefinition, "name">> = {
  complete: {
    description: "Returns the completion.",
    parameters: zodToJsonSchema(completionToolArgs),
  },
};

interface RunChatCompletionOpts {
  cache?: "auto" | "always" | "never";
}

export type AutocompleteOpts<IsAsyncFlush extends boolean> =
  RunChatCompletionOpts & {
    // XXX Is there a cleaner way to do this?
    parentLogger?: Logger<IsAsyncFlush> | Experiment | Span;
  };

async function runChatCompletion({
  args,
  credentials,
  opts,
}: {
  args: ChatCompletionCreateParamsNonStreaming;
  credentials: AutoCompleteCredentials;
  opts?: RunChatCompletionOpts;
}): Promise<ChatCompletion> {
  return await traced(
    async (span) => {
      const { messages, ...rest } = args;

      const startTime = getCurrentUnixTimestamp();
      const response = await fetch(
        _urljoin(credentials.baseUrl, "chat/completions"),
        {
          method: "POST",
          headers: {
            "content-type": "application/json",
            "x-bt-org-name": credentials.orgName,
            "x-bt-use-cache": opts?.cache ?? "auto",
            ...credentials.authHeaders,
          },
          body: JSON.stringify(args),
        },
      );

      if (response.status !== 200) {
        throw new Error(
          `Failed to get chat completion: ${response.status} ${response.statusText}: ${await response.text()}`,
        );
      }

      const ret: ChatCompletion = await response.json();
      span.log({
        input: messages,
        metadata: rest,
        output: ret.choices,
        metrics: {
          time_to_first_token: getCurrentUnixTimestamp() - startTime,
          tokens: ret.usage?.total_tokens,
          prompt_tokens: ret.usage?.prompt_tokens,
          completion_tokens: ret.usage?.completion_tokens,
          cached: parseCachedHeader(response.headers.get(X_CACHED_HEADER)),
        },
      });

      return ret;
    },
    {
      type: "llm",
      name: "Custom chat completion",
    },
  );
}

export async function runAutocomplete<IsAsyncFlush extends boolean>(
  args: AutoCompleteArgs,
  opts?: AutocompleteOpts<IsAsyncFlush>,
): Promise<AutocompleteResponse> {
  const { parentLogger, cache } = opts ?? {};
  return await (parentLogger ?? NOOP_SPAN).traced(
    async (span) => {
      const { prefix, suffix, editorCtx } = args.context;
      const snippets = makeCopilotEditorSnippets(editorCtx);

      snippets.sort((a, b) => a.priority - b.priority);

      const messages: ChatCompletionMessageParam[] = [
        {
          role: "system",
          content: mustache.render(SYSTEM_PROMPT, {
            fewShots,
          }),
        },
        ...snippets.map((snippet) => ({
          role: "user" as const,
          content: snippet.data,
        })),
        {
          role: "assistant",
          content: `give prefix and suffix`,
        },
        {
          role: "user",
          content: `<prefix>${prefix}</prefix>\n<suffix>${suffix}</suffix>`,
        },
        {
          role: "user",
          content: `Return the completion between prefix and suffix. Do not include <completion> tags or anything else.
Just the exact text to place between prefix and suffix. Make sure to take into account the examples, the format, and the
context you've been provided.`,
        },
      ];
      const response = await runChatCompletion({
        args: {
          messages,
          model: args.credentials.model ?? DEFAULT_MODEL,
          temperature: 0,
          max_tokens: 24,
          // tool_choice: {
          //   type: "function",
          //   function: {
          //     name: "complete",
          //   },
          // },
          // tools: Object.entries(tools).map(
          //   ([name, tool]): ChatCompletionTool => ({
          //     type: "function",
          //     function: {
          //       name,
          //       ...tool,
          //     },
          //   }),
          // ),
        },
        credentials: args.credentials,
        opts: {
          cache,
        },
      });

      // const toolCall = response.choices[0].message.tool_calls?.[0];
      // if (!toolCall) {
      //   throw new Error(
      //     "No tool call found:\n" + JSON.stringify(response, null, 2),
      //   );
      // }
      // if (
      //   toolCall.function.name !== "complete" ||
      //   !toolCall.function.arguments
      // ) {
      //   throw new Error(
      //     "Invalid tool call (not 'complete' with args):\n" +
      //       JSON.stringify(toolCall, null, 2),
      //   );
      // }

      // let argumentsStr = toolCall.function.arguments;
      // if (response.choices[0].finish_reason === "length") {
      //   // Make sure it ends with "}. It may have either of those characters though,
      //   // so make sure to trim them first.
      //   if (argumentsStr.endsWith("}")) {
      //     argumentsStr = argumentsStr.slice(0, -1);
      //   }
      //   if (argumentsStr.endsWith('"')) {
      //     argumentsStr = argumentsStr.slice(0, -1);
      //   }
      //   argumentsStr = argumentsStr + '"}';
      // }

      // const output = completionToolArgs.parse(JSON.parse(argumentsStr));
      const output = { completion: response.choices[0].message.content ?? "" };
      span.log({ output });
      return {
        ...output,
        spanId: span.id,
      };
    },
    {
      name: "runAutocomplete",
      type: "function",
      event: {
        input: args.context,
        metadata: {
          orgName: args.credentials.orgName,
          userId: args.credentials.userId,
          email: args.credentials.email,
          model: args.credentials.model,
        },
      },
    },
  );
}

function getCurrentUnixTimestamp(): number {
  return Date.now() / 1000;
}
