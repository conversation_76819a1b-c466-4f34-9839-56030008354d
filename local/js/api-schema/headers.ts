import { JSONSchemaObject } from "@braintrust/btql/schema";
import { BindContext, bindExpr } from "@braintrust/btql/binder";

export const HeaderAliases: Record<string, string> = {
  id: "ID",
  name: "Name",
  span_type_info: "Name",
  slug: "Slug",
  description: "Description",
  prompt_data: "Data",
  function_data: "Function data",
  num_examples: "Examples",
  num_errors: "Errors",
  error_rate: "Error rate",
  creator: "Creator",
  source: "Source",
  tags: "Tags",
  input: "Input",
  output: "Output",
  expected: "Expected",
  last_updated: "Updated",
  metadata: "Metadata",
  created: "Created",
  experiments: "Experiments",
  playgrounds: "Playgrounds",
  datasets: "Datasets",
  dataset: "Dataset",
  duration: "Duration",
  llm_duration: "LLM duration",
  prompt_tokens: "Prompt tokens",
  completion_tokens: "Completion tokens",
  prompt_cached_tokens: "Prompt cached tokens",
  prompt_cache_creation_tokens: "Prompt cache creation tokens",
  total_tokens: "Total tokens",
  estimated_cost: "Estimated LLM cost",
  error: "Error",
  origin: "Origin",
  comments: "Comments",
  start: "Start",
  end: "End",
};

export interface ComputedField {
  deps: string[];
  btql: string;
  aliases: string[];
}
export const HeaderComputedFields: Record<string, ComputedField> = {
  duration: {
    deps: ["metrics"],
    btql: "metrics.end-metrics.start",
    aliases: ["Duration"],
  },
  // LLM-duration is just a filtered version of duration (defined in
  // experimentScanSpanSummary). For now we just leave the expression identical
  // to duration, and leave it up to the frontend to apply the full filter for
  // LLM duration.
  llm_duration: {
    deps: ["metrics"],
    btql: "metrics.end-metrics.start",
    aliases: ["LLM Duration"],
  },
  name: {
    deps: ["span_attributes"],
    btql: "span_attributes.name",
    aliases: ["Name"],
  },
};

export type TopLevelField = {
  prefix: string[];
  fields: string[];
};

export function applyComputedFields({
  bindCtx,
  schema,
  topLevelFields,
  btqlFields,
}: {
  bindCtx: BindContext;
  schema: JSONSchemaObject;
  topLevelFields?: TopLevelField[];
  btqlFields?: {
    field: string;
    btql: string;
    typeOverride?: JSONSchemaObject;
  }[];
}) {
  for (const { prefix, fields } of topLevelFields ?? []) {
    for (const field of fields) {
      if (schema.properties?.[field] || bindCtx.scope[field]) {
        continue;
      }

      bindCtx.scope[field] = bindExpr(bindCtx, {
        op: "ident",
        name: [...prefix, field],
      });
    }
  }

  for (const { field, btql, typeOverride } of btqlFields ?? []) {
    if (schema.properties?.[field] || bindCtx.scope[field]) {
      continue;
    }
    const bound = bindExpr(bindCtx, { btql });
    bindCtx.scope[field] =
      bound.op === "field" && typeOverride
        ? { ...bound, type: typeOverride }
        : bound;
  }

  for (const [field, { deps, btql, aliases }] of Object.entries(
    HeaderComputedFields,
  )) {
    if (
      !deps.every((dep) => schema.properties?.[dep]) ||
      schema.properties?.[field] ||
      bindCtx.scope[field]
    ) {
      continue;
    }
    bindCtx.scope[field] = bindExpr(bindCtx, {
      btql,
    });

    for (const alias of aliases) {
      bindCtx.scope[alias] = bindExpr(bindCtx, {
        op: "ident",
        name: [field],
      });
    }
  }

  for (const [field, headerLabel] of Object.entries(HeaderAliases)) {
    if (
      !schema.properties?.[field] ||
      !headerLabel ||
      bindCtx.scope[headerLabel]
    ) {
      continue;
    }
    bindCtx.scope[headerLabel] = bindExpr(bindCtx, {
      op: "ident",
      name: [field],
    });
  }
}
