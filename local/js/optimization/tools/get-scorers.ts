import { z } from "zod";
import { type Tool } from "./types";

export const getAvailableScorersParamsSchema = z.object({});
export type GetAvailableScorersParams = z.infer<
  typeof getAvailableScorersParamsSchema
>;

export const getAvailableScorersResultSchema = z.object({
  id: z.string().describe("The id of the scorer."),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
});
export type GetAvailableScorersResult = z.infer<
  typeof getAvailableScorersResultSchema
>;

export const GetAvailableScorersTool: Tool<
  GetAvailableScorersParams,
  GetAvailableScorersResult[]
> = {
  description:
    "Get a list of scorers that could be added or removed from the eval using the edit_scorers tool. This is helpful when you want to add or remove a scorer from the eval.",
  parameters: getAvailableScorersParamsSchema,
};
