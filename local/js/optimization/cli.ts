// This package doesn't have types
// @ts-ignore
import inquirerCommandPrompt from "inquirer-command-prompt";

import { ArgumentParser } from "argparse";
import { Chat<PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, jsonTo<PERSON>aml } from "./llm/chat";
import {
  _internalGetGlobalState,
  currentSpan,
  initLogger,
  login,
} from "braintrust";
import inquirer from "inquirer";
import chalk from "chalk";
import figlet from "figlet";
import ora from "ora";
import boxen from "boxen";
import { z } from "zod";
import { EVALS } from "./evals/tasks";
import { makeTools } from "./cli-provider";
import path from "path";
import os from "os";
import { PROJECT_NAME } from "./evals/meta-eval";
import { ALL_TOOL_NAMES, ToolName } from "./tools";
import { SpanComponentsV3, SpanObjectTypeV3 } from "@braintrust/core";
import { DEFAULT_MODEL } from "./llm/system-prompt";

// Configure command history
inquirerCommandPrompt.setConfig({
  history: {
    save: true,
    folder: path.join(os.homedir(), ".braintrust"),
    limit: 100,
    blacklist: ["exit"],
  },
});

// Configure and register the command prompt
inquirer.registerPrompt("command", inquirerCommandPrompt);

export type Spinner = ReturnType<typeof ora>;

// Custom logger that accumulates output instead of printing to console
class CustomLogger implements ChatLogger {
  private output: string = "";
  private spinner: ReturnType<typeof ora> | null = null;

  constructor(spinner: Spinner | null = null) {
    this.spinner = spinner;
  }

  write(message: string): void {
    this.output += message;

    // Update spinner text with the actual message as it comes in
    if (this.spinner) {
      // Display the last few lines of text in the spinner
      const lines = this.output.split("\n");
      const lastLines = lines.slice(-8).join("\n"); // Show last 8 lines
      this.spinner.text = lastLines;
    }
  }

  flush(): void {
    if (this.spinner) {
      this.spinner.text = "...";
      this.spinner.clear();
    }
    console.log(
      boxen(this.output, {
        padding: 1,
        borderStyle: "round",
        borderColor: "green",
      }),
    );
    this.output = "";
  }
}

function clearScreen(): void {
  console.clear();
}

async function displayBanner() {
  clearScreen();

  // Create a minimal banner with figlet
  const bannerText = figlet.textSync("Braintrust", {
    font: "Slant",
  });

  console.log(chalk.cyan(bannerText));
  console.log(chalk.dim("  AI Assistant"));
  console.log();
}

const argsSchema = z.object({
  eval_project_name: z.string(),
  log_project_name: z.string(),
  eval_name: z.string(),
  model: z.string(),
});

async function startChat() {
  const parser = new ArgumentParser({
    description: "Optimize evals",
  });
  parser.add_argument("--eval-project-name", {
    type: "string",
    help: "The name of the project containing the eval dataset",
    default: PROJECT_NAME,
  });
  parser.add_argument("--log-project-name", {
    type: "string",
    help: "The name of the project to store optimization logs",
    default: "Optimization cli logs",
  });
  parser.add_argument("--model", {
    type: "string",
    help: "The model to use for optimization",
    default: DEFAULT_MODEL,
  });
  parser.add_argument("eval-name", {
    help: "Evaluation name",
    choices: Object.keys(EVALS),
  });

  const rawArgs = parser.parse_args();
  const args = argsSchema.parse({
    eval_name: rawArgs["eval-name"],
    ...rawArgs,
  });

  // Start the spinner which will also show partial text
  const responseSpinner = ora({
    text: "...",
    spinner: "dots",
    color: "green",
  });

  const customLogger = new CustomLogger(responseSpinner);

  await login();

  const tools = await makeTools({
    def: EVALS[args.eval_name],
    projectName: args.eval_project_name,
    logProjectName: args.log_project_name,
    spinner: responseSpinner,
    printExperimentResult: (result) => {
      console.log(
        "\n" +
          boxen(result, {
            padding: 1,
            borderStyle: "round",
            borderColor: "blue",
          }),
      );
    },
    model: args.model,
  });

  await displayBanner();

  const btLogger = initLogger({
    projectName: args.log_project_name,
    setCurrent: false,
  });

  // Create an AbortController to handle interruptions
  let abortController = new AbortController();
  let isGenerating = false;

  // We'll manage Ctrl+C interrupts manually during generation
  const onGenerationSigint = () => {
    if (isGenerating) {
      // Cancel the generation but don't exit
      abortController.abort();

      if (responseSpinner.isSpinning) {
        responseSpinner.stop();
        console.log(chalk.yellow("\nGeneration interrupted"));
      }

      // Create a new controller for next time
      abortController = new AbortController();
      isGenerating = false;
    }
  };

  // Start the chat loop
  let chatActive = true;

  // Create a new chat context for each turn with a custom logger
  const chat = new ChatContext({
    loggingState: _internalGetGlobalState(),
    orgName: undefined,
    tools,
    loggingRoot: new SpanComponentsV3({
      object_type: SpanObjectTypeV3.PROJECT_LOGS,
      object_id: (await btLogger.project).id,
    }),
    loggingMetadata: {
      user_id: undefined,
      email: undefined,
      project_id: undefined,
      org_id: undefined,
    },
    enableLogging: true,
    consoleLogger: customLogger,
    model: args.model,
    modelParams: {
      max_tokens: 10240,
    },
  });

  while (chatActive) {
    // Format the user prompt with minimal styling
    console.log(chalk.blue("You"));

    let response;
    try {
      // Remove any listeners from previous turns
      process.removeAllListeners("SIGINT");

      // Use type assertion to bypass type checking
      response = await inquirer.prompt({
        // @ts-ignore - command type is registered at runtime but TypeScript doesn't know about it
        type: "command",
        name: "message",
        message: "› ",
        // This will ensure history is separated by eval name
        context: args.eval_name,
        // Support viewing available commands with autocomplete
        autocompletePrompt: ">> Available commands:",
        noColorOnAnswered: true,
        onClose: () => {
          process.exit(0);
        },
      });
    } catch {
      // Just continue - don't exit here
      continue;
    }

    const userMessage = String(response.message).trim();

    if (userMessage.toLowerCase() === "exit") {
      chatActive = false;
      process.exit(0);
    } else if (userMessage.toLocaleLowerCase().startsWith("execute_tool")) {
      // Parse the tool execution command
      const toolCommandMatch = userMessage.match(
        /^execute_tool\s+(\w+)\((.*)\)$/,
      );
      if (!toolCommandMatch) {
        console.error(
          chalk.red(
            "Invalid tool execution format. Expected: toolName(key1=value1, key2=value2)",
          ),
        );
        continue;
      }

      const toolName = toolCommandMatch[1];
      const argsString = toolCommandMatch[2];

      // Parse the arguments
      const args: Record<string, unknown> = {};
      if (argsString.trim()) {
        const argPairs = argsString.split(",");
        for (const pair of argPairs) {
          const [key, value] = pair.trim().split("=");
          if (key && value !== undefined) {
            try {
              args[key] = JSON.parse(value);
            } catch {
              args[key] = value;
            }
          }
        }
      }

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      if (!ALL_TOOL_NAMES.includes(toolName as ToolName)) {
        console.error(chalk.red(`Unknown tool: ${toolName}`));
        continue;
      }

      console.log(chalk.green("Tool"));
      try {
        const result = await tools.executeTool(
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          toolName as ToolName,
          args,
          currentSpan(),
        );
        customLogger.write(jsonToYaml(result));
        customLogger.flush();
      } catch (error) {
        console.error(
          chalk.red(
            `Error: ${error instanceof Error ? error.message : String(error)}`,
          ),
        );
      }
    } else if (userMessage.length > 0) {
      console.log(chalk.green("AI"));

      // Reset abort controller
      abortController = new AbortController();
      isGenerating = true;

      // Set up SIGINT handler just for this generation
      process.on("SIGINT", onGenerationSigint);

      responseSpinner.start();
      try {
        await chat.turn(userMessage, { abortController });
        responseSpinner.stop();
      } catch (error) {
        responseSpinner.stop();

        if (!(error instanceof Error && error.name === "AbortError")) {
          console.error(
            chalk.red(
              `Error: ${error instanceof Error ? error.message : String(error)}`,
            ),
          );
        }
      } finally {
        isGenerating = false;
        // Clean up the SIGINT handler
        process.removeAllListeners("SIGINT");
      }
    }

    // Add a subtle separator line
    console.log();
  }
}

// Wrap the entire application in a try/catch to handle uncaught errors
try {
  startChat().catch((error) => {
    console.error(
      chalk.red(
        `Error: ${error instanceof Error ? error.message : String(error)}`,
      ),
    );
    process.exit(1);
  });
} catch (error) {
  console.error(
    chalk.red(
      `Uncaught error: ${error instanceof Error ? error.message : String(error)}`,
    ),
  );
  process.exit(1);
}
