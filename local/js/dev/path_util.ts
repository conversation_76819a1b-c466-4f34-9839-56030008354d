import * as path from "path";

// NOTE(austin): The `require.resolve` below will cause a "MODULE NOT FOUND"
// error in certain environments, like in Vercel serverless functions. Avoid
// calling this function outside of test environments.
let _rootPath: string | null = null;
export function braintrustRootPath() {
  if (_rootPath === null) {
    // Should refer to the dist/index.js of this module.
    const module_path = require.resolve("@braintrust/local/dev");
    _rootPath = path.join(path.dirname(module_path), "..", "..", "..", "..");
  }
  return _rootPath;
}
