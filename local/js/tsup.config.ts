import { defineConfig } from "tsup";

export default defineConfig([
  {
    entry: ["index.ts"],
    format: ["cjs", "esm"],
    outDir: "dist",
    external: ["@braintrust/local"],
  },
  {
    entry: ["app-schema/index.ts"],
    format: ["cjs", "esm"],
    outDir: "app-schema/dist",
  },
  {
    entry: ["api-schema/index.ts"],
    format: ["cjs", "esm"],
    outDir: "api-schema/dist",
    external: ["@braintrust/core"],
  },
  {
    entry: ["copilot/index.ts"],
    format: ["cjs", "esm"],
    outDir: "copilot/dist",
    external: ["@braintrust/core", "braintrust"],
  },
  {
    entry: ["query/index.ts"],
    format: ["cjs", "esm"],
    outDir: "query/dist",
    external: ["@braintrust/core"],
  },
  {
    entry: ["functions/index.ts"],
    format: ["cjs", "esm"],
    outDir: "functions/dist",
    external: ["@braintrust/core"],
  },
  {
    entry: ["dev/index.ts"],
    format: ["cjs", "esm"],
    outDir: "dev/dist",
    external: ["@braintrust/local/dev"],
  },
  {
    entry: ["bt-pg/index.ts"],
    format: ["cjs"],
    outDir: "bt-pg/dist",
    external: ["@braintrust/local/bt-pg"],
  },
  {
    entry: ["optimization/index.ts", "optimization/tools.ts"],
    format: ["cjs", "esm"],
    outDir: "optimization/dist",
    external: ["@braintrust/local/optimization"],
  },
]);
