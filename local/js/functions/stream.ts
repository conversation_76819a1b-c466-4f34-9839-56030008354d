import {
  create<PERSON><PERSON>er,
  EventSourceParser,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";
import { RawSSEEvent, serializeSSEEvent } from "./sse";
import { isChatCompletionChunk, isCompletion } from "@braintrust/proxy/utils";
import {
  callEventSchema,
  ModelParams,
  StreamingMode,
} from "@braintrust/core/typespecs";
import { isEmpty, Score } from "@braintrust/core";
import { z } from "zod";

export interface InvokeResponse {
  data: unknown;
  error?: string;
}

export const parallelToolCallSchema = z.object({
  tool_call_id: z.string(),
  function_name: z.string(),
  arguments: z.record(z.unknown()),
});
export type ParallelToolCall = z.infer<typeof parallelToolCallSchema>;

export function isJSONResponseParams(params: ModelParams) {
  const responseFormat = params.response_format;
  if (
    responseFormat &&
    typeof responseFormat === "object" &&
    "type" in responseFormat
  ) {
    return (
      responseFormat.type === "json_schema" ||
      responseFormat.type === "json_object"
    );
  }
  return false;
}

export type StreamingTextDelta = string;
export type StreamingToolDelta = {
  tool_call_id?: string;
  function_name?: string;
  arguments?: string;
};
export type StreamingError = string;

export function createConditionalJsonProcessingStream({
  mode,
  isFail,
  isJSONResponse,
  includeToolCallIds,
  onTextDelta,
  onToolDelta,
  onError,
  onFinish,
}: {
  mode: StreamingMode | "internal_full";
  isFail: () => boolean;
  isJSONResponse: boolean;
  includeToolCallIds?: boolean;
  onTextDelta?: (text: StreamingTextDelta) => void;
  onToolDelta?: (args: StreamingToolDelta) => void;
  onError?: (error: StreamingError) => void;
  onFinish?: () => void;
}): TransformStream<Uint8Array, Uint8Array> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  let eventSourceParser: EventSourceParser;
  let isFunctionStreamingIn = false;
  let isFirstFn = true;
  let terminated = false;

  // Sometimes, no arguments will come through, and in this case we should emit
  // {} before proceeding to the next tool call.
  let receivedToolCallArguments = true;

  const safeTerminate = (
    controller: TransformStreamDefaultController<Uint8Array>,
  ) => {
    if (terminated) {
      return;
    }
    terminated = true;
    if (!isFail()) {
      controller.enqueue(
        encoder.encode(serializeSSEEvent({ event: "done", data: "" })),
      );
    }
  };

  const enqueue = (
    controller: TransformStreamDefaultController<Uint8Array>,
    event: RawSSEEvent,
  ) => {
    controller.enqueue(encoder.encode(serializeSSEEvent(event)));
  };

  return new TransformStream<Uint8Array, Uint8Array>({
    start(controller) {
      eventSourceParser = createParser(
        (event: ParsedEvent | ReconnectInterval) => {
          if (event.type === "reconnect-interval") {
            return;
          }

          if (event.data === "[DONE]") {
            if (mode === "parallel" && isFirstFn && !isJSONResponse) {
              enqueue(controller, {
                event: "json_delta",
                data: "[]",
              });
            }

            safeTerminate(controller);

            return;
          }

          const json = JSON.parse(event.data);

          if (terminated) {
            return;
          }

          if (json.error) {
            enqueue(controller, {
              event: "error",
              data: JSON.stringify(json.error),
            });
            onError?.(json.error);
            return;
          }

          if (isCompletion(json)) {
            enqueue(controller, {
              event: "text_delta",
              data: JSON.stringify(json.choices[0].text),
            });
            onTextDelta?.(json.choices[0].text);
            return;
          }

          if (!isChatCompletionChunk(json)) {
            // E.g. an empty usage block
            return;
          }

          const delta = json.choices[0]?.delta;

          if (delta.reasoning?.content) {
            enqueue(controller, {
              event: "reasoning_delta",
              data: JSON.stringify(delta.reasoning.content),
            });
          }

          if (
            !isFirstFn &&
            mode !== "parallel" &&
            (delta.function_call?.name || delta.tool_calls?.[0]?.function?.name)
          ) {
            safeTerminate(controller);
            return;
          }

          try {
            if (delta.function_call?.name) {
              if (mode === "parallel") {
                const prefix =
                  (!receivedToolCallArguments ? "{}" : "") +
                  (isFirstFn ? "[" : "} ,");
                enqueue(controller, {
                  event: "json_delta",
                  data: `${prefix}{"function_name":${JSON.stringify(
                    delta.function_call.name,
                  )}, "arguments":`,
                });
                receivedToolCallArguments = false;
              }
              isFirstFn = false;
              isFunctionStreamingIn = true;
            } else if (delta.tool_calls?.[0]?.function?.name) {
              if (mode === "parallel") {
                const prefix =
                  (!receivedToolCallArguments ? "{}" : "") +
                  (isFirstFn ? "[" : "} ,");
                enqueue(controller, {
                  event: "json_delta",
                  data: `${prefix}{"function_name":${JSON.stringify(
                    delta.tool_calls[0].function.name,
                  )}, ${
                    delta.tool_calls[0].id && includeToolCallIds
                      ? `"tool_call_id": ${JSON.stringify(delta.tool_calls[0].id)},`
                      : ""
                  } "arguments":`,
                });
                onToolDelta?.({
                  tool_call_id: delta.tool_calls[0].id,
                  function_name: delta.tool_calls[0].function.name,
                  arguments: delta.tool_calls[0].function.arguments,
                });
                receivedToolCallArguments = false;
              }
              isFirstFn = false;
              isFunctionStreamingIn = true;
            }

            if (delta.function_call?.arguments) {
              enqueue(controller, {
                event: "json_delta",
                // OpenAI and others will now include newlines in the generated JSON, which we're about to place "raw" into the SSE event,
                // which cannot contain newlines. These newlines only exist _outside_ of the JSON data itself, so replacing them with ordinary
                // spaces should have no effect.
                data: delta.function_call.arguments.replace(/\n/g, " "),
              });
              receivedToolCallArguments = true;
            } else if (delta.tool_calls?.[0]?.function?.arguments) {
              const cleaned = delta.tool_calls[0].function.arguments.replace(
                /\n/g,
                " ",
              );
              enqueue(controller, {
                event: "json_delta",
                data: cleaned,
              });
              onToolDelta?.({
                tool_call_id: delta.tool_calls[0].id,
                function_name: delta.tool_calls[0].function.name,
                arguments: cleaned,
              });
              receivedToolCallArguments = true;
            }

            if (
              delta.tool_calls &&
              delta.tool_calls.length > 0 &&
              mode === "parallel"
            ) {
              for (let i = 1; i < delta.tool_calls.length; i++) {
                const tc = delta.tool_calls[i];
                const toolDelta: StreamingToolDelta = {};
                if (tc.function?.name) {
                  enqueue(controller, {
                    event: "json_delta",
                    data: `}, {"function_name":${JSON.stringify(
                      tc.function?.name,
                    )}, ${
                      tc.id && includeToolCallIds
                        ? `"tool_call_id": ${JSON.stringify(tc.id)},`
                        : ""
                    } "arguments":`,
                  });
                  toolDelta.function_name = tc.function?.name;
                  toolDelta.tool_call_id = tc.id;

                  receivedToolCallArguments = false;
                }
                if (tc.function?.arguments) {
                  enqueue(controller, {
                    event: "json_delta",
                    data: tc.function?.arguments.replace(/\n/g, " "),
                  });
                  toolDelta.arguments = tc.function?.arguments;

                  receivedToolCallArguments = true;
                }
                if (
                  toolDelta.tool_call_id ||
                  toolDelta.function_name ||
                  toolDelta.arguments
                ) {
                  onToolDelta?.(toolDelta);
                }
              }
            }

            if (
              isFunctionStreamingIn &&
              (json.choices[0]?.finish_reason === "function_call" ||
                json.choices[0]?.finish_reason === "tool_calls" ||
                json.choices[0]?.finish_reason === "stop")
            ) {
              if (mode === "parallel") {
                enqueue(controller, {
                  event: "json_delta",
                  data: (!receivedToolCallArguments ? "{}" : "") + "}]",
                });
              }
              isFunctionStreamingIn = false; // Reset the flag
              receivedToolCallArguments = true;
            } else if (json.choices[0]?.finish_reason === "length") {
              // Assume that in the boundary case in which the JSON is finished
              // with the last available token,
              // the model provider will give one of the above finish reasons.
              // This implies that receiving finish reason "length" is always an error.
              enqueue(controller, {
                event: "error",
                data: JSON.stringify("Max completion tokens reached"),
              });
              onError?.("Max completion tokens reached");
              receivedToolCallArguments = true;
            } else if (!isEmpty(json.choices[0].delta.content)) {
              if (isJSONResponse) {
                enqueue(controller, {
                  event: "json_delta",
                  // Remove raw newlines from SSE events since newlines are used as delimiters.
                  // This does not affect escaped newlines in the JSON payload itself.
                  data: json.choices[0].delta.content.replace(/\n/g, ""),
                });
              } else {
                enqueue(controller, {
                  event: "text_delta",
                  data: JSON.stringify(json.choices[0].delta.content) ?? "",
                });
                onTextDelta?.(json.choices[0].delta.content);
              }
            }
          } catch (e) {
            console.error(
              "Error parsing data. Will skip, which may result in an empty result",
              delta,
              e,
            );
          }
        },
      );
    },
    transform(chunk, controller) {
      if (isFail()) {
        controller.enqueue(chunk);
      } else {
        eventSourceParser.feed(decoder.decode(chunk));
      }
    },
    flush(controller) {
      safeTerminate(controller);
      onFinish?.();
    },
  });
}

export function createChoiceScoreParserStream({
  name,
  choiceScores,
  isFail,
}: {
  name: string;
  choiceScores: Record<string, number>;
  isFail: () => boolean;
}): TransformStream<Uint8Array, Uint8Array> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();

  const jsonChunks: string[] = [];
  let errored = false;

  let eventSourceParser: EventSourceParser;
  const transformStream = new TransformStream<Uint8Array, Uint8Array>({
    start(controller) {
      eventSourceParser = createParser(
        (event: ParsedEvent | ReconnectInterval) => {
          const parsed = callEventSchema.safeParse(event);
          if (!parsed.success) {
            console.warn("Failed to parse event", event, parsed.error);
            return;
          }

          const data = parsed.data.data;
          switch (parsed.data.event) {
            case "text_delta":
              console.warn(
                "Choice scores should only receive json delta, ignoring",
                data,
              );
              break;
            case "json_delta":
              jsonChunks.push(data);
              break;
            case "error":
              errored = true;
              controller.enqueue(
                encoder.encode(serializeSSEEvent(parsed.data)),
              );
              break;
            case "start":
            case "done":
            case "progress":
            case "console":
            case "reasoning_delta":
              break;
            default:
              const _event: never = parsed.data;
              throw new Error(`Unhandled event type: ${_event}`);
          }
        },
      );
    },

    transform(chunk, controller) {
      if (isFail()) {
        controller.enqueue(chunk);
      } else {
        eventSourceParser.feed(decoder.decode(chunk));
      }
    },
    flush(controller) {
      if (isFail()) {
        return;
      }

      if (!errored) {
        let result: Score;
        if (jsonChunks.length === 0) {
          console.warn("No choice scores received");
          result = {
            name,
            score: null,
            metadata: {
              error: "No choice scores received",
            },
          };
        } else {
          const toolCall = JSON.parse(jsonChunks.join(""));
          const choice = toolCall["choice"]?.trim();

          result = {
            name,
            score: choiceScores[choice],
            metadata: {
              rationale: toolCall["reasons"],
              choice,
            },
          };
        }
        controller.enqueue(
          new TextEncoder().encode(
            serializeSSEEvent({
              event: "json_delta",
              data: JSON.stringify(result),
            }),
          ),
        );
      }
      controller.enqueue(
        new TextEncoder().encode(
          serializeSSEEvent({
            event: "done",
            data: "",
          }),
        ),
      );
    },
  });

  return transformStream;
}

export function createFinalValuePassThroughStream({
  onFinal,
  getStatusCode,
}: {
  onFinal: (result: InvokeResponse) => void;
  getStatusCode: () => number;
}): TransformStream<Uint8Array, Uint8Array> {
  const isFail = () => getStatusCode() !== 200;
  const decoder = new TextDecoder();

  const textChunks: string[] = [];
  const reasoningChunks: string[] = [];
  const jsonChunks: string[] = [];
  const errorChunks: string[] = [];

  const eventSourceParser: EventSourceParser = createParser(
    (event: ParsedEvent | ReconnectInterval) => {
      const parsed = callEventSchema.safeParse(event);
      if (!parsed.success) {
        console.warn("Failed to parse event", event, parsed.error);
        return;
      }

      const data = parsed.data.data;
      switch (parsed.data.event) {
        case "text_delta":
          textChunks.push(JSON.parse(data));
          break;
        case "reasoning_delta":
          reasoningChunks.push(JSON.parse(data));
          break;
        case "json_delta":
          jsonChunks.push(data);
          break;
        case "error":
          errorChunks.push(JSON.parse(data));
          break;
        case "start":
        case "done":
        case "progress":
        case "console":
          break;
        default:
          const _event: never = parsed.data;
          throw new Error(`Unhandled event type: ${_event}`);
      }
    },
  );

  const transformStream = new TransformStream<Uint8Array, Uint8Array>({
    transform(chunk, controller) {
      if (isFail()) {
        errorChunks.push(decoder.decode(chunk));
      } else {
        eventSourceParser.feed(decoder.decode(chunk));
      }
      controller.enqueue(chunk);
    },
    flush(controller) {
      let error =
        errorChunks.length > 0
          ? errorChunks.join("")
          : getStatusCode() !== 200
            ? `Failed to execute (${getStatusCode()})`
            : undefined;
      if (isFail()) {
        onFinal({ data: undefined, error });
      } else if (jsonChunks.length > 0) {
        // If we received both text and json deltas in the same stream, we
        // only return the json delta
        let data;
        try {
          data = JSON.parse(jsonChunks.join(""));
        } catch (e) {
          error = error ?? `invalid JSON: ${e}`;
        }
        onFinal({ data, error });
      } else if (textChunks.length > 0) {
        onFinal({ data: textChunks.join(""), error });
      } else if (reasoningChunks.length > 0) {
        onFinal({ data: reasoningChunks.join(""), error });
      } else {
        onFinal({ data: undefined, error });
      }
    },
  });

  return transformStream;
}

export function devNullWritableStream(): WritableStream {
  return new WritableStream({});
}
