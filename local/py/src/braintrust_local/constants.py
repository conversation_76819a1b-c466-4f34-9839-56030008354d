# Mirror of constants defined in api-ts/src/env.ts and api-ts/src/util.ts.
LOCAL_REDIS_HOST = "localhost"
LOCAL_REDIS_PORT = 6479
REDIS_OUTBOUND_RATE_LIMIT_COUNTER = "outbound_rate_limit_counter"
BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER = "x-bt-object-cache-was-cached-redis-token"
BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER = "x-bt-resource-check-cached-redis-token"
BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER = "x-bt-prompt-was-cached-redis-token"
BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER = "x-bt-async-scoring-config-was-cached-redis-token"

ANON_USER_ID = "80c48a10-4888-4382-a55b-255018e70fe5"
