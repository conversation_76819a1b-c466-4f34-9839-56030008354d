import json

import braintrust
from braintrust.util import response_raise_for_status


def get_api_db_url():
    return "postgres://postgres:postgres@localhost:5532/postgres"


def print_api_db_url():
    print(get_api_db_url())


def get_object_json(object_type, object_id, audit_log=False, limit=None):
    resp = braintrust.api_conn().post(
        "/btql",
        json=dict(
            query={
                "from": {
                    "op": "function",
                    "name": {"op": "ident", "name": [object_type]},
                    "args": [{"op": "literal", "value": object_id}],
                },
                "select": [{"op": "star"}],
                "limit": limit if limit is not None else 1000000,
            },
            fmt="json",
            audit_log=audit_log,
            _testing_only_allow_query_full_audit_log=audit_log,
        ),
    )
    response_raise_for_status(resp)
    return resp.json()["data"]


def log_raw(*rows):
    resp = braintrust.api_conn().post("/logs", data=json.dumps(rows))
    response_raise_for_status(resp)
    return resp.json()


def log3_raw(*rows, **kwargs):
    resp = braintrust.api_conn().post("/logs3", json=dict(rows=rows, api_version=2, **kwargs))
    response_raise_for_status(resp)
    return resp.json()
