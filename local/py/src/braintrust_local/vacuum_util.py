import json
import time

import braintrust


def catchup_brainstore_vacuum(object_ids):
    for _ in range(1000):
        d = braintrust.api_conn().post_json(
            "brainstore/vacuum/status",
            args={"object_ids": object_ids},
        )
        # Just wait for all segments to get vacuumed once. We're not enforcing
        # that vacuum actually deleted anything, and it likely didn't unless
        # we happened to wait long enough to overcome the deletion grace period
        # for any stale files.
        if d["all_vacuumed_once"]:
            # Shouldn't be possible to get a truthy value if no segments matched
            assert d["num_live_segments"] > 0
            print("Vacuum completed")
            return d

        time.sleep(0.1)

    raise Exception("Brainstore vacuum did not catch up")


def force_brainstore_revacuum(
    object_ids,
    sleep_for_vacuum_index_stale_seconds=True,
):
    # Reset the vacuum state so the next vacuum loop iteration
    # will pick up and re-vacuum all segments. Brainstore will
    # sleep for the total grace period + slop seconds before
    # running the next vacuum loop.
    braintrust.api_conn().post_json("brainstore/vacuum/reset_state", args={"object_ids": object_ids})

    # Wait for vacuum to complete.
    print("Waiting for forced vacuum to complete")
    for _ in range(1000):
        d = braintrust.api_conn().post_json(
            "brainstore/vacuum/status",
            args={
                "object_ids": object_ids,
                # Make brainstore sleep at least the deletion grace period before
                # resetting so vacuum will be able to delete files written before
                # this function was called.
                "sleep_for_vacuum_index_stale_seconds": sleep_for_vacuum_index_stale_seconds,
            },
        )

        # Wait for all segments to have been vacuumed recently enough to delete
        # any stale files written before this function was called.
        if d["all_vacuumed_up_to_date"]:
            # Shouldn't be possible to get a truthy value if no segments matched
            assert d["num_live_segments"] > 0
            print("Forced vacuum completed")
            return d

        time.sleep(0.1)

    raise Exception("Brainstore vacuum was not successfully forced")
