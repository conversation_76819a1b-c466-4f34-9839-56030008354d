resource "aws_route53_zone" "braintrustdata_dot_com" {
  name    = "braintrustdata.com"
  comment = "Managed by Terraform"
}

resource "aws_route53_record" "braintrustdata_dot_com_ns" {
  zone_id = aws_route53_zone.braintrustdata_dot_com.zone_id
  name    = "braintrustdata.com"
  type    = "NS"
  ttl     = "172800"
  records = [
    "${aws_route53_zone.braintrustdata_dot_com.name_servers[0]}.",
    "${aws_route53_zone.braintrustdata_dot_com.name_servers[1]}.",
    "${aws_route53_zone.braintrustdata_dot_com.name_servers[2]}.",
    "${aws_route53_zone.braintrustdata_dot_com.name_servers[3]}."
  ]
}

### Records ---

# Pylon email verification
resource "aws_route53_record" "pylon_braintrustdata_domainkey" {
  zone_id = aws_route53_zone.braintrustdata_dot_com.zone_id
  name    = "20250327015347pm._domainkey"
  type    = "TXT"
  ttl     = "300"
  records = ["k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCle1LDmUNr/BJjByADiSjKMYF+WCxM1Tw9o2BQoPGnPFtV+rqHY8SwukbVdRGooTk3efDN581mpxyQeAFg6lXwsplCEAx8sBOu7pAY8l5O8TCSCGizBmnN/7c/Vg3C9ORn05g+MHVScRp++XagKMQbbw+sAJLXEYmsaPP5AU4AmwIDAQAB"]
}

resource "aws_route53_record" "pylon_braintrustdata_pmbounces" {
  zone_id = aws_route53_zone.braintrustdata_dot_com.zone_id
  name    = "pm-bounces"
  type    = "CNAME"
  ttl     = "300"
  records = ["pm.mtasv.net"]
}
