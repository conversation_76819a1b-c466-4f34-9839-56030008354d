#!/usr/bin/env python3

# Script for launching unit tests using the images built from
# deployment-internal/deploy_standalone_docker.py. Prerequisites:
#
#  - Run the deployment-internal/deploy_standalone_docker.py to build the docker
#  images.
#
#  - Turn off any conflicting services:
#       - `make services-down`
#
#  - Make sure this script is run with `source env.sh`.

import argparse
import os
import subprocess
import sys
import tempfile

import yaml
from braintrust_local import generate_docker_compose, healthcheck
from braintrust_local.pull_docker_compose import pull_docker_compose

SCRIPT_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.join(SCRIPT_DIR, "..")
DOCKER_COMPOSE_FILE = "docker-compose.full.test.yml"


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


def get_testenv():
    env = os.environ.copy()
    # We don't ship S3 with the docker deployment, so we skip unit tests which
    # test for it.
    env["BT_UNITTEST_SKIP_S3"] = "1"
    env["BT_UNITTEST_SKIP_CLICKHOUSE"] = "1"
    # We don't open up arbitrary ports in the docker deployment, so we skip for now
    env["BT_UNITTEST_SKIP_WEBHOOK"] = "1"
    return env


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Run standalone docker tests",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--dump-logs",
        type=str,
        help="Dump the logs of all services to the specified file",
    )
    args = parser.parse_args()

    tmpdirname = tempfile.mkdtemp()
    eprint(f"Copying runfiles to {tmpdirname}")

    with open(os.path.join(tmpdirname, DOCKER_COMPOSE_FILE), "w") as f:
        f.write(generate_docker_compose.generate("test"))

    # Modify the compose file to use our locally-built images. If any of the
    # image names change, we'll need to modify this script.
    with open(os.path.join(tmpdirname, DOCKER_COMPOSE_FILE)) as f:
        compose_contents = yaml.load(f, Loader=yaml.Loader)
    compose_services = compose_contents["services"]
    for service in [
        "redis",
        "postgres",
        "standalone-api",
        "standalone-app",
        "standalone-app-bootstrap",
        "standalone-realtime",
        "brainstore",
    ]:
        compose_services[f"braintrust-{service}"]["image"] = service
    with open(os.path.join(tmpdirname, DOCKER_COMPOSE_FILE), "w") as f:
        f.write(yaml.dump(compose_contents, Dumper=yaml.Dumper))

    # Launch minio for S3 support for brainstore
    pull_docker_compose("services/docker-compose.yml", services=["minio"])
    subprocess.run(
        [
            "docker",
            "compose",
            "--env-file",
            "services/.env.initial",
            "-f",
            "services/docker-compose.yml",
            "up",
            "-d",
            "--remove-orphans",
            "--build",
            "minio",
            "--wait",
            "--wait-timeout",
            "20",
        ],
        cwd=REPO_ROOT,
        check=True,
    )

    # Launch the docker containers.
    DOCKER_COMPOSE_FULLPATH = os.path.join(tmpdirname, DOCKER_COMPOSE_FILE)
    pull_docker_compose(DOCKER_COMPOSE_FULLPATH, services=["braintrust-standalone-supabase-db"])
    try:
        subprocess.run(
            [
                "docker",
                "compose",
                "-f",
                DOCKER_COMPOSE_FULLPATH,
                "up",
                "--pull",
                "missing",
                "--detach",
                "--remove-orphans",
                "--wait",
                "--wait-timeout",
                "60",
            ],
            check=True,
        )
        # Launch the test proxy.
        eprint("Launching test-proxy")
        subprocess.run(
            ["services/bt_services.py", "restart", "--include", "test-proxy", "--wait", "--wait-timeout", "20"],
            cwd=REPO_ROOT,
            check=True,
        )

        # Run the tests.
        eprint("Running tests")
        subprocess.run(["pnpm", "build", "--filter", "autoevals", "--filter", "braintrust"], check=True)
        subprocess.run(["make", "test"], check=True, env=get_testenv())
    finally:
        if args.dump_logs:
            with open(args.dump_logs, "w") as f:
                subprocess.run(
                    ["docker", "compose", "-f", DOCKER_COMPOSE_FULLPATH, "logs", "--no-color"],
                    check=True,
                    stdout=f,
                )
        subprocess.run(["docker", "compose", "-f", os.path.join(tmpdirname, DOCKER_COMPOSE_FILE), "down"])
        subprocess.run(["docker", "compose", "-f", "services/docker-compose.yml", "down", "minio"])
        subprocess.run(["services/bt_services.py", "stop", "--include", "test-proxy"], cwd=REPO_ROOT)
