#!/usr/bin/env python3

# Sometimes the unit test runner will terminate without fully cleaning up
# (possibly because of user interruption). In this case, there may be lingering
# unit test objects which clutter up the DB. This script cleans up any lingering
# objects.

import psycopg2
from braintrust_local import api_db_util, app_db_util

UNIT_TEST_ORG_PATTERN = "_unit_test_org_%"
UNIT_TEST_USER_PATTERN = "_unit_test_user_%"

if __name__ == "__main__":
    app_db_util.update_foreign_keys_enable_cascade_delete()
    with psycopg2.connect(app_db_util.get_app_db_url()) as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                delete from secrets.org_secrets
                where id in (
                    select org_secrets.id
                    from
                        secrets.org_secrets
                        join organizations on org_secrets.org_id = organizations.id
                    where
                        organizations.name like %s
                )
            """,
                (UNIT_TEST_ORG_PATTERN,),
            )
            cursor.execute("delete from organizations where name like %s", (UNIT_TEST_ORG_PATTERN,))
            cursor.execute("delete from users where email like %s", (UNIT_TEST_USER_PATTERN,))

            cursor.execute("select distinct id from organizations")
            remaining_orgs = tuple(x[0] for x in cursor.fetchall())

    with psycopg2.connect(api_db_util.get_api_db_url()) as conn:
        with conn.cursor() as cursor:
            if remaining_orgs:
                placeholders = ", ".join(["%s"] * len(remaining_orgs))
                cursor.execute(
                    f"""
                    delete from brainstore_backfill_tracked_objects
                    where org_id not in ({placeholders})
                """,
                    remaining_orgs,
                )
            else:
                cursor.execute("delete from brainstore_backfill_tracked_objects")
