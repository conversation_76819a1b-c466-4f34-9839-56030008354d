# /// script
# dependencies = [
#   "psycopg2-binary",
#   "tqdm",
# ]
# ///

import argparse
import json
import os
import subprocess
import sys
from typing import Dict, List, Optional, Set, Tuple

# Constants
INDEX_SEGMENTS_PREFIX = "brainstore/index/segments/"
SUFFIXES = {"index_total": "", "index_wal": "object-global-store-wal/", "index_tantivy": "tantivy/"}


class S3StorageAnalyzer:
    def __init__(self, bucket: str):
        self.bucket = bucket

    def load_segment_prefixes(self, filename: str) -> List[str]:
        """Load segment prefixes from a file."""
        with open(filename, "r") as f:
            return [os.path.join(INDEX_SEGMENTS_PREFIX, line.strip()) for line in f]

    def get_total_size_subproc(self, prefix: str) -> subprocess.Popen:
        """Get total size of objects with given prefix using AWS CLI."""
        return subprocess.Popen(
            [
                "aws",
                "s3api",
                "list-objects",
                "--bucket",
                self.bucket,
                "--prefix",
                prefix,
                "--output",
                "json",
                "--query",
                "[sum(Contents[].Size), length(Contents[])]",
            ],
            stdout=subprocess.PIPE,
            text=True,
        )

    def extract_total_size(self, prefix: str, proc: subprocess.Popen) -> int:
        """Extract total size from subprocess output."""
        try:
            stdout, _ = proc.communicate()
            data = json.loads(stdout)
            return data[0] if data else 0
        except Exception as e:
            print(f"Failed to get output for prefix {prefix}\n{e}", file=sys.stderr)
            return 0

    def get_tantivy_file_sizes(self, tantivy_prefix: str) -> subprocess.Popen:
        """Get file sizes for tantivy index using AWS CLI."""
        fullpath = os.path.join(self.bucket, tantivy_prefix)
        return subprocess.Popen(["aws", "s3", "ls", f"s3://{fullpath}"], stdout=subprocess.PIPE, text=True)

    def extract_file_sizes(self, prefix: str, proc: subprocess.Popen) -> Dict[str, int]:
        """Extract file sizes from subprocess output."""
        try:
            stdout, _ = proc.communicate()
            file_to_size = {}
            for line in stdout.splitlines():
                _, _, size, filename = line.split()
                file_to_size[filename] = int(size)
            return file_to_size
        except Exception as e:
            print(f"Failed to get sizes for prefix {prefix}\n{e}", file=sys.stderr)
            return {}

    def get_tantivy_meta_json(self, tantivy_prefix: str) -> subprocess.Popen:
        """Get meta.json content for tantivy index using AWS CLI."""
        fullpath = os.path.join(self.bucket, tantivy_prefix, "meta.json")
        return subprocess.Popen(["aws", "s3", "cp", f"s3://{fullpath}", "-"], stdout=subprocess.PIPE, text=True)

    def extract_meta_json_segments(self, prefix: str, proc: subprocess.Popen) -> Set[str]:
        """Extract segment IDs from meta.json."""
        try:
            stdout, _ = proc.communicate()
            data = json.loads(stdout)
            return set(s["segment_id"].replace("-", "") for s in data["segments"])
        except Exception as e:
            print(f"Failed to get output for prefix {prefix}\n{e}", file=sys.stderr)
            return set()

    def calculate_segment_file_sizes(self, file_sizes: Dict[str, int], segments: Set[str]) -> Tuple[int, int]:
        """Calculate live and non-live segment file sizes."""
        live_size = 0
        nonlive_size = 0
        for filepath, size in file_sizes.items():
            if filepath.endswith(".json") or filepath.endswith(".footer"):
                # Ideally we should precisely identify the right footers, but it's hard to do that because it relies
                # on a hash of the meta.json file.
                continue
            if any(filepath.startswith(s) for s in segments):
                live_size += size
            else:
                nonlive_size += size
        return live_size, nonlive_size

    def list_index_segments(self, subset: Optional[int] = None) -> None:
        """List all index segments in the bucket."""
        fullpath = os.path.join(self.bucket, INDEX_SEGMENTS_PREFIX, "")
        proc = subprocess.Popen(["aws", "s3", "ls", f"s3://{fullpath}"], stdout=subprocess.PIPE, text=True)

        if proc.stdout is None:
            print("Error: Could not read from subprocess stdout", file=sys.stderr)
            return

        lines = []
        while True:
            line = proc.stdout.readline()
            if not line:
                break
            if subset and len(lines) == subset:
                break
            _, segment_id = line.split()
            lines.append(segment_id.strip().strip("/"))

        proc.terminate()
        for line in lines:
            print(line)

    def list_live_segments_from_db(self, object_id: str, pg_url: str) -> None:
        """List live segments for a given object ID from the database.

        Args:
            object_id: The object ID to query for
            pg_url: PostgreSQL connection URL
        """
        try:
            import psycopg2
        except ImportError:
            print(
                "Error: psycopg2 package is required for database operations. Please install it with: pip install psycopg2-binary"
            )
            sys.exit(1)

        conn = None
        try:
            conn = psycopg2.connect(pg_url)
            with conn.cursor() as cur:
                cur.execute(
                    "select segment_id from brainstore_global_store_segment_id_to_liveness where object_id=%s and is_live",
                    (object_id,),
                )
                for (segment_id,) in cur:
                    print(segment_id)
        except Exception as e:
            print(f"Error querying database: {e}", file=sys.stderr)
            sys.exit(1)
        finally:
            if conn is not None:
                conn.close()

    def analyze_index_size(self, operation: str, prefixes: List[str], batch_size: int) -> None:
        """Analyze index size based on the specified operation.

        Args:
            operation: Type of analysis to perform
            prefixes: List of segment prefixes to analyze
            batch_size: Number of segments to process in each batch
        """
        try:
            from tqdm import tqdm
        except ImportError:
            print("Error: tqdm package is required for progress bars. Please install it with: pip install tqdm")
            sys.exit(1)

        total_size = 0
        total_live_size = 0
        total_nonlive_size = 0

        # Process prefixes in batches with progress bar
        for i in tqdm(range(0, len(prefixes), batch_size), desc="Processing segments"):
            batch_prefixes = prefixes[i : i + batch_size]

            if operation in ["index_total", "index_wal", "index_tantivy"]:
                full_paths = [os.path.join(p, SUFFIXES[operation]) for p in batch_prefixes]
                subprocs = [self.get_total_size_subproc(p) for p in full_paths]
                sizes = [self.extract_total_size(p, proc) for p, proc in zip(full_paths, subprocs)]
                this_batch_size = sum(sizes)
                total_size += this_batch_size
                tqdm.write(
                    f"Batch {i//batch_size + 1}: Processed {len(batch_prefixes)} segments, size: {this_batch_size:,} bytes"
                )

            elif operation == "index_tantivy_live_segment":
                full_paths = [os.path.join(p, SUFFIXES["index_tantivy"]) for p in batch_prefixes]

                file_sizes_subprocs = [self.get_tantivy_file_sizes(p) for p in full_paths]
                meta_json_subprocs = [self.get_tantivy_meta_json(p) for p in full_paths]

                prefix_to_data = {}
                for prefix, file_sizes_proc, meta_json_proc in zip(
                    full_paths, file_sizes_subprocs, meta_json_subprocs
                ):
                    file_sizes = self.extract_file_sizes(prefix, file_sizes_proc)
                    segments = self.extract_meta_json_segments(prefix, meta_json_proc)
                    if file_sizes and segments:
                        prefix_to_data[prefix] = {"file_sizes": file_sizes, "segments": segments}

                batch_live_size = 0
                batch_nonlive_size = 0
                for prefix, data in prefix_to_data.items():
                    live_size, nonlive_size = self.calculate_segment_file_sizes(data["file_sizes"], data["segments"])
                    batch_live_size += live_size
                    batch_nonlive_size += nonlive_size

                total_live_size += batch_live_size
                total_nonlive_size += batch_nonlive_size
                tqdm.write(
                    f"Batch {i//batch_size + 1}: Processed {len(batch_prefixes)} segments, "
                    f"live: {batch_live_size:,} bytes, non-live: {batch_nonlive_size:,} bytes"
                )

        # Print final results
        if operation in ["index_total", "index_wal", "index_tantivy"]:
            print(f"\nTotal size: {total_size:,} bytes")
        else:
            print(f"\nTotal live size: {total_live_size:,} bytes")
            print(f"Total non-live size: {total_nonlive_size:,} bytes")


def main():
    parser = argparse.ArgumentParser(
        description="Analyze S3 storage usage for brainstore indices",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # List segments command
    list_parser = subparsers.add_parser("list-segments", help="List all index segments in the bucket")
    list_parser.add_argument("--bucket", type=str, required=True, help="S3 bucket name")
    list_parser.add_argument("--subset", type=int, help="Limit the number of segments to list")

    # List live segments command
    list_live_parser = subparsers.add_parser(
        "list-live-segments", help="List live segments for a given object ID from the database"
    )
    list_live_parser.add_argument("--object-id", type=str, required=True, help="Object ID to query for live segments")
    list_live_parser.add_argument("--pg-url", type=str, required=True, help="PostgreSQL connection URL")

    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze index storage usage")
    analyze_parser.add_argument("--bucket", type=str, required=True, help="S3 bucket name")
    analyze_parser.add_argument(
        "--segments-path", type=str, required=True, help="Path to newline-separated index segments"
    )
    analyze_parser.add_argument(
        "--operation",
        type=str,
        required=True,
        choices=["index_total", "index_wal", "index_tantivy", "index_tantivy_live_segment"],
        help="Type of analysis to perform",
    )
    analyze_parser.add_argument("--subset", type=int, help="Limit the number of segments to analyze")
    analyze_parser.add_argument(
        "--batch-size", type=int, default=10, help="Number of segments to process in each batch"
    )

    args = parser.parse_args()

    if args.command == "list-segments":
        analyzer = S3StorageAnalyzer(args.bucket)
        analyzer.list_index_segments(args.subset)
    elif args.command == "list-live-segments":
        analyzer = S3StorageAnalyzer("")  # bucket not needed for this operation
        analyzer.list_live_segments_from_db(args.object_id, args.pg_url)
    elif args.command == "analyze":
        analyzer = S3StorageAnalyzer(args.bucket)
        prefixes = analyzer.load_segment_prefixes(args.segments_path)
        if args.subset:
            prefixes = prefixes[: args.subset]
        analyzer.analyze_index_size(args.operation, prefixes, args.batch_size)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
