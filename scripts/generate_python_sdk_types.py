# Converts a subset of Zod typespecs to Python TypedDict subclasses, via an
# intermediate OpenAPI spec, for usage in the Python SDK.

import argparse
import os
import shutil
import subprocess
import sys
import tempfile


def main():
    parser = argparse.ArgumentParser(description="Generate Python SDK types")
    parser.add_argument("--write", help="Write the output to the repo", action="store_true", default=False)
    args = parser.parse_args()

    subprocess.run(
        ["pnpm", "build", "--filter", "@braintrust/openapi-deployment"], stdout=subprocess.DEVNULL, check=True
    )

    temp_dir = tempfile.mkdtemp()
    input_path = os.path.join(temp_dir, "spec.json")
    with open(input_path, "w") as f:
        subprocess.run(["npx", "generate-openapi-spec", "py_sdk"], stdout=f, check=True)
    print(f"Generated intermediate OpenAPI spec: {input_path}", file=sys.stderr)

    output_path = os.path.join(temp_dir, "_types.py")
    subprocess.run(
        [
            "datamodel-codegen",
            "--input",
            input_path,
            "--input-file-type",
            "openapi",
            "--output",
            output_path,
            "--output-model-type",
            "typing.TypedDict",
            "--target-python-version",
            "3.8",
            "--custom-file-header",
            '''"""
Do not import this file directly. See `types.py` for the classes that have a stable API.

Auto-generated file -- do not modify.
"""''',
            "--special-field-name-prefix",
            "",
            "--enum-field-as-literal",
            "all",
            "--capitalize-enum-members",
            "--use-generic-container-types",
            "--use-field-description",
            "--strict-nullable",
        ],
        stdout=sys.stderr,
        check=True,
    )

    # Format immediately so that we don't have to deal with newlines in the middle of types.
    subprocess.run(["pre-commit", "run", "--files", output_path], stdout=sys.stderr)

    # Workaround for --strict-nullable not working consistently.
    subprocess.run(
        ["pnpm", "dlx", "tsx", "scripts/generate_python_sdk_types.ts", output_path], stdout=sys.stderr, check=True
    )

    # Format again at the end to ensure compliant output.
    try:
        subprocess.run(["pre-commit", "run", "--files", output_path], stdout=sys.stderr, check=True)
    except subprocess.CalledProcessError:
        subprocess.run(["pre-commit", "run", "--files", output_path], stdout=sys.stderr, check=True)

    if args.write:
        shutil.copyfile(output_path, "sdk/py/src/braintrust/_types.py")
    else:
        with open(output_path, "r") as f:
            shutil.copyfileobj(f, sys.stdout)


if __name__ == "__main__":
    main()
