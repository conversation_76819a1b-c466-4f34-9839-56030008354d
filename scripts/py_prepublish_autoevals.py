#!/usr/bin/env python3

import json
import os
import subprocess
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(SCRIPT_DIR, "..", "sdk", "core", "py", "src"))
from braintrust_core.version import VERSION as CORE_VERSION

if __name__ == "__main__":
    setup_py_file = os.path.join(SCRIPT_DIR, "..", "autoevals", "setup.py")
    with open(setup_py_file, "r") as f:
        setup_py = f.read()

    setup_py = setup_py.replace('"braintrust_core"', f'"braintrust_core=={CORE_VERSION}"')

    with open(setup_py_file, "w") as f:
        f.write(setup_py)
