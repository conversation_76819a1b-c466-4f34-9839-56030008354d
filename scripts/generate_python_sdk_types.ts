import fs from "fs";

/**
 * Helper to fix the output code from datamodel-codegen.
 */
function main() {
  const inputPath = process.argv[2];
  if (!inputPath.endsWith(".py")) {
    throw new Error("Only supports Python files");
  }
  let contents = fs.readFileSync(inputPath, "utf-8") + "\n";

  // Replace `NotRequired[...]` with `NotRequired[Optional[...]]` for Python
  // TypedDict definitions.
  contents = contents.replace(
    /(\s[A-Za-z0-9_]+: NotRequired\[)(?!Optional\[\s*)(.+)(\]\n)/g,
    "$1Optional[$2]$3",
  );
  // Replace `schema_` with `schema`; this happens because datamodel-codegen
  // treats `schema` specially, expecting Pydantic.
  contents = contents.replace(/(\s+)schema_:/g, "$1schema:");

  // Discourage direct imports.
  contents += "__all__ = []";

  fs.writeFileSync(inputPath, contents);
}

main();
