#!/usr/bin/env python3

import json
import os
import subprocess
import sys

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(SCRIPT_DIR, "..", "sdk", "py", "src"))
sys.path.append(os.path.join(SCRIPT_DIR, "..", "sdk", "core", "py", "src"))

if __name__ == "__main__":
    package_file = os.path.join(SCRIPT_DIR, "..", "sdk", "js", "package.json")
    with open(package_file, "r") as f:
        package_json = json.load(f)

    with open(os.path.join(SCRIPT_DIR, "..", "sdk", "core", "js", "package.json"), "r") as f:
        CORE_VERSION = json.load(f)["version"]
    package_json["dependencies"]["@braintrust/core"] = f"{CORE_VERSION}"

    with open(package_file, "w") as f:
        json.dump(package_json, f, indent=2)

    subprocess.call(f"rm -r {os.path.join(SCRIPT_DIR, '..', 'sdk', 'js', 'dist')}/*", shell=True)
    subprocess.call(["npm", "run", "build"], cwd=os.path.join(SCRIPT_DIR, "..", "sdk", "js"))
