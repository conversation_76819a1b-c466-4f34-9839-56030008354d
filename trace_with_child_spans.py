#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> demonstrating how to create a trace with multiple child spans in Braintrust.

This script shows how to:
1. Create a root span for a main task
2. Create multiple child spans for sub-tasks
3. Create nested child spans (grandchildren)
4. Log data at different levels of the hierarchy

Usage:
    python trace_with_child_spans.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import time
import braintrust


def create_simple_trace_with_children():
    """Create a simple trace with multiple child spans."""
    print("=== Simple Trace with Child Spans ===")
    
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Create root span for the main task
    with logger.start_span(name="document_processing_pipeline") as root_span:
        root_span.log(
            input="Process document: research_paper.pdf",
            metadata={"document_type": "research_paper", "pages": 15}
        )
        
        # Child span 1: Document parsing
        with root_span.start_span(name="document_parsing") as parse_span:
            parse_span.log(
                input="research_paper.pdf",
                output="Extracted text content",
                scores={"extraction_quality": 0.95},
                metadata={"method": "OCR", "confidence": 0.98}
            )
        
        # Child span 2: Text analysis
        with root_span.start_span(name="text_analysis") as analysis_span:
            analysis_span.log(
                input="Extracted text content",
                output="Analysis results",
                scores={"sentiment": 0.7, "complexity": 0.8},
                metadata={"word_count": 3500, "language": "english"}
            )
        
        # Child span 3: Summary generation
        with root_span.start_span(name="summary_generation") as summary_span:
            summary_span.log(
                input="Analysis results",
                output="Generated summary of the research paper",
                scores={"coherence": 0.9, "completeness": 0.85},
                metadata={"summary_length": 250, "compression_ratio": 0.07}
            )
        
        # Log final result on root span
        root_span.log(
            output="Document processing completed successfully",
            scores={"overall_success": 1.0, "processing_time": 45.2},
            metadata={"total_steps": 3, "final_status": "completed"}
        )
    
    logger.flush()
    print("Simple trace with child spans logged successfully")


def create_nested_trace_with_grandchildren():
    """Create a more complex trace with nested child spans (grandchildren)."""
    print("\n=== Nested Trace with Grandchildren Spans ===")
    
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Root span for AI agent task
    with logger.start_span(name="ai_agent_conversation") as root_span:
        root_span.log(
            input="User question: How do I optimize my machine learning model?",
            metadata={"user_id": "user123", "session_id": "sess_456"}
        )
        
        # Child span 1: Query understanding
        with root_span.start_span(name="query_understanding") as understanding_span:
            understanding_span.log(
                input="How do I optimize my machine learning model?",
                metadata={"intent_detection": "model_optimization"}
            )
            
            # Grandchild span 1.1: Intent classification
            with understanding_span.start_span(name="intent_classification") as intent_span:
                intent_span.log(
                    input="How do I optimize my machine learning model?",
                    output="model_optimization",
                    scores={"confidence": 0.92},
                    metadata={"model": "intent_classifier_v2"}
                )
            
            # Grandchild span 1.2: Entity extraction
            with understanding_span.start_span(name="entity_extraction") as entity_span:
                entity_span.log(
                    input="How do I optimize my machine learning model?",
                    output={"entities": ["machine learning", "model", "optimization"]},
                    scores={"extraction_confidence": 0.88},
                    metadata={"extractor": "spacy_ner"}
                )
            
            # Log understanding result
            understanding_span.log(
                output="Query understood: User wants ML model optimization advice",
                scores={"understanding_quality": 0.9}
            )
        
        # Child span 2: Knowledge retrieval
        with root_span.start_span(name="knowledge_retrieval") as retrieval_span:
            retrieval_span.log(
                input="model_optimization query",
                metadata={"search_type": "semantic_search"}
            )
            
            # Grandchild span 2.1: Vector search
            with retrieval_span.start_span(name="vector_search") as vector_span:
                vector_span.log(
                    input="model optimization embedding",
                    output="Retrieved 5 relevant documents",
                    scores={"relevance_score": 0.85},
                    metadata={"search_engine": "pinecone", "top_k": 5}
                )
            
            # Grandchild span 2.2: Document ranking
            with retrieval_span.start_span(name="document_ranking") as ranking_span:
                ranking_span.log(
                    input="5 candidate documents",
                    output="Ranked documents by relevance",
                    scores={"ranking_quality": 0.78},
                    metadata={"ranking_model": "cross_encoder"}
                )
            
            # Log retrieval result
            retrieval_span.log(
                output="Retrieved relevant knowledge about ML optimization",
                scores={"retrieval_success": 0.82}
            )
        
        # Child span 3: Response generation
        with root_span.start_span(name="response_generation") as generation_span:
            generation_span.log(
                input="Query understanding + Retrieved knowledge",
                metadata={"generation_model": "gpt-4"}
            )
            
            # Grandchild span 3.1: Content synthesis
            with generation_span.start_span(name="content_synthesis") as synthesis_span:
                synthesis_span.log(
                    input="Knowledge chunks + user query",
                    output="Synthesized response content",
                    scores={"synthesis_quality": 0.87},
                    metadata={"synthesis_method": "retrieval_augmented_generation"}
                )
            
            # Grandchild span 3.2: Response formatting
            with generation_span.start_span(name="response_formatting") as formatting_span:
                formatting_span.log(
                    input="Raw response content",
                    output="Formatted response with structure",
                    scores={"formatting_quality": 0.93},
                    metadata={"format": "markdown", "sections": 4}
                )
            
            # Log generation result
            generation_span.log(
                output="Generated comprehensive response about ML optimization",
                scores={"response_quality": 0.89, "helpfulness": 0.91}
            )
        
        # Log final conversation result on root span
        root_span.log(
            output="AI agent successfully answered user's ML optimization question",
            scores={"conversation_success": 0.88, "user_satisfaction": 0.85},
            metadata={
                "total_processing_time_ms": 1250,
                "tokens_used": 1500,
                "response_length": 450
            }
        )
    
    logger.flush()
    print("Nested trace with grandchildren spans logged successfully")


def create_parallel_child_spans():
    """Create a trace with multiple parallel child spans."""
    print("\n=== Trace with Parallel Child Spans ===")
    
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Root span for parallel processing task
    with logger.start_span(name="parallel_data_processing") as root_span:
        root_span.log(
            input="Process 3 data sources simultaneously",
            metadata={"processing_mode": "parallel", "data_sources": 3}
        )
        
        # Simulate parallel processing with multiple child spans
        # Child span 1: Process data source A
        with root_span.start_span(name="process_source_a") as source_a_span:
            source_a_span.log(
                input="data_source_a.csv",
                output="Processed 1000 records from source A",
                scores={"processing_success": 1.0, "data_quality": 0.95},
                metadata={"records_processed": 1000, "processing_time_ms": 500}
            )
        
        # Child span 2: Process data source B
        with root_span.start_span(name="process_source_b") as source_b_span:
            source_b_span.log(
                input="data_source_b.json",
                output="Processed 750 records from source B",
                scores={"processing_success": 1.0, "data_quality": 0.88},
                metadata={"records_processed": 750, "processing_time_ms": 650}
            )
        
        # Child span 3: Process data source C
        with root_span.start_span(name="process_source_c") as source_c_span:
            source_c_span.log(
                input="data_source_c.xml",
                output="Processed 1200 records from source C",
                scores={"processing_success": 1.0, "data_quality": 0.92},
                metadata={"records_processed": 1200, "processing_time_ms": 800}
            )
        
        # Child span 4: Merge results
        with root_span.start_span(name="merge_results") as merge_span:
            merge_span.log(
                input="Results from sources A, B, C",
                output="Merged dataset with 2950 total records",
                scores={"merge_success": 1.0, "data_consistency": 0.94},
                metadata={"total_records": 2950, "merge_time_ms": 200}
            )
        
        # Log final result
        root_span.log(
            output="Parallel data processing completed successfully",
            scores={"overall_success": 1.0, "efficiency": 0.91},
            metadata={
                "total_processing_time_ms": 1350,
                "total_records": 2950,
                "parallel_speedup": 2.1
            }
        )
    
    logger.flush()
    print("Parallel child spans trace logged successfully")


def main():
    """Run all trace examples."""
    print("Braintrust Trace with Child Spans Examples")
    print("=" * 45)
    
    try:
        # Run all examples
        create_simple_trace_with_children()
        create_nested_trace_with_grandchildren()
        create_parallel_child_spans()
        
        print("\n" + "=" * 45)
        print("All trace examples completed successfully!")
        print("Check your Braintrust dashboard to see the hierarchical traces.")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have a valid BRAINTRUST_API_KEY and internet connection.")


if __name__ == "__main__":
    main()
