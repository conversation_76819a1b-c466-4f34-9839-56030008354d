// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`datasets > filter by is null/is not null 1`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "description",
        ],
        "op": "field",
        "type": {
          "type": "string",
        },
      },
      "op": "isnull",
    },
    "from": {
      "name": "datasets",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "description",
        "expr": {
          "name": [
            "description",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
    ],
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 30,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "description",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 38,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 23,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "datasets",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "description",
        "expr": {
          "loc": {
            "end": {
              "col": 34,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "description",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 23,
            "line": 5,
          },
          "start": {
            "col": 17,
            "line": 5,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "description": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "description",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "description",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "description",
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") OR (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "description",
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS NOT DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "description": null,
      "id": "null",
    },
    {
      "description": "null",
      "id": "null_string",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "description": {
          "type": "string",
        },
        "id": {
          "type": "string",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`datasets > filter by is null/is not null 2`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "description",
        ],
        "op": "field",
        "type": {
          "type": "string",
        },
      },
      "op": "isnotnull",
    },
    "from": {
      "name": "datasets",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "description",
        "expr": {
          "name": [
            "description",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
    ],
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 30,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "description",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 42,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnotnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 23,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "datasets",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "description",
        "expr": {
          "loc": {
            "end": {
              "col": 34,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "description",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 23,
            "line": 5,
          },
          "start": {
            "col": 17,
            "line": 5,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "description": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "description",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "description",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "description",
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NOT NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") AND (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "description",
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "description": "",
      "id": "empty_value",
    },
    {
      "description": "valid description",
      "id": "valid",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "description": {
          "type": "string",
        },
        "id": {
          "type": "string",
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`datasets > filter by is null/is not null 3`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "num_examples",
        ],
        "op": "field",
        "type": {
          "type": [
            "number",
            "null",
          ],
        },
      },
      "op": "isnull",
    },
    "from": {
      "name": "datasets",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "num_examples",
        "expr": {
          "name": [
            "num_examples",
          ],
          "op": "field",
          "type": {
            "type": [
              "number",
              "null",
            ],
          },
        },
      },
    ],
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 31,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "num_examples",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 39,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 23,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "datasets",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "num_examples",
        "expr": {
          "loc": {
            "end": {
              "col": 35,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "num_examples",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 23,
            "line": 5,
          },
          "start": {
            "col": 17,
            "line": 5,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "id": {
            "type": "string",
          },
          "num_examples": {
            "type": [
              "number",
              "null",
            ],
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "num_examples",
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "numeric",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "num_examples",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        Ident {
                          "name": [
                            "datasets",
                            "num_examples",
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "numeric",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") OR (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        Ident {
                          "name": [
                            "datasets",
                            "num_examples",
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "numeric",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS NOT DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "id": "null",
      "num_examples": null,
    },
    {
      "id": "null_string",
      "num_examples": null,
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "id": {
          "type": "string",
        },
        "num_examples": {
          "type": [
            "number",
            "null",
          ],
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`datasets > filter by is null/is not null 4`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "num_examples",
        ],
        "op": "field",
        "type": {
          "type": [
            "number",
            "null",
          ],
        },
      },
      "op": "isnotnull",
    },
    "from": {
      "name": "datasets",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "num_examples",
        "expr": {
          "name": [
            "num_examples",
          ],
          "op": "field",
          "type": {
            "type": [
              "number",
              "null",
            ],
          },
        },
      },
    ],
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 31,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "num_examples",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 43,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnotnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 23,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "datasets",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "num_examples",
        "expr": {
          "loc": {
            "end": {
              "col": 35,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "num_examples",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 23,
            "line": 5,
          },
          "start": {
            "col": 17,
            "line": 5,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "id": {
            "type": "string",
          },
          "num_examples": {
            "type": [
              "number",
              "null",
            ],
          },
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "datasets",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "datasets",
                        "num_examples",
                      ],
                    },
                    PlainSnippet {
                      "text": "::",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "numeric",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "num_examples",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "datasets",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        Ident {
                          "name": [
                            "datasets",
                            "num_examples",
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "numeric",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NOT NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") AND (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    ParameterizedSnippet {
                      "fragments": [
                        PlainSnippet {
                          "text": "",
                        },
                        Ident {
                          "name": [
                            "datasets",
                            "num_examples",
                          ],
                        },
                        PlainSnippet {
                          "text": "::",
                        },
                        ParameterizedSnippet {
                          "fragments": [
                            PlainSnippet {
                              "text": "numeric",
                            },
                          ],
                        },
                        PlainSnippet {
                          "text": "",
                        },
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "id": "empty_value",
      "num_examples": 0,
    },
    {
      "id": "valid",
      "num_examples": 100,
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "id": {
          "type": "string",
        },
        "num_examples": {
          "type": [
            "number",
            "null",
          ],
        },
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`logs > filter by is null/is not null 1`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "output",
        ],
        "op": "field",
        "type": {},
      },
      "op": "isnull",
    },
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "output",
        "expr": {
          "name": [
            "output",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "sort": [
      {
        "alias": "id",
        "dir": "asc",
      },
    ],
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 25,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "output",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 33,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 19,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "output",
        "expr": {
          "loc": {
            "end": {
              "col": 29,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "output",
          ],
          "op": "ident",
        },
      },
    ],
    "sort": [
      {
        "dir": "asc",
        "expr": {
          "loc": {
            "end": {
              "col": 19,
              "line": 5,
            },
            "start": {
              "col": 17,
              "line": 5,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
        "loc": {
          "end": {
            "col": 23,
            "line": 5,
          },
          "start": {
            "col": 17,
            "line": 5,
          },
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "id": {
            "type": "string",
          },
          "output": {},
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "output",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "output",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "output",
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") OR (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "output",
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS NOT DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
ORDER BY ",
            },
            ParameterizedSnippet {
              "fragments": [
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "id",
                      ],
                    },
                    PlainSnippet {
                      "text": " ",
                    },
                    PlainSnippet {
                      "text": "asc",
                    },
                    PlainSnippet {
                      "text": "",
                    },
                  ],
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "id": "null",
      "output": null,
    },
    {
      "id": "null_string",
      "output": null,
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "id": {
          "type": "string",
        },
        "output": {},
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;

exports[`logs > filter by is null/is not null 2`] = `
{
  "bound": {
    "cursor": undefined,
    "filter": {
      "expr": {
        "name": [
          "output",
        ],
        "op": "field",
        "type": {},
      },
      "op": "isnotnull",
    },
    "from": {
      "name": "logs",
      "objects": undefined,
      "shape": undefined,
    },
    "limit": undefined,
    "select": [
      {
        "alias": "id",
        "expr": {
          "name": [
            "id",
          ],
          "op": "field",
          "type": {
            "type": "string",
          },
        },
      },
      {
        "alias": "output",
        "expr": {
          "name": [
            "output",
          ],
          "op": "field",
          "type": {},
        },
      },
    ],
    "sort": undefined,
    "summary": undefined,
    "unpivot": [],
  },
  "parsed": {
    "filter": {
      "expr": {
        "loc": {
          "end": {
            "col": 25,
            "line": 4,
          },
          "start": {
            "col": 19,
            "line": 4,
          },
        },
        "name": [
          "output",
        ],
        "op": "ident",
      },
      "loc": {
        "end": {
          "col": 37,
          "line": 4,
        },
        "start": {
          "col": 19,
          "line": 4,
        },
      },
      "op": "isnotnull",
    },
    "from": {
      "loc": {
        "end": {
          "col": 19,
          "line": 2,
        },
        "start": {
          "col": 15,
          "line": 2,
        },
      },
      "name": [
        "logs",
      ],
      "op": "ident",
    },
    "select": [
      {
        "alias": "id",
        "expr": {
          "loc": {
            "end": {
              "col": 21,
              "line": 3,
            },
            "start": {
              "col": 19,
              "line": 3,
            },
          },
          "name": [
            "id",
          ],
          "op": "ident",
        },
      },
      {
        "alias": "output",
        "expr": {
          "loc": {
            "end": {
              "col": 29,
              "line": 3,
            },
            "start": {
              "col": 23,
              "line": 3,
            },
          },
          "name": [
            "output",
          ],
          "op": "ident",
        },
      },
    ],
  },
  "plan": {
    "postProcess": [Function],
    "schema": {
      "items": {
        "properties": {
          "id": {
            "type": "string",
          },
          "output": {},
        },
        "type": "object",
      },
      "type": "array",
    },
    "sql": ParameterizedSnippet {
      "fragments": [
        PlainSnippet {
          "text": "SELECT ",
        },
        ParameterizedSnippet {
          "fragments": [
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "id",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            RawSnippet {
              "query": "
, ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
                Ident {
                  "name": [
                    "logs",
                    "output",
                  ],
                },
                PlainSnippet {
                  "text": " AS ",
                },
                Ident {
                  "name": [
                    "output",
                  ],
                },
                PlainSnippet {
                  "text": "",
                },
              ],
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "",
                },
              ],
            },
            PlainSnippet {
              "text": "
FROM ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": " AS ",
            },
            Ident {
              "name": [
                "logs",
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "
WHERE ",
            },
            ParameterizedSnippet {
              "fragments": [
                PlainSnippet {
                  "text": "((",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "output",
                      ],
                    },
                    PlainSnippet {
                      "text": " IS NOT NULL",
                    },
                  ],
                },
                PlainSnippet {
                  "text": ") AND (",
                },
                ParameterizedSnippet {
                  "fragments": [
                    PlainSnippet {
                      "text": "TRY_CAST(",
                    },
                    Ident {
                      "name": [
                        "logs",
                        "output",
                      ],
                    },
                    PlainSnippet {
                      "text": " AS JSON) IS DISTINCT FROM 'null'::JSON",
                    },
                  ],
                },
                PlainSnippet {
                  "text": "))",
                },
              ],
            },
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
        ParameterizedSnippet {
          "fragments": [
            PlainSnippet {
              "text": "",
            },
          ],
        },
        PlainSnippet {
          "text": "",
        },
      ],
    },
  },
  "resultData": [
    {
      "id": "empty_string",
      "output": "",
    },
    {
      "id": "valid",
      "output": "valid output",
    },
  ],
  "resultSchema": {
    "items": {
      "properties": {
        "id": {
          "type": "string",
        },
        "output": {},
      },
      "type": "object",
    },
    "type": "array",
  },
}
`;
