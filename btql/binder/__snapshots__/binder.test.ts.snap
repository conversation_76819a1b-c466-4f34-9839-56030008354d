// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`bind timestamp functions 1`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 2`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 3`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 4`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 5`] = `
{
  "args": [],
  "name": "current_date",
  "op": "function",
}
`;

exports[`bind timestamp functions 6`] = `
{
  "left": {
    "args": [],
    "name": "current_date",
    "op": "function",
  },
  "op": "add",
  "right": {
    "op": "interval",
    "unit": "day",
    "value": 1,
  },
}
`;

exports[`bind timestamp functions 7`] = `
{
  "left": {
    "args": [],
    "name": "current_timestamp",
    "op": "function",
  },
  "op": "add",
  "right": {
    "op": "interval",
    "unit": "day",
    "value": 1,
  },
}
`;

exports[`bind timestamp functions 8`] = `
{
  "left": {
    "expr": {
      "args": [],
      "name": "current_date",
      "op": "function",
    },
    "op": "cast",
    "type": "datetime",
  },
  "op": "gt",
  "right": {
    "args": [],
    "name": "current_timestamp",
    "op": "function",
  },
}
`;

exports[`bind timestamp functions 9`] = `
{
  "name": [
    "current_date",
  ],
  "op": "field",
  "type": {
    "type": "integer",
  },
}
`;
