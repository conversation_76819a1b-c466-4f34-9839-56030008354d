import {
  ArithmeticOp,
  arithmeticOps,
  BooleanOp,
  booleanOps,
  ComparisonOp,
  comparisonOps,
  intervalLiteralSchema,
  shapeSchema,
  sortDirectionSchema,
  UnaryArithmeticOp,
  unaryArithmeticOps,
  UnaryOp,
} from "#/parser/ast";
import { LogicalSchema, ScalarType, scalarTypeSchema } from "#/schema";
import { jsonSchemaObjectSchema } from "#/schema/json-schema";
import { z } from "zod";
import { functionNameSchema, FUNCTIONS } from "./functions";

export type Type = LogicalSchema;

export const literalSchema = z.strictObject({
  op: z.literal("literal"),
  value: z.unknown(),
  type: scalarTypeSchema,
});
export type Literal = z.infer<typeof literalSchema>;

export const intervalSchema = z
  .strictObject({
    op: z.literal("interval"),
  })
  .merge(intervalLiteralSchema.omit({ loc: true }));
export type Interval = z.infer<typeof intervalSchema>;

export const fieldSchema = z.strictObject({
  op: z.literal("field"),
  name: z.array(z.union([z.string(), z.number()])),
  type: jsonSchemaObjectSchema,
  source: z
    .union([
      z.strictObject({
        unpivot: z.number().int(),
        type: z.enum(["key", "value", "element"]),
      }),
      z.strictObject({
        kind: z.literal("unpivot-arg"),
      }),
    ])
    .nullish(),
});
export type Field = z.infer<typeof fieldSchema>;

export type Function = {
  op: "function";
  name: keyof typeof FUNCTIONS;
  args: BoundExpr[];
};
export const functionSchema: z.ZodType<Function> = z.strictObject({
  op: z.literal("function"),
  name: functionNameSchema,
  args: z.array(z.lazy(() => boundExprSchema)),
});

export type ComparisonExpr = {
  op: ComparisonOp;
  left: BoundExpr;
  right: BoundExpr;
};
export const comparisonSchema: z.ZodType<ComparisonExpr> = z.strictObject({
  op: z.enum(comparisonOps),
  left: z.lazy(() => boundExprSchema),
  right: z.lazy(() => boundExprSchema),
});

export type IncludesExpr = {
  op: "includes";
  haystack: BoundExpr;
  needle: BoundExpr;
};
export const includesSchema: z.ZodType<IncludesExpr> = z.strictObject({
  op: z.literal("includes"),
  haystack: z.lazy(() => boundExprSchema),
  needle: z.lazy(() => boundExprSchema),
});

export type BoolExpr = {
  op: BooleanOp;
  children: BoundExpr[];
};
export const boolExprSchema: z.ZodType<BoolExpr> = z.strictObject({
  op: z.enum(booleanOps),
  children: z.array(z.lazy(() => boundExprSchema)),
});

export type TernaryCond = {
  cond: BoundExpr;
  then: BoundExpr;
};

export const ternaryCondSchema: z.ZodType<TernaryCond> = z.strictObject({
  cond: z.lazy(() => boundExprSchema),
  then: z.lazy(() => boundExprSchema),
});

export type TernaryExpr = {
  op: "if";
  conds: TernaryCond[];
  else: BoundExpr;
};

export const ternaryExprSchema: z.ZodType<TernaryExpr> = z.strictObject({
  op: z.literal("if"),
  conds: z.array(ternaryCondSchema),
  else: z.lazy(() => boundExprSchema),
});

export type ArithmeticExpr = {
  op: ArithmeticOp;
  left: BoundExpr;
  right: BoundExpr;
};
export const arithmeticExprSchema: z.ZodType<ArithmeticExpr> = z.strictObject({
  op: z.enum(arithmeticOps),
  left: z.lazy(() => boundExprSchema),
  right: z.lazy(() => boundExprSchema),
});

export type UnaryArithmetic = {
  op: UnaryArithmeticOp;
  expr: BoundExpr;
};
export const unaryArithmeticSchema: z.ZodType<UnaryArithmetic> = z.strictObject(
  {
    op: z.enum(unaryArithmeticOps),
    expr: z.lazy(() => boundExprSchema),
  },
);

export type UnaryExpr = {
  op: UnaryOp;
  expr: BoundExpr;
};
export const unaryExprSchema: z.ZodType<UnaryExpr> = z.strictObject({
  op: z.enum(["not", "isnull", "isnotnull"]),
  expr: z.lazy(() => boundExprSchema),
});

export type CastExpr = {
  op: "cast";
  expr: BoundExpr;
  type: ScalarType;
};
export const castExprSchema: z.ZodType<CastExpr> = z.strictObject({
  op: z.literal("cast"),
  expr: z.lazy(() => boundExprSchema),
  type: scalarTypeSchema,
});

export type BoundExpr =
  | Literal
  | Interval
  | Field
  | Function
  | ComparisonExpr
  | IncludesExpr
  | BoolExpr
  | TernaryExpr
  | ArithmeticExpr
  | UnaryArithmetic
  | UnaryExpr
  | CastExpr;

export const boundExprSchema: z.ZodType<BoundExpr> = z.union([
  literalSchema,
  intervalSchema,
  fieldSchema,
  functionSchema,
  comparisonSchema,
  includesSchema,
  boolExprSchema,
  ternaryExprSchema,
  arithmeticExprSchema,
  unaryArithmeticSchema,
  unaryExprSchema,
  castExprSchema,
]);

export const boundAliasSchema = z.strictObject({
  expr: boundExprSchema,
  alias: z.string(),
});
export type BoundAlias = z.infer<typeof boundAliasSchema>;

export const boundUnpivotExprSchema = z.strictObject({
  expr: boundExprSchema,
  type: z.enum(["object", "array"]),
});
export type BoundUnpivotExpr = z.infer<typeof boundUnpivotExprSchema>;

export const boundProjectionSchema = z.union([
  z.object({
    dimensions: z.array(boundAliasSchema),
    pivot: z.array(boundAliasSchema),
    measures: z.array(boundAliasSchema),
  }),
  z.object({
    select: z.array(boundAliasSchema),
  }),
  z.object({
    infer: z.array(fieldSchema),
  }),
]);

export type BoundProjection = z.infer<typeof boundProjectionSchema>;

export const boundFromSchema = z.strictObject({
  name: z.string(),
  objects: z.array(z.string()).nullish(),
  shape: shapeSchema.nullish(),
});

export type BoundFrom = z.infer<typeof boundFromSchema>;

export const boundSortItem = z
  .object({
    dir: sortDirectionSchema,
  })
  .and(
    z.union([
      z.object({
        alias: z.string(),
      }),
      z.object({
        expr: boundExprSchema,
      }),
    ]),
  );

export type BoundSortItem = z.infer<typeof boundSortItem>;

export const boundSortSchema = z.array(boundSortItem);
export type BoundSort = z.infer<typeof boundSortSchema>;

export const boundSummarySchema = z.object({
  comparison_key: boundExprSchema,
  weighted_scores: z.array(boundAliasSchema),
  custom_columns: z.array(boundAliasSchema),
  preview_length: z.number().int().nullish(),
});

export const boundQuerySchema = z
  .object({
    filter: boundExprSchema.nullish(),
    from: boundFromSchema.nullish(),
    unpivot: z.array(boundUnpivotExprSchema),
    sort: boundSortSchema.nullish(),
    limit: z.number().int().nullish(),
    cursor: z.string().nullish(),
    summary: boundSummarySchema.nullish(),
  })
  .and(boundProjectionSchema);
export type BoundQuery = z.infer<typeof boundQuerySchema>;
