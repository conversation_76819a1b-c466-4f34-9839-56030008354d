import {
  deriveScalarTypes,
  JSONSchemaObject,
  jsonSchemaObjectSchema,
  LogicalSchema,
  ScalarType,
  scalarTypeToLogicalSchema,
  toSchemaObject,
} from "#/schema";
import { z } from "zod";
import { BoundExpr, <PERSON>und<PERSON>uery } from "./ast";
import { FUNCTIONS } from "./functions";
import { datetimeLiteralSchema } from "#/parser";

export function getExprSchema(expr: BoundExpr): LogicalSchema {
  switch (expr.op) {
    case "literal":
      return scalarTypeToLogicalSchema(expr.type);
    case "interval":
      return scalarTypeToLogicalSchema("interval");
    case "field":
      return expr.type;
    case "function":
      const fnDef = FUNCTIONS[expr.name];
      const named = Object.fromEntries(
        Object.keys(fnDef.args.named).map((a, i) => [
          a,
          getExprSchema(expr.args[i]),
        ]),
      );
      const variadic = expr.args
        .slice(Object.keys(fnDef.args.named).length)
        .map(getExprSchema);
      return FUNCTIONS[expr.name].type(named, variadic);
    case "cast":
      return scalarTypeToLogicalSchema(expr.type);
    case "eq":
    case "is":
    case "ne":
    case "lt":
    case "le":
    case "gt":
    case "ge":
    case "like":
    case "ilike":
    case "match":
    case "includes":
    case "and":
    case "or":
    case "not":
    case "isnull":
    case "isnotnull":
      return { type: "boolean" };
    case "add":
    case "sub":
    case "mul":
    case "div":
    case "mod":
      // We can be more precise about this, e.g. derive integer
      const left = getExprScalarType(expr.left);
      const right = getExprScalarType(expr.right);
      if (
        (left === "integer" || left === "boolean") &&
        (right === "integer" || right === "boolean") &&
        expr.op !== "div"
      ) {
        return { type: "integer" };
      } else if (isDateLikeAndInterval("datetime", left, right)) {
        return scalarTypeToLogicalSchema("datetime");
      } else if (isDateLikeAndInterval("date", left, right)) {
        return scalarTypeToLogicalSchema("date");
      } else {
        return { type: "number" };
      }
    case "if":
      // We should have coerced all of these to be the same type
      const schemas: LogicalSchema[] = [];
      for (const cond of expr.conds) {
        schemas.push(getExprSchema(cond.then));
      }
      schemas.push(getExprSchema(expr.else));
      return {
        anyOf: schemas,
      };
    case "neg":
      return getExprSchema(expr.expr);
  }
}

function isDateLikeAndInterval(
  dateLike: ScalarType,
  left: ScalarType,
  right: ScalarType,
): boolean {
  return (
    (left === dateLike && right === "interval") ||
    (left === "interval" && right === dateLike)
  );
}

export const responseSchemaSchema = z.strictObject({
  type: z.literal("array"),
  items: z.strictObject({
    type: z.literal("object"),
    properties: z.record(jsonSchemaObjectSchema),
  }),
});
export type ResponseSchema = z.infer<typeof responseSchemaSchema>;

export function getResultSchema(query: BoundQuery): ResponseSchema {
  let projectionTypes: Record<string, LogicalSchema> = {};
  if ("select" in query) {
    projectionTypes = Object.fromEntries(
      query.select.map((a) => [a.alias, getExprSchema(a.expr)]),
    );
  } else if ("infer" in query) {
    projectionTypes = {
      name: {
        type: "array",
        items: {
          anyOf: [{ type: "string" }, { type: "number" }],
        },
      },
      type: {},
      top_values: {
        type: "array",
        items: {
          type: "object",
          properties: {
            value: {},
            count: {
              type: "integer",
            },
          },
        },
      },
    };
  } else {
    const measuresSchema = Object.fromEntries(
      query.measures.map((a) => [a.alias, getExprSchema(a.expr)]),
    );

    projectionTypes = {
      ...Object.fromEntries(
        query.dimensions.map((a) => [a.alias, getExprSchema(a.expr)]),
      ),
      ...measuresSchema,
      ...(query.pivot.length > 0
        ? {
            [query.pivot[0].alias]: constructPivotSchema(
              query.pivot.slice(1).map((a) => a.alias),
              measuresSchema,
            ),
          }
        : {}),
    };
  }

  return {
    type: "array",
    items: {
      type: "object",
      properties: projectionTypes,
    },
  };
}

function constructPivotSchema(
  pivotFields: string[],
  measuresSchema: Record<string, LogicalSchema>,
): LogicalSchema {
  if (pivotFields.length === 0) {
    return {
      type: "object",
      additionalProperties: { type: "object", properties: measuresSchema },
    };
  } else {
    return {
      type: "object",
      additionalProperties: {
        type: "object",
        properties: {
          ...measuresSchema,
          [pivotFields[0]]: constructPivotSchema(
            pivotFields.slice(1),
            measuresSchema,
          ),
        },
      },
    };
  }
}

export function getExprScalarType(expr: BoundExpr): ScalarType {
  return weakestScalarType(getExprSchema(expr));
}

export function weakestScalarType(
  schema: LogicalSchema | ScalarType[],
): ScalarType {
  if (schema instanceof Object && !Array.isArray(schema)) {
    schema = deriveScalarTypes(schema);
  }
  return schema.sort((a, b) => typeRank(a) - typeRank(b))[0];
}

export function isObjectLike(type: ScalarType): boolean {
  return type === "object" || type === "array" || type === "unknown";
}

// Returns a cast for each of the expressions to the common type
export function coerceTypes(
  a: LogicalSchema,
  b: LogicalSchema,
  coerceUnknown: boolean,
): [ScalarType | null, ScalarType | null] {
  if (JSON.stringify(a) === JSON.stringify(b)) {
    return [null, null];
  }

  const lowestLeft = weakestScalarType(a);
  const lowestRight = weakestScalarType(b);

  return coerceScalarTypes(lowestLeft, lowestRight, coerceUnknown);
}

export function coerceScalarTypes(
  a: ScalarType,
  b: ScalarType,
  coerceUnknown: boolean,
): [ScalarType | null, ScalarType | null] {
  if (a === b) {
    return [null, null];
  } else if (!coerceUnknown && (a === "unknown" || b === "unknown")) {
    // If the types aren't known, then don't coerce them. If we do, we might accidentally
    // transform something like 1.5::unknown == 1::integer into 1.5::integer == 1::integer.
    return [null, null];
  }

  let rankLeft = typeRank(a);
  let rankRight = typeRank(b);

  // Generally speaking, we want to cast to the strongest type,
  // but numbers are a bit of a special case. If we have a number
  // and an integer, we want to cast the integer to a number. Similarly,
  // if we have an integer and a bool, we want to cast the bool to an integer.
  const numberTypes = ["number", "integer", "boolean"];
  if (numberTypes.includes(a) && numberTypes.includes(b)) {
    rankLeft *= -1;
    rankRight *= -1;
  }

  // If we have an interval and a non-interval, we want to cast the non-interval
  // to a timestamp.
  if (a === "interval" && b !== "interval") {
    return [null, b === "datetime" ? null : "datetime"];
  } else if (b === "interval" && a !== "interval") {
    return [a === "datetime" ? null : "datetime", null];
  }

  return rankLeft === rankRight
    ? [null, null]
    : rankLeft > rankRight
      ? [null, a]
      : [b, null];
}

export function weakestExprType(exprs: BoundExpr[]): ScalarType {
  const allExprTypes = exprs.map(getExprScalarType);
  return weakestScalarType(allExprTypes);
}

export function coerceAllExprTypes(
  exprs: BoundExpr[],
  coerceUnknown: boolean,
): ScalarType {
  const allExprTypes = exprs.map((e) => getExprScalarType(e));
  return coerceAllTypes(allExprTypes, coerceUnknown);
}

// This function coerces types using the same logic that we do to upcast
// <unknown>=<string> to <unknown::string=string. We use it in COALESCE
// so that COALESCE(object, string) becomes COALESCE(object::string, string)
//
// At time of writing, I'm not convinced it's the right behavior for all cases.
export function coerceAllTypes(
  allExprTypes: ScalarType[],
  coerceUnknown: boolean,
): ScalarType {
  // I am sure this warrants further iteration, and it feels a bit specific to COALESCE.
  // The idea here is to ignore all NULL values (b/c again, this is used in COALESCE), but
  // I could imagine a single NULL "poisoning" another variadic function (like add or something).
  const nonNullExprTypes = allExprTypes.filter((t) => t !== "null");
  if (nonNullExprTypes.length === 0) {
    return "null";
  }

  let scalarType = nonNullExprTypes[0];
  for (const t of nonNullExprTypes) {
    const [leftCast, rightCast] = coerceScalarTypes(
      scalarType,
      t,
      coerceUnknown,
    );
    if (leftCast && leftCast !== "null") {
      scalarType = leftCast;
    } else if (rightCast && rightCast !== "null") {
      scalarType = rightCast;
    }
  }
  return scalarType;
}

// The rank of a type, where higher types are "stronger" than lower types. Generally speaking,
// we want to cast "weaker" types to "stronger" types.
function typeRank(type: ScalarType) {
  switch (type) {
    case "null":
      return 10;
    case "interval":
      return 9;
    case "datetime":
      return 8;
    case "date":
      return 7;
    case "boolean":
      return 6;
    case "integer":
      return 5;
    case "number":
      return 4;
    case "string":
      return 3;
    case "array":
      return 2;
    case "object":
      return 1;
    case "unknown":
      return 0;
  }
}

// Returns the type that is closest to the given type, or the weakest
// of the target types if the given type is weaker than all of them.
export function castToClosest(
  source: ScalarType[],
  target: ScalarType[],
): ScalarType | null {
  const type = weakestScalarType(source);
  const sorted = [...target];
  sorted.sort((a, b) => typeRank(b) - typeRank(a));
  for (const t of sorted) {
    if (typeRank(t) === typeRank(type)) {
      return null;
    } else if (typeRank(t) < typeRank(type)) {
      return t;
    }
  }
  return sorted[sorted.length - 1];
}

export function literalValueToScalarType(value: unknown): ScalarType {
  return value === null
    ? "null"
    : value === true || value === false
      ? "boolean"
      : typeof value === "bigint"
        ? "integer"
        : typeof value === "number"
          ? Number.isInteger(value)
            ? "integer"
            : "number"
          : typeof value === "string"
            ? datetimeLiteralSchema.safeParse(value).success
              ? "datetime"
              : "string"
            : Array.isArray(value)
              ? "array"
              : value && typeof value === "object"
                ? "object"
                : "unknown";
}

export function stripNull(schema: JSONSchemaObject): JSONSchemaObject {
  if (schema.anyOf) {
    const remaining = schema.anyOf.filter(
      (s) => toSchemaObject(s).type !== "null",
    );
    if (remaining.length === 1) {
      return toSchemaObject(remaining[0]);
    }
  }
  return schema;
}
