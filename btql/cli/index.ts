#!/usr/bin/env node

import { bindQuery, DEFAULT_SCHEMA } from "#/binder";
import { parseQuery } from "#/parser";
import { tokenize } from "#/parser/lexer";
import { planQuery } from "#/planner";
import { LogicalSchema, parseLogicalSchema, PhysicalSchema } from "#/schema";
import { ArgumentParser } from "argparse";
import chalk from "chalk";
import { readFileSync } from "fs";
import { z } from "zod";
import { jsonSchema } from "./json-schema";
import { createInterface } from "readline";

// This requires require
// https://stackoverflow.com/questions/50822310/how-to-import-package-json-in-typescript
const { version } = require("../package.json");

export const error = chalk.bold.red;
export const warning = chalk.hex("#FFA500"); // Orange color

export function logError(e: unknown, verbose: boolean) {
  if (!verbose) {
    console.error(error(`${e}`));
  } else {
    console.error(e);
  }
}

const parseArgsSchema = z.object({
  query: z.string(),
});
async function parse(argsRaw: unknown) {
  const args = parseArgsSchema.parse(argsRaw);
  console.log("QUERY", args.query);

  const tokenizer = tokenize(args.query);
  for (const token of tokenizer.tokens()) {
    console.log("TOKEN", token);
  }

  console.log("PARSED", JSON.stringify(parseQuery(args.query), null, 2));
}

const bindArgsSchema = z.object({
  query: z.string(),
  schema: z.string().optional(),
  skip_field_casts: z.boolean().optional(),
});
async function bind(argsRaw: unknown) {
  const args = bindArgsSchema.parse(argsRaw);

  const parsed = parseQuery(args.query);

  let schema: LogicalSchema = DEFAULT_SCHEMA;
  if (args.schema) {
    schema = parseLogicalSchema(JSON.parse(readFileSync(args.schema, "utf-8")));
  }

  const bound = bindQuery({
    query: parsed,
    schema,
    skipFieldCasts: args.skip_field_casts,
    queryText: args.query,
  });
  process.stdout.write(JSON.stringify(bound, null, 2) + "\n");
}

async function bind_repl(argsRaw: unknown) {
  const args = z
    .object({
      schema: z.string().optional(),
      skip_field_casts: z.boolean().optional(),
    })
    .parse(argsRaw);

  let schema: LogicalSchema = DEFAULT_SCHEMA;
  if (args.schema) {
    schema = parseLogicalSchema(JSON.parse(readFileSync(args.schema, "utf-8")));
  }

  // Process each line as a JSON-serialized query string until EOF
  const rl = createInterface({
    input: process.stdin,
    terminal: false,
  });

  for await (const line of rl) {
    try {
      const query = JSON.parse(line);
      const parsed = parseQuery(query);
      const bound = bindQuery({
        query: parsed,
        schema,
        skipFieldCasts: args.skip_field_casts,
        queryText: query,
      });
      process.stdout.write(JSON.stringify({ ok: bound }) + "\n");
    } catch (e) {
      process.stdout.write(JSON.stringify({ error: `${e}` }) + "\n");
    }
  }

  rl.close();
}

const planArgsSchema = z.object({
  query: z.string(),
  schema: z.string().optional(),
});
async function plan(argsRaw: unknown) {
  const args = planArgsSchema.parse(argsRaw);

  const parsed = parseQuery(args.query);

  let schema: LogicalSchema = DEFAULT_SCHEMA;
  if (args.schema) {
    schema = parseLogicalSchema(JSON.parse(readFileSync(args.schema, "utf-8")));
  }

  const bound = bindQuery({ query: parsed, schema, queryText: args.query });

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const physicalSchema = {
    type: "duckdb",
    tables: {
      foo: {
        path: ["foo"],
        columns: {
          id: { path: ["id"], type: { type: "bigint" } },
          name: { path: ["name"], type: { type: "varchar" } },
        },
      },
    },
  } as PhysicalSchema;
  const planned = planQuery(physicalSchema, bound);
  console.log("PLANNED", JSON.stringify(planned, null, 2));
}

async function main() {
  const parser = new ArgumentParser({
    description: "Braintrust query language",
  });

  parser.add_argument("-v", "--version", { action: "version", version });

  const parentParser = new ArgumentParser({ add_help: false });
  parentParser.add_argument("--verbose", {
    action: "store_true",
    help: "Include additional details, including full stack traces on errors.",
  });

  const subparser = parser.add_subparsers({
    required: true,
  });

  const parser_parse = subparser.add_parser("parse", {
    help: "Parse a query",
    parents: [parentParser],
  });
  parser_parse.add_argument("query", {
    help: "The query to parse",
  });
  parser_parse.set_defaults({ func: parse });

  const parser_bind = subparser.add_parser("bind", {
    help: "Bind a query",
    parents: [parentParser],
  });
  parser_bind.add_argument("query", {
    help: "The query to bind",
  });
  parser_bind.add_argument("--schema", {
    help: "A file containing the schema to bind against",
  });
  parser_bind.add_argument("--skip-field-casts", {
    action: "store_true",
    help: "Don't apply casts to field expressions",
  });
  parser_bind.set_defaults({ func: bind });

  const parser_bind_repl = subparser.add_parser("bind-repl", {
    help: "Bind queries interactively",
    parents: [parentParser],
  });
  parser_bind_repl.add_argument("--schema", {
    help: "A file containing the schema to bind against",
  });
  parser_bind_repl.add_argument("--skip-field-casts", {
    action: "store_true",
    help: "Don't apply casts to field expressions",
  });
  parser_bind_repl.set_defaults({ func: bind_repl });

  const parser_plan = subparser.add_parser("plan", {
    help: "Plan a query",
    parents: [parentParser],
  });
  parser_plan.add_argument("query", {
    help: "The query to plan",
  });
  parser_plan.add_argument("--schema", {
    help: "A file containing the logical schema to plan against",
  });
  parser_plan.set_defaults({ func: plan });

  const parser_json_schema = subparser.add_parser("json-schema", {
    help: "Generate JSON schema for the parser and binder AST",
  });
  parser_json_schema.add_argument("--out", {
    help: "The directory to write the JSON schemas to",
    default: "./json-schemas",
  });
  parser_json_schema.set_defaults({ func: jsonSchema });

  const parsed = parser.parse_args();

  try {
    await parsed.func(parsed);
  } catch (e) {
    logError(e, parsed.verbose);
    process.exit(1);
  }
}

main();
