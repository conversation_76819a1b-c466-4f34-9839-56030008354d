{"cells": [{"cell_type": "markdown", "metadata": {"id": "vigvBmpNxHvb"}, "source": ["# Function Call Evals (OpenAI | Together)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set your API key here\n", "%env BRAINTRUST_API_KEY="]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5AFngU_sw1K5"}, "outputs": [], "source": ["import dataclasses\n", "import datetime\n", "import os\n", "import json\n", "import time\n", "from pprint import pprint\n", "from typing import Any, Dict, List, Optional\n", "\n", "import chevron\n", "import openai\n", "import braintrust\n", "from braintrust_core.score import Score, Scorer\n", "from braintrust_core.util import SerializableDataClass\n", "from Levenshtein import distance\n", "from pydantic import BaseModel\n", "\n", "from eval import Example, SearchOutput, render_example, score_clause\n", "from prompt import build_project_search_completion_kwargs\n", "\n", "\n", "client_opts = dict(\n", "    base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    api_key=os.environ[\"BRAINTRUST_API_KEY\"],\n", ")\n", "client = braintrust.wrap_openai(openai.AsyncOpenAI(default_headers={\"x-bt-use-cache\": \"always\"}, **client_opts))\n", "\n", "PROJECT_NAME = \"AI Search Evals\"\n", "dataset = braintrust.init_dataset(PROJECT_NAME, \"Project Search Examples\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the data and render the templates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXAMPLES_FILE = \"examples.json\"\n", "PROMPT_FILE = \"project.tmpl\"\n", "LONG_PROMPT_FILE = \"project_long.tmpl\"\n", "\n", "with open(EXAMPLES_FILE) as f:\n", "    examples_json = json.load(f)\n", "with open(PROMPT_FILE) as f:\n", "    prompt = f.read()\n", "with open(LONG_PROMPT_FILE) as f:\n", "    long_prompt = f.read()\n", "\n", "ai_schema_columns = [\n", "    'avg_avg_relevance_score',\n", "    'avg_max_relevance_score',\n", "    'avg_min_relevance_score',\n", "    'avg_factuality_score',\n", "]\n", "example_render_args = dict(aiSchemaColumns=ai_schema_columns)\n", "example_templates = [Example(input=e[\"input\"], expected=SearchOutput(**e[\"expected\"])) for e in examples_json[\"examples\"]]\n", "examples = [render_example(e, example_render_args) for e in example_templates]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create a Braintrust dataset from the examples and prepare the data for evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, e in enumerate(examples):\n", "    dataset.insert(\n", "        input=e.input,\n", "        output=e.expected.as_dict(),\n", "    )\n", "dataset.flush()\n", "    \n", "data = []\n", "for i, row in enumerate(dataset):\n", "    data.append(\n", "        {\n", "            \"input\": row[\"input\"],\n", "            \"expected\": row[\"output\"],\n", "            \"metadata\": {\n", "                \"dataset_record_id\": row[\"id\"],\n", "            },\n", "        }\n", "    )\n", "print(f\"Generated {len(data)} examples. Here are the first 2...\")\n", "for x in data[:2]:\n", "    pprint(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up the OpenAI and Together eval tasks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _extract_output(completion):\n", "    try:\n", "        tool_calls = completion.choices[0].message.tool_calls\n", "        if tool_calls is None or len(tool_calls) != 1:\n", "            raise Exception(\"No tool call: \", completion.choices[0].message)\n", "        tool_call = tool_calls[0]\n", "        if tool_call.type != \"function\":\n", "            raise Exception(\"Tool call is not a function\")\n", "        function_name = tool_call.function.name\n", "        arguments = json.loads(tool_call.function.arguments)\n", "        output = SearchOutput(match=function_name == \"MATCH\", **arguments)\n", "    except Exception as e:\n", "        output = SearchOutput(error=str(e))\n", "    return output.as_dict()\n", "\n", "\n", "async def together_task(input):\n", "    completion_kwargs = build_project_search_completion_kwargs(\n", "        query=input,\n", "        model=\"mistralai/Mistral-7B-Instruct-v0.1\",\n", "        prompt=prompt,\n", "        ai_schema_columns=ai_schema_columns,\n", "    )\n", "    completion = await client.chat.completions.create(**completion_kwargs)\n", "    output = _extract_output(completion)\n", "    return output\n", "\n", "\n", "async def openai_task(input):\n", "    completion_kwargs = build_project_search_completion_kwargs(\n", "        query=input,\n", "        model=\"gpt-3.5-turbo\",\n", "        prompt=prompt,\n", "        ai_schema_columns=ai_schema_columns,\n", "    )\n", "    completion = await client.chat.completions.create(**completion_kwargs)\n", "    output = _extract_output(completion)\n", "    return output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up our scoring functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import duckdb\n", "from eval import AutoSearch, SimpleSearch, _single_quote\n", "\n", "\n", "duckdb.sql(\"DROP TABLE IF EXISTS experiments; CREATE TABLE experiments AS SELECT * FROM 'experiments.parquet'\")\n", "duckdb.sql(\"DROP TABLE IF EXISTS experiments_summary; CREATE TABLE experiments_summary AS SELECT * FROM 'experiments_summary.parquet'\")\n", "\n", "def test_clause(*, filter=None, sort=None):\n", "    return f\"\"\"\n", "        SELECT\n", "          experiments.id AS id,\n", "          experiments.name,\n", "          experiments_summary.last_updated,\n", "          experiments.user AS creator,\n", "          experiments.repo_info AS source,\n", "          experiments_summary.* EXCLUDE (experiment_id, last_updated),\n", "        FROM experiments\n", "        LEFT JOIN experiments_summary ON experiments.id = experiments_summary.experiment_id\n", "        WHERE ({filter or 'true'})\n", "        ORDER BY {sort or 'experiments_summary.last_updated DESC NULLS LAST'}\n", "    \"\"\"\n", "\n", "scorers = [\n", "    # SimpleSearch runs each filter/sort clause against the actual data\n", "    # to check the SQL is valid, and runs a \"dumb\" distance-wise text\n", "    # comparison to the expected output.\n", "    SimpleSearch(\n", "        test_filter=lambda filter: test_clause(filter=filter),\n", "        test_sort=lambda sort: test_clause(sort=sort),\n", "    ),\n", "    # AutoSearch uses the `Sql` scorer from the autoevals library to\n", "    # auto-score each filter/sort clause against the expected output\n", "    # using an LLM.\n", "    AutoSearch(client_opts),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the evals!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await braintrust.<PERSON>(\n", "    name=PROJECT_NAME,\n", "    experiment_name=\"OpenAI Eval\",\n", "    data=data,\n", "    task=openai_task,\n", "    scores=scorers,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["await braintrust.<PERSON>(\n", "    name=PROJECT_NAME,\n", "    experiment_name=\"Together Eval\",\n", "    data=data,\n", "    task=together_task,\n", "    scores=scorers,\n", ")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}