"use client";

import { useCallback } from "react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";

const copyCache = new Map<string, string>();
export function LLMCopyButton({ path }: { path: string }) {
  const getTextToCopy = useCallback(async () => {
    const url = `/docs/${path}`;
    const content: string =
      copyCache.get(url) ?? (await fetch(url).then((res) => res.text()));
    copyCache.set(url, content);
    return content;
  }, [path]);

  return (
    <CopyToClipboardButton
      className="text-primary-600"
      variant="ghost"
      textToCopy=""
      getTextToCopy={getTextToCopy}
      prependIcon
    >
      Copy page
    </CopyToClipboardButton>
  );
}
