import { But<PERSON> } from "#/ui/button";
import { Spinner } from "#/ui/icons/spinner";
import { useState, useEffect, useCallback, useRef } from "react";
import { useSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";
import { RefreshCw, Trash2, Play, Pause } from "lucide-react";
import { apiPost } from "#/utils/btapi/fetch";
import { TruncatedText } from "#/ui/truncated-text";
import Link from "next/link";
import { MultiTenantApiURL } from "#/utils/user-types";

interface BillingStatusResponse {
  enabled?: boolean;
  configs?: Record<
    string,
    {
      url: string;
      secret: string;
      source: string;
    }
  >;
  aggregation?: AggregationStatusResponse;
}

interface AggregationStatusResponse {
  state: {
    now: number;
    currentWindow: number;
    sleepIntervalSeconds: number;
    windowSizeMinutes: number;
    dataExpiresMinutes: number;
    cacheVersion: number;
  };
  pending: {
    windows: number;
    events: number;
  };
  orgs: Record<string, { events: number }>;
}

// This component is used for debugging purposes. You need to enable the endpoints in the data plane.
// See app.py and api.ts.
export function BillingStatus({
  orgId,
  apiUrl,
  refreshOrgRows,
}: {
  orgId: string;
  apiUrl: string;
  refreshOrgRows: () => Promise<void>;
}) {
  const [isResetting, setIsResetting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<BillingStatusResponse | undefined>();

  // Auto-refresh state
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [countdown, setCountdown] = useState(5);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [overrideRestriction, setOverrideRestriction] = useState(false);

  const { getOrRefreshToken } = useSessionToken();

  const isBraintrustHosted = apiUrl === MultiTenantApiURL;
  const shouldEnableBilling = isBraintrustHosted || overrideRestriction;

  const fetchBillingStatus = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const sessionToken = await getOrRefreshToken();
      const response = await apiPost({
        url: `${apiUrl}/billing/status`,
        sessionToken,
        payload: {
          orgIds: [orgId],
        },
        alreadySerialized: false,
      });

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
            if (errorData.message) {
              errorMessage += `: ${errorData.message}`;
            }
          }
        } catch {
          // If JSON parsing fails, use the default HTTP error message
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setData(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  }, [apiUrl, getOrRefreshToken, orgId]);

  useEffect(() => {
    if (shouldEnableBilling) {
      fetchBillingStatus();
    }
  }, [fetchBillingStatus, shouldEnableBilling]);

  // Auto-refresh effect
  useEffect(() => {
    if (shouldEnableBilling && autoRefreshEnabled && !error) {
      intervalRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            fetchBillingStatus();
            return 5;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    } else {
      setCountdown(5);
    }
  }, [autoRefreshEnabled, fetchBillingStatus, shouldEnableBilling, error]);

  const handleReset = async () => {
    setIsResetting(true);
    try {
      const sessionToken = await getOrRefreshToken();
      await apiPost({
        url: `${apiUrl}/billing/refresh`,
        sessionToken,
        payload: {
          orgIds: [orgId],
        },
        alreadySerialized: false,
      });

      await refreshOrgRows();
      toast.success("Billing configuration reset");
      setData({});
    } catch (error) {
      toast.error("Failed to reset billing configuration");
      console.error("Error resetting billing configuration:", error);
    } finally {
      setIsResetting(false);
    }
  };

  if (error) {
    return (
      <>
        <div className="mb-3 text-xs font-medium text-primary-500">
          Billing Status
        </div>
        <div className="rounded-lg border p-4 bg-red-50 border-red-200">
          <div className="flex items-start justify-between">
            <div className="flex">
              <div className="shrink-0">
                <svg
                  className="size-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Failed to load billing status
                </h3>
                <div className="mt-1 text-sm text-red-700">{error.message}</div>
              </div>
            </div>
            <Button
              size="xs"
              variant="ghost"
              onClick={() => {
                setError(null);
                fetchBillingStatus();
              }}
              className="text-red-600 hover:text-red-800"
              title="Retry"
            >
              <RefreshCw className="size-3" />
            </Button>
          </div>
        </div>
      </>
    );
  }

  const orgBillingStatus = data?.configs?.[orgId];

  if (!shouldEnableBilling) {
    return (
      <>
        <div className="mb-3 text-xs font-medium text-primary-500">
          Billing Status
        </div>
        <div className="rounded-lg border p-3 bg-gray-50 border-gray-200">
          <div className="flex items-center justify-between gap-2">
            <div className="text-xs text-gray-600">
              Only available for Braintrust-hosted orgs
            </div>
            <Button
              size="xs"
              onClick={() => {
                setOverrideRestriction(true);
                setError(null);
              }}
              className="text-xs"
            >
              Try anyway
            </Button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="text-xs font-medium text-primary-500">
            Data Plane Status
            {!isBraintrustHosted && (
              <span className="ml-2 text-xs text-orange-600">
                (Override active)
              </span>
            )}
          </div>
          {shouldEnableBilling && autoRefreshEnabled && !error && (
            <span className="text-xs text-primary-500">
              • Next refresh in {countdown}s
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="xs"
            variant="ghost"
            onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
            className="shrink-0 gap-1"
            disabled={!shouldEnableBilling}
            title={
              autoRefreshEnabled ? "Pause auto-refresh" : "Start auto-refresh"
            }
          >
            {autoRefreshEnabled ? (
              <Pause className="size-3" />
            ) : (
              <Play className="size-3" />
            )}
          </Button>
          <Button
            size="xs"
            variant="ghost"
            onClick={fetchBillingStatus}
            disabled={isLoading || !shouldEnableBilling}
            className="shrink-0 gap-1"
            title="Refresh now"
          >
            {isLoading ? (
              <Spinner className="size-3" />
            ) : (
              <RefreshCw className="size-3" />
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-3 rounded border p-3">
        <div className="flex items-center justify-between">
          <div className="text-xs text-primary-500">Configured Billing</div>
          <Button
            size="xs"
            variant="ghost"
            onClick={handleReset}
            disabled={isResetting}
            className="shrink-0 gap-1"
            title="Reset billing configuration"
          >
            {isResetting ? (
              <Spinner className="size-3" />
            ) : (
              <Trash2 className="size-3" />
            )}
          </Button>
        </div>

        <div className="space-y-2 text-xs">
          <div className="flex items-start justify-between gap-4">
            <span className="shrink-0 text-primary-400">Enabled?</span>
            <span className="text-right">
              {data?.enabled !== undefined ? (data.enabled ? "Yes" : "No") : ""}
            </span>
          </div>
          <div className="flex items-start justify-between gap-4">
            <span className="shrink-0 text-primary-400">Endpoint</span>
            <TruncatedText className="max-w-[240px] text-right hover:text-primary-600">
              {orgBillingStatus?.url}
            </TruncatedText>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Redacted Secret</span>
            <span className="text-right">{orgBillingStatus?.secret}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Source</span>
            <span className="text-right">{orgBillingStatus?.source}</span>
          </div>
        </div>
      </div>

      <div className="mt-3 flex flex-col gap-3 rounded border p-3">
        <div className="flex items-center justify-between">
          <div className="text-xs text-primary-500">Aggregation Status</div>
        </div>

        <div className="space-y-2 text-xs">
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Org{"'"}s Events</span>
            <span className="text-right">
              {data?.aggregation?.orgs[orgId]?.events ?? 0}
            </span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="mt-4  shrink-0 text-primary-400">
              All Pending (see{" "}
              <Link
                href="https://tinyurl.com/3hze3vpj"
                target="_blank"
                className="hover:underline"
              >
                Dashboard
              </Link>
              )
            </span>
            <span className="text-right"></span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Windows</span>
            <span className="text-right">
              {data?.aggregation?.pending.windows ?? "—"}
            </span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Events</span>
            <span className="text-right">
              {data?.aggregation?.pending.events ?? "—"}
            </span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="mt-4  shrink-0 text-primary-400">
              Configuration
            </span>
            <span className="text-right"></span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Current Window</span>
            <span className="text-right">
              {data?.aggregation?.state.currentWindow ?? "—"}
            </span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">Window Size (min)</span>
            <span className="text-right">
              {data?.aggregation?.state.windowSizeMinutes ?? "—"}
            </span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="shrink-0 text-primary-400">
              Sleep Interval (sec)
            </span>
            <span className="text-right">
              {(data?.aggregation?.state.sleepIntervalSeconds ?? 0) / 1000 ||
                "—"}
            </span>
          </div>
        </div>
      </div>
    </>
  );
}
