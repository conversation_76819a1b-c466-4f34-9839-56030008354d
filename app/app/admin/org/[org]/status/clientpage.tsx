"use client";

import { useMemo, useRef } from "react";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useGetRequest } from "#/utils/btapi/get";
import { brainstoreSystemStatus } from "@braintrust/local/app-schema";
import { zodErrorToString } from "#/utils/validation";
import { Button } from "#/ui/button";
import { RefreshCw } from "lucide-react";
import { relativeTimeMs } from "#/ui/date";
import prettyBytes from "pretty-bytes";

export function ClientPage({ apiUrl }: { apiUrl: string }) {
  const isClient = useIsClient();

  const {
    data: systemStatus,
    error: requestError,
    refresh: refreshStatus,
  } = useGetRequest(
    `/brainstore/status`,
    useMemo(() => ({}), []),
    {
      apiUrl,
    },
  );

  const [parsedData, parseError] = useMemo(() => {
    if (!systemStatus) {
      return [null, null];
    }

    const status = brainstoreSystemStatus.safeParse(systemStatus);
    if (status.success) {
      return [status.data, null];
    } else {
      return [null, zodErrorToString(status.error, 2, true)];
    }
  }, [systemStatus]);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const error = requestError ?? parseError;

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <div ref={scrollContainerRef} className="relative flex-1 overflow-auto">
      <div className="flex flex-row gap-8 p-5">
        <Button size="xs" className="flex-none" onClick={() => refreshStatus()}>
          <RefreshCw className="size-3" />
          Refresh
        </Button>
      </div>
      {error && (
        <div className="mb-2 flex flex-none items-center gap-1.5 rounded-md border p-2 font-mono text-xs font-semibold bg-rose-500/5 border-rose-500/10 text-bad-700">
          {`${error}`}
        </div>
      )}
      {parsedData && (
        <div className="p-5">
          <div className="mb-2 text-lg font-semibold">Brainstore status</div>
          <p>Overall status: {parsedData.status}</p>
          <p>Uptime: {relativeTimeMs(parsedData.uptime * 1000)}</p>
          <div className="mb-2 mt-4 text-lg font-semibold">Services</div>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(parsedData.services).map(([service, status]) => (
              <div key={service}>
                <p className="font-semibold">{service}</p>
                <p>{status.success ? "OK" : "ERROR"}</p>
                <p>{status.message}</p>
                <p>{status.error}</p>
              </div>
            ))}
          </div>
          {parsedData.system && (
            <div className="mt-4">
              {parsedData.system.memory && (
                <>
                  <div className="mb-2 mt-4 text-lg font-semibold">Memory</div>
                  <div>
                    {parsedData.system?.memory?.proc && (
                      <p>
                        Brainstore memory:{" "}
                        {prettyBytes(parsedData.system?.memory?.proc)}
                      </p>
                    )}
                    <p>
                      Used memory:{" "}
                      {prettyBytes(parsedData.system?.memory?.used)}
                    </p>
                    <p>
                      Total memory:{" "}
                      {prettyBytes(parsedData.system?.memory?.total)}
                    </p>
                  </div>
                </>
              )}
              {parsedData.system.disk && (
                <>
                  <div className="mb-2 mt-4 text-lg font-semibold">Disk</div>
                  <div>
                    {parsedData.system?.disk?.free && (
                      <p>
                        Disk free: {prettyBytes(parsedData.system?.disk?.free)}
                      </p>
                    )}
                    {parsedData.system?.disk?.total && (
                      <p>
                        Disk total:{" "}
                        {prettyBytes(parsedData.system?.disk?.total)}
                      </p>
                    )}
                  </div>
                </>
              )}
              {parsedData.system.process && (
                <>
                  <div className="mb-2 mt-4 text-lg font-semibold">Process</div>
                  <div>
                    <p>Active threads: {parsedData.system?.process?.threads}</p>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
