"use client";

import { useMemo, useRef, useEffect, useCallback } from "react";
import TextEditor, { type TextEditorHandle } from "#/ui/text-editor";
import {
  btqlSupport,
  type SearchableItemInfo,
  type BtqlMode,
} from "#/utils/codemirror/btql-lang";
import { githubLightInit, githubDarkInit } from "@uiw/codemirror-theme-github";
import { useDarkMode } from "#/utils/useDarkMode";
import { type ProjectSummary } from "../org-actions"; // Assuming path to ProjectSummary
import { type DataObjectType } from "#/utils/btapi/btapi";
import { sql } from "@codemirror/lang-sql";

interface BtqlEditorProps {
  mode: BtqlMode;
  value: string;
  onValueChange?: (value: string) => void;
  onDebouncedSave?: (value: string) => void;
  onMetaEnter: () => void;
  projectData: ProjectSummary[] | undefined; // Accept ProjectSummary[] from parent
  className?: string;
  textEditorRef?: React.RefObject<TextEditorHandle | null>;
  placeholder?: string;
  autoFocus?: boolean;
}

const editorThemeSettings = {
  settings: { background: "transparent" },
};

export function BtqlEditor({
  value,
  onValueChange,
  onDebouncedSave,
  onMetaEnter,
  projectData,
  className,
  textEditorRef,
  autoFocus,
  mode,
  placeholder = "Enter BTQL query",
}: BtqlEditorProps) {
  const darkMode = useDarkMode();

  // Ref for projects converted to SearchableItemInfo (for project_logs)
  const projectInfoForSearchRef = useRef<SearchableItemInfo[]>([]);
  useEffect(() => {
    projectInfoForSearchRef.current =
      projectData?.map((p) => ({
        id: p.project_id,
        name: p.project_name,
        type: "project",
        // description: p.description, // If available in ProjectSummary
      })) || [];
  }, [projectData]);

  const searchItemsAsyncCallback = useCallback(
    async ({
      objectType,
      currentPrefix,
      projectScope,
    }: {
      objectType: DataObjectType;
      currentPrefix: string;
      projectScope?: string; // TODO: Remove
    }): Promise<SearchableItemInfo[]> => {
      const lowerPrefix = currentPrefix.toLowerCase();

      if (objectType === "project_logs") {
        return projectInfoForSearchRef.current.filter(
          (proj) =>
            proj.name.toLowerCase().includes(lowerPrefix) ||
            proj.id.toLowerCase().includes(lowerPrefix),
        );
      } else {
        return [];
      }
    },
    [], // projectInfoForSearchRef is a ref, its .current is accessed inside, so it's not a dependency for useCallback itself.
    // If projectData was directly used here, it would be a dependency.
  );

  const extensions = useMemo(() => {
    return [
      darkMode
        ? githubDarkInit(editorThemeSettings)
        : githubLightInit(editorThemeSettings),
      sql(),
      // eslint-disable-next-line react-compiler/react-compiler
      btqlSupport(mode, searchItemsAsyncCallback), // Pass the async callback
    ];
  }, [darkMode, searchItemsAsyncCallback, mode]);

  return (
    <>
      <TextEditor
        className={className}
        onSave={(newValue: string | undefined) => {
          onDebouncedSave?.(newValue ?? "");
        }}
        onChange={(newValue: string | undefined) => {
          onValueChange?.(newValue ?? "");
        }}
        placeholder={placeholder}
        autoFocus={autoFocus}
        value={value}
        onMetaEnter={onMetaEnter}
        extensions={extensions}
        ref={textEditorRef}
        wrap
        styled
      />
    </>
  );
}
