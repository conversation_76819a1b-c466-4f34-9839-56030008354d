import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import { getProjectSummary } from "../org-actions";
import ClientPage, { type Params } from "./clientpage";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const projectSummary = await getProjectSummary({ org_name });
  return <ClientPage projectSummary={projectSummary} params={params} />;
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "BTQL sandbox",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/${params.org}`,
  });
}
