"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  experimentSchema,
  projectSchema,
  userSchema,
} from "@braintrust/core/typespecs";

const projectSummarySchema = z.object({
  project_name: projectSchema.shape.name,
  project_id: projectSchema.shape.id,
  project_created_at: projectSchema.shape.created,
  num_experiments: z.number(),
  num_playgrounds: z.number(),
  num_datasets: z.number(),
  project_created_by: userSchema.shape.id.nullish(),
  created_by_name: z.string().nullish(),
  created_by_email: userSchema.shape.email.nullish(),
  created_by_avatar_url: userSchema.shape.avatar_url.nullish(),
});
export type ProjectSummary = z.infer<typeof projectSummarySchema>;

export type GetProjectSummaryInputs = {
  org_name?: string;
};

export async function getProjectSummary(
  { org_name }: GetProjectSummaryInputs,
  authLookupRaw?: AuthLookup,
): Promise<ProjectSummary[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!org_name) {
    return [];
  }

  const { query: allProjectsQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "project",
      aclPermission: "read",
    },
    filters: {
      org_name,
    },
  });

  const fullQuery = `
  with
  org_projects as (
    ${allProjectsQuery}
  ),
  num_experiments_per_project as (
    select project_id, count(*) count
    from
        experiments
        join org_projects on experiments.project_id = org_projects.id
    where experiments.deleted_at isnull
    group by project_id
  ),
  num_playgrounds_per_project as (
    select project_id, count(*) count
    from
        prompt_sessions
        join org_projects on prompt_sessions.project_id = org_projects.id
    where prompt_sessions.deleted_at isnull
    group by project_id
  ),
  num_datasets_per_project as (
    select project_id, count(*) count
    from
        datasets
        join org_projects on datasets.project_id = org_projects.id
    where datasets.deleted_at isnull
    group by project_id
  )
  select
    org_projects.name project_name,
    org_projects.id project_id,
    org_projects.created project_created_at,
    -- Cast these to integers so that they get parsed out as javascript numbers.
    cast(coalesce(num_experiments_per_project.count, 0) as integer) num_experiments,
    cast(coalesce(num_playgrounds_per_project.count, 0) as integer) num_playgrounds,
    cast(coalesce(num_datasets_per_project.count, 0) as integer) num_datasets,
    org_projects.user_id project_created_by,
    CONCAT_WS(' ', users.given_name, users.family_name) created_by_name,
    users.email created_by_email,
    users.avatar_url created_by_avatar_url
  from
    org_projects
        left join num_experiments_per_project on org_projects.id = num_experiments_per_project.project_id
        left join num_playgrounds_per_project on org_projects.id = num_playgrounds_per_project.project_id
        left join num_datasets_per_project on org_projects.id = num_datasets_per_project.project_id
        left join users on (org_projects.user_id = users.id)
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);
  return projectSummarySchema.array().parse(rows ?? []);
}

const getOnboardingExperimentInfoSchema = z.object({
  project_name: projectSchema.shape.name,
  experiment_name: experimentSchema.shape.name,
  // RBAC_DISCLAIMER: The user may not technically be able to read other
  // experiments, but the onboarding page needs a way to know if other
  // experiments exist in the org, and it seems like relatively harmless
  // information to leak.
  exist_other_experiments_in_org: z.boolean(),
});

export type GetOnboardingExperimentInfo = z.infer<
  typeof getOnboardingExperimentInfoSchema
>;

export async function getOnboardingExperimentInfo(
  {
    experimentId,
  }: {
    experimentId: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<GetOnboardingExperimentInfo | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "experiment",
      aclPermission: "read",
    },
    filters: {
      id: [experimentId],
    },
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
      with
      experiment_info as (
        select
            experiments.id experiment_id,
            projects.org_id org_id,
            projects.name project_name,
            experiments.name experiment_name
        from
            (${fullResultSetQuery}) experiments
            join projects on experiments.project_id = projects.id
      )
      select
          experiment_info.project_name,
          experiment_info.experiment_name,
          exists(
              select 1
              from
                  experiments
                  join projects on experiments.project_id = projects.id
              where
                  experiments.id <> experiment_info.experiment_id
                  and projects.org_id = experiment_info.org_id
                  and experiments.deleted_at isnull
                  and projects.deleted_at isnull
          ) exist_other_experiments_in_org
      from experiment_info
  `,
    queryParams.params,
  );
  if (rows?.length !== 1) {
    return null;
  }
  return getOnboardingExperimentInfoSchema.parse(rows[0]);
}
