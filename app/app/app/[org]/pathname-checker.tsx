import { usePathname } from "next/navigation";
import { match } from "path-to-regexp";
import { useMemo } from "react";

// detail
export const isExperimentPage = match(
  "/app/:org/p/:project/experiments/:experiment",
);
export const isPlaygroundPage = match(
  "/app/:org/p/:project/playgrounds/:playground",
);
export const isDatasetPage = match("/app/:org/p/:project/datasets/:dataset");

// list
export const isProjectOverviewPage = match("/app/:org/p/:project");
export const isExperimentsPage = match("/app/:org/p/:project/experiments");
export const isPlaygroundsPage = match("/app/:org/p/:project/playgrounds");
export const isDatasetsPage = match("/app/:org/p/:project/datasets");
export const isPromptsPage = match("/app/:org/p/:project/prompts");
export const isToolsPage = match("/app/:org/p/:project/tools");
export const isScorersPage = match("/app/:org/p/:project/scorers");
export const isAgentsPage = match("/app/:org/p/:project/agents");
export const isLogsPage = match("/app/:org/p/:project/logs");
export const isProjectConfigurationPage = match(
  "/app/:org/p/:project/configuration/:page",
);

export const isOrgPage = match("/app/:org");
export const isMonitorPage = match("/app/:org/monitor");
export const isProjectPage = match("/app/:org/p/:project/:page");
export const isSettingsPage = match("/app/:org/settings/:page");
export const isBTQLSandboxPage = match("/app/:org/btql");

export const useActivePage = () => {
  const pathname = usePathname();

  const activePage = useMemo(() => {
    if (!pathname) return null;
    if (isExperimentsPage(pathname)) {
      return "experiments";
    }
    if (isPlaygroundsPage(pathname)) {
      return "playgrounds";
    }
    if (isDatasetsPage(pathname)) {
      return "datasets";
    }
    if (isPromptsPage(pathname)) {
      return "prompts";
    }
    if (isToolsPage(pathname)) {
      return "tools";
    }
    if (isScorersPage(pathname)) {
      return "scorers";
    }
    if (isAgentsPage(pathname)) {
      return "agents";
    }
    if (isLogsPage(pathname)) {
      return "logs";
    }
    if (isProjectConfigurationPage(pathname)) {
      return "configuration";
    }
    if (isProjectOverviewPage(pathname)) {
      return "project";
    }
  }, [pathname]);

  return activePage;
};

export const useIsAdmin = () => {
  const pathname = usePathname();
  return pathname && pathname.startsWith("/admin");
};
