"use server";

import { SqlQueryParams } from "#/utils/sql-query-params";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { type Organization } from "@braintrust/core/typespecs";
import { resourcesSchema } from "#/utils/user-types";

// RBAC_DISCLAIMER: This function should only be called after successfully
// charging a credit card. We assume that users that have gotten that far are
// trusted to update resource limits.
export async function updateResourceLimitsIfHigher({
  orgId,
  resourceLimits,
}: {
  orgId: Organization["id"];
  resourceLimits: {
    datasets?: number;
    experiments?: number;
    logs?: number;
    logsBytes?: number;
  };
}) {
  if (!orgId) {
    return undefined;
  }

  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);
  const experimentsLimitParam = queryParams.add(resourceLimits.experiments);
  const logsLimitParam = queryParams.add(resourceLimits.logs);
  const logsBytesLimitParam = queryParams.add(resourceLimits.logsBytes);
  const datasetsLimitParam = queryParams.add(resourceLimits.datasets);

  const supabase = getServiceRoleSupabase();

  await supabase.query(
    `
    WITH org_check AS (
      SELECT id FROM organizations WHERE id = ${orgIdParam}
    )
    INSERT INTO resources (
      org_id,
      num_private_experiment_row_actions,
      num_private_experiment_row_actions_calendar_months,
      num_production_log_row_actions,
      num_production_log_row_actions_calendar_months,
      num_dataset_row_actions,
      num_dataset_row_actions_calendar_months,
      num_log_bytes,
      num_log_bytes_calendar_months
    )
    SELECT
      ${orgIdParam},
      CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${experimentsLimitParam}/ 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${experimentsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsLimitParam}/ 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${datasetsLimitParam}/ 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${datasetsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsBytesLimitParam}/ 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsBytesLimitParam})::max_over_calendar_months_underlying
      END
    FROM org_check
    ON CONFLICT (org_id) DO UPDATE
    SET
      num_private_experiment_row_actions = CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_private_experiment_row_actions IS NULL THEN resources.num_private_experiment_row_actions
        ELSE ROW(7, GREATEST(${experimentsLimitParam} / 4, (resources.num_private_experiment_row_actions).max_value))::max_over_window_underlying
      END,
      num_private_experiment_row_actions_calendar_months = CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_private_experiment_row_actions_calendar_months IS NULL THEN resources.num_private_experiment_row_actions_calendar_months
        ELSE ROW(1, GREATEST(${experimentsLimitParam}, (resources.num_private_experiment_row_actions_calendar_months).max_value))::max_over_calendar_months_underlying
      END,
      num_production_log_row_actions = CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_production_log_row_actions IS NULL THEN resources.num_production_log_row_actions
        ELSE ROW(7, GREATEST(${logsLimitParam} / 4, (resources.num_production_log_row_actions).max_value))::max_over_window_underlying
      END,
      num_production_log_row_actions_calendar_months = CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_production_log_row_actions_calendar_months IS NULL THEN resources.num_production_log_row_actions_calendar_months
        ELSE ROW(1, GREATEST(${logsLimitParam}, (resources.num_production_log_row_actions_calendar_months).max_value))::max_over_calendar_months_underlying
      END,
      num_dataset_row_actions = CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_dataset_row_actions IS NULL THEN resources.num_dataset_row_actions
        ELSE ROW(7, GREATEST(${datasetsLimitParam} / 4, (resources.num_dataset_row_actions).max_value))::max_over_window_underlying
      END,
      num_dataset_row_actions_calendar_months = CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_dataset_row_actions_calendar_months IS NULL THEN resources.num_dataset_row_actions_calendar_months
        ELSE ROW(1, GREATEST(${datasetsLimitParam}, (resources.num_dataset_row_actions_calendar_months).max_value))::max_over_calendar_months_underlying
      END,
      num_log_bytes = CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_log_bytes IS NULL THEN resources.num_log_bytes
        ELSE ROW(7, GREATEST(${logsBytesLimitParam} / 4, (resources.num_log_bytes).max_value))::max_over_window_underlying
      END,
      num_log_bytes_calendar_months = CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        WHEN resources.num_log_bytes_calendar_months IS NULL THEN resources.num_log_bytes_calendar_months
        ELSE ROW(1, GREATEST(${logsBytesLimitParam}, (resources.num_log_bytes_calendar_months).max_value))::max_over_calendar_months_underlying
      END
  `,
    queryParams.params,
  );
}

// RBAC_DISCLAIMER: This function should only be called after successfully
// charging a credit card. We assume that users that have gotten that far are
// trusted to update resource limits.
export async function updateResourceLimits({
  orgId,
  resourceLimits,
}: {
  orgId: Organization["id"];
  resourceLimits: {
    datasets?: number;
    experiments?: number;
    logs?: number;
    logsBytes?: number;
  };
}) {
  if (!orgId) {
    return undefined;
  }

  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);
  const experimentsLimitParam = queryParams.add(resourceLimits.experiments);
  const logsLimitParam = queryParams.add(resourceLimits.logs);
  const logsBytesLimitParam = queryParams.add(resourceLimits.logsBytes);
  const datasetsLimitParam = queryParams.add(resourceLimits.datasets);

  const supabase = getServiceRoleSupabase();

  await supabase.query(
    `
    WITH org_check AS (
      SELECT id FROM organizations WHERE id = ${orgIdParam}
    )
    INSERT INTO resources (
      org_id,
      num_private_experiment_row_actions,
      num_private_experiment_row_actions_calendar_months,
      num_production_log_row_actions,
      num_production_log_row_actions_calendar_months,
      num_dataset_row_actions,
      num_dataset_row_actions_calendar_months,
      num_log_bytes,
      num_log_bytes_calendar_months
    )
    SELECT
      ${orgIdParam},
      CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${experimentsLimitParam} / 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${experimentsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsLimitParam} / 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${datasetsLimitParam} / 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${datasetsLimitParam})::max_over_calendar_months_underlying
      END,
      CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsBytesLimitParam} / 4)::max_over_window_underlying
      END,
      CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsBytesLimitParam})::max_over_calendar_months_underlying
      END
    FROM org_check
    ON CONFLICT (org_id) DO UPDATE
    SET
      num_private_experiment_row_actions = CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${experimentsLimitParam}/ 4)::max_over_window_underlying
      END,
      num_private_experiment_row_actions_calendar_months = CASE
        WHEN (${experimentsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${experimentsLimitParam})::max_over_calendar_months_underlying
      END,
      num_production_log_row_actions = CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsLimitParam}/ 4)::max_over_window_underlying
      END,
      num_production_log_row_actions_calendar_months = CASE
        WHEN (${logsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsLimitParam})::max_over_calendar_months_underlying
      END,
      num_dataset_row_actions = CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${datasetsLimitParam}/ 4)::max_over_window_underlying
      END,
      num_dataset_row_actions_calendar_months = CASE
        WHEN (${datasetsLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${datasetsLimitParam})::max_over_calendar_months_underlying
      END,
      num_log_bytes = CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(7, ${logsBytesLimitParam}/ 4)::max_over_window_underlying
      END,
      num_log_bytes_calendar_months = CASE
        WHEN (${logsBytesLimitParam}::bigint) IS NULL THEN NULL
        ELSE ROW(1, ${logsBytesLimitParam})::max_over_calendar_months_underlying
      END
  `,
    queryParams.params,
  );
}

// RBAC_DISCLAIMER: This function should only be called after successfully
// charging a credit card. We assume that users that have gotten that far are
// trusted to update their plan id.
export async function updatePlanId({
  orgId,
  planId,
}: {
  orgId: Organization["id"];
  planId: string;
}) {
  if (!orgId) {
    return undefined;
  }

  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);
  const planIdParam = queryParams.add(planId);

  const query = `
    WITH org_check AS (
      SELECT id FROM organizations WHERE id = ${orgIdParam}
    )
    INSERT INTO org_billing (org_id, plan_id)
    SELECT ${orgIdParam}, ${planIdParam}
    FROM org_check
    ON CONFLICT (org_id) DO UPDATE
      SET plan_id = EXCLUDED.plan_id
  `;

  const supabase = getServiceRoleSupabase();
  await supabase.query(query, queryParams.params);
}

// RBAC_DISCLAIMER: This function should only be called by the billing webhooks to fetch the resource limits for a given org.
export async function getResourceLimits({
  orgId,
}: {
  orgId: Organization["id"];
}) {
  const queryParams = new SqlQueryParams();
  const orgIdParam = queryParams.add(orgId);

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `SELECT to_jsonb(resources) AS resources FROM resources WHERE org_id = ${orgIdParam}`,
    queryParams.params,
  );

  if (rows.length === 0) {
    return null;
  }

  return resourcesSchema.parse(rows[0].resources);
}
