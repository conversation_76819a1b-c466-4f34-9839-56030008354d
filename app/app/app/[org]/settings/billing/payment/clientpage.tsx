"use client";

import { BillingForm } from "./billing-form";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, type Stripe } from "@stripe/stripe-js";
import { useSearchParams } from "next/navigation";
import { type Permission } from "@braintrust/core/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useDarkMode } from "#/utils/useDarkMode";
import { useMemo } from "react";

let stripePromise: Promise<Stripe | null> | null = null;
if (process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
}

function ClientPage({ orgPermissions }: { orgPermissions: Permission[] }) {
  const searchParams = useSearchParams();
  const purchasePlanSlug = searchParams?.get("purchasePlanSlug");

  const stripeTheme = useStripeTheme();

  if (!stripePromise) {
    return (
      <div>
        Stripe not initialized, set the NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
        environment variable
      </div>
    );
  }

  const isAllowedToPay = orgPermissions.includes("update");
  if (!isAllowedToPay) {
    return (
      <TableEmptyState label='To enter a payment method, ask your administrator to grant the "Manage settings" permission for this organization.' />
    );
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        mode: "setup",
        currency: "usd",
        setupFutureUsage: "off_session",
        paymentMethodTypes: ["card"],
        appearance: {
          theme: "stripe",
          variables: stripeTheme,
          rules: {
            ".Label": {
              marginBottom: "8px",
              marginTop: "8px",
            },
          },
        },
      }}
    >
      <BillingForm purchasePlanSlug={purchasePlanSlug ?? undefined} />
    </Elements>
  );
}

export interface Params {
  org: string;
}

export { ClientPage };

const useStripeTheme = () => {
  const isDarkMode = useDarkMode();

  const stripeTheme = useMemo(() => {
    // Get computed values from CSS variables
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    return {
      colorBackground: `rgb(${computedStyle.getPropertyValue("--primary-100").trim()})`,
      colorText: `rgb(${computedStyle.getPropertyValue("--primary-900").trim()})`,
      colorPrimary: `rgb(${computedStyle.getPropertyValue("--accent-500").trim()})`,
      colorDanger: `rgb(${computedStyle.getPropertyValue("--bad-700").trim()})`,
      fontSizeSm: computedStyle.getPropertyValue("--text-xs").trim(),
      fontFamily: "'Inter', sans-serif",
      spacingUnit: "3px",
    };
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDarkMode]);

  return stripeTheme;
};
