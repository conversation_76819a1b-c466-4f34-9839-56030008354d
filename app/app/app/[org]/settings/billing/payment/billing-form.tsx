"use client";

import * as Sentry from "@sentry/nextjs";
import { type addPaymentIntent } from "#/utils/billing/utils";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useOrg, useUser } from "#/utils/user";
import { useAuth } from "@clerk/nextjs";
import { PLAN_LABELS, getPlanId, isValidPlanSlug } from "../plans";

import {
  useStripe,
  useElements,
  PaymentElement,
  AddressElement,
} from "@stripe/react-stripe-js";
import { useState } from "react";
import { CouponModal } from "./coupon-modal";
import { Alert, AlertDescription, AlertTitle } from "#/ui/alert";
import { AlertCircle } from "lucide-react";

import { useSearchParams } from "next/navigation";

function PaymentErrorBanner({ errorMessage }: { errorMessage: string }) {
  return (
    <Alert className="mb-4" variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>
        There was a problem with your payment. Please try again
      </AlertTitle>
      <AlertDescription>{errorMessage}</AlertDescription>
    </Alert>
  );
}

function BillingForm({ purchasePlanSlug }: { purchasePlanSlug?: string }) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [addressLoaded, setAddressLoaded] = useState(false);
  const [paymentLoaded, setPaymentLoaded] = useState(false);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const errorMessage = searchParams?.get("errorMessage") ?? null;
  const hasError = errorMessage !== null;

  const allElementsLoaded = addressLoaded && paymentLoaded;

  const { name: orgName, id: orgId } = useOrg();

  const { user } = useUser();
  const { getToken } = useAuth();

  let purchasePlanLabel: string | undefined;
  let purchasePlanId: string | undefined;
  if (purchasePlanSlug && isValidPlanSlug(purchasePlanSlug)) {
    purchasePlanLabel = PLAN_LABELS[purchasePlanSlug];
    purchasePlanId = getPlanId(purchasePlanSlug);
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { error: submitError } = await elements.submit();

      if (submitError) {
        // If a clientside validation problem occurs, show the user the error
        // and don't bother pinging Sentry
        if (submitError.type === "validation_error") {
          setError(submitError.message ?? "An unexpected error occurred");
          return;
        }

        // Otherwise, we'll log the error to Sentry and show the user a generic error message
        throw new Error(submitError.message ?? "An unexpected error occurred");
      }

      const { clientSecret, orbCustomerId } = await invokeServerAction<
        typeof addPaymentIntent
      >({
        fName: "addPaymentIntent",
        args: {
          orgId,
          orgName,
          userEmail: user?.email ?? "",
          couponCode: appliedCoupon,
        },
        getToken,
      });

      if (!clientSecret) {
        throw new Error("Failed to create client secret");
      }

      const searchParams = new URLSearchParams({
        orbCustomerId,
        ...(purchasePlanId && { purchasePlanId }),
        ...(purchasePlanSlug && { purchasePlanSlug }),
        ...(appliedCoupon && { couponCode: appliedCoupon }),
      });

      const { error: confirmError } = await stripe.confirmSetup({
        elements,
        clientSecret,
        confirmParams: {
          return_url: `${window.location.origin}/app/${encodeURIComponent(orgName)}/settings/billing/success?${searchParams}`,
        },
      });
      if (confirmError) {
        throw new Error(confirmError.message ?? "Failed to confirm setup");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred",
      );

      // All serverside errors will ping the #billing-errors channel in Slack so that we can monitor them closely
      Sentry.captureException(err, {
        tags: {
          page: "billing-page",
        },
      });

      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const heading =
    purchasePlanId && purchasePlanLabel
      ? `Subscribe to ${purchasePlanLabel} plan`
      : "Enter updated payment details";

  return (
    <div className="w-full max-w-lg">
      <form onSubmit={handleSubmit}>
        <h2 className="mb-8 text-lg font-semibold">{heading}</h2>
        {hasError && <PaymentErrorBanner errorMessage={errorMessage} />}
        <div className="space-y-4">
          <div className="pb-4">
            <AddressElement
              options={{
                mode: "billing",
              }}
              onReady={() => setAddressLoaded(true)}
            />
          </div>
          <PaymentElement onReady={() => setPaymentLoaded(true)} />

          {error && <div className="mt-2 text-sm text-red-500">{error}</div>}

          {allElementsLoaded && (
            <div className="flex items-center justify-between">
              {!appliedCoupon && (
                <button
                  type="button"
                  onClick={() => setIsCouponModalOpen(true)}
                  className="text-sm text-accent-600 hover:text-accent-700"
                >
                  Enter coupon code
                </button>
              )}
              {appliedCoupon && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-primary-600">
                    Applied coupon:
                  </span>
                  <span className="rounded-md px-2 py-1 text-sm font-medium bg-accent-100 text-accent-700">
                    {appliedCoupon}
                  </span>
                  <button
                    type="button"
                    onClick={() => setAppliedCoupon(null)}
                    className="text-sm text-primary-500 hover:text-primary-700"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>
          )}

          <button
            type="submit"
            disabled={!stripe || !elements || isLoading}
            className={`mt-4 w-full rounded-md px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50 ${
              !allElementsLoaded ? "invisible" : ""
            }`}
          >
            {isLoading ? "Processing..." : "Submit"}
          </button>
        </div>
      </form>

      {isCouponModalOpen && (
        <CouponModal
          open={isCouponModalOpen}
          onClose={() => setIsCouponModalOpen(false)}
          onCouponApplied={(couponCode) => {
            setAppliedCoupon(couponCode);
            setIsCouponModalOpen(false);
          }}
        />
      )}
    </div>
  );
}

export { BillingForm };
