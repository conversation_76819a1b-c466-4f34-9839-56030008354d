import type Orb from "orb-billing";

import { ClientPage, type Params } from "./clientpage";
import { decodeURIComponentPatched } from "#/utils/url";
import { getOrganization } from "../../../actions";
import {
  getCurrentSubscription,
  getDefaultPaymentMethodSummary,
  getInvoices,
} from "#/utils/billing/utils";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { type Permission } from "@braintrust/core/typespecs";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let currentSubscription: Orb.Subscription | null = null;
  if (org) {
    try {
      currentSubscription = await getCurrentSubscription(org.id);
    } catch (e) {
      console.error("Error fetching current subscription:", e);
    }
  }

  let invoices: Orb.Invoice[] | null = null;
  if (org) {
    try {
      invoices = await getInvoices({ orgId: org.id });
    } catch (e) {
      console.error("Error fetching invoices");
    }
  }

  let orgPermissions: Permission[] = [];

  try {
    const orgPerms = await getObjectAclPermissions({
      objectType: "organization",
      objectId: org?.id,
    });

    orgPermissions = orgPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on billing page", e);
  }

  let paymentMethodSummary: {
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null = null;

  if (currentSubscription?.customer.payment_provider_id) {
    paymentMethodSummary = await getDefaultPaymentMethodSummary(
      currentSubscription.customer.payment_provider_id,
    );
  }

  return (
    <ClientPage
      orgName={org_name}
      currentSubscription={currentSubscription}
      invoices={invoices}
      paymentMethodSummary={paymentMethodSummary}
      orgPermissions={orgPermissions}
    />
  );
}
