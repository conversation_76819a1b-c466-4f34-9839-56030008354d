"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { But<PERSON> } from "#/ui/button";
import { Loading } from "#/ui/loading";
import { type ReactNode, useCallback, useState } from "react";
import { AISecretTypes, CloudSecretTypes } from "@braintrust/proxy/schema";
import { PencilLineIcon, Trash2Icon, AlertTriangle, Check } from "lucide-react";
import { smartTimeFormat } from "#/ui/date";
import { useAvailableModels } from "#/ui/prompts/models";
import { type ModalType, ProviderKeyModal } from "./provider-key-modal";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { getProviderIcon, providerReadableName } from "./utils";
import { toast } from "sonner";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { type AISecret } from "@braintrust/core/typespecs";
import { cn } from "#/utils/classnames";
import { type Permission } from "@braintrust/core/typespecs";
import { InfoBanner } from "#/ui/info-banner";

// TODO: Eventually this could be extensible, e.g. if a customer deployed a model through their
// own internal service and needed to manage a key
const DefaultSecretNames = Object.keys(AISecretTypes);
const DefaultCloudSecretNames = Object.keys(CloudSecretTypes);
interface Params {
  orgName: string;
  orgPermissions: Permission[];
}

export default function OrgSecrets(props: Params) {
  const orgName = props.orgName;

  const {
    refresh: refreshSecrets,
    apiSecrets: apiSecretsDB,
    configuredModelsByProvider,
    customModelErrors,
  } = useAvailableModels({ orgName });

  const [openedModal, setOpenedModal] = useState<ModalType | undefined>(
    undefined,
  );
  const [openedRow, setOpenedRow] = useState<AISecret | undefined>(undefined);
  const [rowToDelete, setRowToDelete] = useState<AISecret | undefined>(
    undefined,
  );

  const apiSecretsByName = Object.fromEntries(
    apiSecretsDB?.map((s) => [s.name, s]) || [],
  );

  const defaultSecrets = DefaultSecretNames.map((name, i) => {
    return apiSecretsByName[name] || { name, id: i, type: AISecretTypes[name] };
  });

  const defaultCloudSecrets = DefaultCloudSecretNames.map((name, i) => {
    return (
      apiSecretsByName[name] || { name, id: i, type: CloudSecretTypes[name] }
    );
  });

  const customSecrets = apiSecretsDB?.filter(
    (row) =>
      !DefaultSecretNames.includes(row.name) &&
      !DefaultCloudSecretNames.includes(row.name),
  );

  const deleteKey = useCallback(
    async (name: string) => {
      try {
        const resp = await fetch(`/api/ai_secret/delete`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name,
            org_name: orgName,
          }),
        });
        if (!resp.ok) {
          throw new Error(await resp.text());
        }
        refreshSecrets();
        setRowToDelete(undefined);
      } catch (error) {
        toast.error(`Failed to delete secret`, {
          description: `${error}`,
        });
      }
      refreshSecrets();
    },
    [orgName, refreshSecrets],
  );

  const createKey = useCallback(
    async ({
      type,
      name,
      value,
      metadata,
    }: {
      type: string | null | undefined;
      name: string;
      value?: string;
      metadata?: Record<string, unknown>;
    }) => {
      try {
        const resp = await fetch(`/api/ai_secret/register`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ai_secret_name: name,
            type,
            metadata,
            secret: value,
            update: true,
            org_name: orgName,
          }),
        });
        if (!resp.ok) {
          throw new Error(await resp.text());
        }
        refreshSecrets();
        setOpenedRow(undefined);
        toast.success("Secret saved successfully");
      } catch (error) {
        toast.error(`Failed to set secret`, {
          description: `${error}`,
        });
      }
    },
    [orgName, refreshSecrets],
  );

  const getModelsContent = (row: AISecret): ReactNode => {
    const models = row.preview_secret
      ? (configuredModelsByProvider[row.name] ?? [])
      : [];
    const modelNames = models.map((m) => m.displayName || m.modelName);
    return modelNames.length > 0 ? (
      <Tooltip>
        <TooltipTrigger className="w-full cursor-text text-left">
          {modelNames.length}
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs font-medium">
            {modelNames.map((name) => (
              <ul key={name}>{name}</ul>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    ) : (
      "-"
    );
  };

  function getCustomModelErrorForSecret(secretName: string) {
    return customModelErrors?.find((err) => err.secretName === secretName);
  }

  const isAllowedToEditSecrets = props.orgPermissions?.includes("update");

  return (
    <div>
      <h2 className="mb-1 text-lg font-semibold">AI providers</h2>
      <p className="mb-6 text-sm text-primary-600">
        Set organization-level secrets for AI providers
      </p>
      {!isAllowedToEditSecrets && (
        <InfoBanner>
          To edit AI providers, ask your administrator to grant the{" "}
          <span className="font-medium">Manage settings</span> permission for
          this organization.
        </InfoBanner>
      )}
      {isAllowedToEditSecrets && (
        <>
          {apiSecretsDB === undefined ? (
            <Loading />
          ) : (
            <>
              <h2 className="mt-8 text-base font-medium">Model providers</h2>
              <Table className="mt-6 table-auto text-left">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-60">Name</TableHead>
                    <TableHead className="w-48">Status</TableHead>
                    <TableHead className="w-48">Last updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {defaultSecrets.map((row) => {
                    const updatedOrCreated = row.updated_at || row.created;
                    return (
                      <TableRow
                        key={row.id}
                        className="py-2.5 hover:cursor-pointer"
                        onClick={() => {
                          setOpenedRow(row);
                          setOpenedModal("DEFAULT");
                        }}
                      >
                        <TableCell className="flex w-60 items-center gap-3">
                          {row.type && getProviderIcon(row.type, 28)}
                          <div>
                            <div className="font-medium">
                              {providerReadableName(row.type ?? "") ?? row.name}
                            </div>
                            <div className="font-mono text-xs text-primary-500">
                              {row.name}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="flex w-48 items-center gap-2">
                          {row?.preview_secret ? (
                            <>
                              <Check className="size-3" />
                              Configured
                            </>
                          ) : (
                            <>
                              <span className="text-primary-400">
                                Not configured
                              </span>
                            </>
                          )}
                        </TableCell>
                        <TableCell className="w-48">
                          {updatedOrCreated ? (
                            <time
                              dateTime={updatedOrCreated}
                              title={updatedOrCreated}
                            >
                              {smartTimeFormat(
                                new Date(updatedOrCreated).getTime(),
                              )}
                            </time>
                          ) : (
                            <NullFormatter />
                          )}
                        </TableCell>
                        <TableCell className="flex flex-1 items-center justify-end gap-2 pr-0">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-6"
                            title="Edit provider"
                          >
                            <PencilLineIcon className="size-4" />
                          </Button>
                          {row.preview_secret && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="size-6"
                              onClick={(e) => {
                                e.stopPropagation();
                                setRowToDelete(row);
                              }}
                              title="Delete provider"
                            >
                              <Trash2Icon className="size-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </>
          )}

          {apiSecretsDB !== undefined && defaultCloudSecrets && (
            <>
              <h2 className="mt-8 text-base font-medium">Cloud providers</h2>
              <Table className="mt-6 table-auto text-left">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-80">Name</TableHead>
                    <TableHead className="w-48">Status</TableHead>
                    <TableHead className="w-48">Models</TableHead>
                    <TableHead className="w-48">Last updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {defaultCloudSecrets.map((row) => {
                    const updatedOrCreated = row.updated_at || row.created;
                    return (
                      <TableRow
                        key={row.id}
                        className="py-2.5 hover:cursor-pointer"
                        onClick={() => {
                          setOpenedRow(row);
                          setOpenedModal("CUSTOM");
                        }}
                      >
                        <TableCell className="flex w-80 items-center gap-3">
                          {row.type && getProviderIcon(row.type, 28)}
                          <div>
                            <div className="font-medium">
                              {providerReadableName(row.type ?? "") ?? row.name}
                            </div>
                            <div className="font-mono text-xs text-primary-500">
                              {row.name}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="flex w-48 items-center gap-2">
                          {row?.preview_secret ? (
                            <>
                              <Check className="size-3" />
                              Configured
                            </>
                          ) : (
                            <>
                              <span className="text-primary-400">
                                Not configured
                              </span>
                            </>
                          )}
                        </TableCell>
                        <TableCell className="w-48">
                          {getModelsContent(row)}
                        </TableCell>
                        <TableCell className="w-48">
                          {updatedOrCreated ? (
                            <time
                              dateTime={updatedOrCreated}
                              title={updatedOrCreated}
                            >
                              {smartTimeFormat(
                                new Date(updatedOrCreated).getTime(),
                              )}
                            </time>
                          ) : (
                            <NullFormatter />
                          )}
                        </TableCell>
                        <TableCell className="flex flex-1 items-center justify-end gap-2 pr-0">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-6"
                            title="Edit provider"
                          >
                            <PencilLineIcon className="size-4" />
                          </Button>
                          {row.preview_secret && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="size-6"
                              onClick={(e) => {
                                e.stopPropagation();
                                setRowToDelete(row);
                              }}
                              title="Delete provider"
                            >
                              <Trash2Icon className="size-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </>
          )}

          {apiSecretsDB !== undefined && customSecrets && (
            <>
              <h2 className="mt-8 text-base font-medium">Custom providers</h2>
              <Table className="mt-6 table-auto text-left">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-32">Type</TableHead>
                    <TableHead className="w-60">Name</TableHead>
                    <TableHead className="w-32">Models</TableHead>
                    <TableHead className="w-36">Last updated</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {customSecrets.map((row) => {
                    const updatedOrCreated = row.updated_at || row.created;
                    const error = getCustomModelErrorForSecret(row.name);
                    const rowIsDisabled = !!error;
                    return (
                      <TableRow
                        key={row.id}
                        className={cn(
                          "py-2",
                          rowIsDisabled
                            ? "cursor-not-allowed"
                            : "hover:cursor-pointer",
                        )}
                        onClick={() => {
                          if (!rowIsDisabled) {
                            setOpenedRow(row);
                            setOpenedModal("CUSTOM");
                          }
                        }}
                      >
                        <TableCell className="w-32 gap-3">
                          {row.type && getProviderIcon(row.type, 18)}
                          {row.type && providerReadableName(row.type)}
                        </TableCell>
                        <TableCell className="flex w-60 items-center gap-2 truncate">
                          <span className={rowIsDisabled ? "opacity-60" : ""}>
                            {row.name}
                          </span>
                        </TableCell>
                        <TableCell className="w-32">
                          {getModelsContent(row)}
                        </TableCell>
                        <TableCell className="w-36 truncate">
                          {updatedOrCreated ? (
                            <time
                              dateTime={updatedOrCreated}
                              title={updatedOrCreated}
                            >
                              {smartTimeFormat(
                                new Date(updatedOrCreated).getTime(),
                              )}
                            </time>
                          ) : (
                            <NullFormatter />
                          )}
                        </TableCell>
                        <TableCell className="flex flex-1 items-center justify-end gap-2 pr-0">
                          {error && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>
                                  <AlertTriangle className="size-4 text-bad-600" />
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="max-h-48 max-w-md overflow-y-auto whitespace-pre-line break-words p-2 text-xs text-bad-700">
                                  {`This provider has an invalid custom model configuration. Validation problems:\n\n${error.error}`}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-6"
                            title="Edit provider"
                            disabled={rowIsDisabled}
                          >
                            <PencilLineIcon className="size-4" />
                          </Button>
                          {row.preview_secret && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="size-6"
                              onClick={(e) => {
                                e.stopPropagation();
                                setRowToDelete(row);
                              }}
                              title="Delete provider"
                            >
                              <Trash2Icon className="size-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
              <div className="mt-4">
                <Button
                  size="sm"
                  onClick={(_e) => {
                    setOpenedRow(undefined);
                    setOpenedModal("CUSTOM");
                  }}
                >
                  Create
                </Button>
              </div>
            </>
          )}
        </>
      )}
      <ConfirmationDialog
        onConfirm={() => {
          rowToDelete && deleteKey(rowToDelete.name);
        }}
        open={!!rowToDelete}
        onOpenChange={(open) => {
          if (!open) setRowToDelete(undefined);
        }}
        title="Delete Key"
        description={`Are you sure you want to delete "${
          rowToDelete?.name && AISecretTypes[rowToDelete.name]
            ? providerReadableName(AISecretTypes[rowToDelete.name])
            : rowToDelete?.name
        }"? `}
        confirmText={"Delete"}
      />
      <ProviderKeyModal
        row={openedRow}
        modalType={openedModal}
        setModalType={setOpenedModal}
        setOpened={(opened) => {
          setOpenedModal(!opened ? undefined : "DEFAULT");
          setOpenedRow(undefined);
        }}
        setKey={createKey}
        existingSecrets={apiSecretsDB}
      />
    </div>
  );
}
