import React, { useEffect, useState } from "react";
import { FormControl, FormField, FormMessage } from "#/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";
import {
  type UseFieldArrayRemove,
  useFormContext,
  useWatch,
} from "react-hook-form";
import { providerReadableName, getDisplayName } from "./utils";
import { ModelFormats, PromptInputs } from "@braintrust/proxy/schema";
import { Button } from "#/ui/button";
import { Settings2, Trash2 } from "lucide-react";
import { Switch } from "#/ui/switch";
import { CostInput } from "#/ui/cost-input";
import { Input } from "#/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { ProviderFormField } from "./provider-form-field";
import {
  BasicTooltip,
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";

export const DefaultCustomModel = {
  modelName: "",
  format: ModelFormats[0],
  flavor: PromptInputs[0],
  multimodal: false,
  input_cost_per_mil_tokens: null,
  output_cost_per_mil_tokens: null,
};

const getFormatFromName = (modelName: string) => {
  if (modelName.startsWith("claude") || modelName.startsWith("anthropic")) {
    return "anthropic";
  }

  if (modelName.startsWith("gemini")) {
    return "google";
  }

  if (modelName.startsWith("amazon")) {
    return "converse";
  }

  return "openai";
};

function CustomModelForm({
  index,
  remove,
  isCreate,
  showLocations = false,
}: {
  index: number;
  remove: UseFieldArrayRemove;
  isCreate: boolean;
  showLocations?: boolean;
}) {
  const {
    control,
    register,
    setValue,
    formState: { touchedFields },
  } = useFormContext();
  const [open, setOpen] = useState(false);

  const watchedItems = useWatch({
    control,
    name: `customModels.${index}`,
    defaultValue: DefaultCustomModel,
  });

  const flavor = watchedItems?.flavor;
  const multimodal = watchedItems?.multimodal;
  const modelName = watchedItems?.modelName;
  const multimodalDisabled = flavor !== "chat";

  useEffect(() => {
    if (multimodalDisabled && multimodal) {
      setValue(`customModels.${index}.multimodal`, false);
    }
  }, [multimodalDisabled, multimodal, index, setValue]);

  const reasoningDisabled = flavor !== "chat";
  useEffect(() => {
    if (reasoningDisabled && watchedItems?.reasoning) {
      setValue(`customModels.${index}.reasoning`, false);
    }
  }, [reasoningDisabled, watchedItems?.reasoning, index, setValue]);

  const reasoningBudgetDisabled = flavor !== "chat";
  useEffect(() => {
    if (reasoningBudgetDisabled && watchedItems?.reasoning_budget) {
      setValue(`customModels.${index}.reasoning_budget`, false);
    }
  }, [
    reasoningBudgetDisabled,
    watchedItems?.reasoning_budget,
    index,
    setValue,
  ]);

  useEffect(() => {
    if (isCreate && !touchedFields.customModels?.[index]?.format) {
      const format = getFormatFromName(modelName);
      setValue(`customModels.${index}.format`, format);
    }
  }, [isCreate, modelName, index, setValue, touchedFields.customModels]);

  return (
    <div className="flex gap-2">
      <FormField
        control={control}
        name={`customModels.${index}.modelName`}
        render={({ field }) => (
          <div className="flex flex-1 flex-col">
            <Input
              className="h-8"
              type="text"
              placeholder="Enter model name"
              {...register(field.name)}
            />
            <FormMessage />
          </div>
        )}
      />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
            }}
            className="size-8 p-0"
            type="button"
          >
            <Settings2 className="size-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          collisionPadding={8}
          side="right"
          align="start"
          className="flex w-[400px] flex-col gap-2"
        >
          <div className="text-sm">Model options</div>
          <FormField
            control={control}
            name={`customModels.${index}.displayName`}
            render={({ field }) => (
              <ProviderFormField label="Display name">
                <Input
                  className="h-8"
                  type="text"
                  placeholder="Optional"
                  {...register(field.name)}
                />
                <FormMessage />
              </ProviderFormField>
            )}
          />
          <FormField
            control={control}
            name={`customModels.${index}.description`}
            render={({ field }) => (
              <ProviderFormField label="Description">
                <Input
                  className="h-8"
                  type="text"
                  placeholder="Optional"
                  {...register(field.name)}
                />
                <FormMessage />
              </ProviderFormField>
            )}
          />
          <FormField
            control={control}
            name={`customModels.${index}.flavor`}
            render={({ field }) => {
              const { ref: _ignore, onChange, ...fieldProps } = field;
              return (
                <ProviderFormField label="Flavor">
                  <Select
                    {...fieldProps}
                    onValueChange={(newValue) => {
                      onChange(newValue);
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="h-8 w-full">
                        <SelectValue placeholder="None" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {PromptInputs.map((input) => (
                        <SelectItem key={input} value={input}>
                          {getDisplayName(input)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </ProviderFormField>
              );
            }}
          />
          <FormField
            control={control}
            name={`customModels.${index}.format`}
            render={({ field }) => {
              const { ref: _ignore, ...fieldProps } = field;
              return (
                <ProviderFormField label="Format">
                  <Select
                    {...fieldProps}
                    onValueChange={(newValue) => {
                      setValue(`customModels.${index}.format`, newValue, {
                        shouldTouch: true,
                      });
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="h-8 w-full">
                        <SelectValue placeholder="None" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ModelFormats.map((format) => (
                        <SelectItem key={format} value={format}>
                          {providerReadableName(format)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </ProviderFormField>
              );
            }}
          />
          <FormField
            control={control}
            name={`customModels.${index}.multimodal`}
            render={({ field }) => (
              <ProviderFormField label="Multimodal">
                <div className="flex h-8 items-center">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <Switch
                          className="data-[state=checked]:bg-primary-700"
                          checked={!!field.value}
                          onCheckedChange={field.onChange}
                          disabled={multimodalDisabled}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipPortal>
                      {multimodalDisabled && (
                        <TooltipContent className="text-xs">
                          Multimodal only supported when flavor is{" "}
                          <span className="font-medium">Chat</span>
                        </TooltipContent>
                      )}
                    </TooltipPortal>
                  </Tooltip>
                </div>
              </ProviderFormField>
            )}
          />
          <FormField
            control={control}
            name={`customModels.${index}.reasoning`}
            render={({ field }) => (
              <ProviderFormField label="Reasoning">
                <div className="flex h-8 items-center">
                  <BasicTooltip
                    tooltipContent={
                      reasoningDisabled ? null : (
                        <>
                          {" "}
                          Reasoning only supported when flavor is{" "}
                          <span className="font-medium">Chat</span>
                        </>
                      )
                    }
                  >
                    <div>
                      <Switch
                        className="data-[state=checked]:bg-primary-700"
                        checked={!!field.value}
                        onCheckedChange={field.onChange}
                        disabled={reasoningDisabled}
                      />
                    </div>
                  </BasicTooltip>
                </div>
              </ProviderFormField>
            )}
          />
          <FormField
            control={control}
            name={`customModels.${index}.reasoning_budget`}
            render={({ field }) => (
              <ProviderFormField label="Reasoning Budget">
                <div className="flex h-8 items-center">
                  <BasicTooltip
                    tooltipContent={
                      reasoningBudgetDisabled ? null : (
                        <>
                          {" "}
                          Reasoning budget only supported when flavor is{" "}
                          <span className="font-medium">Chat</span>
                        </>
                      )
                    }
                  >
                    <div>
                      <Switch
                        className="data-[state=checked]:bg-primary-700"
                        checked={!!field.value}
                        onCheckedChange={field.onChange}
                        disabled={reasoningBudgetDisabled}
                      />
                    </div>
                  </BasicTooltip>
                </div>
              </ProviderFormField>
            )}
          />
          {showLocations && (
            <FormField
              control={control}
              name={`customModels.${index}.locations`}
              render={({ field }) => (
                <ProviderFormField label="Locations">
                  <div className="flex flex-col gap-2">
                    {Array.isArray(field.value) &&
                      field.value.map((location, i) => (
                        <div key={i} className="flex gap-2">
                          <Input
                            className="h-8 flex-1"
                            type="text"
                            placeholder="e.g. us-central1"
                            value={location}
                            onChange={(e) => {
                              const newLocations = [...field.value];
                              newLocations[i] = e.target.value.trim();
                              field.onChange(newLocations.filter(Boolean));
                            }}
                          />
                          <Button
                            type="button"
                            size="sm"
                            className="size-8 p-0"
                            onClick={() => {
                              const newLocations = [...field.value];
                              newLocations.splice(i, 1);
                              field.onChange(newLocations);
                            }}
                          >
                            <Trash2 className="size-3" />
                          </Button>
                        </div>
                      ))}
                    <Button
                      type="button"
                      variant="border"
                      size="sm"
                      className="h-8"
                      onClick={() => {
                        field.onChange([...(field.value || []), ""]);
                      }}
                    >
                      Add location
                    </Button>
                  </div>
                </ProviderFormField>
              )}
            />
          )}
          <FormField
            control={control}
            name={`customModels.${index}.input_cost_per_mil_tokens`}
            render={({ field }) => (
              <ProviderFormField label="Input cost">
                <div className="flex items-center gap-2">
                  <CostInput
                    className="h-8 flex-1"
                    placeholder="Optional"
                    {...register(field.name)}
                    onBlur={() => field.onBlur()}
                  />
                  <span className="flex-none text-xs text-primary-600">
                    / 1M tokens
                  </span>
                </div>
              </ProviderFormField>
            )}
          />
          <FormField
            control={control}
            name={`customModels.${index}.output_cost_per_mil_tokens`}
            render={({ field }) => (
              <ProviderFormField
                label="Output cost"
                helpText="Only used to display estimated LLM cost for evals"
              >
                <div className="flex items-center gap-2">
                  <CostInput
                    className="h-8 flex-1"
                    placeholder="Optional"
                    {...register(field.name)}
                    onBlur={() => field.onBlur()}
                  />
                  <span className="flex-none text-xs text-primary-600">
                    / 1M tokens
                  </span>
                </div>
              </ProviderFormField>
            )}
          />
        </PopoverContent>
      </Popover>
      <Button
        onClick={(e) => {
          e.stopPropagation();
          remove(index);
        }}
        className="size-8 p-0"
        size="sm"
        title="Remove model"
        type="button"
      >
        <Trash2 className="size-3" />
      </Button>
    </div>
  );
}

export default CustomModelForm;
