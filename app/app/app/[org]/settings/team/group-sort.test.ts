import { describe, expect, test } from "vitest";
import { sortGroups } from "./add-to-group-combobox";

describe("sortGroups", () => {
  test("should place system groups at the top, followed by non-system groups", () => {
    const data = [
      {
        value: "848f85f2-d667-4dcd-b396-b9e4b12100ab",
        label: "C new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "no-group",
        label: "No permission group",
        description: "Invite somenow now, but set up their permissions later.",
        userId: null,
      },
      {
        value: "bcfd79b2-ad6d-41fc-8c15-d7c2ecce3948",
        label: "Engineers",
        description:
          "Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "391a48f5-6f99-42cc-a593-7611f10f713f",
        label: "Danlees",
        description: "Everyone in this group is a Danlee",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "9e367622-2e44-44dd-a7cf-539eb18e3ca7",
        label: "Viewers",
        description:
          "Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "27578675-4ed0-41db-9f45-dea76f37ca59",
        label: "A new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "c3f246b6-1eeb-4e30-9056-65a7f4b3abd9",
        label: "Owners",
        description:
          "Can create, read, update, and delete all resources in this org.",
        userId: null,
      },
      {
        value: "b511c97b-1c7f-4066-8993-5ec7ea09f662",
        label: "B new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
    ];

    expect(data.sort(sortGroups)).toEqual([
      {
        value: "bcfd79b2-ad6d-41fc-8c15-d7c2ecce3948",
        label: "Engineers",
        description:
          "Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "c3f246b6-1eeb-4e30-9056-65a7f4b3abd9",
        label: "Owners",
        description:
          "Can create, read, update, and delete all resources in this org.",
        userId: null,
      },
      {
        value: "9e367622-2e44-44dd-a7cf-539eb18e3ca7",
        label: "Viewers",
        description:
          "Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "no-group",
        label: "No permission group",
        description: "Invite somenow now, but set up their permissions later.",
        userId: null,
      },
      {
        value: "27578675-4ed0-41db-9f45-dea76f37ca59",
        label: "A new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "b511c97b-1c7f-4066-8993-5ec7ea09f662",
        label: "B new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "848f85f2-d667-4dcd-b396-b9e4b12100ab",
        label: "C new Group",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
      {
        value: "391a48f5-6f99-42cc-a593-7611f10f713f",
        label: "Danlees",
        description: "Everyone in this group is a Danlee",
        userId: "fac36c53-c882-458b-bf80-60d06c3e8a0d",
      },
    ]);
  });

  test("should sort only system groups alphabetically", () => {
    const data = [
      {
        value: "c3f246b6-1eeb-4e30-9056-65a7f4b3abd9",
        label: "Owners",
        description:
          "Can create, read, update, and delete all resources in this org.",
        userId: null,
      },
      {
        value: "9e367622-2e44-44dd-a7cf-539eb18e3ca7",
        label: "Viewers",
        description:
          "Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "no-group",
        label: "No permission group",
        description: "Invite somenow now, but set up their permissions later.",
        userId: null,
      },
      {
        value: "bcfd79b2-ad6d-41fc-8c15-d7c2ecce3948",
        label: "Engineers",
        description:
          "Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
    ];

    expect(data.sort(sortGroups)).toEqual([
      {
        value: "bcfd79b2-ad6d-41fc-8c15-d7c2ecce3948",
        label: "Engineers",
        description:
          "Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "c3f246b6-1eeb-4e30-9056-65a7f4b3abd9",
        label: "Owners",
        description:
          "Can create, read, update, and delete all resources in this org.",
        userId: null,
      },
      {
        value: "9e367622-2e44-44dd-a7cf-539eb18e3ca7",
        label: "Viewers",
        description:
          "Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.",
        userId: null,
      },
      {
        value: "no-group",
        label: "No permission group",
        description: "Invite somenow now, but set up their permissions later.",
        userId: null,
      },
    ]);
  });
});
