import { cn } from "#/utils/classnames";
import React from "react";

type Props = {
  name: string;
  onDelete: (name: string) => void;
};

const NameTag = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & Props
>(({ name, onDelete, className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "border-primary-500 text-primary-700 rounded-full border bg-transparent px-3 py-0.5",
        className,
      )}
      {...props}
    >
      <span>{name}</span>
      <button
        className="ml-2 text-primary-700 hover:text-primary-900 focus:outline-none"
        onClick={() => onDelete(name)}
        tabIndex={-1}
      >
        &times;
      </button>
    </div>
  );
});
NameTag.displayName = "NameTag";

export default NameTag;
