import React from "react";
import { But<PERSON> } from "#/ui/button";
import { AddToGroupCombobox } from "./add-to-group-combobox";
import { cn } from "#/utils/classnames";

interface GroupSelectorProps {
  selectedGroups: { groupId: string; groupLabel: string }[];
  onGroupChange: (groupId: string, groupLabel: string) => void;
  placeholder?: string;
  children?: React.ReactNode;
  size?: "default" | "lg";
  orgName: string;
}

export function GroupSelector({
  selectedGroups,
  onGroupChange,
  placeholder = "Select permission groups",
  size = "default",
  orgName,
}: GroupSelectorProps) {
  return (
    <AddToGroupCombobox
      selectedGroups={selectedGroups.map((g) => g.groupId)}
      onChange={onGroupChange}
      orgName={orgName}
    >
      <Button
        isDropdown
        className={cn(
          "flex-1 justify-start overflow-hidden pl-2 pr-3 text-left",
          { "text-base pl-3 h-9": size === "lg" },
        )}
      >
        {selectedGroups.length === 0 ? (
          <span className="flex-1 truncate text-primary-500">
            {placeholder}
          </span>
        ) : (
          <span className="flex-1 truncate font-normal">
            {selectedGroups.map((g) => g.groupLabel).join(", ")}
          </span>
        )}
      </Button>
    </AddToGroupCombobox>
  );
}
