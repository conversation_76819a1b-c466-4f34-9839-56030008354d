import { PageTracker } from "#/ui/use-analytics";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { LoginRequired } from "#/ui/root";
import React from "react";
import { RenderIfUser } from "../clientlayout";
import { SidebarNav } from "./sidebar-nav";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { ClientLayout } from "./clientlayout";
import { BodyWrapper } from "../../body-wrapper";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <LoginRequired loginRequired>
      <RenderIfUser>
        <ClientLayout>
          <PageTracker category="settings">
            <BodyWrapper innerClassName="overflow-auto">
              <MainContentWrapper className="h-auto min-h-[calc(100vh-45px)] w-full max-w-screen-xl">
                <div className="lg-gap-10 flex w-full flex-col gap-6 lg:flex-row">
                  <aside className="w-full flex-none lg:max-w-[180px]">
                    <SidebarNav />
                  </aside>
                  <div className="flex-1 overflow-hidden p-0.5">{children}</div>
                </div>
              </MainContentWrapper>
            </BodyWrapper>
          </PageTracker>
        </ClientLayout>
      </RenderIfUser>
    </LoginRequired>
  );
}

export async function generateMetadata(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Settings",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/${params.org}/settings`,
  });
}
