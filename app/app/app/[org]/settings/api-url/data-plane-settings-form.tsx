import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "#/ui/dialog";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "#/ui/form";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import TextLink from "#/ui/text-link";

export const DataPlaneSettingsForm = ({
  initialData,
  open,
  setOpen,
  onSubmit,
  focusField = null,
}: {
  initialData: {
    apiUrl: string | null;
    proxyUrl: string | null;
    realtimeUrl: string | null;
  };
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (data: {
    apiUrl: string | null;
    proxyUrl: string | null;
    realtimeUrl: string | null;
  }) => Promise<void>;
  focusField?: "api" | "proxy" | "realtime" | null;
}) => {
  const form = useForm({
    defaultValues: initialData,
    resolver: zodResolver(
      z.object({
        apiUrl: z.string().nullable(),
        proxyUrl: z.string().nullable(),
        realtimeUrl: z.string().nullable(),
      }),
    ),
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit data plane URLs</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormItem>
              <FormLabel>API URL</FormLabel>
              <FormControl>
                <Input
                  {...form.register("apiUrl")}
                  onChange={(e) => {
                    form.setValue("apiUrl", e.target.value || null, {
                      shouldDirty: true,
                      shouldValidate: true,
                    });
                  }}
                  type="url"
                  placeholder="Enter API URL"
                  autoFocus={focusField === "api"}
                />
              </FormControl>
              <FormDescription>
                The API URL for your Braintrust data plane in your{" "}
                <TextLink
                  className="font-medium text-accent-600"
                  href="/docs/guides/self-hosting/aws"
                >
                  own cloud environment
                </TextLink>
              </FormDescription>
              <FormMessage />
            </FormItem>

            <FormItem>
              <FormLabel>Custom proxy URL</FormLabel>
              <FormControl>
                <Input
                  {...form.register("proxyUrl")}
                  onChange={(e) => {
                    form.setValue("proxyUrl", e.target.value || null, {
                      shouldDirty: true,
                      shouldValidate: true,
                    });
                  }}
                  type="url"
                  placeholder="Enter Proxy URL"
                  autoFocus={focusField === "proxy"}
                />
              </FormControl>
              <FormDescription>
                Leave blank unless you are also deploying a custom proxy inside
                your own cloud environment,{" "}
                <TextLink
                  className="font-medium text-accent-600"
                  href="/docs/guides/self-hosting/docker"
                >
                  e.g. as a docker container
                </TextLink>{" "}
                (this is rare)
              </FormDescription>
              <FormMessage />
            </FormItem>

            <FormItem>
              <FormLabel>Custom realtime URL</FormLabel>
              <FormControl>
                <Input
                  {...form.register("realtimeUrl")}
                  onChange={(e) => {
                    form.setValue("realtimeUrl", e.target.value || null, {
                      shouldDirty: true,
                      shouldValidate: true,
                    });
                  }}
                  type="url"
                  placeholder="Enter Realtime URL"
                  autoFocus={focusField === "realtime"}
                />
              </FormControl>
              <FormDescription>
                Leave blank unless you are also deploying a custom realtime
                service inside your own cloud environment,{" "}
                <TextLink
                  className="font-medium text-accent-600"
                  href="/docs/guides/self-hosting/docker"
                >
                  e.g. as a docker container
                </TextLink>{" "}
                (this is rare)
              </FormDescription>
              <FormMessage />
            </FormItem>

            <DialogFooter>
              <Button
                type="submit"
                size="sm"
                variant="primary"
                isLoading={form.formState.isSubmitting}
                disabled={form.formState.isSubmitting}
              >
                Save
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
