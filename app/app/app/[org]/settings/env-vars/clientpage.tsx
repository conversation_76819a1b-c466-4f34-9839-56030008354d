"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { But<PERSON> } from "#/ui/button";
import { smartTimeFormat } from "#/ui/date";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "#/ui/dialog";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { Form, FormField, FormItem, FormLabel } from "#/ui/form";
import {
  type SecretForm,
  secretFormSchema,
  useEnvVars,
} from "#/utils/env-vars/use-env-vars";
import { Input } from "#/ui/input";
import { Label } from "#/ui/label";
import { Loading } from "#/ui/loading";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { useOrg } from "#/utils/user";
import { zodResolver } from "@hookform/resolvers/zod";
import { Minus, PencilLineIcon, PlusIcon, Trash2Icon } from "lucide-react";
import { useRef, useState } from "react";
import { type FieldValues, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { type Permission, type EnvVar } from "@braintrust/core/typespecs";
import { MinVersion } from "#/lib/feature-flags";
import { cn } from "#/utils/classnames";
import { InfoBanner } from "#/ui/info-banner";

export default function EnvironmentVariables({
  orgPermissions,
}: {
  orgPermissions: Permission[];
}) {
  const { id: orgId } = useOrg();

  const [rowToDelete, setRowToDelete] = useState<EnvVar | null>(null);
  const [rowToEdit, setRowToEdit] = useState<EnvVar | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [varsEnabled, setVarsEnabled] = useState(true);

  const { data, isPending, createVariables, deleteVariable, updateVariable } =
    useEnvVars({
      objectType: "organization",
      objectId: orgId,
      setVarsEnabled,
    });

  const form = useForm<SecretForm>({
    resolver: zodResolver(secretFormSchema),
    defaultValues: {
      envVars: [
        {
          key: "",
          value: "",
        },
      ],
    },
  });

  const { fields, append, remove, replace } = useFieldArray({
    control: form.control,
    name: "envVars",
  });

  const envVars = form.watch("envVars");

  const handlePaste = (e: React.ClipboardEvent, index: number) => {
    // Get pasted content
    const pastedText = e.clipboardData.getData("text");

    // Check if it contains multiple lines or key=value pairs
    if (pastedText.includes("\n") || pastedText.includes("=")) {
      e.preventDefault(); // Prevent default paste behavior

      // Parse the pasted content
      const lines = pastedText
        .split("\n")
        .filter((line) => line.trim() && !line.startsWith("#"));

      if (lines.length === 0) return;

      const parsedVariables: SecretForm["envVars"] = [];

      // Process each line
      lines.forEach((line) => {
        const match = line.match(/^([^=]+)=(.*)$/);
        if (match) {
          const [, key, value] = match;
          parsedVariables.push({ key: key.trim(), value: value.trim() });
        } else if (line.trim()) {
          // If there's no equals sign, treat the whole line as a key with empty value
          parsedVariables.push({ key: line.trim(), value: "" });
        }
      });

      if (parsedVariables.length === 0) return;

      // Replace the current field with the first parsed variable
      form.setValue(`envVars.${index}.key`, parsedVariables[0].key);
      form.setValue(`envVars.${index}.value`, parsedVariables[0].value);

      // Add the rest of the parsed variables as new fields
      if (parsedVariables.length > 1) {
        const currentVariables = [...envVars];

        // Remove the current field (it will be replaced)
        currentVariables.splice(index, 1);

        // Insert all parsed variables at the current index
        currentVariables.splice(index, 0, ...parsedVariables);

        // Update the form
        replace(currentVariables);
      }
    }
  };

  const onSubmit = async (data: FieldValues) => {
    setLoading(true);
    setError(null);

    const formData = secretFormSchema.parse(data);
    const filteredFormData = {
      ...formData,
      envVars: formData.envVars.filter((envVar) => envVar.key),
    };

    if (filteredFormData.envVars.length === 0) {
      setError("Please define the key of the environment variable");
      setLoading(false);
      return;
    }

    try {
      createVariables(filteredFormData);
    } catch (error) {
      toast.error(`Failed to save variable`, {
        description: `${error}`,
      });
    } finally {
      setLoading(false);
    }

    form.reset();
    setLoading(false);
  };

  const isAllowedToEditEnvVars = orgPermissions?.includes("update");

  if (!varsEnabled) {
    return (
      <div>
        <h2 className="mb-2 text-lg font-semibold">Environment variables</h2>
        <span className="text-sm text-primary-600">
          Environment variables not available. Please upgrade your API server to
          a version {MinVersion.functionTools} or above.
        </span>
      </div>
    );
  }

  return (
    <div>
      <h2 className="mb-1 text-lg font-semibold">Environment variables</h2>
      <div className="mb-6 text-sm text-primary-600">
        Environment variables are secrets accessible to all functions (prompts,
        scorers, and tools) in this organization
      </div>
      {isAllowedToEditEnvVars && (
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col rounded-md border"
          >
            <div className="flex flex-col gap-2 p-4">
              <div className="mb-1 text-sm font-medium">Add a variable</div>
              {fields.map((field, index) => {
                return (
                  <div className="flex items-end gap-2" key={field.id}>
                    <FormItem className="flex-1 space-y-1.5">
                      {index === 0 && (
                        <FormLabel className="text-primary-600">Key</FormLabel>
                      )}
                      <FormField
                        control={form.control}
                        name={`envVars.${index}.key`}
                        render={({ field }) => (
                          <Input
                            className="h-9 font-mono"
                            type="text"
                            onPaste={(e) => handlePaste(e, index)}
                            placeholder="e.g. BT_ENV_VAR"
                            {...form.register(field.name)}
                          />
                        )}
                      />
                    </FormItem>
                    <FormItem className="flex-1 space-y-1.5">
                      {index === 0 && (
                        <FormLabel className="text-primary-600">
                          Value
                        </FormLabel>
                      )}
                      <FormField
                        control={form.control}
                        name={`envVars.${index}.value`}
                        render={({ field }) => (
                          <Input
                            className="h-9 font-mono"
                            type="text"
                            {...form.register(field.name)}
                          />
                        )}
                      />
                    </FormItem>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      disabled={fields.length === 1}
                    >
                      <Minus className="size-4" />
                    </Button>
                  </div>
                );
              })}
              <div className="mt-2 flex items-center justify-between gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-fit px-0 transition-all hover:px-2"
                  onClick={(e) => {
                    e.preventDefault();
                    append({ key: "", value: "" });
                  }}
                >
                  <PlusIcon className="size-3 text-primary-600" />
                  Add more
                </Button>
                <div className="flex items-center gap-2">
                  {error && (
                    <div className="flex justify-end text-xs font-medium text-bad-600">
                      {error}
                    </div>
                  )}
                  <Button
                    variant="primary"
                    size={"sm"}
                    disabled={
                      isPending ||
                      !form.formState.isValid ||
                      !form.formState.isDirty
                    }
                    isLoading={loading}
                    type="submit"
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>
      )}
      {!isAllowedToEditEnvVars && (
        <InfoBanner>
          To edit environment variables, ask your administrator to grant the{" "}
          <span className="font-medium">Manage settings</span> permission for
          this organization.
        </InfoBanner>
      )}
      {isPending ? (
        <Loading className="pt-6" />
      ) : data && data.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow>
              <TableHead className="w-60 flex-1">Key</TableHead>
              <TableHead className="w-48">Value</TableHead>
              <TableHead className="w-48">Created</TableHead>
              <TableHead className="w-48">Last used</TableHead>
              {isAllowedToEditEnvVars && <TableHead className="w-28" />}
            </TableRow>
          </TableHeader>
          <TableBody>
            {(data || []).map((row) => (
              <TableRow
                key={row.id}
                className={cn(
                  "py-2",
                  isAllowedToEditEnvVars && "hover:cursor-pointer",
                )}
                onClick={
                  isAllowedToEditEnvVars ? () => setRowToEdit(row) : undefined
                }
              >
                <TableCell className="flex w-60 flex-1 items-center gap-3 font-mono">
                  <span className="truncate font-semibold">{row.name}</span>
                </TableCell>
                <TableCell className="flex w-48 items-center gap-1 font-mono text-primary-600">
                  {"*".repeat(10)}
                </TableCell>
                <TableCell className="w-48">
                  {row.created ? (
                    <time dateTime={row.created} title={row.created}>
                      {smartTimeFormat(new Date(row.created).getTime())}
                    </time>
                  ) : (
                    <NullFormatter />
                  )}
                </TableCell>
                <TableCell className="w-48">
                  {row.used ? (
                    <time dateTime={row.used} title={row.used}>
                      {smartTimeFormat(new Date(row.used).getTime())}
                    </time>
                  ) : (
                    <NullFormatter />
                  )}
                </TableCell>
                {isAllowedToEditEnvVars && (
                  <TableCell className="flex items-center justify-end pr-0">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-6"
                      title="Edit variable"
                    >
                      <PencilLineIcon className="size-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        setRowToDelete(row);
                      }}
                      title="Delete variable"
                      type="button"
                    >
                      <Trash2Icon className="size-4" />
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : null}
      <ConfirmationDialog
        onConfirm={() => {
          if (rowToDelete) {
            try {
              deleteVariable(rowToDelete.id);
              setRowToDelete(null);
            } catch (err) {
              toast.error(`Failed to delete secret`, {
                description: `${err}`,
              });
            }
          }
        }}
        open={!!rowToDelete}
        onOpenChange={(open) => {
          if (!open) setRowToDelete(null);
        }}
        title="Delete variable"
        description={`Are you sure you want to delete "${rowToDelete?.name}"?`}
        confirmText="Delete"
      />
      {rowToEdit && (
        <EditEnvVarModal
          row={rowToEdit}
          setRow={setRowToEdit}
          updateVariable={updateVariable}
        />
      )}
    </div>
  );
}

const EditEnvVarModal = ({
  row,
  setRow,
  updateVariable,
}: {
  row: EnvVar;
  setRow: (secret: EnvVar | null) => void;
  updateVariable: (envVarId: string, value: string) => Promise<void>;
}) => {
  const valueSet = useRef(false);
  const [value, setValue] = useState("*".repeat(16));
  const [saving, setSaving] = useState(false);

  const onClickUpdate = async () => {
    if (!valueSet.current) {
      setRow(null);
      return;
    }

    setSaving(true);
    try {
      updateVariable(row.id, value);
      setRow(null);
    } catch (error) {
      toast.error(`Failed to update secret`, {
        description: `${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog
      open={!!row}
      onOpenChange={(open) => {
        if (!open) {
          setRow(null);
        }
      }}
    >
      <DialogContent>
        <DialogHeader className="mb-2 overflow-hidden break-words">
          <DialogTitle>
            Edit variable{" "}
            <span className="font-mono font-semibold">{row.name ?? ""}</span>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-1">
          <Label className="text-primary-600">Value</Label>
          <Input
            className="h-8 font-mono"
            type="text"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={(e) => {
              const regex =
                /^[A-Za-z0-9`~!@#$%^&*()\_\-\+={}[\]\|\:;"'<>,.?\/}]{1}$/;
              if (
                !valueSet.current &&
                (e.key === "Backspace" || regex.test(e.key))
              ) {
                setValue("");
              }
              valueSet.current = true;
            }}
          />
        </div>
        <div className="flex justify-end">
          <Button
            variant="primary"
            size="sm"
            isLoading={saving}
            onClick={onClickUpdate}
          >
            Update
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
