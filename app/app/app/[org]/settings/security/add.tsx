"use client";

import { useUser as useClerkUser } from "@clerk/nextjs";
import { type TOTPResource, type Clerk<PERSON>IError } from "@clerk/types";
import { QRCodeSVG } from "qrcode.react";
import { <PERSON><PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { ClerkErrors, handleClerkError } from "./errors";
import { useState } from "react";
import TextArea from "#/ui/text-area";

function AddMfa({ totp }: { totp: TOTPResource }) {
  const { user } = useClerkUser();
  const [showUri, setShowUri] = useState(false);
  const [errors, setErrors] = useState<ClerkAPIError[]>();
  const [isVerifyInProgress, setIsVerifyInProgress] = useState(false);

  const verifyTotp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setIsVerifyInProgress(true);
    setErrors(undefined);

    const formData = new FormData(e.currentTarget);
    const code = formData.get("totp-code");

    try {
      if (code && typeof code === "string") {
        await user?.verifyTOTP({ code });
      }
    } catch (err) {
      setErrors(handleClerkError(err));
    } finally {
      setIsVerifyInProgress(false);
    }
  };

  const title = showUri
    ? "Enter URI in authenticator app"
    : "Scan QR code in authenticator app";

  return (
    <>
      <h2 className="mb-1 pt-8 font-semibold">{title}</h2>
      <div className="mb-4 text-sm text-primary-600">
        {!showUri
          ? `Set up a new sign-in method in your authenticator app and scan the following QR code to link it to your account.`
          : `Set up a new sign-in method in your authenticator and enter the URI provided below.`}
      </div>

      {totp && (
        <>
          {!showUri && (
            <>
              <div>
                <QRCodeSVG value={totp?.uri || ""} size={200} />
              </div>
              <Button
                className="my-2 text-accent-600"
                transparent
                size="inline"
                onClick={() => setShowUri(true)}
              >
                Can&apos;t scan QR code?
              </Button>
            </>
          )}

          {showUri && (
            <div className="mb-4 max-w-lg">
              <TextArea
                className="mb-1 resize-none break-all font-mono text-sm"
                readOnly
                value={totp?.uri}
              />
              <div className="flex items-center justify-between gap-2">
                <Button
                  className="my-2 text-sm text-accent-600"
                  transparent
                  size="inline"
                  onClick={() => setShowUri(false)}
                >
                  Use QR code instead
                </Button>
                <CopyToClipboardButton
                  prependIcon
                  size="xs"
                  textToCopy={totp?.uri ?? ""}
                >
                  Copy TOTP URI
                </CopyToClipboardButton>
              </div>
            </div>
          )}
        </>
      )}

      <form className="max-w-md pt-4" onSubmit={verifyTotp}>
        <div className="pb-2 text-sm text-primary-600">
          Enter the 6-digit code from your authenticator app
        </div>
        <div className="flex items-center gap-2">
          <Input
            name="totp-code"
            id="totp-code"
            type="text"
            placeholder="Enter code"
            autoFocus
          />
          <Button isLoading={isVerifyInProgress} type="submit">
            Verify
          </Button>
        </div>
      </form>
      {errors && <ClerkErrors errors={errors} />}
    </>
  );
}

export { AddMfa };
