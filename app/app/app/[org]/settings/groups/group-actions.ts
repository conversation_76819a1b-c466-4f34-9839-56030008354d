"use server";

import { z } from "zod";
import { groupSchema, userSchema } from "@braintrust/core/typespecs";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import { getHelperQuery } from "#/pages/api/user/_util";
import { aclCheckAdditionalRowsQuery as rolesAclCheckAdditionalRolesQuery } from "#/pages/api/role/_constants";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

const groupSummarySchema = groupSchema.pick({
  id: true,
  name: true,
  description: true,
});

const groupMembersSummarySchema = z.object({
  groupId: groupSchema.shape.id,
  users: z.array(
    z.object({
      id: userSchema.shape.id,
      given_name: userSchema.shape.given_name,
      family_name: userSchema.shape.family_name,
      email: userSchema.shape.email,
      is_group_member: z.boolean(),
      is_inherited_group_member: z.boolean(),
    }),
  ),
});

const groupRoleDescriptionsSchema = z.record(z.string(), z.string());

export type GroupMembersSummary = z.infer<typeof groupMembersSummarySchema>;

export async function getGroupsSummary(
  { org_name }: { org_name: string },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!org_name) {
    return [];
  }

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "group",
      aclPermission: "read",
    },
    filters: {
      org_name,
    },
  });

  const fullQuery = `
  with full_groups_set as (
    ${fullResultSetQuery})
    select id, name, description from full_groups_set;
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);
  return groupSummarySchema.array().parse(rows ?? []);
}

export async function getGroupMembers(
  {
    org_name,
    group_name,
  }: {
    org_name: string;
    group_name: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<GroupMembersSummary | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!org_name || !group_name) {
    return null;
  }

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "group",
      aclPermission: "read",
    },
    filters: {
      name: group_name,
      org_name,
    },
  });

  const usersQuery = getHelperQuery({
    queryParams,
    params: { org_name: [org_name] },
    authLookup,
  });

  const groupNameParam = queryParams.add(group_name);

  const fullQuery = `
  with single_group_set as (
    ${fullResultSetQuery}
  ),
  org_users AS (
    ${usersQuery}
  ),
  augmented_org_users as (
    select
        org_users.*,
        (group_users.user_id is not null) as is_group_member,
        (_expanded_group_members.user_group_id is not null and group_users.user_id is null) as is_inherited_group_member
    from
        org_users join single_group_set on true
        left join group_users on group_users.user_id = org_users.id and group_users.group_id = single_group_set.id
        left join _expanded_group_members on (
            _expanded_group_members.user_object_type = 'user'
            and _expanded_group_members.user_group_id = org_users.id
            and _expanded_group_members.group_id = single_group_set.id
        )
    ),
  group_info AS (
    select id, name
    from groups
    where name = ${groupNameParam}
)
select
    single_group_set.id "groupId",
    json_agg(
      json_build_object(
          'id', augmented_org_users.id,
          'given_name', augmented_org_users.given_name,
          'family_name', augmented_org_users.family_name,
          'email', augmented_org_users.email,
          'is_group_member', augmented_org_users.is_group_member,
          'is_inherited_group_member', augmented_org_users.is_inherited_group_member
      )
  ) users
from
    augmented_org_users join single_group_set on true
group by
    single_group_set.id;`;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);

  if (!rows || rows.length === 0) {
    return null;
  }

  return groupMembersSummarySchema.parse(rows[0]);
}

export async function getGroupRoleDescriptions(
  { orgName }: { orgName: string },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!orgName) {
    return null;
  }

  const { query: groupsQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "group",
      aclPermission: "read",
    },
    filters: {
      org_name: orgName,
    },
  });

  const { query: rolesQuery } = makeFullResultSetQuery({
    authLookup,
    startingParams: queryParams,
    permissionInfo: {
      aclObjectType: "role",
      aclPermission: "read",
      aclCheckAdditionalRowsQuery: rolesAclCheckAdditionalRolesQuery,
    },
  });

  const fullQuery = `
    with
    full_groups_set as (${groupsQuery}),
    full_roles_set as (${rolesQuery}),
    role_descriptions_per_group as (
        select
            full_groups_set.id group_id,
            array_agg(full_roles_set.description) role_descriptions
        from
            full_groups_set
            join acls on full_groups_set.id = acls.group_id
            join full_roles_set on acls.role_id = full_roles_set.id
        group by
            full_groups_set.id
    )
    select jsonb_build_object(group_id, role_descriptions[1]) result
    from role_descriptions_per_group
    where
        cardinality(role_descriptions) = 1
        and role_descriptions[1] is not null
`;

  //console.log("Running query\n", substituteParamsDebug(fullQuery, queryParams.params));
  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);

  if (!rows || rows.length === 0) {
    return null;
  }

  const result = {};
  rows.forEach((row) => {
    Object.assign(result, row.result);
  });

  return groupRoleDescriptionsSchema.parse(result);
}
