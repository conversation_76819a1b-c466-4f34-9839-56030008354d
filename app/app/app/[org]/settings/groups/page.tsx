import { getGroupsSummary } from "./group-actions";
import ClientPage from "./clientpage";
import { decodeURIComponentPatched } from "#/utils/url";
import { type Permission } from "@braintrust/core/typespecs";
import { getOrganization } from "#/app/app/actions";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";

export default async function Page(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const groups = await getGroupsSummary({ org_name });
  const org = await getOrganization({ org_name });

  let orgGroupPermissions: Permission[] = [];
  if (org) {
    orgGroupPermissions =
      (await getObjectAclPermissions({
        objectType: "organization",
        objectId: org.id,
        overrideRestrictObjectType: "group",
      })) ?? [];
  }

  return (
    <ClientPage
      groups={groups}
      orgName={org_name}
      orgGroupPermissions={orgGroupPermissions}
    />
  );
}
