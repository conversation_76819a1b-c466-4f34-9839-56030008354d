import { getOrganization } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { type Permission } from "@braintrust/core/typespecs";
import ClientPage from "./clientpage";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";

export default async function Page(props: {
  params: Promise<{ org: string }>;
}) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let orgPermissions: Permission[] = [];

  try {
    const orgPerms = await getObjectAclPermissions({
      objectType: "organization",
      objectId: org?.id,
      overrideRestrictObjectType: "organization",
    });

    orgPermissions = orgPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on environments settings page", e);
  }

  return <ClientPage orgPermissions={orgPermissions} />;
}
