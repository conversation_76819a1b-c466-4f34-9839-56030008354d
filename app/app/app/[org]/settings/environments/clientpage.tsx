"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { But<PERSON> } from "#/ui/button";
import { Input } from "#/ui/input";
import { Loading } from "#/ui/loading";
import { smartTimeFormat } from "#/ui/date";
import { useState } from "react";
import { toast } from "sonner";
import { type Permission, type Environment } from "@braintrust/core/typespecs";
import { InfoBanner } from "#/ui/info-banner";
import { PlusIcon, PencilLineIcon, Trash2Icon } from "lucide-react";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import TextArea from "#/ui/text-area";
import { useEnvironments } from "#/utils/environments/use-environments";
import { cn } from "#/utils/classnames";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { NULL_DASH } from "#/ui/table/formatters/null-formatter";

const environmentFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().min(1, "Slug is required"),
  description: z.string().optional(),
});

type EnvironmentFormData = z.infer<typeof environmentFormSchema>;

export default function EnvironmentsPage({
  orgPermissions,
}: {
  orgPermissions: Permission[];
}) {
  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");

  const {
    data: environments,
    isPending: loading,
    createEnvironment,
    updateEnvironment,
    deleteEnvironment,
  } = useEnvironments();

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [environmentToDelete, setEnvironmentToDelete] =
    useState<Environment | null>(null);
  const [environmentToEdit, setEnvironmentToEdit] =
    useState<Environment | null>(null);

  const isAllowedToEdit = orgPermissions?.includes("update");

  const handleCreateEnvironment = async (formData: Partial<Environment>) => {
    if (!formData.name || !formData.slug) {
      toast.error("Name and slug are required");
      return;
    }

    try {
      await createEnvironment({
        name: formData.name,
        slug: formData.slug,
        description: formData.description || undefined,
      });
      setShowCreateDialog(false);
      toast.success("Environment created successfully");
    } catch (error) {
      toast.error(`Failed to create environment: ${error}`);
    } finally {
      // No loading state needed since dialog handles it internally
    }
  };

  const handleDeleteEnvironment = async (environment: Environment) => {
    try {
      await deleteEnvironment(environment.id);
      toast.success("Environment deleted successfully");
    } catch (error) {
      toast.error("Failed to delete environment");
    }
    setEnvironmentToDelete(null);
  };

  const handleUpdateEnvironment = async (
    environment: Environment,
    updates: Partial<Environment>,
  ) => {
    try {
      await updateEnvironment(environment.id, updates);
      toast.success("Environment updated successfully");
    } catch (error) {
      toast.error("Failed to update environment");
    }
  };

  if (loading) {
    return <Loading className="pt-6" />;
  }

  // TODO: change this to a minversion message when the feature is fully rolled out
  if (!isEnvironmentsEnabled) {
    return (
      <div>
        <h2 className="mb-2 text-lg font-semibold">Environments</h2>
        <span className="text-sm text-primary-600">
          Environment management not available.
        </span>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="mb-1 text-lg font-semibold">Environments</h2>
          <div className="mb-6 text-sm text-primary-600">
            Tag and organize resources with environments for this organization
          </div>
        </div>
        {isAllowedToEdit && environments.length > 0 && (
          <Button
            size="sm"
            onClick={() => {
              setShowCreateDialog(true);
            }}
            Icon={PlusIcon}
          >
            Environment
          </Button>
        )}
      </div>

      {!isAllowedToEdit && (
        <InfoBanner className="mb-6">
          To manage environments, ask your administrator to grant the{" "}
          <span className="font-medium">Manage settings</span> permission for
          this organization.
        </InfoBanner>
      )}

      {environments.length > 0 ? (
        <Table className="mt-6">
          <TableHeader>
            <TableRow>
              <TableHead className="w-48">Name</TableHead>
              <TableHead className="w-32">Slug</TableHead>
              <TableHead className="flex-1">Description</TableHead>
              <TableHead className="w-32">Created</TableHead>
              {isAllowedToEdit && <TableHead className="w-16"></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {environments.map((env) => (
              <TableRow
                key={env.id}
                className={cn("py-2", isAllowedToEdit && "hover:bg-primary-50")}
              >
                <TableCell className="w-48 font-medium">
                  <span className="block truncate">{env.name}</span>
                </TableCell>
                <TableCell className="w-32 font-mono text-sm text-primary-600">
                  <span className="block truncate">{env.slug}</span>
                </TableCell>
                <TableCell className="flex-1 text-sm text-primary-700">
                  <span className="block truncate">
                    {env.description || <span>{NULL_DASH}</span>}
                  </span>
                </TableCell>
                <TableCell className="w-32 text-sm">
                  {env.created ? (
                    <time dateTime={env.created} title={env.created}>
                      {smartTimeFormat(new Date(env.created).getTime())}
                    </time>
                  ) : (
                    <span className="text-primary-400">{NULL_DASH}</span>
                  )}
                </TableCell>
                {isAllowedToEdit && (
                  <TableCell className="w-16 text-right">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEnvironmentToEdit(env);
                        }}
                        title="Edit environment"
                        Icon={PencilLineIcon}
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="size-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEnvironmentToDelete(env);
                        }}
                        title="Delete environment"
                        Icon={Trash2Icon}
                      />
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="py-12 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="space-y-2">
              <p className="text-base font-medium text-primary-700">
                No environments yet
              </p>
              <p className="text-sm text-primary-600">
                Create your first environment to get started
              </p>
            </div>
            {isAllowedToEdit && (
              <Button
                size="sm"
                onClick={() => {
                  setShowCreateDialog(true);
                }}
                Icon={PlusIcon}
              >
                Create environment
              </Button>
            )}
          </div>
        </div>
      )}

      {showCreateDialog && (
        <EnvironmentDialog
          onOpenChange={setShowCreateDialog}
          environment={null}
          onSave={async (formData) => {
            await handleCreateEnvironment(formData);
          }}
        />
      )}

      <ConfirmationDialog
        open={!!environmentToDelete}
        onOpenChange={(open) => {
          if (!open) setEnvironmentToDelete(null);
        }}
        onConfirm={() => {
          if (environmentToDelete) {
            handleDeleteEnvironment(environmentToDelete);
          }
        }}
        title="Delete environment"
        description={`Are you sure you want to delete "${environmentToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
      />

      {environmentToEdit && (
        <EnvironmentDialog
          onOpenChange={() => setEnvironmentToEdit(null)}
          environment={environmentToEdit}
          onSave={async (updates) => {
            await handleUpdateEnvironment(environmentToEdit, updates);
            setEnvironmentToEdit(null);
          }}
        />
      )}
    </div>
  );
}

interface EnvironmentDialogProps {
  onOpenChange: (open: boolean) => void;
  environment: Environment | null;
  onSave: (updates: Partial<Environment>) => Promise<void>;
}

function EnvironmentDialog({
  onOpenChange,
  environment,
  onSave,
}: EnvironmentDialogProps) {
  const isEdit = !!environment;
  const action = isEdit ? "Edit" : "Create";

  const [saving, setSaving] = useState(false);

  const form = useForm<EnvironmentFormData>({
    resolver: zodResolver(environmentFormSchema),
    defaultValues: {
      name: environment?.name || "",
      slug: environment?.slug || "",
      description: environment?.description || "",
    },
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    setSaving(true);

    try {
      await onSave({
        name: data.name,
        slug: data.slug,
        description: data.description || null,
      });
    } finally {
      setSaving(false);
    }
  });

  return (
    <Dialog open={true} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>{action} environment</DialogTitle>
          {!isEdit && (
            <DialogDescription>
              Create a new environment configuration for your organization.
            </DialogDescription>
          )}
        </DialogHeader>
        <Form {...form}>
          <form className="flex flex-col gap-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-3">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter env name"
                        autoFocus
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Eg. Development, Staging, Production
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter env slug" {...field} />
                    </FormControl>
                    <FormDescription>Eg. dev, staging, prod</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (optional)</FormLabel>
                  <FormControl>
                    <TextArea
                      placeholder="Enter a brief description of this environment"
                      rows={3}
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="ghost"
                onClick={() => onOpenChange(false)}
                size="sm"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={saving}
                isLoading={saving}
                size="sm"
              >
                {isEdit ? "Save changes" : "Create environment"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
