"use client";

import { useOrg } from "#/utils/user";
import React, { useEffect, useCallback, useState } from "react";

import "react-image-crop/dist/ReactCrop.css";
import { Checkbox } from "#/ui/checkbox";
import { type fetchGitMetadata, type upsertGitMetadata } from "./actions";
import { type Permission, type RepoInfo } from "@braintrust/core/typespecs";
import { Switch } from "#/ui/switch";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

const GIT_FIELDS: { [name in keyof RepoInfo]: string } = {
  commit: "Commit hash",
  branch: "Branch",
  tag: "Tag",
  author_name: "Author name",
  author_email: "Author email",
  commit_message: "Commit message",
  commit_time: "Commit time",
  dirty: "Is dirty",
  git_diff: "Diff",
} as const;

type GitFields = Array<keyof typeof GIT_FIELDS>;

type CollectMetadata = "all" | "none" | "some";

type GitMetadata = {
  collect: CollectMetadata;
  fields?: GitFields;
};

export default function Page({
  orgPermissions,
}: {
  orgPermissions: Permission[];
}) {
  const { getToken } = useAuth();
  const [gitMetadata, setGitMetadata] = useState<GitMetadata | null>(null);

  const org = useOrg();

  useEffect(() => {
    if (!org.id) {
      return;
    }
    const run = async () => {
      const gitMetadata = await invokeServerAction<typeof fetchGitMetadata>({
        fName: "fetchGitMetadata",
        args: {
          orgId: org.id,
        },
        getToken,
      });
      setGitMetadata(gitMetadata ?? { collect: "all", fields: [] });
    };
    run();
  }, [org.id, getToken]);

  const updateSetting = useCallback(
    async (updated: GitMetadata) => {
      try {
        const result = await invokeServerAction<typeof upsertGitMetadata>({
          fName: "upsertGitMetadata",
          args: {
            orgId: org.id,
            gitMetadata: updated,
          },
          getToken,
        });
        setGitMetadata(result);
      } catch (error) {
        toast.error(`Failed to update git metadata`, {
          description: `${error}`,
        });
      }
    },
    [org.id, getToken],
  );

  if (!gitMetadata) return null;

  const isAllowedToUpdateLoggingSettings = orgPermissions.includes("update");

  if (!isAllowedToUpdateLoggingSettings) {
    return (
      <TableEmptyState label='To edit logging settings, ask your administrator to grant the "Manage settings" permission for this organization.' />
    );
  }

  return (
    <>
      <h2 className="mb-2 text-lg font-semibold">Metadata</h2>
      <div className="mb-4 flex items-center gap-3">
        <Switch
          id="collect-git-metadata"
          checked={gitMetadata.collect !== "none"}
          onClick={() => {
            updateSetting({
              fields: gitMetadata.fields,
              collect: gitMetadata.collect === "none" ? "all" : "none",
            });
          }}
        />
        <label htmlFor="collect-git-metadata">Collect git metadata</label>
      </div>
      {gitMetadata.collect !== "none" && (
        <div className="ml-11">
          <div className="mb-2 text-sm text-primary-600">Select fields</div>
          {Object.entries(GIT_FIELDS).map(([field, displayName]) => (
            <div className="mb-2 flex items-center gap-2 text-sm" key={field}>
              <Checkbox
                id={field}
                type="checkbox"
                label={displayName}
                onChange={(e) => {
                  const updated: GitMetadata = {
                    collect: "some",
                    fields:
                      gitMetadata.collect === "all"
                        ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                          (Object.keys(GIT_FIELDS) as (keyof RepoInfo)[])
                        : [...(gitMetadata.fields || [])],
                  };

                  updated.fields = (updated.fields || []).filter(
                    (x) => x !== field,
                  );
                  if (e.target.checked) {
                    updated.fields = updated.fields.concat([
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                      field as keyof typeof GIT_FIELDS,
                    ]);
                  }
                  updateSetting(updated);
                }}
                checked={
                  gitMetadata.collect === "all" ||
                  gitMetadata.fields?.includes(
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    field as keyof typeof GIT_FIELDS,
                  ) ||
                  false
                }
              />
            </div>
          ))}
        </div>
      )}
    </>
  );
}
