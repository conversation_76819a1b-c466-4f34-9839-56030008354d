"use client";

import {
  Command<PERSON><PERSON><PERSON>,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "#/ui/command";
import { useOrg, useUser } from "#/utils/user";
import { getOrgLink, getOrgSettingsLink } from "./getOrgLink";
import {
  Activity,
  AlertCircleIcon,
  Asterisk,
  BadgeCheck,
  Beaker,
  Bolt,
  BookOpen,
  ChartSpline,
  CornerDownRight,
  Database,
  Folder,
  Home,
  MessageCircle,
  Newspaper,
  Percent,
  Route,
  Rss,
  Shapes,
  SlidersHorizontal,
} from "lucide-react";
import {
  orgSettingsItems,
  personalSettingsItems,
} from "./settings/sidebar-nav";
import { type getProjectSummary } from "./org-actions";
import { useQueryFunc } from "#/utils/react-query";
import {
  getProjectConfigurationLink,
  getProjectLink,
} from "./p/[project]/getProjectLink";
import { commandBar<PERSON>penAtom } from "./sidenav-state";
import { useAtom } from "jotai";
import { useHotkeys } from "react-hotkeys-hook";
import { usePara<PERSON>, usePathname, useRouter } from "next/navigation";
import { getMonitorLink } from "./monitor/getMonitorLink";
import { getProjectLogsLink } from "./p/[project]/logs/getProjectLogsLink";
import { getPlaygroundsLink } from "./prompt/[prompt]/getPromptLink";
import { getDatasetsLink } from "./p/[project]/datasets/[dataset]/getDatasetLink";
import { projectConfigurationNavItems } from "./p/[project]/configuration/sidebar-nav";
import { useFeatureFlags } from "#/lib/feature-flags";
import { decodeURIComponentPatched } from "#/utils/url";
import { getExperimentsLink } from "./p/[project]/experiments/[experiment]/getExperimentLink";
import { getProjectHref } from "#/ui/layout/header-menu";

export const CommandBar = () => {
  const { orgs } = useUser();
  const { id: orgId, name: orgName } = useOrg();

  const params = useParams<{ project?: string }>();
  const projectName = decodeURIComponentPatched(params?.project ?? "");

  const pathname = usePathname();

  const { flags } = useFeatureFlags();

  const orgValues = Object.values(orgs).filter((org) => org.id !== orgId);

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: orgName,
    },
  });

  const [commandBarOpen, setCommandBarOpen] = useAtom(commandBarOpenAtom);

  useHotkeys(
    "Mod+k",
    (e) => {
      // https://braintrustdata.slack.com/archives/C06SC4JFZ4L/p1748197541985709
      if (e.key === "k") {
        setCommandBarOpen((o) => !o);
        e.preventDefault();
      }
    },
    { description: "Open command bar" },
  );

  const router = useRouter();

  const navigateTo = (path: string, newTab?: boolean) => {
    if (newTab) {
      window.open(path, "_blank");
    } else {
      router.push(path);
    }
    setCommandBarOpen(false);
  };

  if (!orgId) {
    return null;
  }

  return (
    <CommandDialog
      open={commandBarOpen}
      onOpenChange={setCommandBarOpen}
      className="h-[500px]"
    >
      <CommandInput
        className="px-0 text-base placeholder:text-primary-400"
        placeholder="What are you looking for?"
        hideIcon
      />
      <CommandList>
        <CommandEmpty className="p-4 text-center text-sm text-primary-400">
          No results found
        </CommandEmpty>
        {projectName && (
          <CommandGroup heading={projectName}>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLink({
                    orgName,
                    projectName,
                  }),
                )
              }
            >
              <Home className="!size-4 text-primary-400" />
              Overview
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLogsLink({
                    orgName,
                    projectName,
                  }),
                )
              }
            >
              <Activity className="!size-4 text-primary-400" />
              Logs
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getPlaygroundsLink({
                    orgName,
                    projectName,
                  }),
                )
              }
            >
              <Shapes className="!size-4 text-primary-400" />
              Playgrounds
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getExperimentsLink({
                    orgName,
                    projectName,
                  }),
                )
              }
            >
              <Beaker className="!size-4 text-primary-400" />
              Experiments
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getDatasetsLink({
                    orgName,
                    projectName,
                  }),
                )
              }
            >
              <Database className="!size-4 text-primary-400" />
              Datasets
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLink({
                    orgName,
                    projectName,
                  }) + "/prompts",
                )
              }
            >
              <MessageCircle className="!size-4 text-primary-400" />
              Prompts
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLink({
                    orgName,
                    projectName,
                  }) + "/tools",
                )
              }
            >
              <Bolt className="!size-4 text-primary-400" />
              Tools
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLink({
                    orgName,
                    projectName,
                  }) + "/scorers",
                )
              }
            >
              <Percent className="!size-4 text-primary-400" />
              Scorers
            </Item>
            <Item
              onSelect={() =>
                navigateTo(
                  getProjectLink({
                    orgName,
                    projectName,
                  }) + "/agents",
                )
              }
            >
              <Route className="!size-4 text-primary-400" />
              Agents
            </Item>
            {projectConfigurationNavItems
              .filter((i) => !i.featureFlag || flags[i.featureFlag])
              .map((i) => (
                <Item
                  key={i.href}
                  onSelect={() =>
                    navigateTo(
                      getProjectConfigurationLink({
                        orgName,
                        projectName,
                      }) + `/${i.href}`,
                    )
                  }
                >
                  <i.Icon className="!size-4 text-primary-400" />
                  {i.title} configuration
                </Item>
              ))}
          </CommandGroup>
        )}
        <CommandGroup heading="Switch project">
          {projects?.map((project) => (
            <Item
              key={project.project_id}
              onSelect={() =>
                navigateTo(
                  getProjectHref({
                    orgName,
                    projectName: project.project_name,
                    pathname,
                  }),
                )
              }
            >
              <CornerDownRight className="!size-4 text-primary-400" />
              {project.project_name}
            </Item>
          ))}
        </CommandGroup>
        {orgValues.length > 0 && (
          <CommandGroup heading="Switch organization">
            {orgValues.map((org) => (
              <Item
                key={org.id}
                onSelect={() => navigateTo(getOrgLink({ orgName: org.name }))}
              >
                <CornerDownRight className="!size-4 text-primary-400" />
                {org.name}
              </Item>
            ))}
          </CommandGroup>
        )}
        <CommandGroup heading={orgName}>
          <Item onSelect={() => navigateTo(getOrgLink({ orgName }))}>
            <Folder className="!size-4 text-primary-400" />
            Projects
          </Item>
          <Item onSelect={() => navigateTo(getMonitorLink({ orgName }))}>
            <ChartSpline className="!size-4 text-primary-400" />
            Monitor
          </Item>
          <Item onSelect={() => navigateTo(getOrgLink({ orgName }) + "/btql")}>
            <Asterisk className="!size-4 text-primary-400" />
            BTQL sandbox
          </Item>
          <Item onSelect={() => navigateTo(getOrgSettingsLink({ orgName }))}>
            <SlidersHorizontal className="!size-4 text-primary-400" />
            Settings
          </Item>
        </CommandGroup>
        <CommandGroup heading={`${orgName} organization settings`}>
          {orgSettingsItems.map((item) => (
            <Item
              key={item.href}
              onSelect={() =>
                navigateTo(`${getOrgSettingsLink({ orgName })}/${item.href}`)
              }
            >
              <item.Icon className="!size-4 text-primary-400" />
              {item.title}
            </Item>
          ))}
        </CommandGroup>
        <CommandGroup heading="Personal settings">
          {personalSettingsItems.map((item) => (
            <Item
              key={item.href}
              onSelect={() =>
                navigateTo(`${getOrgSettingsLink({ orgName })}/${item.href}`)
              }
            >
              <item.Icon className="!size-4 text-primary-400" />
              {item.title}
            </Item>
          ))}
        </CommandGroup>
        <CommandGroup heading="Help">
          <Item
            onSelect={() => navigateTo("https://braintrust.dev/docs", true)}
          >
            <BookOpen className="!size-4 text-primary-400" />
            Docs
          </Item>
          <Item
            onSelect={() =>
              navigateTo("https://braintrust.dev/docs/changelog", true)
            }
          >
            <Rss className="!size-4 text-primary-400" />
            Changelog
          </Item>
          <Item
            onSelect={() => navigateTo("https://braintrust.dev/blog", true)}
          >
            <Newspaper className="!size-4 text-primary-400" />
            Blog
          </Item>
          <Item
            onSelect={() => navigateTo("https://status.braintrust.dev", true)}
          >
            <AlertCircleIcon className="!size-4 text-primary-400" />
            Status
          </Item>
          <Item
            onSelect={() => navigateTo("https://trust.braintrust.dev", true)}
          >
            <BadgeCheck className="!size-4 text-primary-400" />
            Trust center
          </Item>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
};

const Item = ({
  children,
  ...props
}: React.ComponentProps<typeof CommandItem>) => {
  return (
    <CommandItem {...props} className="flex gap-2 !py-2 text-sm">
      {children}
    </CommandItem>
  );
};
