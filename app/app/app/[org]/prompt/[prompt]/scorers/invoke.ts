import {
  type FunctionId,
  type InvokeFunctionRequest,
} from "@braintrust/core/typespecs";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { _urljoin } from "@braintrust/core";
import { type BtSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";
import { apiPostCors } from "#/utils/btapi/fetch";

// NOTE: This is a general method, so maybe we want to move it outside of scores.
export async function invoke({
  orgName,
  sessionToken,
  proxyUrl,
  functionId,
  input,
  stream,
  parent,
  mode,
  messages,
  expected,
  metadata,
}: {
  orgName: string;
  sessionToken: BtSessionToken;
  proxyUrl: string;
  functionId: FunctionId;
  input: unknown;
  stream?: boolean;
  parent?: InvokeFunctionRequest["parent"];
  mode?: InvokeFunctionRequest["mode"];
  messages?: InvokeFunctionRequest["messages"];
  expected?: unknown;
  metadata?: Record<string, unknown> | null;
}): Promise<Response | null> {
  if (sessionToken === "loading") {
    toast.error("Session token still loading. Try again in a few seconds.");
    return null;
  }

  const payload: InvokeFunctionRequest = {
    ...functionId,
    input,
    stream,
    parent,
    mode,
    messages,
    expected,
    metadata,
  };

  const result = await apiPostCors({
    url: _urljoin(normalizeProxyUrlBase(proxyUrl), "function", "invoke"),
    sessionToken,
    payload: payload,
    alreadySerialized: false,
    headers: {
      "x-bt-org-name": orgName,
    },
  });
  if (!result.ok) {
    const text = await result.text();
    console.error(result.status, result.statusText, text);
    throw new Error(`Failed to invoke function: ${text}`);
  }

  return result;
}
