import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "#/ui/dialog";
import { Loading } from "#/ui/loading";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import { ImportWizard } from "../../p/[project]/datasets/[dataset]/importWizard";
import { useCreateDatasetToUpload } from "../../p/[project]/datasets/[dataset]/useCreateDatasetToUpload";
import { type ImportProgressType, UploadContext } from "#/ui/upload-provider";

interface UploadDatasetDialogProps {
  onSuccessfullyUploaded: (createdDataset: {
    project_id: string;
    id: string;
  }) => Promise<void>;
  promptName: string;
  projectName: string;
  orgId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UploadDatasetDialog({
  onSuccessfullyUploaded,
  open,
  onOpenChange,
  promptName,
  projectName,
  orgId,
}: UploadDatasetDialogProps) {
  const { importProgress, setImportProgress, executeImport } =
    useContext(UploadContext);
  const [dataToUpload, setDataToUpload] = useState<Record<string, unknown>[]>(
    [],
  );

  const { createdDataset, createDataset, isCreatingDataset, insertRows } =
    useCreateDatasetToUpload();

  const setImportProgressPartial = (params: Partial<ImportProgressType>) => {
    setImportProgress?.((oldState) => ({ ...oldState, ...params }));
  };
  const isImportRunning = useRef(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (createdDataset && !isImportRunning.current && insertRows) {
      const importData = async () => {
        isImportRunning.current = true;
        setErrorMessage(null);
        try {
          await executeImport({
            data: dataToUpload,
            insertRows,
            setImportProgress: setImportProgressPartial,
          });
          await onSuccessfullyUploaded(createdDataset);
        } catch (e) {
          console.error(e);
          setErrorMessage(
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            (e as Error)?.message || `Error while importing dataset`,
          );
        }
        isImportRunning.current = false;
        onOpenChange(false);
      };
      importData();
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createdDataset, insertRows]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-full overflow-auto sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>Upload dataset</DialogTitle>
        </DialogHeader>
        {isCreatingDataset ? (
          <Loading />
        ) : (
          <ImportWizard
            setDataToImport={({ data, file }) => {
              if (!orgId) throw new Error(`orgId is not defined`);
              createDataset({
                orgId,
                projectName,
                datasetName:
                  promptName + " " + file.name + " " + new Date().valueOf(),
              });
              setDataToUpload(data);
            }}
            importProgress={importProgress}
            setImportProgress={setImportProgressPartial}
          />
        )}
        {errorMessage && (
          <div className="px-3 py-4 text-center text-bad-700">
            {errorMessage}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

export const useUploadDatasetDialog = (
  params: Omit<UploadDatasetDialogProps, "open" | "onOpenChange">,
) => {
  const [isModalOpened, setIsModalOpened] = useState(false);

  return {
    open: useCallback(() => {
      setIsModalOpened(true);
    }, []),
    modal: isModalOpened ? (
      <UploadDatasetDialog
        open={isModalOpened}
        onOpenChange={setIsModalOpened}
        {...params}
      />
    ) : null,
  };
};
