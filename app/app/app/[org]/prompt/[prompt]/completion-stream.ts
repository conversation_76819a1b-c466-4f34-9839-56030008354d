import { BraintrustStream, type BraintrustStreamChunk } from "braintrust";
import {
  type SSEConsoleEventData,
  sseConsoleEventDataSchema,
} from "@braintrust/core/typespecs";
import { zodErrorToString } from "#/utils/validation";
import { z } from "zod";

export function applyBraintrustSSETransformStreams<
  T extends Uint8Array | BraintrustStreamChunk,
>({
  readable,
  setIsJSON,
  onError,
  setCurrentStatus,
  addConsoleMessages,
  addReasoning,
  onTaskDone,
}: {
  readable: ReadableStream<T>;
  setIsJSON: (isJSON: boolean) => void;
  onError: (error: Error) => void;
  setCurrentStatus?: (status: string) => void;
  addConsoleMessages?: (messages: SSEConsoleEventData[]) => void;
  addReasoning?: (thought: string) => void;
  onTaskDone?: () => void;
}): ReadableStream<Uint8Array> {
  const braintrustStream = new BraintrustStream(
    // Typescript doesn't like the union
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    readable as unknown as ReadableStream,
  );

  const encoder = new TextEncoder();
  let prevAutoChunkType: BraintrustStreamChunk["type"] | null = null;

  return braintrustStream.toReadableStream().pipeThrough(
    new TransformStream({
      transform(chunk: BraintrustStreamChunk, controller) {
        setCurrentStatus?.(
          chunk.type === "reasoning_delta" ? "Thinking" : "Streaming",
        );
        if (chunk.type === "progress") {
          setCurrentStatus?.(
            `Running ${chunk.data.object_type} ${chunk.data.name}`,
          );

          if (chunk.data.event === "console") {
            const parsed = sseConsoleEventDataSchema.safeParse(
              JSON.parse(chunk.data.data),
            );
            if (parsed.success) {
              addConsoleMessages?.([parsed.data]);
            } else {
              console.warn(
                `Failed to parse console event: ${zodErrorToString(
                  parsed.error,
                  2,
                  true,
                )}`,
              );
            }
          } else if (chunk.data.event === "error") {
            const parsed = z.string().safeParse(JSON.parse(chunk.data.data));
            if (parsed.success) {
              addConsoleMessages?.([
                { stream: "stderr", message: parsed.data },
              ]);
            } else {
              console.warn(
                `Failed to parse error event: ${zodErrorToString(
                  parsed.error,
                  2,
                  true,
                )}`,
              );
            }
          }

          return;
        } else if (chunk.type === "error") {
          onError(new Error(chunk.data));
          return;
        }

        if (chunk.type === "console") {
          addConsoleMessages?.([chunk.data]);
          return;
        }

        if (chunk.type === "done") {
          onTaskDone?.();
          return;
        }

        if (chunk.type === "reasoning_delta") {
          addReasoning?.(chunk.data);
          return;
        }

        // skip non-text/json chunks from showing in the UI
        if (!["text_delta", "json_delta"].includes(chunk.type)) {
          return;
        }

        if (prevAutoChunkType && prevAutoChunkType !== chunk.type) {
          controller.enqueue(encoder.encode("\n"));
        }
        controller.enqueue(encoder.encode(chunk.data));
        prevAutoChunkType = chunk.type;
        setIsJSON(chunk.type === "json_delta");
      },
    }),
  );
}
