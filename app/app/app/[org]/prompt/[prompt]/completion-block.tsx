"use client";

import { cn } from "#/utils/classnames";
import { isEmpty } from "#/utils/object";
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  type FunctionId,
  type InvokeFunctionRequest,
  type Message,
  type SSEConsoleEventData,
} from "@braintrust/core/typespecs";
import { MarkdownViewer } from "#/ui/markdown";
import { applyBraintrustSSETransformStreams } from "./completion-stream";
import { useSessionToken } from "#/utils/auth/session-token";
import { type Score } from "autoevals";
import { Spinner } from "#/ui/icons/spinner";
import { invoke } from "./scorers/invoke";
import { BlockCell } from "./block-cell";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { Brain, Plus } from "lucide-react";
import { Button } from "#/ui/button";

export type CompletionBlockFunction = {
  type: "function";
  functionId: FunctionId;
  input: unknown;
  parent?: InvokeFunctionRequest["parent"];
  messages?: Message[];
};

export interface CompletionBlockHandle {
  submit(prompt: CompletionBlockFunction, orgName: string): void;
}

export interface MergedCompletion {
  // isJSON is used _only_ so that we know what type the completion would be
  // if you ran a full eval, so that when you're editing scorers, you have the
  // correct type to play with, and it will be consistent with the type that's
  // used in an eval.
  completion: string;
  error?: string | null;
  isJSON: boolean;

  scores: Score[];
  consoleMessages: SSEConsoleEventData[];
  reasoning?: string;

  streamingStatus?: string;

  start?: number;
  end?: number;
}

interface CompletionBlockProps {
  proxyUrl: string;
  onAddMessageToPrompt?: (message: Message) => void;
}

export const CompletionBlock = forwardRef<
  CompletionBlockHandle,
  CompletionBlockProps
>(({ proxyUrl, onAddMessageToPrompt }, ref) => {
  const { getOrRefreshToken } = useSessionToken();

  const [streaming, setStreaming] = useState(false);
  const [mergedCompletion, setMergedCompletion] = useState<
    MergedCompletion | undefined
  >(undefined);

  const addConsoleMessages = useCallback(
    (messages: SSEConsoleEventData[]) => {
      setMergedCompletion((prev) => ({
        ...defaultMergedCompletion(),
        ...prev,
        consoleMessages: [...(prev?.consoleMessages ?? []), ...messages],
      }));
    },
    [setMergedCompletion],
  );

  const addReasoning = useCallback(
    (thought: string) => {
      setMergedCompletion((prev) => ({
        ...defaultMergedCompletion(),
        ...prev,
        reasoning: (prev?.reasoning ?? "") + thought,
      }));
    },
    [setMergedCompletion],
  );

  // This ref is important so that we have an up-to-date value we can propagate along
  // when we save the completion, which also happens to be in a callback defined via a ref
  // (so cannot be updated in time by a state variable).
  const isJSONRef = useRef(false);
  const setIsJSON = useCallback(
    (isJSON: boolean) => {
      isJSONRef.current = isJSON;
      setMergedCompletion((prev) => ({
        ...defaultMergedCompletion(),
        ...prev,
        isJSON,
      }));
    },
    [setMergedCompletion],
  );

  const setError = useCallback(
    (error: Error | undefined) => {
      setMergedCompletion((prev) => ({
        ...defaultMergedCompletion(),
        ...prev,
        error: error?.message ?? undefined,
      }));
    },
    [setMergedCompletion],
  );

  const setStreamingStatus = useCallback(
    (status: string) => {
      setMergedCompletion((prev) => ({
        ...defaultMergedCompletion(),
        ...prev,
        streamingStatus: status,
      }));
    },
    [setMergedCompletion],
  );

  const onFinish = useCallback(
    async (completion: string, start: number, end: number) => {
      setMergedCompletion((prev) => ({
        scores: [],
        isJSON: false,
        consoleMessages: [],
        ...prev,
        completion,
        start: start / 1000,
        end: end / 1000,
      }));
      setStreaming(false);
    },
    [setMergedCompletion],
  );
  const onError = useCallback(
    (error: Error) => {
      console.error(error);
      setError(error);
      setStreaming(false);
    },
    [setError],
  );

  useImperativeHandle(
    ref,
    () => ({
      submit(functionParams: CompletionBlockFunction, orgName: string) {
        const start = Date.now();
        setMergedCompletion((prev) => ({
          isJSON: false,
          scores: [],
          ...prev,
          completion: " ", // empty space to avoid displaying "Empty result received, potentially due to rate limits or token limits."
          reasoning: "",
          consoleMessages: [],
          end: undefined,
        }));

        if (!proxyUrl) {
          setError(
            new Error("Proxy URL unset (this is a configuration issue)"),
          );
          return;
        }

        setStreaming(true);

        (async () => {
          const sessionToken = await getOrRefreshToken();
          const response = await invoke({
            orgName,
            sessionToken,
            proxyUrl,
            stream: true,
            ...functionParams,
          });

          let body = response?.body;
          if (!body) {
            throw new Error("No response body");
          }

          setError(undefined);

          body = applyBraintrustSSETransformStreams({
            readable: body,
            setIsJSON,
            setCurrentStatus: (status: string) => {
              setStreamingStatus(status);
            },
            addReasoning,
            addConsoleMessages,
            onError: onError,
          });

          const decoder = new TextDecoder();
          let result = "";
          const reader = body.getReader();
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              break;
            }

            // Update the completion state with the new message tokens.
            result += decoder.decode(value);
            setMergedCompletion((prev) => ({
              ...defaultMergedCompletion(),
              ...prev,
              completion: result,
            }));
          }

          onFinish(result, start, Date.now());
        })()
          .catch(onError)
          .finally(() => setStreaming(false));
      },
    }),
    [
      setMergedCompletion,
      proxyUrl,
      setError,
      onError,
      getOrRefreshToken,
      setIsJSON,
      addConsoleMessages,
      addReasoning,
      onFinish,
      setStreamingStatus,
    ],
  );

  const error = mergedCompletion?.error;
  const showError = !!error;

  const timeDiff =
    mergedCompletion?.end && mergedCompletion?.start
      ? mergedCompletion?.end - mergedCompletion?.start
      : undefined;

  const consoleMessages = mergedCompletion?.consoleMessages ?? [];
  const showEmpty =
    mergedCompletion?.completion === "" &&
    !isEmpty(mergedCompletion.end) &&
    !showError;
  const reasoning = mergedCompletion?.reasoning ?? "";

  return (
    <div className={cn("group/datarow flex min-w-full")}>
      <BlockCell>
        {(streaming || timeDiff) && (
          <div className="h-8 flex-none truncate align-middle text-xs leading-8 text-primary-600">
            {streaming ? (
              <div className="flex items-center gap-1">
                <Spinner className="size-3" />
                {mergedCompletion?.streamingStatus ?? "Executing"}
              </div>
            ) : (
              <span>{timeDiff?.toFixed(2)}s</span>
            )}
          </div>
        )}
        <div className={cn("flex flex-col text-sm")}>
          {showError && (
            <div
              className={cn(
                "font-mono text-xs text-bad-700 text-left cursor-default mb-2 whitespace-pre-wrap",
              )}
            >
              {`${error}`}
            </div>
          )}
          <div className="mb-2">
            {consoleMessages.length > 0 && mergedCompletion?.completion && (
              <div className="mb-1 text-xs text-primary-500">Text output</div>
            )}
            {reasoning && (
              <div className="mb-3">
                <CollapsibleSection
                  className="inline-flex flex-none text-primary-600"
                  title={
                    <>
                      <Brain className="mr-1 size-3" /> Reasoning
                    </>
                  }
                >
                  <div className="text-primary-500">{reasoning}</div>
                </CollapsibleSection>
              </div>
            )}
            <CompletedValue
              value={mergedCompletion?.completion}
              explicitEmpty={showEmpty}
            />
            {mergedCompletion?.completion &&
              !streaming &&
              onAddMessageToPrompt && (
                <Button
                  size="xs"
                  Icon={Plus}
                  className="mt-3"
                  onClick={() =>
                    onAddMessageToPrompt({
                      role: "assistant",
                      content: mergedCompletion.completion,
                    })
                  }
                >
                  Add to prompt
                </Button>
              )}
            {consoleMessages.length > 0 && (
              <div className="mb-1 mt-2 text-xs text-primary-500">
                Execution log
              </div>
            )}
            <ConsoleMessages
              messages={mergedCompletion?.consoleMessages ?? []}
            />
          </div>
        </div>
      </BlockCell>
    </div>
  );
});
CompletionBlock.displayName = "CompletionBlock";

function CompletedValue({
  value,
  explicitEmpty,
}: {
  value: string | undefined;
  explicitEmpty?: boolean;
}) {
  if (explicitEmpty) {
    return (
      <div className="mb-1 text-xs text-primary-500">
        Empty result received, potentially due to rate limits or token limits.
      </div>
    );
  }

  return (
    <MarkdownViewer
      value={value ?? ""}
      className="whitespace-normal py-0 prose-p:whitespace-pre-line"
    />
  );
}

function ConsoleMessages({ messages }: { messages: SSEConsoleEventData[] }) {
  if (messages.length === 0) {
    return null;
  }

  return (
    <div className="whitespace-pre-wrap font-mono text-xs text-primary-700">
      {messages.map((m, i) => (
        <div
          key={i}
          className={cn("flex items-baseline gap-1", {
            // TODO: when we can detect console.error
            // "text-bad-700": isError
          })}
        >
          <span className="flex-none text-primary-400">{">"}</span>
          {m.message}
        </div>
      ))}
    </div>
  );
}

function defaultMergedCompletion(): MergedCompletion {
  return {
    scores: [],
    completion: "",
    isJSON: false,
    consoleMessages: [],
  };
}
