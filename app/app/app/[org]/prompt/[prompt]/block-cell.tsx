import { cn } from "#/utils/classnames";
import { type PropsWithChildren } from "react";

export type BlockCellContext = "promptModal";

export const BlockCell = ({ children }: PropsWithChildren<{}>) => {
  return (
    <div
      className={cn(
        "group/blockcell relative flex transition-colors overflow-hidden flex-1 min-w-[20rem] px-2 flex-col px-0",
      )}
    >
      {children}
    </div>
  );
};
