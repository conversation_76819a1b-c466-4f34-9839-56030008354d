// Permissions checkboxes for the project-level modal. These permissions
// generally grant access to all sub-objects of a particular object type
// category, e.g. all experiments or all datasets.

import { z } from "zod";

import {
  type AclObjectType,
  aclObjectTypeEnum,
} from "@braintrust/core/typespecs";
import {
  type PermissionsRequirement,
  crudPermissions,
  manageAccessPermissions,
  permissionsCheckboxSchema,
  permissionsRequirementKeyStr,
} from "#/ui/permissions/permissions-types";
import { aclSpecs } from "@braintrust/local/app-schema";

export const projectPermissionsObjectTypes: AclObjectType[] =
  aclObjectTypeEnum.options.filter(
    (x) => x === "project" || aclSpecs[x].parentAclObjectTypes[0] === "project",
  );

export const projectPermissionsCheckboxesSchema = z.strictObject(
  Object.fromEntries(
    projectPermissionsObjectTypes.map((x) => [
      x,
      z.strictObject({
        create: permissionsCheckboxSchema,
        read: permissionsCheckboxSchema,
        update: permissionsCheckboxSchema,
        delete: permissionsCheckboxSchema,
        manageAccess: permissionsCheckboxSchema,
      }),
    ]),
  ),
);

export type ProjectPermissionsCheckboxes = z.infer<
  typeof projectPermissionsCheckboxesSchema
>;

export const projectPermissionsRequirements: Record<
  string,
  PermissionsRequirement
> = Object.fromEntries(
  projectPermissionsObjectTypes.flatMap((x) => [
    ...crudPermissions.map((p) => [
      permissionsRequirementKeyStr(x, p),
      {
        objectType: "project",
        permissions: [{ permission: p, restrictObjectType: x }],
      },
    ]),
    [
      permissionsRequirementKeyStr(x, "manageAccess"),
      {
        objectType: "project",
        permissions: manageAccessPermissions.map((p) => ({
          permission: p,
          restrictObjectType: x,
        })),
      },
    ],
  ]),
);
