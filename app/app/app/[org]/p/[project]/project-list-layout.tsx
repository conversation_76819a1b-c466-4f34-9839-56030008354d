import * as React from "react";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import Link from "next/link";
import {
  Activity,
  ArrowUpRight,
  Beaker,
  Bolt,
  Database,
  MessageCircle,
  Percent,
  Route,
  Settings2,
  Shapes,
} from "lucide-react";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { getPlaygroundsLink } from "../../prompt/[prompt]/getPromptLink";
import { getExperimentsLink } from "./experiments/[experiment]/getExperimentLink";
import Footer from "#/ui/landing/footer";
import { getProjectConfigurationLink, getProjectLink } from "./getProjectLink";
import { useFeatureFlags } from "#/lib/feature-flags";
import { getDatasetsLink } from "./datasets/[dataset]/getDatasetLink";
import { getProjectLogsLink } from "./logs/getProjectLogsLink";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { useIsSidenavDocked } from "../../sidenav-state";

export const ProjectNavItem = ({
  href,
  active,
  className,
  activeClassName,
  children,
  onClick,
}: {
  href: string;
  active?: boolean;
  className?: string;
  activeClassName?: string;
  children: React.ReactNode;
  onClick?: () => void;
}) => {
  return (
    <Link
      prefetch
      href={href}
      className={cn(
        buttonVariants({
          variant: "ghost",
          size: "xs",
        }),
        "px-2 gap-1.5 justify-start",
        className,
        {
          [`text-primary-950 pointer-events-none ${activeClassName}`]: active,
        },
      )}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export const ProjectListLayout = ({
  children,
  orgName,
  projectName,
  active,
  scrollContainerRef,
}: React.PropsWithChildren<{
  orgName: string;
  projectName: string;
  active?:
    | "playgrounds"
    | "experiments"
    | "datasets"
    | "prompts"
    | "scorers"
    | "tools"
    | "agents"
    | "configuration";
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
}>) => {
  const { flags, isLoading } = useFeatureFlags();
  const agentsEnabled = !isLoading && flags.agents;

  const isSidenavDocked = useIsSidenavDocked();

  const projectLink = getProjectLink({
    orgName,
    projectName,
  });
  return (
    <MainContentWrapper
      className={cn(
        "flex flex-col overflow-hidden p-0",
        HEIGHT_WITH_TOP_OFFSET,
      )}
      hideFooter
    >
      {!isSidenavDocked && (
        <div className="no-scrollbar flex flex-none gap-1.5 overflow-x-auto px-3 pb-2 pt-1 bg-primary-50">
          <ProjectNavItem
            href={getPlaygroundsLink({
              orgName,
              projectName,
            })}
            className="hover:bg-accent-100 dark:hover:bg-accent-100/50"
            activeClassName="bg-accent-100 dark:bg-accent-100/50"
            active={active === "playgrounds"}
          >
            <Shapes className="size-4 text-accent-500" />
            Playgrounds
          </ProjectNavItem>
          <ProjectNavItem
            href={getExperimentsLink({
              orgName,
              projectName,
            })}
            className="hover:bg-comparison-100 dark:hover:bg-comparison-100/50"
            activeClassName="bg-comparison-100 dark:bg-comparison-100/50"
            active={active === "experiments"}
          >
            <Beaker className="size-4 text-comparison-500" />
            Experiments
          </ProjectNavItem>
          <ProjectNavItem
            href={getDatasetsLink({
              orgName,
              projectName,
            })}
            className="hover:bg-fuchsia-100 dark:hover:bg-fuchsia-900/50"
            activeClassName="bg-fuchsia-100 dark:bg-fuchsia-900/50"
            active={active === "datasets"}
          >
            <Database className="size-4 text-fuchsia-500" />
            Datasets
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/prompts`}
            className="hover:bg-cyan-100 dark:hover:bg-cyan-900/50"
            activeClassName="bg-cyan-100 dark:bg-cyan-900/50"
            active={active === "prompts"}
          >
            <MessageCircle className="size-4 text-cyan-500" />
            Prompts
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/tools`}
            className="hover:bg-amber-100 dark:hover:bg-amber-900/50"
            activeClassName="bg-amber-100 dark:bg-amber-900/50"
            active={active === "tools"}
          >
            <Bolt className="size-4 text-amber-500" />
            Tools
          </ProjectNavItem>

          <ProjectNavItem
            href={`${projectLink}/scorers`}
            className="hover:bg-lime-100 dark:hover:bg-lime-900/50"
            activeClassName="bg-lime-100 dark:bg-lime-900/50"
            active={active === "scorers"}
          >
            <Percent className="size-4 text-lime-500" />
            Scorers
          </ProjectNavItem>

          {agentsEnabled && (
            <ProjectNavItem
              href={`${projectLink}/agents`}
              className="hover:bg-red-100 dark:hover:bg-red-900/50"
              activeClassName="bg-red-100 dark:bg-red-900/50"
              active={active === "agents"}
            >
              <Route className="size-4 text-red-500" />
              Agents
            </ProjectNavItem>
          )}
          <ProjectNavItem
            href={getProjectConfigurationLink({
              orgName,
              projectName,
            })}
            activeClassName="bg-primary-100"
            active={active === "configuration"}
          >
            <Settings2 className="size-4 text-primary-500" />
            Configuration
          </ProjectNavItem>
          <div className="flex flex-1 justify-end">
            <Link
              href={getProjectLogsLink({
                orgName,
                projectName,
              })}
              className={cn(
                buttonVariants({
                  size: "xs",
                }),
              )}
            >
              <Activity className="size-4 text-primary-600" />
              Logs
              <ArrowUpRight className="size-3 text-primary-400" />
            </Link>
          </div>
        </div>
      )}
      <BodyWrapper
        innerClassName="flex"
        outerClassName={cn({
          [HEIGHT_WITH_DOUBLE_TOP_OFFSET]: !isSidenavDocked,
          [HEIGHT_WITH_TOP_OFFSET]: isSidenavDocked,
        })}
      >
        <div
          className="relative flex w-full flex-1 flex-col overflow-auto px-3"
          ref={scrollContainerRef}
        >
          {children}
          <div className="grow" />
          <Footer
            className="sticky left-0 w-full pb-4 sm:pb-4 lg:pb-4"
            inApp
            orgName={orgName}
          />
        </div>
      </BodyWrapper>
    </MainContentWrapper>
  );
};
