"use client";

import { useMemo, useCallback } from "react";
import {
  DEFAULT_CONFIG,
  ProjectContext,
  emptyDatasetSchema,
  emptyExperimentSchema,
} from "#/app/app/[org]/p/[project]/projectContext";
import {
  type getProjectContextProjects,
  type getProjectContextExperiments,
  type getProjectContextDatasets,
  type getProjectDatasets,
  type ProjectContextProject,
} from "./project-actions";
import { type fetchProjectConfig } from "./configuration/configuration-actions";
import { useMaterializedRecords } from "#/utils/duckdb";
import { sha1 } from "#/utils/hash";
import { useQueryFunc } from "#/utils/react-query";
import { UploadProvider } from "#/ui/upload-provider";
import { type Permission } from "@braintrust/core/typespecs";

export type ProjectContextElements = {
  projects: ProjectContextProject[];
};

export function ProjectClientLayout({
  children,
  orgName,
  projectName,
  projectContextElements,
  projectPermissions,
}: {
  children: React.ReactNode;
  orgName: string;
  projectName: string;
  projectContextElements: ProjectContextElements;
  projectPermissions: Permission[];
}) {
  const args = useMemo(
    () => ({ org_name: orgName, project_name: projectName }),
    [orgName, projectName],
  );
  const { data: projectRows, invalidate: refreshProjectRows } = useQueryFunc<
    typeof getProjectContextProjects
  >({
    fName: "getProjectContextProjects",
    args,
    serverData: projectContextElements.projects,
  });
  const {
    data: experimentRows,
    isLoading: isExperimentsLoading,
    invalidate: refreshExperimentRows,
  } = useQueryFunc<typeof getProjectContextExperiments>({
    fName: "getProjectContextExperiments",
    args,
    queryOptions: {
      refetchOnWindowFocus: "always",
    },
  });
  const { data: orgDatasets, invalidate: refreshDatasetRows } = useQueryFunc<
    typeof getProjectContextDatasets
  >({
    fName: "getProjectContextDatasets",
    args,
  });
  const { data: projectDatasets, invalidate: refreshProjectDatasets } =
    useQueryFunc<typeof getProjectDatasets>({
      fName: "getProjectDatasets",
      args,
    });
  const {
    data: config,
    invalidate: mutateConfig,
    isLoading: isConfigLoading,
  } = useQueryFunc<typeof fetchProjectConfig>({
    fName: "fetchProjectConfig",
    args,
  });

  const projectRow = projectRows?.length ? projectRows[0] : null;
  const projectId = projectRow && projectRow.id;
  const projectSettings = projectRow?.settings ?? null;

  const experiments = useMemo(
    () =>
      experimentRows?.map((e) => {
        const repoInfo = e.repo_info;
        return {
          ...e,
          // Add search text.
          search_text: [
            e.name,
            ...(e.metadata
              ? Object.values(e.metadata).map((v) => JSON.stringify(v, null, 1))
              : []),
            ...(repoInfo
              ? [
                  repoInfo.commit,
                  repoInfo.branch,
                  repoInfo.author_name,
                  repoInfo.commit_message,
                  repoInfo.dirty ? "dirty" : "clean",
                ]
              : []),
          ]
            .filter((v) => !!v)
            .join(" "),
        };
      }),
    [experimentRows],
  );

  const { refreshed: experimentsReady, name: experimentsTable } =
    useMaterializedRecords(
      projectId ? `experiments_${sha1(projectId)}` : null,
      experiments ?? null,
      emptyExperimentSchema,
      "search_text",
    );

  const { refreshed: datasetsReady, name: datasetsTable } =
    useMaterializedRecords(
      projectId ? `datasets_${projectId}` : null,
      projectDatasets ?? null,
      emptyDatasetSchema,
    );

  const mutateDatasets = useCallback(async () => {
    await Promise.all([refreshDatasetRows(), refreshProjectDatasets()]);
  }, [refreshDatasetRows, refreshProjectDatasets]);

  return (
    <ProjectContext.Provider
      value={useMemo(
        () => ({
          orgName,
          projectId,
          projectName,
          projectSettings,
          projectPermissions,
          mutateProject: refreshProjectRows,
          experimentsReady,
          experimentsTable,
          isExperimentsLoading,
          experiments: experiments ?? [],
          mutateExperiments: refreshExperimentRows,
          datasetsReady,
          datasetsTable,
          orgDatasets: orgDatasets ?? [],
          projectDatasets: projectDatasets ?? [],
          mutateDatasets,
          mutateProjectDatasets: refreshProjectDatasets,
          config: config ?? DEFAULT_CONFIG,
          mutateConfig,
          isConfigLoading,
        }),
        [
          config,
          datasetsReady,
          datasetsTable,
          experiments,
          experimentsReady,
          experimentsTable,
          isExperimentsLoading,
          isConfigLoading,
          mutateConfig,
          mutateDatasets,
          orgDatasets,
          projectDatasets,
          orgName,
          projectId,
          projectName,
          projectSettings,
          projectPermissions,
          refreshProjectDatasets,
          refreshExperimentRows,
          refreshProjectRows,
        ],
      )}
    >
      <UploadProvider>{children}</UploadProvider>
    </ProjectContext.Provider>
  );
}
