import { Button, type ButtonProps } from "#/ui/button";
import { ListChecks } from "lucide-react";

export const ReviewButton = (props: ButtonProps) => (
  <Button
    size="xs"
    variant="ghost"
    Icon={props.children ? undefined : ListChecks}
    onClick={() => {
      // synthetically emit "r" keypress
      const event = new KeyboardEvent("keydown", {
        key: "r",
        code: "KeyR",
        keyCode: 82,
        charCode: 82,
        bubbles: true,
        cancelable: true,
      });
      document.dispatchEvent(event);
    }}
    {...props}
  >
    {props.children ?? "Review"}
  </Button>
);
