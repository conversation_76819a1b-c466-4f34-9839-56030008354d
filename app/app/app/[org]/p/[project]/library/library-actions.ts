import {
  makeFullResultSetQuery,
  makeGetObjectsQuery,
} from "#/pages/api/_object_crud_util";
import { additionalProjections } from "#/pages/api/experiment/_constants";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { type AuthLookup } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { promptSessionSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const orgExperimentSchema = z.object({
  id: z.string(),
  name: z.string(),
  project_name: z.string(),
  project_id: z.string(),
});

type OrgExperiment = z.infer<typeof orgExperimentSchema>;

const orgPromptSessionSchema = z.object({
  id: promptSessionSchema.shape.id,
  name: promptSessionSchema.shape.name,
  project_id: promptSessionSchema.shape.project_id,
});

export type OrgPromptSession = z.infer<typeof orgPromptSessionSchema>;

export async function getOrgPromptSessions(
  { org_name }: { org_name: string },
  authLookupRaw?: AuthLookup,
): Promise<OrgPromptSession[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: allPromptSessionsQuery, queryParams } = makeFullResultSetQuery(
    {
      authLookup,
      permissionInfo: {
        aclObjectType: "prompt_session",
        aclPermission: "read",
      },
      filters: {
        org_name,
      },
    },
  );

  const fullQuery = `
  select
    all_prompt_sessions.id,
    all_prompt_sessions.name,
    all_prompt_sessions.project_id
  from
    (${allPromptSessionsQuery}) "all_prompt_sessions"
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);
  return orgPromptSessionSchema.array().parse(rows ?? []);
}

export async function getOrgExperiments({
  org_name,
  dataset_id,
  start_time,
}: {
  org_name: string;
  dataset_id?: string;
  start_time?: string;
}): Promise<OrgExperiment[] | null> {
  const authLookup = await getServerSessionAuthLookup();

  const extendedAdditionalProjections = (baseTableName: string) =>
    additionalProjections(baseTableName);

  const { query, queryParams } = makeGetObjectsQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "experiment",
      aclPermission: "read",
    },
    fullResultSetAdditionalProjections: ["projects.name as project_name"],
    filters: {
      org_name,
      dataset_id,
    },
    finalResultSetAdditionalProjections: extendedAdditionalProjections,
    fullResultsSize: undefined,
  });

  const fullQuery = `
  select
    experiments.id,
    experiments.name,
    experiments.project_name,
    experiments.project_id
  from (${query}) "experiments"
  ${start_time ? `where experiments.created > '${start_time}'` : ""}
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);

  return z.array(orgExperimentSchema).parse(rows);
}
