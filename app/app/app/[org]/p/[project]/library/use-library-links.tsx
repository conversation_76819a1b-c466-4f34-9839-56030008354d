import { isEmpty } from "#/utils/object";
import { type ReactNode, useMemo } from "react";
import { useBtql } from "#/utils/btql/btql";
import { useQueryFunc } from "#/utils/react-query";
import { useOrg } from "#/utils/user";
import {
  type getOrgPromptSessions,
  type getOrgExperiments,
} from "./library-actions";
import * as Query from "#/utils/btql/query-builder";
import { z } from "zod";
import { type getProjectSummary } from "../../../org-actions";
import { type Expr } from "@braintrust/btql/parser";
import { savedScoreSchema } from "#/utils/scorers";
import { extendedSavedFunctionIdSchema } from "@braintrust/core/typespecs";
import { Beaker, Shapes } from "lucide-react";
import { getPlaygroundLink } from "../../../prompt/[prompt]/getPromptLink";
import { getExperimentLink } from "../experiments/[experiment]/getExperimentLink";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";

export type LinkItem = {
  projectId: string;
  projectName?: string;
  objectName?: string;
  icon: ReactNode;
  href?: string;
};

const experimentLinkSchema = z.object({
  experiment_id: z.string(),
  project_id: z.string(),
});

const playgroundLinkSchema = z.object({
  id: z.string(),
  prompt_session_id: z.string(),
  project_id: z.string(),
  prompt_session_data: z
    .object({
      scorers: z.array(savedScoreSchema).default([]),
    })
    .nullish(),
  prompt_data: z
    .object({
      tool_functions: z.array(extendedSavedFunctionIdSchema).nullish(),
    })
    .nullish(),
});

export function useLibraryLinks({
  objectType,
  objectId,
  objectName,
  objectSlug,
  projectName,
}: {
  objectType: "dataset" | "prompt" | "scorer" | "tool" | "task" | "agent";
  objectId: string;
  objectName?: string;
  objectSlug?: string;
  projectName: string;
}) {
  const org = useOrg();

  const { data: allProjects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: org.name,
    },
  });

  const { data: allPromptSessions } = useQueryFunc<typeof getOrgPromptSessions>(
    {
      fName: "getOrgPromptSessions",
      args: { org_name: org.name },
    },
  );

  const startTime = useMemo(() => {
    if (objectType === "dataset") {
      return undefined;
    }
    const now = new Date();
    const newStartTimeMs = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return new Date(newStartTimeMs).toISOString();
  }, [objectType]);

  const { data: allExperiments, isLoading } = useQueryFunc<
    typeof getOrgExperiments
  >({
    fName: "getOrgExperiments",
    args: {
      org_name: org.name,
      dataset_id: objectType === "dataset" ? objectId : undefined,
      start_time: startTime,
    },
  });

  const promptSessionParams:
    | {
        select: string[];
        filter?: Expr;
        postQueryFilter?: (
          item: z.infer<typeof playgroundLinkSchema>,
        ) => boolean;
      }
    | undefined = useMemo(() => {
    switch (objectType) {
      case "prompt":
        return {
          select: ["prompt_session_id", "project_id", "id"],
          filter: {
            btql: `prompt_data.origin.prompt_id = '${objectId}'`,
          },
        };
      case "dataset":
        return {
          select: ["prompt_session_id", "project_id", "id"],
          filter: {
            btql: `prompt_session_data.dataset_id = '${objectId}'`,
          },
        };
      case "scorer":
        return {
          select: [
            "prompt_session_id",
            "project_id",
            "id",
            "prompt_session_data",
          ],
          filter: {
            btql: `prompt_session_data.scorers IS NOT NULL AND prompt_session_data.scorers != []`,
          },
          postQueryFilter: (item: z.infer<typeof playgroundLinkSchema>) =>
            !!item.prompt_session_data?.scorers.find(
              (scorer) => scorer.type === "function" && scorer.id === objectId,
            ),
        };
      case "tool":
        return {
          select: ["prompt_session_id", "project_id", "id", "prompt_data"],
          filter: {
            btql: `prompt_data.tool_functions IS NOT NULL AND prompt_data.tool_functions != []`,
          },
          postQueryFilter: (item: z.infer<typeof playgroundLinkSchema>) =>
            !!item.prompt_data?.tool_functions?.find(
              (tool) => tool.type === "function" && tool.id === objectId,
            ),
        };
      case "task":
        return;
      case "agent":
        return;
    }
  }, [objectType, objectId]);

  const {
    data: promptSessions,
    loading: loadingPromptSessions,
    error: promptSessionsError,
  } = useBtql({
    name: "Playground references query",
    query: useMemo(
      () =>
        allPromptSessions && allPromptSessions.length > 0 && promptSessionParams
          ? {
              from: Query.from(
                "prompt_session",
                allPromptSessions.map(({ id }) => id),
              ),
              select: promptSessionParams.select.map((s) => ({
                expr: { btql: s },
                alias: s,
              })),
              filter: promptSessionParams.filter,
            }
          : null,
      [allPromptSessions, promptSessionParams],
    ),
    useColumnstore: false,
    brainstoreRealtime: false,
  });

  const builder = useBtqlQueryBuilder({});

  const experimentPromptFilters: Expr[] | undefined = useMemo(() => {
    if (objectType !== "prompt" || !promptSessions) {
      return undefined;
    }

    return promptSessions.toArray().map((r) => {
      const { id } = playgroundLinkSchema.parse(r.toJSON());
      return {
        op: "eq",
        left: {
          btql: "metadata.prompt.id",
        },
        right: {
          op: "literal",
          value: id,
        },
      };
    });
  }, [objectType, promptSessions]);

  const experimentParams:
    | { select?: string[]; dimensions?: string[]; filter?: Expr }
    | undefined = useMemo(() => {
    switch (objectType) {
      case "prompt":
        return experimentPromptFilters && experimentPromptFilters.length > 0
          ? {
              dimensions: ["experiment_id", "project_id"],
              filter: builder.and(
                {
                  btql: `span_attributes.type = 'llm'`,
                },
                builder.or(...experimentPromptFilters),
              ),
            }
          : undefined;
      case "scorer":
        return objectSlug
          ? {
              dimensions: ["experiment_id", "project_id"],
              filter: {
                btql: `span_attributes.type = 'score' AND span_attributes.name = '${objectSlug}'`,
              },
            }
          : undefined;
      case "tool":
        return {
          dimensions: ["experiment_id", "project_id"],
          filter: {
            btql: `(span_attributes.type = 'tool' OR span_attributes.type = 'function') AND span_attributes.name = '${objectName}'`,
          },
        };
      case "dataset":
      case "task":
        return;
    }
  }, [objectType, experimentPromptFilters, builder, objectSlug, objectName]);

  const {
    data: experiments,
    loading: loadingExperiments,
    error: experimentsError,
  } = useBtql({
    name: "Experiment references query",
    query: useMemo(
      () =>
        allExperiments && allExperiments.length > 0 && experimentParams
          ? {
              from: Query.from(
                "experiment",
                allExperiments.map(({ id }) => id),
              ),
              select: experimentParams.select?.map((s) => ({
                expr: { btql: s },
                alias: s,
              })),
              dimensions: experimentParams.dimensions?.map((s) => ({
                expr: { btql: s },
                alias: s,
              })),
              filter: experimentParams.filter,
            }
          : null,
      [allExperiments, experimentParams],
    ),
    useColumnstore: false,
    brainstoreRealtime: false,
  });

  const promptSessionLinks: LinkItem[] | undefined | null = useMemo(() => {
    if (!promptSessionParams) {
      return null;
    }
    return promptSessions?.toArray().reduce((acc: LinkItem[], r) => {
      const rowJson = r.toJSON();
      const toParse = {
        ...rowJson,
        prompt_session_data:
          rowJson.prompt_session_data &&
          JSON.parse(rowJson.prompt_session_data),
        prompt_data: rowJson.prompt_data && JSON.parse(rowJson.prompt_data),
      };
      const parsedRow = playgroundLinkSchema.safeParse(toParse);
      if (!parsedRow.success) {
        return acc;
      }
      const row = parsedRow.data;
      if (
        !promptSessionParams.postQueryFilter ||
        promptSessionParams.postQueryFilter(row)
      ) {
        const projectName = allProjects?.find(
          (project) => project.project_id === row.project_id,
        )?.project_name;
        const objectName = allPromptSessions?.find(
          (p) => p.id === row.prompt_session_id,
        )?.name;
        acc.push({
          projectId: row.project_id,
          projectName,
          objectName,
          href:
            projectName &&
            objectName &&
            getPlaygroundLink({
              orgName: org.name,
              projectName: projectName,
              playgroundName: objectName,
            }),
          icon: <Shapes className="size-3 flex-none text-accent-600" />,
        });
      }
      return acc;
    }, []);
  }, [
    promptSessions,
    allProjects,
    allPromptSessions,
    promptSessionParams,
    org.name,
  ]);

  const experimentLinks: LinkItem[] | undefined = useMemo(() => {
    const icon = (
      <Beaker className="size-3 flex-none text-violet-600 dark:text-violet-400" />
    );
    if (objectType === "dataset") {
      return allExperiments?.map((e) => ({
        projectId: e.project_id,
        projectName: e.project_name,
        objectName: e.name,
        href: getExperimentLink({
          orgName: org.name,
          projectName: e.project_name,
          experimentName: e.name,
        }),
        icon,
      }));
    }
    return experiments?.toArray().reduce((acc: LinkItem[], r) => {
      const { project_id, experiment_id } = experimentLinkSchema.parse(
        r.toJSON(),
      );

      const projectName = allProjects?.find(
        (project) => project.project_id === project_id,
      )?.project_name;
      const objectName = allExperiments?.find(
        (p) => p.id === experiment_id,
      )?.name;
      acc.push({
        projectId: project_id,
        projectName,
        objectName,
        href:
          projectName &&
          objectName &&
          getExperimentLink({
            orgName: org.name,
            projectName: projectName,
            experimentName: objectName,
          }),
        icon,
      });
      return acc;
    }, []);
  }, [experiments, allExperiments, allProjects, objectType, org.name]);

  const linksByProject = useMemo(() => {
    if (isEmpty(promptSessionLinks) && isEmpty(experimentLinks)) {
      return undefined;
    }
    const allLinks = (experimentLinks || [])
      .concat(promptSessionLinks || [])
      .reduce<{
        [projectId: string]: {
          projectId: string;
          projectName: string;
          links: LinkItem[];
        };
      }>((acc, link) => {
        const { projectId } = link;
        if (!acc[projectId]) {
          acc[projectId] = {
            projectId: projectId,
            projectName: link.projectName || "<unknown project>",
            links: [],
          };
        }

        acc[projectId].links.push(link);
        return acc;
      }, {});

    return Object.values(allLinks).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : a.projectName.localeCompare(b.projectName),
    );
  }, [promptSessionLinks, experimentLinks, projectName]);

  return {
    loading: isLoading || loadingPromptSessions || loadingExperiments,
    promptSessionsError,
    experimentsError,
    linksByProject,
  };
}
