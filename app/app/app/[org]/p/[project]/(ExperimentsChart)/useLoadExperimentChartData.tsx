import { useDBQuery, useDuckDB } from "#/utils/duckdb";
import { type SetStateAction, useCallback, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { metadataFieldsQuery } from "#/utils/queries/metadata";
import { getObjectPathValue } from "./utils";
import {
  type SelectionType,
  type SelectionTypesEnum,
} from "#/ui/charts/selectionTypes";
import { nextColor } from "#/ui/charts/colors";

export const MAX_EXPERIMENTS = 500;
const DEFAULT_SCORE_COUNT = 5;

export type ExperimentData = {
  scores: Record<string, number>;
  metrics: Record<string, number>;
  name: string;
  id: string;
  last_updated: number;
  count: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  metadata: any;
};

export function useLoadExperimentChartData({
  experimentsQuery,
  experimentsSignals,
  scoreNames,
  metricNames,
  excludedMeasures,
  setExcludedMeasures,
  isViewsPending,
}: {
  experimentsQuery: string | null;
  experimentsSignals: number[];
  scoreNames: string[];
  metricNames: string[];
  excludedMeasures: SelectionType[];
  setExcludedMeasures: (v: React.SetStateAction<SelectionType[]>) => void;
  isViewsPending: boolean;
}) {
  const { data, hasLoaded } = useDBQuery(
    experimentsQuery &&
      `WITH base AS (
        SELECT * FROM (${experimentsQuery})
        WHERE last_updated IS NOT NULL
        ORDER BY last_updated DESC NULLS LAST
        LIMIT ${MAX_EXPERIMENTS}
      )
      SELECT * FROM base
      ORDER BY last_updated ASC NULLS LAST
    `,
    experimentsSignals,
  );

  const duck = useDuckDB();
  const metadataQuery = useQuery({
    queryKey: ["metadataFields", experimentsQuery],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      return (
        (await metadataFieldsQuery({
          conn,
          abortSignal: signal,
          query: experimentsQuery,
          whereClause: "last_updated IS NOT NULL",
          limit: MAX_EXPERIMENTS,
        })) ?? []
      );
    },
    enabled:
      !!duck && !!experimentsQuery && experimentsSignals.every((s) => s > 0),
  });

  const loading = !hasLoaded && !metadataQuery.isPending;
  const [numericMetadataFields, metadataFields] = useMemo(() => {
    if (loading || !data || !metadataQuery.data) {
      return [];
    }

    const dataTypes: Record<string, "numeric" | "any"> = {};
    (data?.toArray() ?? []).forEach((r) => {
      let metadata = {};
      try {
        metadata = JSON.parse(r?.metadata ?? "{}", function (key, value) {
          // potentially needed for non-chrome browsers
          return typeof value === "number" &&
            (value > Number.MAX_SAFE_INTEGER || value < Number.MIN_SAFE_INTEGER)
            ? BigInt(value)
            : value;
        });
      } catch {
        return;
      }
      metadataQuery.data?.forEach((m) => {
        const fieldPath = JSON.stringify(m);
        const dataType = dataTypes[fieldPath];
        if (dataType != null && dataType !== "numeric") return;

        const v = getObjectPathValue(metadata, fieldPath);
        dataTypes[fieldPath] =
          v == null || Number.isNaN(parseFloat(v)) ? "any" : "numeric";
      });
    });

    const { numeric, withData } = (metadataQuery.data ?? []).reduce(
      (
        acc: {
          numeric: typeof metadataQuery.data;
          withData: typeof metadataQuery.data;
        },
        m,
      ) => {
        if (m.length === 0) {
          return acc;
        }
        const fieldPath = JSON.stringify(m);
        if (dataTypes[fieldPath] == null) {
          return acc;
        }

        if (dataTypes[fieldPath] === "numeric") {
          acc.numeric.push(m);
        }
        acc.withData.push(m);

        return acc;
      },
      {
        numeric: [],
        withData: [],
      },
    );
    return [numeric, withData];
  }, [loading, data, metadataQuery]);

  const experimentData = useMemo(() => {
    if (loading || !data) return null;

    return data.toArray().map((row) => {
      return {
        scores: Object.fromEntries(
          scoreNames.map((scoreName) => [scoreName, row[scoreName]]),
        ),
        metrics: Object.fromEntries(
          metricNames.map((metricName) => [metricName, row[metricName]]),
        ),
        name: row.name,
        id: row.id,
        last_updated: row.last_updated,
        count: row.num_examples,
        //tag: row.tag,
        metadata: JSON.parse(row?.metadata ?? "{}"),
      };
    });
  }, [loading, data, scoreNames, metricNames]);

  const { selectedMeasures, allMeasures } = useMemo(() => {
    if (isViewsPending || loading || !numericMetadataFields) {
      return {};
    }

    const allMeasures: SelectionType[] = [
      ...scoreNames.map((s) => ({ type: "score" as const, value: s })),
      ...metricNames.map((m) => ({ type: "metric" as const, value: m })),
      ...numericMetadataFields.map((m) => ({
        type: "metadata" as const,
        value: JSON.stringify(m),
      })),
    ];
    const excludedMeasuresMap = (
      excludedMeasures && Array.isArray(excludedMeasures)
        ? excludedMeasures
        : allMeasures.filter(
            (m, i) => m.type !== "score" || i >= DEFAULT_SCORE_COUNT,
          )
    ).reduce(
      (
        acc: Record<string, Record<SelectionTypesEnum, boolean>>,
        { type, value },
      ) => ({
        ...acc,
        [value]: { ...acc[value], [type]: true },
      }),
      {},
    );

    const { selectedMeasures } = allMeasures.reduce(
      (
        acc: {
          selectedMeasures: Record<string, Record<SelectionTypesEnum, number>>;
          colorValues: number[];
        },
        m,
      ) => {
        if (excludedMeasuresMap[m.value]?.[m.type]) {
          return acc;
        }

        const color = nextColor(acc.colorValues)[0];
        acc.selectedMeasures[m.value] = acc.selectedMeasures[m.value] ?? {};
        acc.selectedMeasures[m.value][m.type] = color;
        acc.colorValues.push(color);

        return acc;
      },
      {
        selectedMeasures: {},
        colorValues: [],
      },
    );
    return { selectedMeasures, allMeasures };
  }, [
    isViewsPending,
    loading,
    numericMetadataFields,
    excludedMeasures,
    scoreNames,
    metricNames,
  ]);

  const selectedMeasuresNonNull: Record<
    string,
    Record<SelectionTypesEnum, number>
  > = useMemo(() => {
    return selectedMeasures ?? {};
  }, [selectedMeasures]);

  const setSelectedMeasures = useCallback(
    (
      valueOrUpdater: SetStateAction<
        Record<string, Record<SelectionTypesEnum, number>>
      >,
    ) => {
      const newSelectedMeasures =
        typeof valueOrUpdater === "function"
          ? valueOrUpdater(selectedMeasures ?? {})
          : valueOrUpdater;

      const next = (allMeasures ?? []).filter(
        (m) => newSelectedMeasures[m.value]?.[m.type] == null,
      );
      setExcludedMeasures(next);
    },
    [allMeasures, selectedMeasures, setExcludedMeasures],
  );

  return {
    experimentData,
    loading: loading || !selectedMeasures,
    metadataFields,
    numericMetadataFields,
    selectedMeasures: selectedMeasuresNonNull,
    setSelectedMeasures,
  };
}
