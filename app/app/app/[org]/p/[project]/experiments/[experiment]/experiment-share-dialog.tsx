import { buttonVariants } from "#/ui/button";
import { useCreateExperimentDialog } from "#/ui/dialogs/create-experiment";
import { ShareModal } from "#/ui/dialogs/share";
import { type SavingState } from "#/ui/saving";
import { sessionFetchProps, useSessionToken } from "#/utils/auth/session-token";
import { useOtherObjectInserter } from "#/utils/other-object-inserter";
import { pluralizeWithCount } from "#/utils/plurals";
import { useOrg } from "#/utils/user";
import { type Experiment } from "@braintrust/core/typespecs";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { getExperimentLink } from "./getExperimentLink";
import {
  ExperimentIdField,
  IdField,
  singleQuote,
} from "@braintrust/local/query";
import { parseObjectJSON } from "#/utils/schema";
import { useDuckDB } from "#/utils/duckdb";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useMemo,
} from "react";
import { ProjectContext } from "../../projectContext";

export const ExperimentShareDialog = ({
  experiment,
  open,
  setOpen,
  setSavingState,
  experimentReadyRaw,
  experimentScanRaw,
}: {
  experiment: Experiment;
  open: boolean;
  setOpen: (open: boolean) => void;
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
  experimentReadyRaw: number;
  experimentScanRaw: string | null;
}) => {
  const org = useOrg();
  const duck = useDuckDB();
  const router = useRouter();

  const { projectName } = useContext(ProjectContext);

  const setCopyToPrivateExperimentInserterInfo = useOtherObjectInserter({
    objectType: "experiment",
    setSavingState,
  });
  const {
    modal: copyToPrivateExperimentModal,
    open: openCopyToPrivateExperimentModal,
  } = useCreateExperimentDialog({
    getRegisterExperimentArgs: (experimentName: string) => {
      if (!experiment) {
        throw new Error("experiment is expected to be available");
      }
      return {
        org_id: org.id,
        project_id: experiment.project_id,
        experiment_name: experimentName,
        description: experiment.description,
        repo_info: experiment.repo_info,
        base_exp_id: experiment.base_exp_id,
        dataset_id: experiment.dataset_id,
        dataset_version: experiment.dataset_version,
        public: false,
      };
    },

    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    onSuccessfulCreate: async (newExperiment: Record<any, any>) => {
      if (!experimentReadyRaw || !duck) {
        throw new Error(
          `Missing required inputs: experimentReady=${experimentReadyRaw} duck=${duck}`,
        );
      }
      setCopyToPrivateExperimentInserterInfo({
        objectInfo: { id: newExperiment.id },
        getRows: async () => {
          // Grab all the rows in the current experiment. Change the experiment
          // ID field to be the new experiment's ID and also create new row IDs.
          const conn = await duck.connect();
          const result = await conn.query(`
            SELECT *
                REPLACE (${singleQuote(
                  newExperiment.id,
                )} as ${ExperimentIdField},
                         uuid() as ${IdField})
            FROM (${experimentScanRaw}) "t"
          `);
          return result
            .toArray()
            .map((row: unknown) => parseObjectJSON("experiment", row));
        },
        onSuccess: (numRows: number) => {
          router.refresh();
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const experimentName = newExperiment.name as string;
          toast(`Copied ${pluralizeWithCount(numRows, "row", "rows")}`, {
            action: (
              <Link
                className={buttonVariants({ size: "xs" })}
                href={getExperimentLink({
                  orgName: org.name,
                  projectName,
                  experimentName,
                })}
              >
                Go to experiment
              </Link>
            ),
          });
        },
        onFailure: async () => {
          try {
            await fetch(`/api/experiment/delete_id`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ id: newExperiment.id }),
            });
          } catch (error) {
            console.error(error);
            toast.error(`Failed to delete unsuccessfully-copied experiment`, {
              description:
                error instanceof Error
                  ? error.message
                  : "An unexpected error occurred, please try again",
            });
          }
        },
      });
    },
  });

  const { getOrRefreshToken } = useSessionToken();

  const onSubmit = useCallback(
    async (pub: boolean) => {
      if (!(experiment && org.api_url)) {
        toast.error("Experiment info not fully loaded. Please try again");
        return;
      }
      const sessionToken = await getOrRefreshToken();
      const { sessionHeaders, sessionExtraFetchProps } =
        sessionFetchProps(sessionToken);
      toast.promise(
        fetch(`${org.api_url}/v1/experiment/${experiment.id}`, {
          method: "PATCH",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            ...sessionHeaders,
          },
          body: JSON.stringify({
            public: pub,
          }),
          ...sessionExtraFetchProps,
        }),
        {
          success: () => {
            router.refresh();
            return "Visibility updated";
          },
          error: async (resp) => `${await resp.text()}`,
          loading: "Updating visibility",
        },
      );
    },
    [experiment, org, router, getOrRefreshToken],
  );

  const onCopyPublicToPrivate = useMemo(() => {
    if (experiment && experiment.name && experimentReadyRaw) {
      return () =>
        openCopyToPrivateExperimentModal(`${experiment.name}-private-copy`);
    }
    return undefined;
  }, [experiment, experimentReadyRaw, openCopyToPrivateExperimentModal]);

  return (
    <>
      {open && (
        <ShareModal
          onSubmit={onSubmit}
          name={experiment.name}
          initialValue={experiment.public || false}
          open={open}
          onOpenChange={setOpen}
          orgName={org.name}
          onCopyPublicToPrivate={onCopyPublicToPrivate}
        />
      )}
      {copyToPrivateExperimentModal}
    </>
  );
};
