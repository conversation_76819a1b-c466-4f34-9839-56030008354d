import { cookies } from "next/headers";
import { type Metadata, type ResolvingMetadata } from "next";
import { getExperiment } from "./experiment-actions";
import ClientPage, { type PanelLayout, type Params } from "./clientpage";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { type Permission } from "@braintrust/core/typespecs";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const experiment = await getExperiment({
    org_name: decodeURIComponentPatched(params.org),
    project_name: decodeURIComponentPatched(params.project),
    experiment_name: decodeURIComponentPatched(params.experiment),
  });

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get(
    "react-resizable-panels:experiment-layout",
  );

  const defaultPanelLayout: PanelLayout = {
    main: 80,
    sidebar: 20,
    trace: 0,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  let experimentPermissions: Permission[] = [];
  try {
    const permissions = await getObjectAclPermissions({
      objectType: "experiment",
      objectId: experiment?.id,
    });

    experimentPermissions = permissions ?? [];
  } catch (e) {
    console.error("Failed to get experiment permissions", e);
  }

  return (
    <ClientPage
      params={params}
      experiment={experiment}
      defaultPanelLayout={defaultPanelLayout}
      permissions={experimentPermissions}
    />
  );
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  _parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const experimentName = decodeURIComponentPatched(params.experiment);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: experimentName,
    sections: ["Experiments", projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/experiments/${params.experiment}`,
  });
}
