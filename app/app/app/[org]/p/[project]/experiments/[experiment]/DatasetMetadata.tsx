"use client";

import <PERSON>ToC<PERSON> from "#/ui/code-to-copy";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, Toolt<PERSON>Trigger } from "#/ui/tooltip";
import { parseTransactionBigInt } from "#/utils/transactions";
import Link from "next/link";
import * as React from "react";
import { getDatasetLink } from "../../datasets/[dataset]/getDatasetLink";
import { type getDatasetName } from "../../datasets/[dataset]/actions";
import { useQueryFunc } from "#/utils/react-query";
import { MINIMUM_BTQL_API_VERSION } from "#/utils/btql/constants";
import { type TooltipContentProps } from "@radix-ui/react-tooltip";
import { smartTimeFormat } from "#/ui/date";
import { TabsTrigger, Tabs, TabsList, TabsContent } from "#/ui/tabs";
import { Database } from "lucide-react";

const generatePythonString = (props: {
  datasetName: string;
  datasetVersion: string;
  projectName: string;
}) => {
  return `braintrust.init_dataset(
  project="${props.projectName}",
  name="${props.datasetName}",
  version="${props.datasetVersion}"
)`;
};
const generateTypescriptString = (props: {
  datasetName: string;
  datasetVersion: string;
  projectName: string;
}) => {
  return `braintrust.initDataset("${props.projectName}", {
  dataset: "${props.datasetName}",
  version: "${props.datasetVersion}",
});`;
};

export function DatasetMetadata(props: {
  showVersionOnPreview?: boolean;
  datasetId: string;
  datasetName?: string;
  datasetVersion: string;
  datasetVersionFromIncompleteData?: boolean;
  projectName: string;
  orgName: string;
  side?: TooltipContentProps["side"];
  children?: React.ReactNode;
}) {
  const { data: datasetName } = useQueryFunc<typeof getDatasetName>({
    fName: "getDatasetName",
    args: { dataset_id: props.datasetId },
    serverData: props.datasetName ?? null,
  });
  const parsedVersion = parseTransactionBigInt(props.datasetVersion);

  if (datasetName == null) {
    return null;
  }

  return (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>
        {props.children ?? (
          <Link
            className="flex flex-none items-center gap-1 font-mono text-xs text-primary-600"
            href={getDatasetLink({
              orgName: props.orgName,
              projectName: props.projectName,
              datasetName,
            })}
          >
            {props.showVersionOnPreview ? props.datasetVersion : datasetName}
          </Link>
        )}
      </TooltipTrigger>
      <TooltipContent
        side={props.side ?? "bottom"}
        align="start"
        className="flex min-w-[450px] max-w-[650px] flex-col gap-1 py-3"
      >
        {props.datasetName && (
          <h3 className="flex items-center gap-2 text-base font-medium">
            <Database className="size-3 flex-none text-fuchsia-600 dark:text-fuchsia-400" />
            {props.datasetName}
          </h3>
        )}
        <p className="mb-2 text-xs text-primary-800">
          Version <code className="font-medium">{props.datasetVersion}</code>{" "}
          updated{" "}
          <time dateTime={parsedVersion?.date?.toISOString()}>
            {smartTimeFormat(parsedVersion?.date.getTime())}
          </time>
          <br />
          {props.datasetVersionFromIncompleteData && (
            <div className="mb-2 flex items-center gap-1 text-sm text-primary-500">
              The version was computed from a partial view of the data, so it
              may fully up-to-date. Please upgrade your API server to a version
              {` >=
            ${MINIMUM_BTQL_API_VERSION} `}{" "}
              for the most accurate result.
            </div>
          )}
        </p>
        <Tabs defaultValue="typescript" className="w-full">
          <TabsList>
            <TabsTrigger value="typescript" className="text-xs">
              TypeScript
            </TabsTrigger>
            <TabsTrigger value="python" className="text-xs">
              Python
            </TabsTrigger>
          </TabsList>
          <TabsContent value="typescript">
            <CodeToCopy
              disableCopyToClipboardTooltip
              data={generateTypescriptString({
                datasetName: datasetName!,
                datasetVersion: props.datasetVersion!,
                projectName: props.projectName!,
              })}
              highlighterClassName="text-xs"
              language="typescript"
            />
          </TabsContent>
          <TabsContent value="python">
            <CodeToCopy
              disableCopyToClipboardTooltip
              data={generatePythonString({
                datasetName: datasetName!,
                datasetVersion: props.datasetVersion!,
                projectName: props.projectName!,
              })}
              highlighterClassName="text-xs"
              language="python"
            />
          </TabsContent>
        </Tabs>
      </TooltipContent>
    </Tooltip>
  );
}
