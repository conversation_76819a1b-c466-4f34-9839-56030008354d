import {
  Object<PERSON><PERSON><PERSON><PERSON>ield,
  type UpdateLog,
  useDB<PERSON><PERSON>y,
  useParquetView,
  useParquetViews,
} from "#/utils/duckdb";
import { type Roster } from "#/utils/realtime-data";
import { type Schema, type TypeMap } from "apache-arrow";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import {
  experimentScanSpanSummaryQuery,
  type SpanSummaryQueryParams,
} from "./experimentQueries";
import { invalidateId } from "#/utils/react-query";
import {
  buildSearchKey,
  type DataObjectSearch,
  type DataObjectType,
  useDataObjects,
} from "#/utils/btapi/btapi";
import { objectSchemaFieldsQuery } from "#/utils/queries/schema";
import { makePreviewBtqlReplace } from "#/utils/btapi/fetch";
import { useFeatureFlags } from "#/lib/feature-flags";
import { type DiscriminatedProjectScore } from "#/utils/score-config";
import { type CustomColumn } from "@braintrust/core/typespecs";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import {
  stableSignals,
  useTempDuckTable,
} from "#/utils/queries/useTempDuckTable";
import { doubleQuote } from "#/utils/sql-utils";
import { EMPTY_CHANNEL } from "#/utils/mutable-object";
import {
  parseSummarySchemaScores,
  makeStructToMapScan,
  useSummaryRealtime,
} from "./use-summary-realtime";
import { type Expr, type AliasExpr } from "@braintrust/btql/parser";
import { BT_GROUP_BY_METADATA } from "#/ui/table/grouping/queries";
import { type Filter } from "#/utils/search/simple-tree";
import { useRegressionFilterState } from "#/ui/query-parameters";
import { fetchBtql, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type RealtimeState } from "@braintrust/local/app-schema";

export type LoadedExperimentSummary = {
  // null means the summary scan is not yet available
  scan: string | null;
  schema: Schema<TypeMap> | null;
  refreshed: number;
  scoreNames: string[] | null;
  metricNames: string[] | null;
  tableName: string | null;
  objectId: string;
  searchKeyName: string;
  realtimeState?: RealtimeState;
};

export type LoadedExperiment = {
  id: string;
  name: string;
  scan: string;
  schema: Schema<TypeMap> | null;
  refreshed: number;
  channel: () => UpdateLog | null;
  roster: Roster;
  searchKeyName: string;
  auditLog?: Omit<LoadedExperiment, "auditLog" | "summary">;
  summary?: LoadedExperimentSummary;
  error?: Error;
};

export const useLoadExperiment = ({
  btObject,
  tableScansPaused,
  projectedColumns,
  filters,
  customColumns,
}: {
  btObject: {
    id: string;
    name: string;
    type: "experiment";
  };
  tableScansPaused: boolean;
  projectedColumns?: AliasExpr[];
  filters?: {
    sql: Filter[];
    btql: Expr[];
  };
  customColumns?: CustomColumn[];
}): LoadedExperiment => {
  const {
    flags: { hasIsRootField, fastExperimentSummary },
  } = useFeatureFlags();
  const { version } = useAPIVersion();

  const experimentSearch: DataObjectSearch = useMemo(
    () => ({
      id: btObject.id,
      replace: makePreviewBtqlReplace({
        objectType: btObject.type,
        relaxedSearchMode: false,
        hasIsRootField,
        version,
      }),
      limit: fastExperimentSummary ? 0 : undefined,
    }),
    [
      btObject.id,
      btObject.type,
      hasIsRootField,
      version,
      fastExperimentSummary,
    ],
  );

  // Load the main object before the audit log (just prioritizes the network requests)
  const {
    refreshed,
    schema,
    scan,
    channel,
    roster,
    error: loadingError,
  } = useParquetView({
    objectType: btObject.type,
    search: experimentSearch,
    disableRealtime: fastExperimentSummary, // TODO: Even better would be not even run this query
  });

  const backendSummary = useBackendSummary({
    btObject,
    fastExperimentSummary,
    projectedColumns,
    filters,
    customColumns,
  });

  const auditLogSearch = useMemo(
    () =>
      fastExperimentSummary
        ? undefined
        : {
            id: btObject.id,
            audit_log: true,
          },
    [btObject.id, fastExperimentSummary],
  );

  const {
    refreshed: auditLogRefreshed,
    scan: auditLogScan,
    schema: auditLogSchema,
    channel: auditLogChannel,
    roster: auditLogRoster,
    error: auditLogError,
  } = useParquetView({
    objectType: btObject.type,
    search: auditLogSearch,
  });

  useEffect(() => {
    if (auditLogError) {
      console.warn(
        "Error loading audit log. Comments may not be shown:\n",
        auditLogError,
      );
    }
  }, [auditLogError]);

  const queryClient = useQueryClient();
  useEffect(() => {
    if (tableScansPaused) {
      return;
    }
    invalidateId(queryClient, btObject.id);
  }, [
    tableScansPaused,
    btObject.id,
    queryClient,
    refreshed,
    auditLogRefreshed,
  ]);

  const refreshedSnapshot = tableScansPaused
    ? Math.min(1, refreshed)
    : refreshed;

  const auditLogRefreshedSnapshot = tableScansPaused
    ? Math.min(1, auditLogRefreshed)
    : auditLogRefreshed;

  return useMemo(
    () => ({
      id: btObject.id,
      name: btObject.name,
      scan: scan ?? "",
      schema,
      refreshed: refreshedSnapshot,
      channel: fastExperimentSummary ? EMPTY_CHANNEL : channel,
      roster,
      searchKeyName: btObject.id,
      auditLog: {
        id: btObject.id,
        name: btObject.name,
        scan: auditLogScan ?? "",
        schema: auditLogSchema,
        refreshed: auditLogRefreshedSnapshot,
        channel: fastExperimentSummary ? EMPTY_CHANNEL : auditLogChannel,
        roster: auditLogRoster,
        searchKeyName: buildSearchKey(auditLogSearch),
      },
      summary: backendSummary,
      error: loadingError,
    }),
    [
      btObject.id,
      btObject.name,
      scan,
      schema,
      refreshedSnapshot,
      fastExperimentSummary,
      channel,
      roster,
      auditLogScan,
      auditLogSchema,
      auditLogRefreshedSnapshot,
      auditLogChannel,
      auditLogRoster,
      auditLogSearch,
      backendSummary,
      loadingError,
    ],
  );
};

const useLoadComparisonExperimentTables = ({
  experiments,
  tableScansPaused,
  auditLog,
  objectType = "experiment",
  enableSummary,
}: {
  experiments: {
    id: string;
    name: string;
    search?: DataObjectSearch;
    scanFilterFn?: (scan: string) => string;
  }[];
  tableScansPaused?: boolean;
  auditLog?: boolean;
  objectType?: DataObjectType;
  enableSummary: boolean;
}): LoadedExperiment[] => {
  const {
    flags: { hasIsRootField, fastExperimentSummary: _summaryFlag },
  } = useFeatureFlags();
  const { version } = useAPIVersion();
  const fastExperimentSummary = enableSummary && _summaryFlag;

  const searchData = useMemo(
    () =>
      experiments.map((e) => {
        const search = {
          ...(e.search ?? {
            id: e.id,
            audit_log: !!auditLog,
            replace: !auditLog
              ? makePreviewBtqlReplace({
                  objectType: "experiment",
                  relaxedSearchMode: false,
                  hasIsRootField,
                  version,
                })
              : {},
            limit: fastExperimentSummary ? 1 : undefined,
          }),
        };
        return {
          id: e.id,
          name: e.name,
          search,
          searchKey: buildSearchKey(search),
          scanFilterFn: e.scanFilterFn,
        };
      }),
    [experiments, auditLog, hasIsRootField, version, fastExperimentSummary],
  );

  const dataObjectSearches = useMemo(
    () =>
      fastExperimentSummary && auditLog
        ? []
        : searchData.map(({ search }) => search),
    [searchData, fastExperimentSummary, auditLog],
  );

  const {
    data: rowDataObjects,
    refreshed: rowDataRefreshed,
    error: rowDataError,
  } = useDataObjects({
    objectType,
    searches: dataObjectSearches,
  });

  useEffect(() => {
    if (rowDataError) {
      console.error("Error loading row data:\n", rowDataError);
    }
  }, [rowDataError]);

  const rowChannelSpec = useMemo(
    () =>
      Object.fromEntries(
        searchData.map(({ searchKey, search }) => [
          searchKey,
          {
            objectType,
            id: search.id,
            audit_log: !!search.audit_log,
            shape: "traces" as const,
          },
        ]),
      ),
    [searchData, objectType],
  );

  const {
    fnames,
    schema: schemas,
    scans,
    refreshed,
    filenameToChannel,
  } = useParquetViews({
    files: rowDataObjects,
    ready: rowDataRefreshed,
    channelSpecs: rowChannelSpec,
  });

  const queryClient = useQueryClient();
  const lastRefreshed = useRef(refreshed);
  useEffect(() => {
    if (tableScansPaused) {
      return;
    }
    Object.keys({
      ...(lastRefreshed.current ?? {}),
      ...refreshed,
    }).forEach(
      (searchKey) =>
        (lastRefreshed.current ?? {})[searchKey] !== refreshed[searchKey] &&
        invalidateId(queryClient, searchKey),
    );
    lastRefreshed.current = refreshed;
  }, [queryClient, refreshed, tableScansPaused]);

  const { summaries } = useBackendSummaries(enableSummary);

  return useMemo(
    () =>
      searchData
        .map(({ id, name, searchKey, scanFilterFn }, i) => {
          if (
            !refreshed[searchKey] ||
            (fastExperimentSummary && !summaries[i])
          ) {
            return null;
          }
          return {
            id,
            name,
            fname: fnames[searchKey],
            scan:
              scans[searchKey] && scanFilterFn
                ? scanFilterFn(scans[searchKey])
                : scans[searchKey],
            schema: schemas[searchKey],
            refreshed: Math.min(
              refreshed[searchKey],
              tableScansPaused ? 1 : refreshed[searchKey],
            ),
            channel: fastExperimentSummary
              ? EMPTY_CHANNEL
              : () => filenameToChannel(searchKey).channel ?? null,
            roster: filenameToChannel(searchKey).roster ?? [],
            searchKeyName: searchKey,
            summary: summaries[i],
          };
        })
        .filter((v) => !!v),
    [
      searchData,
      refreshed,
      fnames,
      scans,
      schemas,
      tableScansPaused,
      fastExperimentSummary,
      filenameToChannel,
      summaries,
    ],
  );
};

const EMPTY_ARRAY: LoadedExperiment[] = [];

export const useLoadComparisonExperiments = (
  experiments: { id: string; name: string }[],
  tableScansPaused: boolean,
  objectType: DataObjectType = "experiment",
  enableSummary: boolean,
): LoadedExperiment[] => {
  const comparisonExperiments = useLoadComparisonExperimentTables({
    experiments,
    tableScansPaused,
    objectType,
    enableSummary,
  });
  const comparisonAuditLogs = useLoadComparisonExperimentTables({
    experiments: objectType === "experiment" ? experiments : EMPTY_ARRAY,
    tableScansPaused,
    auditLog: true,
    objectType,
    enableSummary: false,
  });
  const loadedExperiments = useMemo(() => {
    return comparisonExperiments.map((e) => ({
      ...e,
      auditLog: comparisonAuditLogs.find((a) => a.id === e.id),
    }));
  }, [comparisonExperiments, comparisonAuditLogs]);

  return loadedExperiments;
};

export type LoadExperimentScansParams = {
  modelSpecScan: string | null;
  scoreConfig: DiscriminatedProjectScore[];
  comparisonKey: string | null;
  hasErrorField: boolean;
  isRootAvailable: boolean;
  customColumns?: CustomColumn[];
  originDatasetRowId?: boolean;
};

export async function loadExperimentScans(
  conn: AsyncDuckDBConnection,
  abort: AbortSignal,
  {
    experiment,
    spanSummaryQueryParams,
    originDatasetRowId,
    beforeCreateFn,
  }: {
    experiment: LoadedExperiment;
    spanSummaryQueryParams: SpanSummaryQueryParams;
    originDatasetRowId?: boolean;
    beforeCreateFn?: (t: string) => void;
  },
) {
  const fields =
    (await objectSchemaFieldsQuery(
      conn,
      abort,
      experiment.scan,
      experiment.summary === undefined
        ? experiment.schema
        : experiment.summary.schema,
      spanSummaryQueryParams.scoreConfig,
    )) ?? {};

  if (experiment.summary !== undefined) {
    fields.scoreFields = experiment.summary.scoreNames ?? [];
    fields.metricFields = experiment.summary.metricNames ?? [];
  }

  const {
    scan: tableScan,
    tempTableName,
    customColumns,
    customColumnsSchema,
  } = (await experimentScanSpanSummaryQuery(conn, abort, {
    ...spanSummaryQueryParams,
    scoreFields: fields.scoreFields,
    metricFields: fields.metricFields,
    experimentId: experiment.id,
    experimentScanRaw: experiment.scan,
    auditLogScan: experiment.auditLog?.scan ?? null,
    originDatasetRowId,
    loadedSummary: experiment.summary,
    beforeCreateFn,
  })) ?? {};

  return {
    tempTableName: tempTableName,
    tableScan: tableScan ?? null,
    ...fields,
    scoreFields: fields.scoreFields.map((f) => f),
    customColumns,
    customColumnsSchema,
  };
}

function useBackendSummary({
  btObject,
  fastExperimentSummary,
  projectedColumns,
  filters,
  customColumns,
}: {
  btObject: {
    id: string;
    type: DataObjectType;
  };
  fastExperimentSummary: boolean;
  projectedColumns?: AliasExpr[];
  filters?: {
    sql: Filter[];
    btql: Expr[];
  };
  customColumns?: CustomColumn[];
}): LoadedExperimentSummary | undefined {
  const [regressionFilterState] = useRegressionFilterState();
  const allFilters = useMemo(() => {
    const regressionFilters = regressionFilterState.reduce<Expr[]>((acc, f) => {
      if (
        (f.experimentId === btObject.id || f.experimentId === "any") &&
        f.group &&
        f.group.type !== "input"
      ) {
        const name =
          f.group.type === "custom" ? [f.group.name] : [BT_GROUP_BY_METADATA];
        acc.push(
          f.group.value === "null"
            ? {
                op: "isnull",
                expr: {
                  op: "ident",
                  name,
                },
              }
            : {
                op: "eq",
                left: {
                  op: "ident",
                  name,
                },
                right: {
                  op: "literal",
                  value: f.group.value,
                },
              },
        );
      }
      return acc;
    }, []);
    return {
      btql: (filters?.btql ?? []).concat(regressionFilters),
      sql: filters?.sql ?? [],
    };
  }, [btObject.id, filters, regressionFilterState]);

  const summarySearch: { key: string; search: DataObjectSearch } | undefined =
    useMemo(() => {
      if (fastExperimentSummary) {
        const baseSearch = {
          id: btObject.id,
          shape: "summary" as const,
          preview_length: 1000,
          custom_columns: projectedColumns,
          filters: allFilters,
        };
        // NOTE: This search key is used to force a refresh of the summary scan
        // when the custom columns change. The columns are not included in the
        // search request because they are aggregated on the backend, but we
        // include the __bt_initial_load column in the search key so the query
        // on page load is distinct from a query with no custom columns.
        const searchKey = buildSearchKey({
          ...baseSearch,
          custom_columns: (baseSearch.custom_columns || []).concat(
            customColumns
              ? customColumns.map(({ name, expr }) => ({
                  alias: name,
                  expr: { btql: expr },
                }))
              : [
                  {
                    alias: "__bt_initial_load",
                    expr: { op: "literal", value: null },
                  },
                ],
          ),
        });
        return {
          key: searchKey,
          search: baseSearch,
        };
      }
      return undefined;
    }, [
      btObject.id,
      fastExperimentSummary,
      projectedColumns,
      allFilters,
      customColumns,
    ]);

  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});

  // query limit 1 for now so that we can get the realtime state from a json endpoint.
  // TODO: update the api so that we can get this data from the parquet response
  const { data: realtimeQuery } = useQuery({
    queryKey: ["realtimeStateQuery", btObject.type, btObject.id],
    queryFn: async ({ signal }) => {
      return await fetchBtql({
        args: {
          query: {
            select: [
              {
                alias: "dummy",
                expr: { btql: "''" },
              },
            ],
            from: builder.from(btObject.type, [btObject.id]),
            limit: 1,
          },
          brainstoreRealtime: true,
          useColumnstore: false,
        },
        ...btqlOptions,
      });
    },
  });
  // I don't think we can use anything from the summary scan other than the
  // scan and its refreshed counter, at least for now.
  const { schema: summarySchema, scan: summaryScan } = useParquetView({
    objectType: btObject.type,
    search: summarySearch,
    disableRealtime: true,
    disableCache: true,
  });

  const { scoreNames: baseScoreNames, metricNames: baseMetricNames } = useMemo(
    () => parseSummarySchemaScores(summarySchema),
    [summarySchema],
  );

  const { tableName: summaryTableName, ready: summaryTableReady } =
    useTempDuckTable({
      tableNameHashKey: `experiment_base_summary_${btObject.id}`,
      scan: summaryScan
        ? makeStructToMapScan({
            scoreNames: baseScoreNames,
            metricNames: baseMetricNames,
            summaryScan,
          })
        : null,
      primaryKey: "id",
      signals: stableSignals,
    });

  const { ready: summaryRealtimeReady } = useSummaryRealtime({
    btObject,
    summaryTableName,
    summarySchema,
    projectedColumns,
  });

  const refreshed = useMemo(() => {
    if (!summaryTableReady || !summaryRealtimeReady) {
      return 0;
    }
    return summaryTableReady + summaryRealtimeReady;
  }, [summaryTableReady, summaryRealtimeReady]);

  const { data } = useDBQuery(
    summaryTableName
      ? `
      WITH score_names AS (
        SELECT array_agg(DISTINCT k.unnest) AS score_names FROM ${doubleQuote(summaryTableName)}, unnest(map_keys(scores)) AS k
      ),
      metric_names AS (
        SELECT array_agg(DISTINCT k.unnest) AS metric_names FROM ${doubleQuote(summaryTableName)}, unnest(map_keys(metrics)) AS k
      )
        SELECT score_names.score_names, metric_names.metric_names
          FROM score_names, metric_names
      `
      : null,
    [refreshed],
  );
  const { scoreNames: currentScoreNames, metricNames: currentMetricNames } =
    useMemo(() => {
      const ret = {
        scoreNames: data?.toArray()[0].score_names?.toJSON(),
        metricNames: data?.toArray()[0].metric_names?.toJSON(),
      };
      return ret;
    }, [data]);

  return useMemo(
    () =>
      fastExperimentSummary
        ? {
            scan: summaryTableName
              ? `SELECT * FROM ${doubleQuote(summaryTableName)} WHERE (${doubleQuote(ObjectDeleteField)} IS NULL OR NOT ${doubleQuote(ObjectDeleteField)})`
              : null,
            schema: summarySchema,
            refreshed: refreshed,
            scoreNames: currentScoreNames ?? null,
            metricNames: currentMetricNames ?? null,
            tableName: summaryTableName,
            objectId: btObject.id,
            searchKeyName: summarySearch?.key ?? "",
            realtimeState: realtimeQuery?.realtime_state,
          }
        : undefined,
    [
      fastExperimentSummary,
      summaryTableName,
      summarySchema,
      refreshed,
      currentScoreNames,
      currentMetricNames,
      btObject.id,
      summarySearch?.key,
      realtimeQuery?.realtime_state,
    ],
  );
}

const BackendSummariesContext = createContext<{
  summaries: LoadedExperimentSummary[];
  setSummaries: (
    summaries:
      | LoadedExperimentSummary[]
      | ((prev: LoadedExperimentSummary[]) => LoadedExperimentSummary[]),
  ) => void;
} | null>(null);

export const BackendSummariesProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [summaries, setSummaries] = useState<LoadedExperimentSummary[]>([]);
  return (
    <BackendSummariesContext.Provider
      value={useMemo(
        () => ({ summaries, setSummaries }),
        [summaries, setSummaries],
      )}
    >
      {children}
    </BackendSummariesContext.Provider>
  );
};

const EMPTY_CONTEXT = { summaries: [], setSummaries: () => {} };
const useBackendSummaries = (enableSummary: boolean = true) => {
  const context = useContext(BackendSummariesContext);
  if (!context && enableSummary) {
    throw new Error(
      "useBackendSummaries must be used within a BackendSummariesProvider",
    );
  }
  return context ?? EMPTY_CONTEXT;
};

const BackendSummaryLoader = ({
  btObject,
  fastExperimentSummary,
  projectedColumns,
  customColumns,
}: {
  btObject: {
    id: string;
    type: DataObjectType;
  };
  fastExperimentSummary: boolean;
  projectedColumns?: AliasExpr[];
  customColumns?: CustomColumn[];
}) => {
  const previousBackendSummary = useRef<LoadedExperimentSummary | undefined>(
    undefined,
  );
  const backendSummary = useBackendSummary({
    btObject,
    fastExperimentSummary,
    projectedColumns,
    customColumns,
  });
  const { setSummaries } = useBackendSummaries();
  useEffect(() => {
    if (
      backendSummary &&
      JSON.stringify(backendSummary) !==
        JSON.stringify(previousBackendSummary.current)
    ) {
      previousBackendSummary.current = backendSummary;
      setSummaries((prev) => {
        const newSummaries = [...prev];
        const index = newSummaries.findIndex(
          (s) => s.objectId === backendSummary.objectId,
        );
        if (index === -1) {
          newSummaries.push(backendSummary);
        } else {
          newSummaries[index] = backendSummary;
        }
        return newSummaries;
      });
    }
  }, [backendSummary, setSummaries]);

  useEffect(() => {
    return () => {
      setSummaries((prev) => prev.filter((s) => s.objectId !== btObject.id));
    };
  }, [setSummaries, btObject.id]);

  return null;
};

export const BackendSummariesLoader = ({
  btObjects,
  projectedColumns,
  customColumns,
}: {
  btObjects: {
    id: string;
    type: DataObjectType;
  }[];
  projectedColumns?: AliasExpr[];
  customColumns?: CustomColumn[];
}) => {
  const {
    flags: { fastExperimentSummary },
  } = useFeatureFlags();
  return btObjects.map((btObject) => (
    <BackendSummaryLoader
      key={btObject.id}
      btObject={btObject}
      fastExperimentSummary={fastExperimentSummary}
      projectedColumns={projectedColumns}
      customColumns={customColumns}
    />
  ));
};
