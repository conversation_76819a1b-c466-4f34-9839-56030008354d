"use client";

import { Chart } from "#/ui/charts/Chart";
import { useHistogramChart } from "#/ui/charts/histogram-grouped";
import { dbQuery, useDuckDB } from "#/utils/duckdb";
import { isEmpty } from "#/utils/object";
import { forwardRef, useImperativeHandle, useMemo, useCallback } from "react";
import { doubleQuote } from "#/utils/sql-utils";
import { Skeleton } from "#/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import {
  type ComparisonExperimentSpanSummary,
  type PrimaryExperimentSpanSummary,
} from "./(queries)/useExperiment";

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
const HISTOGRAM_BOUNDS = [0, 1] as [number, number];

export type ChartBrushFilter = {
  scoreName: string;
  scoreBounds?: [number, number];
};

export type DistributionChartInsightProps = {
  baseComparisonQuery: string | null;
  scoreField: string;
  signals: number[];
  setFilter: (filter: ChartBrushFilter) => void;
  primaryExperiment?: PrimaryExperimentSpanSummary;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
};

export const DistributionChartInsight = forwardRef<
  {
    [scoreName: string]: { removeBrush: () => void };
  },
  DistributionChartInsightProps
>(function DistributionChartInsight(
  {
    baseComparisonQuery,
    scoreField,
    setFilter,
    signals,
    primaryExperiment,
    comparisonExperimentData,
  },
  ref,
) {
  const duck = useDuckDB();
  const { data: scoreData, isPending } = useQuery({
    queryKey: [
      "scoreDistributionChartQuery",
      scoreField,
      primaryExperiment,
      comparisonExperimentData,
      signals,
    ],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();
      const queries = [primaryExperiment, ...comparisonExperimentData].map(
        (e) => {
          if (!e || !e.scoreFields.includes(scoreField)) {
            return null;
          }

          return `SELECT scores.${doubleQuote(scoreField)} as score from (${e.tableScan})`;
        },
      );
      const results = await Promise.all(
        queries.map((q) =>
          q == null
            ? Promise.resolve(null)
            : dbQuery(conn, signal, q).then((r) => r?.toArray() ?? []),
        ),
      );

      const scores: number[][] = [];
      results.forEach((rows, i) => {
        if (!rows) {
          return;
        }
        rows.forEach((row) => {
          if (!isEmpty(row?.score)) {
            scores[i] ??= [];
            scores[i].push(Number(row.score));
          }
        });
      });
      return scores;
    },
    enabled:
      !!duck &&
      !!primaryExperiment?.tableScan &&
      signals.every((s) => s > 0) &&
      comparisonExperimentData.every((e) => e.tableScan && e.refreshed > 0),
  });

  const onBrush = useCallback(
    (v: [number, number] | null) => {
      setFilter({
        scoreName: scoreField,
        scoreBounds: v ?? undefined,
      });
    },
    [setFilter, scoreField],
  );

  const chartData = useMemo(() => scoreData ?? [], [scoreData]);
  const {
    chartProps: histogramAreaChartProps,
    removeBrush,
    bottomAxisProps,
  } = useHistogramChart({
    height: 28,
    bucketCount: 10,
    bounds: HISTOGRAM_BOUNDS,
    data: chartData,
    onBrush,
    dimComparisons: true,
  });

  useImperativeHandle(ref, () => {
    const current = ref && "current" in ref ? ref.current || {} : {};

    return {
      ...current,
      [scoreField]: {
        removeBrush,
      },
    };
  });

  if (isPending) {
    return <Skeleton className="mb-1 h-14 w-full" />;
  }

  return (
    <div className="mb-1 rounded-md p-2 bg-primary-50">
      <Chart
        className="w-full"
        {...histogramAreaChartProps}
        bottomAxisProps={{
          ...bottomAxisProps,
          tickCount: 3,
          labelsOnly: true,
          tickFormatter: (v) => {
            const label = v.toLocaleString(undefined, {
              maximumFractionDigits: 0,
              style: "percent",
            });
            // hide X5% labels
            if (label.endsWith("5%")) return "";
            return label;
          },
        }}
        placeholder="No score data"
        renderPlaceholder={() => (
          <div className="py-1 text-center text-xs text-primary-400">
            No score data
          </div>
        )}
      />
    </div>
  );
});
