import { runAISearch } from "#/utils/ai-search/actions/events";
import { type ClientOptions } from "openai";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
} from "react";
import { useOrg } from "#/utils/user";
import { type AISearchType } from "@braintrust/local";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import { type ClauseChecker, type Search } from "#/utils/search/search";
import { GROUP_BY_INPUT_VALUE } from "#/ui/table/grouping/controls";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { type CustomColumn } from "@braintrust/core/typespecs";
import { type ViewProps } from "#/utils/view/use-view";

export function useTableControls({
  projectedPaths,
  searchType = "experiment",
  clauseChecker,
  setSearch,
  experimentTable,
  customColumns,
  pageIdentifier,
  viewProps,
}: {
  projectedPaths: string[];
  searchType: AISearchType;
  clauseChecker: <PERSON>e<PERSON>he<PERSON> | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  experimentTable?: {
    metadataRootFields: string[][];
    scoreFields: string[];
  };
  customColumns: CustomColumn[] | undefined;
  pageIdentifier: string;
  viewProps: ViewProps;
}) {
  const org = useOrg();
  const runExperimentAISearch = useCallback(
    (openAIOpts: ClientOptions, query: string) =>
      runAISearch({
        openAIOpts,
        apiUrl: org.api_url,
        query,
        orgName: org.name,
        searchType,
        aiSchemaColumns: projectedPaths,
        scoreFields: experimentTable?.scoreFields ?? [],
      }),
    [org, searchType, projectedPaths, experimentTable?.scoreFields],
  );

  const { applySearch } = useFilterSortBarSearch({
    runAISearch: runExperimentAISearch,
    clauseChecker,
    setSearch,
  });

  const { grouping, setGrouping } = viewProps;

  const setTableGrouping = useCallback(
    (valueOrUpdater: SetStateAction<string>) => {
      const next =
        typeof valueOrUpdater === "function"
          ? valueOrUpdater(grouping)
          : valueOrUpdater;
      setGrouping(next);
      if (next === GROUP_BY_NONE_VALUE) {
        setSearch((prev) => ({
          ...prev,
          sort: prev.sort?.filter((s) => s.comparison?.type !== "regression"),
        }));
      }
    },
    [grouping, setGrouping, setSearch],
  );
  const tableGroupingOptions = useMemo(
    () =>
      (experimentTable?.metadataRootFields ?? []).map((f) => ({
        label: f.join("."),
        value: JSON.stringify(f),
      })),
    [experimentTable?.metadataRootFields],
  );

  const tableGrouping = useMemo(
    () =>
      [
        GROUP_BY_INPUT_VALUE,
        ...tableGroupingOptions.map(({ value }) => value),
        ...(customColumns ?? []).map(({ name }) => name),
      ].find((v) => v === grouping) ?? GROUP_BY_NONE_VALUE,
    [tableGroupingOptions, customColumns, grouping],
  );

  return {
    applySearch,
    tableGrouping,
    setTableGrouping,
    tableGroupingOptions,
    runExperimentAISearch,
  };
}
