import { OrgUsersContext } from "#/utils/org-users-context";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useMemo,
} from "react";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { useContext } from "react";
import { GroupKeyFormatterWithCell } from "#/ui/table/formatters/group-key-formatter";
import {
  type ComparisonExperimentSpanSummary,
  type PrimaryExperimentSpanSummary,
} from "./(queries)/useExperiment";
import { setTagSearchFn, TagsFormatterFactory } from "#/ui/trace/tags";
import { type FormatterMap } from "#/ui/field-to-column";
import { type GroupAggregationProps } from "#/ui/table/formatters/group-aggregation-formatter";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { type AggregationFieldType } from "#/utils/queries/aggregations";
import { type AggregationType } from "#/utils/queries/aggregations";
import { ErrorCellWithFormatter } from "#/ui/table/formatters/error-formatter";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { CommentsFormatterFactory } from "#/ui/table/formatters/comments-formatter";
import { CurlyBraces, DiffIcon } from "lucide-react";
import { DiffFormatterFactory } from "#/ui/table/formatters/diff-formatter";
import {
  type DiffModeState,
  useComparisonExperimentId,
} from "#/ui/query-parameters";
import { SpanTypeInfoFormatter } from "#/ui/table/formatters/span-info-formatter";
import { ProjectContext } from "../../projectContext";
import { type SummaryBreakdownData } from "./(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { useOrg } from "#/utils/user";
import { type Search } from "#/utils/search/search";
import { type ClauseChecker } from "#/utils/search/search";
import { type RegressionFilter } from "./regressions-query";
import { InputFormatter } from "#/ui/table/formatters/input-formatter";
import { PercentWithAggregationFormatter } from "#/ui/table/formatters/percent-formatter";
import {
  ComputedCostMetricFields,
  ComputedDurationMetricFields,
  ComputedTokenMetricFields,
} from "@braintrust/local/query";
import { DurationWithAggregationFormatter } from "#/ui/table/formatters/duration-formatter";
import { DefaultWithAggregationFormatter } from "#/ui/table/formatters/default-formatter";
import { CostWithAggregationFormatter } from "#/ui/table/formatters/cost-formatter";
import { StartEndFormatter } from "#/ui/table/formatters/start-end-formatter";
import { type TableRowHeight } from "#/ui/table-row-height-toggle";
import { type CustomColumn as CustomColumnDb } from "@braintrust/core/typespecs";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";

export function useTableFormatters({
  experiment,
  comparisonExperimentData,
  diffMode,
  addRegressionFilter,
  summaryBreakdownData,
  clauseChecker,
  setSearch,
  tableGrouping,
  primaryExperimentReady,
  baseQuery,
  diffQuery,
  enableStarColumn,
  rowHeight,
  layout,
  isPlayground,
  customColumns,
  tallGroupRows,
}: {
  experiment: PrimaryExperimentSpanSummary | undefined;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  diffMode: DiffModeState | null;
  addRegressionFilter: (f: RegressionFilter) => void;
  summaryBreakdownData: SummaryBreakdownData;
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  tableGrouping: string;
  rowHeight: TableRowHeight;
  layout: string;
  primaryExperimentReady: number[];
  baseQuery: string | null;
  diffQuery: string | null;
  enableStarColumn?: boolean;
  isPlayground?: boolean;
  customColumns?: CustomColumn[] | CustomColumnDb[];
  tallGroupRows: boolean;
}) {
  const org = useOrg();
  const projectContext = useContext(ProjectContext);
  const { projectName, config } = projectContext;
  const gridLayout = layout === "grid";
  const rowComparisonProps = useMemo(
    () =>
      experiment?.hasTrials &&
      (gridLayout ||
        (diffMode?.enabled && diffMode.enabledValue === "between_experiments"))
        ? {
            orgName: org.name,
            projectName: projectName,
            experimentId: experiment?.id ?? "",
            comparisonExperimentData,
            addRegressionFilter,
            gridLayout,
          }
        : undefined,
    [
      experiment?.hasTrials,
      diffMode,
      org.name,
      projectName,
      experiment?.id,
      comparisonExperimentData,
      addRegressionFilter,
      gridLayout,
    ],
  );

  const [aggregationTypes, _setAggregationType] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: org.id ?? "",
    key: "aggregationTypes",
  });

  const [_aggregationExperimentId, setAggregationExperimentId] =
    useComparisonExperimentId();
  const aggregationExperimentId =
    (_aggregationExperimentId || experiment?.id) ?? "";

  const setAggregationType = useCallback(
    (
      type: AggregationFieldType,
      value: AggregationType,
      experimentId?: string,
    ) => {
      _setAggregationType((prev) => ({
        ...prev,
        [type]: value,
      }));
      if (experimentId) {
        setAggregationExperimentId(experimentId);
      }
    },
    [_setAggregationType, setAggregationExperimentId],
  );

  const onTagClick = useMemo(
    () => setTagSearchFn(clauseChecker, setSearch),
    [clauseChecker, setSearch],
  );

  const showMultiExperimentRowHeight =
    (!!comparisonExperimentData.length &&
      !!diffMode?.enabled &&
      diffMode.enabledValue === "between_experiments") ||
    isPlayground;
  const multiExperimentRowCount = comparisonExperimentData.length + 1;
  const tableRowHeightCount = showMultiExperimentRowHeight
    ? multiExperimentRowCount
    : rowHeight === "tall"
      ? 6
      : 1;
  const multilineRow = useMemo(
    () => ({
      numRows: tableRowHeightCount,
      gapSize: !showMultiExperimentRowHeight ? 0 : 6,
      numGroupRows: tallGroupRows ? multiExperimentRowCount : undefined,
    }),
    [
      tableRowHeightCount,
      showMultiExperimentRowHeight,
      multiExperimentRowCount,
      tallGroupRows,
    ],
  );

  const { orgUsers } = useContext(OrgUsersContext);

  const { formatters, outputExpectedFormatters } = useMemo(() => {
    const groupColumnName = "span_type_info";
    const isGrouping = tableGrouping !== GROUP_BY_NONE_VALUE;
    const groupAggregationProps: GroupAggregationProps = {
      groupAggregationTypes: aggregationTypes,
      setGroupAggregationType: setAggregationType,
      isGrouping,
      summaryData: summaryBreakdownData.hasGroups
        ? {
            groupKey: groupColumnName,
            aggregationExperimentId,
            experimentSummaryData: summaryBreakdownData.summary,
            addRegressionFilter,
            numGroupRows: multilineRow.numGroupRows,
          }
        : undefined,
      customColumns: customColumns ?? experiment?.customColumns,
    };

    const baseFormatters: FormatterMap = {
      input: {
        cell: InputFormatter,
        ignoreMultilineRendering: true,
        colSize: gridLayout
          ? {
              size: 300,
              minSize: 300,
            }
          : undefined,
      },
      start: {
        cell: StartEndFormatter,
      },
      end: {
        cell: StartEndFormatter,
      },
      scores: PercentWithAggregationFormatter({
        ...groupAggregationProps,
        summaryEnabled: true,
      }),
      tags: {
        cell: TagsFormatterFactory({
          tagConfig: config?.tags ?? [],
          onTagClick,
        }),
      },
      span_type_info: {
        ...GroupKeyFormatterWithCell({
          Formatter: SpanTypeInfoFormatter,
          groupKey: groupColumnName,
          groupAggregationProps,
          rowComparisonProps,
        }),
        ignoreMultilineRendering: true,
        pinnedColumnIndex: isGrouping
          ? !!rowComparisonProps
            ? enableStarColumn
              ? 3
              : 2
            : enableStarColumn
              ? 2
              : 1
          : undefined,
      },
      ...Object.fromEntries(
        ComputedDurationMetricFields.map((f) => [
          f,
          DurationWithAggregationFormatter({
            ...groupAggregationProps,
            summaryEnabled: true,
          }),
        ]),
      ),
      ...Object.fromEntries(
        ComputedTokenMetricFields.map((f) => [
          f,
          DefaultWithAggregationFormatter({
            ...groupAggregationProps,
            summaryEnabled: true,
          }),
        ]),
      ),
      ...Object.fromEntries(
        ComputedCostMetricFields.map((f) => [
          f,
          CostWithAggregationFormatter({
            ...groupAggregationProps,
            summaryEnabled: true,
          }),
        ]),
      ),
      error: ErrorCellWithFormatter,
      ...Object.fromEntries(
        (customColumns ?? experiment?.customColumns ?? []).map(({ name }) => [
          name,
          { headerLabel: name, headerIcon: CurlyBraces },
        ]),
      ),
      comments: {
        cell: CommentsFormatterFactory(orgUsers),
      },
    };

    return {
      formatters: makeFormatterMap(baseFormatters),
      outputExpectedFormatters: makeFormatterMap({
        ...baseFormatters,
        output_vs_expected: {
          headerLabel: "Output vs. expected",
          headerIcon: DiffIcon,
          cell: DiffFormatterFactory({
            addedFieldName: "Output",
            removedFieldName: "Expected",
          }),
          ignoreMultilineRendering: true,
        },
      }),
    };
  }, [
    aggregationExperimentId,
    tableGrouping,
    aggregationTypes,
    setAggregationType,
    addRegressionFilter,
    summaryBreakdownData,
    config?.tags,
    onTagClick,
    orgUsers,
    experiment?.customColumns,
    gridLayout,
    rowComparisonProps,
    multilineRow,
    enableStarColumn,
    customColumns,
  ]);

  const tableQuery: {
    query: string | null;
    signals: number[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    formatters: FormatterMap<any, any>;
  } = useMemo(() => {
    if (
      !gridLayout &&
      ((diffMode?.enabled && diffMode.enabledValue === "between_experiments") ||
        isPlayground)
    ) {
      return {
        query: diffQuery,
        signals: [
          ...primaryExperimentReady,
          ...comparisonExperimentData.flatMap((e) =>
            [e.refreshed, e.auditLog?.refreshed].filter((v) => v != null),
          ),
        ],
        formatters,
      };
    } else if (
      diffMode?.enabled &&
      diffMode.enabledValue === "expected_output"
    ) {
      return {
        query: baseQuery,
        signals: primaryExperimentReady,
        formatters: outputExpectedFormatters,
      };
    }

    return {
      query: baseQuery,
      signals: primaryExperimentReady,
      formatters,
    };
  }, [
    diffMode,
    primaryExperimentReady,
    baseQuery,
    diffQuery,
    comparisonExperimentData,
    formatters,
    outputExpectedFormatters,
    gridLayout,
    isPlayground,
  ]);

  return {
    tableQuery,
    rowComparisonProps,
    multilineRow,
    rowHeight,
    setAggregationExperimentId,
  };
}
