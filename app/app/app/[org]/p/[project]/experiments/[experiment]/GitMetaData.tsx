"use client";

import { <PERSON><PERSON><PERSON>iewer } from "#/ui/markdown";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import {
  type TooltipContentProps,
  TooltipPortal,
} from "@radix-ui/react-tooltip";
import { GitBranch, Info } from "lucide-react";

interface GitMetadataProps {
  commit?: string | null;
  branch?: string | null;
  commit_time?: string | null;
  author_name?: string | null;
  author_email?: string | null;
  commit_message?: string | null;
  dirty?: boolean | null;
  git_diff?: string | null;
  isInTable?: boolean;
  enableInteractiveTooltip?: boolean;
  side?: TooltipContentProps["side"];
}

export function GitMetaData({
  commit,
  branch,
  commit_time,
  author_name,
  author_email,
  commit_message,
  dirty,
  git_diff,
  className,
  isInTable,
  enableInteractiveTooltip,
  side,
}: GitMetadataProps & {
  className?: string;
}) {
  let author = "";
  if (author_name) {
    author += author_name;
  }
  if (author_email) {
    author += ` <${author_email}>`;
  }

  if (!commit && !branch) return null;

  return (
    <Tooltip
      delayDuration={100}
      disableHoverableContent={!enableInteractiveTooltip}
    >
      <TooltipTrigger asChild>
        {isInTable ? (
          <span className={cn("font-mono text-sm", className)}>
            {branch}
            {commit ? <span> {commit.slice(0, 7)}</span> : null}
          </span>
        ) : (
          <div
            className={cn(
              "inline-flex flex-none items-center gap-2 font-mono text-sm text-primary-600 truncate max-w-full",
              className,
            )}
          >
            <GitBranch className="size-3 flex-none" />
            <span className="truncate">
              {branch} {commit ? <span>{commit.slice(0, 7)}</span> : null}
            </span>
          </div>
        )}
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          side={side ?? "bottom"}
          align="start"
          sideOffset={8}
          className="w-auto min-w-[450px] max-w-[550px] p-3 text-sm bg-background text-primary-800"
        >
          <>
            <div className="text-xs text-primary-600">
              {author && (
                <>
                  <span>{author}</span>{" "}
                </>
              )}
              {commit_time && (
                <>
                  committed on{" "}
                  <time dateTime={commit_time} title={commit_time}>
                    {new Date(commit_time).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })}
                  </time>
                </>
              )}
              {commit ? (
                <span className="ml-2 font-mono">({commit.slice(0, 7)})</span>
              ) : null}
            </div>
            {commit_message && <MarkdownViewer value={commit_message} />}
          </>
          {dirty && (
            <div className="mb-1 flex items-center gap-1 pt-2 text-xs text-primary-500">
              <Info className="size-3" /> This experiment was run on uncommitted
              changes
            </div>
          )}
          {git_diff && (
            <SyntaxHighlight
              content={git_diff}
              className="relative whitespace-pre-wrap rounded-md border p-2 text-xs border-primary-100"
              language="bash"
            />
          )}
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
}
