import { useCallback } from "react";
import { DatasetIdField, ProjectIdField } from "#/utils/duckdb";
import { type DML } from "#/utils/mutable-object";

export function useInsertRows({
  dml,
  projectId,
  datasetId,
}: {
  dml: DML;
  projectId?: string;
  datasetId?: string;
}) {
  return useCallback(
    async (rows: Record<string, unknown>[]) => {
      const preparedRows = await dml.prepareUpserts(
        rows.map((row) => ({
          [DatasetIdField]: datasetId,
          [ProjectIdField]: projectId,
          ...row,
        })),
      );
      await dml.upsert(preparedRows);
    },
    [datasetId, projectId, dml],
  );
}
