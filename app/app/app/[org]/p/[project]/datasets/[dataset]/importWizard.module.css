.dropzone {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  border-width: 2px;
  min-width: 400px;
  width: 100%;
  min-height: 320px;
  border-style: dashed;
  @apply bg-primary-100;
  border-radius: 8px;
  outline: none;
  transition: border 0.24s ease-in-out;
}
.dropzone:focus {
  @apply border-accent-500;
}

.preview {
  @apply text-xs;
  @apply my-3;
  @apply p-3;
  @apply bg-primary-100;
  position: relative;
  max-width: 600px;
  min-height: 50px;
  overflow: auto;
}

:global(html.dark) .dropzone {
  color: #cccccc;
}

:global(html.dark) .preview:after {
  box-shadow: inset 0 -10px 10px rgba(40, 40, 40, 0.6);
}
