"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  datasetSchema,
  projectSchema,
  organizationSchema,
} from "@braintrust/core/typespecs";

const datasetInfoSchema = z.strictObject({
  id: datasetSchema.shape.id,
  name: datasetSchema.shape.name,
  description: datasetSchema.shape.description,
  metadata: datasetSchema.shape.metadata,
  project_id: projectSchema.shape.id,
  project_name: projectSchema.shape.name,
  org_id: organizationSchema.shape.id,
  org_name: organizationSchema.shape.name,
});

export type DatasetInfo = z.infer<typeof datasetInfoSchema>;

export async function fetchDatasetInfo(
  {
    org_name,
    project_name,
    dataset_name,
  }: {
    org_name: string;
    project_name: string;
    dataset_name: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<DatasetInfo | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: datasetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "dataset",
      aclPermission: "read",
    },
    filters: {
      org_name,
      project_name,
      name: dataset_name,
    },
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
      select
          datasets.id, datasets.name, datasets.description, datasets.metadata,
          projects.id project_id, projects.name project_name,
          organizations.id org_id, organizations.name org_name
      from
          (${datasetQuery}) datasets
          join projects on (datasets.project_id = projects.id)
          join organizations on (projects.org_id = organizations.id)
  `,
    queryParams.params,
  );

  if (!rows || !rows.length) {
    return null;
  } else {
    return datasetInfoSchema.parse(rows[0]);
  }
}

const createdDatasetInfoSchema = z.strictObject({
  id: datasetSchema.shape.id,
  project_id: projectSchema.shape.id,
});

export type CreatedDatasetInfo = z.infer<typeof createdDatasetInfoSchema>;

export async function fetchCreatedDatasetInfo(
  {
    dataset_id,
  }: {
    dataset_id: string | null;
  },
  authLookupRaw?: AuthLookup,
): Promise<CreatedDatasetInfo | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!dataset_id) {
    return null;
  }

  const { query: datasetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "dataset",
      aclPermission: "read",
    },
    filters: {
      id: [dataset_id],
    },
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `
      select datasets.id, projects.id project_id
      from
          (${datasetQuery}) datasets join projects on (datasets.project_id = projects.id)
  `,
    queryParams.params,
  );
  if (!rows || !rows.length) {
    return null;
  } else {
    return createdDatasetInfoSchema.parse(rows[0]);
  }
}

export async function getDatasetName(
  {
    dataset_id,
  }: {
    dataset_id: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<string | null> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: datasetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "dataset",
      aclPermission: "read",
    },
    filters: {
      id: [dataset_id],
    },
  });

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    `select name from (${datasetQuery}) datasets`,
    queryParams.params,
  );
  if (!rows?.length) {
    return null;
  } else {
    return z.string().parse(rows[0]["name"]);
  }
}
