import { useParquetView } from "#/utils/duckdb";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "#/ui/dialog";
import { useMutableObject } from "#/utils/mutable-object";
import { useContext, useState } from "react";
import { ImportWizard } from "./importWizard";
import { useInsertRows } from "./useInsertRows";
import { toast } from "sonner";
import { type ImportProgressType, UploadContext } from "#/ui/upload-provider";

interface UploadRowsToDatasetDialogProps {
  projectId?: string;
  datasetId?: string;
  open: boolean;
  setIsOpen: (open: boolean) => void;
}

export function UploadRowsToDatasetDialog({
  projectId,
  datasetId,
  open,
  setIsOpen,
}: UploadRowsToDatasetDialogProps) {
  const { executeImport, importProgress, setImportProgress } =
    useContext(UploadContext);

  const { scan: datasetScan, channel: datasetChannel } = useParquetView({
    objectType: "dataset",
    search: datasetId,
  });

  const dml = useMutableObject({
    scan: datasetScan,
    objectType: "dataset",
    channel: datasetChannel,
  });

  const insertRows = useInsertRows({ dml, projectId, datasetId });

  const setImportProgressPartial = (params: Partial<ImportProgressType>) => {
    setImportProgress?.((oldState) => ({ ...oldState, ...params }));
  };
  const [isImporting, setIsImporting] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setIsOpen}>
      <DialogContent className="max-h-full overflow-auto sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>Upload dataset</DialogTitle>
        </DialogHeader>
        <ImportWizard
          setDataToImport={async ({ data }) => {
            if (isImporting || data.length === 0) return;
            const importData = async () => {
              setIsImporting(true);
              setIsOpen(false);
              try {
                await executeImport({
                  data: data,
                  insertRows,
                  setImportProgress: setImportProgressPartial,
                });
              } catch (e) {
                console.error(e);
                toast.error("Dataset import error", {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  description: (e as Error)?.message,
                });
              }
              setIsImporting(false);
            };
            importData();
          }}
          importProgress={importProgress}
          setImportProgress={setImportProgressPartial}
        />
      </DialogContent>
    </Dialog>
  );
}

export const useUploadRowsToDatasetDialog = (
  projectId?: string,
  datasetId?: string,
) => {
  const [isModalOpened, setIsModalOpened] = useState(false);

  return {
    open: () => {
      setIsModalOpened(true);
    },
    modal: (
      <UploadRowsToDatasetDialog
        projectId={projectId}
        datasetId={datasetId}
        open={isModalOpened}
        setIsOpen={setIsModalOpened}
      />
    ),
  };
};
