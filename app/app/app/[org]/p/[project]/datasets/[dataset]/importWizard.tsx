import { But<PERSON>, buttonVariants } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Loading } from "#/ui/loading";
import { newId } from "#/utils/btapi/btapi";
import { cn } from "#/utils/classnames";
import { CreatedField, IdField } from "#/utils/duckdb";
import { csvParse } from "d3-dsv";
import React, {
  type RefObject,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import { useDropzone } from "react-dropzone";
import styles from "./importWizard.module.css";
import { ExternalLink } from "#/ui/link";
import { safeDeserializePlainStringAsJSON, isEmpty } from "#/utils/object";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { FileSpreadsheet, GripVertical } from "lucide-react";
import {
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useDroppable,
  useDraggable,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { Switch } from "#/ui/switch";
import { BasicTooltip } from "#/ui/tooltip";
import { type ImportProgressType } from "#/ui/upload-provider";
import StyledDndContext from "#/ui/styled-dnd-context";

const dataFields = ["input", "expected", "metadata"] as const;
type DataField = (typeof dataFields)[number];
type Category = DataField | "unused";

type Row = {
  [IdField]: string;
  [CreatedField]: number;
} & {
  [field in DataField]: unknown;
};

const _supportedFileTypes = ["csv", "json"] as const;
type SupportedFileType = (typeof _supportedFileTypes)[number];

type LoadedFile = {
  text: string;
  type: SupportedFileType;
};

function loadFile(file: File, setFile: (f: LoadedFile) => void) {
  const reader = new FileReader();
  reader.onload = () => {
    setFile({
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      text: reader.result as string,
      // If the MIME type isn't recognized (like for JSONL files),
      // try to parse the file as JSON/JSONL.
      type: file.type === "text/csv" ? "csv" : "json",
    });
  };
  reader.readAsText(file);
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function parseJSONFile(file: LoadedFile): any[] {
  try {
    const parsed = JSON.parse(file.text);
    if (
      Array.isArray(parsed) &&
      parsed.every((row) => typeof row === "object")
    ) {
      return parsed;
    }
  } catch {
    // If the file is not a valid JSON array, try parsing it as JSONL.
  }

  const lines = file.text.split("\n").filter((line) => line.trim().length > 0);
  try {
    return lines.map((line) => {
      const row = JSON.parse(line);
      if (typeof row !== "object" || row === null) {
        throw new Error(
          "Could not parse file as JSONL. Each line must be a valid JSON object",
        );
      }
      return row;
    });
  } catch (e) {
    throw new Error(
      "The input is neither a valid JSON array nor a valid JSONL (each row must be a valid JSON object)",
    );
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function parseFile(file: LoadedFile): { rows: any[]; columns: string[] } {
  switch (file.type) {
    case "csv":
      const result = csvParse(file.text);
      return {
        rows: result,
        columns: result.columns,
      };
    case "json":
      const rows = parseJSONFile(file);
      return {
        rows,
        columns: [...new Set(rows.flatMap(Object.keys))],
      };
  }
}

type Item = { value: string; label: string; category: Category };

const CardDropZone = ({
  title,
  items,
  category,
}: {
  title: string;
  items: Item[];
  category: Category;
}) => {
  const { isOver, active, setNodeRef } = useDroppable({
    id: category,
    data: { category },
  });

  return (
    <div
      className={cn(
        "w-64 min-h-[200px] p-2 flex-none rounded-lg border transition-colors",
        {
          "bg-accent-100":
            isOver && active?.data.current?.category !== category,
        },
      )}
      ref={setNodeRef}
    >
      <h2 className="mb-3 text-sm font-medium">{title}</h2>
      {items.map((item, i) => (
        <Card key={i} item={item} />
      ))}
    </div>
  );
};

const Card = ({ item, isOverlay }: { item: Item; isOverlay?: boolean }) => {
  const { isDragging, attributes, listeners, setNodeRef } = useDraggable({
    id: item.value,
    data: item,
  });

  return (
    <div
      className={cn(
        "p-2 mb-2 bg-primary-100 rounded-md border flex gap-2 items-center cursor-grab text-sm overflow-hidden",
        {
          "z-50 opacity-50": isDragging,
          "hover:bg-primary-200": !isDragging,
          "border-2 border-accent-500": isOverlay,
        },
      )}
      {...attributes}
      {...listeners}
      ref={setNodeRef}
    >
      <GripVertical className="size-3 flex-none text-primary-400" />
      <span className="flex-1 truncate" title={item.label}>
        {item.label}
      </span>
    </div>
  );
};

interface SorterWizardProps {
  columns: string[];
  apiRef: RefObject<{
    getCategorizedColumns: () => Record<DataField, string[]>;
    setAllAsInput: () => void;
  } | null>;
}

const SorterWizard = ({ columns, apiRef }: SorterWizardProps) => {
  const [categorized, setCategorized] = useState<{
    [cat in Category]: Item[];
  }>({
    unused: [],
    input: [],
    expected: [],
    metadata: [],
  });

  useEffect(() => {
    if (Object.values(categorized).flat().length > 0) return;

    const assignments: { [cat in Category]: Item[] } = { ...categorized };
    const makeItem = (col: string, category: Category) => ({
      value: col,
      label: col,
      category,
    });
    assignments.unused = columns
      .filter((col) => !dataFields.some((f) => col.toLowerCase() === f))
      .map((col) => makeItem(col, "unused"));
    for (const category of dataFields) {
      const match = columns.find((col) => col.toLowerCase() === category);
      if (match) {
        assignments[category] = [makeItem(match, category)];
      }
    }
    setCategorized(assignments);
  }, [categorized, columns]);

  useImperativeHandle(apiRef, () => ({
    getCategorizedColumns: () => {
      return {
        input: categorized.input.map((i) => i.value),
        expected: categorized.expected.map((i) => i.value),
        metadata: categorized.metadata.map((i) => i.value),
      };
    },
    setAllAsInput: () => {
      setCategorized((prev) => ({
        input: [
          ...prev.input,
          ...prev.expected,
          ...prev.metadata,
          ...prev.unused,
        ].map((item) => ({ ...item, category: "input" })),
        expected: [],
        metadata: [],
        unused: [],
      }));
    },
  }));

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor),
  );

  const [activeItem, setActiveItem] = useState<Item | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const activeItem = event.active.data.current as unknown as Item;
    setActiveItem(activeItem);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveItem(null);
    const { active, over } = event;

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const activeData = active.data.current as Item;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const overData = over?.data.current as Item | undefined;

    if (!over || !activeData || !overData) return;

    if (activeData.category === overData.category) {
      return;
    }

    const fromCategory = categorized[activeData.category];
    const toCategory = categorized[overData.category];

    // Remove the item from the source category
    const updatedFrom = fromCategory.filter(
      (item) => item.value !== activeData.value,
    );

    // Add the item to the destination category
    const updatedTo = [
      ...toCategory,
      { ...activeData, category: overData.category },
    ];

    setCategorized({
      ...categorized,
      [activeData.category]: updatedFrom,
      [overData.category]: updatedTo,
    });
  };

  return (
    <StyledDndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="flex flex-wrap gap-2">
        <CardDropZone
          title="Input"
          category="input"
          items={categorized.input}
        />
        <CardDropZone
          title="Expected"
          items={categorized.expected}
          category="expected"
        />
        <CardDropZone
          title="Metadata"
          items={categorized.metadata}
          category="metadata"
        />
        <CardDropZone
          title="Do not import"
          items={categorized.unused}
          category="unused"
        />
      </div>
      <DragOverlay>
        {activeItem ? <Card item={activeItem} isOverlay /> : null}
      </DragOverlay>
    </StyledDndContext>
  );
};

export const ImportWizard = ({
  forEmpty,
  setDataToImport,
  setImportProgress,
  importProgress,
}: {
  forEmpty?: boolean;
  setDataToImport: (params: { data: Row[]; file: File }) => void;
  setImportProgress: (params: Partial<ImportProgressType>) => void;
  importProgress: ImportProgressType;
}) => {
  const [acceptedFiles, setAcceptedFiles] = useState<File[]>([]);
  const { getRootProps, getInputProps } = useDropzone({
    multiple: false,
    onDrop: (acceptedFiles) => {
      setAcceptedFiles(acceptedFiles);
    },
  });

  const [loadedFile, setLoadedFile] = useState<LoadedFile | null>(null);
  const file = acceptedFiles.at(-1);

  React.useEffect(() => {
    file && loadFile(file, setLoadedFile);
  }, [file]);

  const sortedWizardRef = React.useRef<{
    getCategorizedColumns: () => Record<DataField, string[]>;
    setAllAsInput: () => void;
  }>({
    getCategorizedColumns: () => {
      return {
        input: [],
        expected: [],
        metadata: [],
      };
    },
    setAllAsInput: () => {},
  });

  const { rows, columns, error } = useMemo((): {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rows: any[];
    columns: string[];
    error?: string;
  } => {
    if (loadedFile) {
      try {
        const parsed = parseFile(loadedFile);
        if (parsed.rows.length === 0) {
          throw new Error("No rows found. Is the JSON file empty?");
        }
        return parsed;
      } catch (e: unknown) {
        return {
          rows: [],
          columns: [],
          error:
            e instanceof Error ? e.toString() : "An unknown error occurred.",
        };
      }
    }
    return { rows: [], columns: [] };
  }, [loadedFile]);

  const resetWizard = () => {
    setAcceptedFiles([]);
    setLoadedFile(null);
  };

  const uploadLimits = [10, 20, 50, 100];
  const [numRows, setNumRows] = useState<number | null>(null);

  const [flattenSingleColumn, setFlattenSingleColumn] = useState(true);

  if (!file) {
    return (
      <TableEmptyState
        label={
          <div className="text-lg font-medium text-primary-800">
            {forEmpty ? "This dataset is empty" : "Import data"}
          </div>
        }
        className="w-full text-primary-600"
      >
        <div className="text-sm text-primary-600">
          Populate this dataset by uploading a CSV or JSON file or{" "}
          <ExternalLink
            href="/docs/guides/datasets#inserting-records"
            className="inline font-medium text-accent-700"
          >
            insert data programmatically
          </ExternalLink>
        </div>
        <div className="flex w-full flex-col pt-8">
          <div {...getRootProps()} className={styles.dropzone}>
            <input {...getInputProps()} />
            <FileSpreadsheet className="size-8 text-primary-400" />
            <p className="text-pretty pt-3 text-center text-primary-400">
              Drag and drop CSV or JSON file here,
              <br />
              or click to select
            </p>
          </div>
        </div>
      </TableEmptyState>
    );
  }
  if (loadedFile === null) {
    return (
      <TableEmptyState label="Uploading">
        <Loading />
      </TableEmptyState>
    );
  }

  const handleMoveAllToInputClick = () => {
    sortedWizardRef.current.setAllAsInput();
  };

  const handleImportClick = () => {
    setImportProgress({
      totalRows: rows.length,
    });
    const data = dataTransformer({
      rows: numRows ? rows.slice(0, numRows) : rows,
      columnCategorized: sortedWizardRef.current.getCategorizedColumns(),
      fileType: loadedFile.type,
      flattenSingleColumn,
    });
    setDataToImport({ data, file });
  };

  const actionDisabled =
    importProgress.currentState !== "not started" &&
    importProgress.currentState !== "closed" &&
    importProgress.currentState !== "finished";

  return (
    <TableEmptyState
      label={
        <div className="flex items-center gap-2">
          <div className="text-lg font-semibold text-primary-800">
            Import {file.name}
          </div>
          <Button size="xs" onClick={resetWizard}>
            Reset
          </Button>
        </div>
      }
      className="w-full text-primary-800"
    >
      <pre
        className={cn(
          styles.preview,
          "max-w-screen-lg max-h-96 overflow-auto w-full border rounded-lg",
        )}
      >
        <div className="mb-2 font-inter text-xs text-primary-400">Preview</div>
        {loadedFile.text.length === 0 ? (
          <center className="mt-2">File is empty</center>
        ) : (
          loadedFile.text.slice(0, 700).split("\n").slice(0, 70).join("\n")
        )}
      </pre>
      {columns.length > 0 ? (
        <>
          <div className="mb-6 mt-3 max-w-screen-md text-balance text-center text-sm text-primary-600">
            Assign {loadedFile.type === "csv" ? "CSV columns" : "JSON keys"} to
            dataset fields.{" "}
            {loadedFile.type === "csv" &&
              "Note that the first row of the CSV file should contain the column names."}
          </div>
          <SorterWizard
            columns={columns}
            apiRef={sortedWizardRef}
            key={columns.join(",")}
          />
          <div className="flex flex-col items-center gap-2 pt-4">
            <div className="mb-2 flex items-center gap-2">
              {rows.length > 10 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="xs" isDropdown>
                      {numRows
                        ? `Upload first ${numRows} rows`
                        : `Upload all ${rows.length} rows`}
                    </Button>
                  </DropdownMenuTrigger>

                  <DropdownMenuContent align="start">
                    {uploadLimits
                      .filter((n) => n < rows.length)
                      .map((n) => (
                        <DropdownMenuItem key={n} onClick={() => setNumRows(n)}>
                          First {n} rows
                        </DropdownMenuItem>
                      ))}
                    <DropdownMenuItem onClick={() => setNumRows(null)}>
                      All {rows.length} rows
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <BasicTooltip tooltipContent="If multiple columns are added to a field, they will be formatted as an object where the keys are the column names. If this option is unchecked, fields with a single column will be formatted as an object as well.">
                <a
                  className={cn(
                    buttonVariants({ size: "xs" }),
                    "cursor-pointer gap-2",
                  )}
                  onClick={() => setFlattenSingleColumn(!flattenSingleColumn)}
                >
                  <Switch checked={flattenSingleColumn} />
                  Flatten value if a single column is added to a field
                </a>
              </BasicTooltip>
            </div>
            <div className="flex flex-row gap-2">
              <Button
                variant="border"
                disabled={actionDisabled}
                onClick={handleMoveAllToInputClick}
              >
                Move all to input
              </Button>
              <Button
                variant="primary"
                disabled={actionDisabled}
                onClick={handleImportClick}
              >
                Import
              </Button>
            </div>
          </div>
        </>
      ) : (
        <div className="my-3">
          <div>
            Unable to parse the selected file. Please choose a different file.
          </div>
          <div className="text-xs font-medium text-red-700">{error}</div>
        </div>
      )}
    </TableEmptyState>
  );
};

function makeField({
  row,
  columns,
  shouldDeserialize,
  flattenSingleColumn,
}: {
  row: Record<string, string | undefined>;
  columns: string[];
  shouldDeserialize: boolean;
  flattenSingleColumn: boolean;
}): unknown {
  if (columns.length === 0) {
    return undefined;
  }
  const maybeDeserialize = (value: string | undefined) =>
    shouldDeserialize && !isEmpty(value)
      ? safeDeserializePlainStringAsJSON(value)
      : value;
  const fieldValues = columns.map((col) => [col, maybeDeserialize(row[col])]);
  // Don't nest single-column fields.
  return fieldValues.length === 1 && flattenSingleColumn
    ? fieldValues[0][1]
    : Object.fromEntries(fieldValues);
}

const dataTransformer = ({
  rows,
  columnCategorized,
  fileType,
  flattenSingleColumn,
}: {
  rows: Record<string, string | undefined>[];
  columnCategorized: { [f in DataField]: string[] };
  fileType: SupportedFileType;
  flattenSingleColumn: boolean;
}): Row[] => {
  const shouldDeserialize = fileType === "csv";

  const baseMs = Date.now();

  const transformedRows = rows.map((row, i) => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const fieldMap = Object.fromEntries(
      dataFields.map((field) => [
        field,
        makeField({
          row,
          columns: columnCategorized[field],
          shouldDeserialize,
          flattenSingleColumn,
        }),
      ]),
    ) as { [f in DataField]: unknown };
    return {
      [IdField]: isEmpty(row.id) ? newId() : String(row.id),
      ...fieldMap,
      // Subtract an additional millisecond for each subsequent row
      // so that we retain the original CSV row order when using the
      // default sort order in the app (`created DESC`).
      //
      // TODO(austin): We used to use microsecond row offsets here, but it
      // turns out that we only support millisecond precision because:
      // - Writes (`runLogData`) truncate dates to milliseconds.
      // - Reads (node `pg`) convert to `Date` which has millisecond precision.
      //
      // I think it makes sense to just match our existing level of precision
      // rather than change more stuff, so I changed the offset to 1 ms per row.
      //
      // Unfortunately, in Clickhouse we only have 1-second resolution on our
      // datetime fields (like created) because we're using DateTime instead
      // of DateTime64. So this ordering will still break for CH queries.
      // Here are a couple ways we could fix this:
      // - Use 1-second row offsets instead of 1 ms.
      // - Instead of applying this time offset hack to retain the original CSV
      //   order, add a dedicated "row order" field to metadata and sort by that
      //   value if it's available.
      [CreatedField]: baseMs - i,
    };
  });

  // If there's only one metadata column, and it's not an object, wrap it in an object.
  return columnCategorized.metadata.length === 1 &&
    transformedRows.some(
      (row) => !isEmpty(row.metadata) && typeof row.metadata !== "object",
    )
    ? transformedRows.map((row) => ({
        ...row,
        metadata: { [columnCategorized.metadata[0]]: row.metadata },
      }))
    : transformedRows;
};
