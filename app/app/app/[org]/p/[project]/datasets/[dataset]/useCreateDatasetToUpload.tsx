import { type SavingState } from "#/ui/saving";
import { useParquetView } from "#/utils/duckdb";
import { useMutableObject } from "#/utils/mutable-object";
import { useState } from "react";
import { createDataset } from "./createDataset";
import { type fetchCreatedDatasetInfo } from "./actions";
import { useQueryFunc } from "#/utils/react-query";
import { useInsertRows } from "./useInsertRows";

export const useCreateDatasetToUpload = () => {
  const [createdDatasetId, setCreatedDatasetId] = useState<string | null>(null);
  const [isDatasetBeingCreated, setIsDatasetBeingCreated] = useState(false);

  const { data: dataset } = useQueryFunc<typeof fetchCreatedDatasetInfo>({
    fName: "fetchCreatedDatasetInfo",
    args: { dataset_id: createdDatasetId },
  });

  const {
    refreshed: datasetReady,
    scan: datasetScan,
    channel: datasetChannel,
  } = useParquetView({
    objectType: "dataset",
    search: dataset?.id,
  });

  const [savingState, setSavingState] = useState<SavingState>("none");

  const dml = useMutableObject({
    scan: datasetScan,
    objectType: "dataset",
    channel: datasetChannel,
    setSavingState,
  });

  const insertRows = useInsertRows({
    dml,
    projectId: dataset?.project_id,
    datasetId: dataset?.id,
  });

  return {
    createDataset: async (params: {
      orgId: string;
      projectName: string;
      datasetName: string;
    }) => {
      setIsDatasetBeingCreated(true);
      return createDataset({
        datasetName: params.datasetName,
        orgId: params.orgId,
        projectName: params.projectName,
      })
        .then((resp) => {
          setCreatedDatasetId(resp.data?.dataset?.id);
        })
        .catch((err) => {
          throw err;
        });
    },
    createdDataset: datasetReady ? dataset : null,
    insertRows: datasetReady ? insertRows : null,
    isCreatingDataset: isDatasetBeingCreated && !datasetReady,
    savingState,
  };
};
