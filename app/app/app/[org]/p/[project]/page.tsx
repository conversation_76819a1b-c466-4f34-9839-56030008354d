import { getOrgLink } from "../../getOrgLink";
import { getProjectContextProjects } from "./project-actions";
import { redirect } from "next/navigation";
import { decodeURIComponentPatched } from "#/utils/url";
import ClientPage from "./clientpage";
import { getPromptSessionSummary } from "./playgrounds/playground-actions";
import { isEmpty } from "#/utils/object";
import { AccessFailed } from "#/ui/access-failed";
import { type Metadata } from "next";
import { buildMetadata } from "#/app/metadata";

type Params = { org: string; project: string };

export default async function ProjectPage(props: {
  params: Promise<Params>;
  searchParams?: Promise<{ redirectToOrgIfNotFound?: string }>;
}) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);

  const projectRows = await getProjectContextProjects({
    org_name: orgName,
    project_name: projectName,
  });

  const playgrounds = await getPromptSessionSummary({
    org_name: orgName,
    project_name: projectName,
  });

  if (projectRows.length === 0 && searchParams?.redirectToOrgIfNotFound) {
    redirect(getOrgLink({ orgName }));
  }

  if (isEmpty(projectRows) || projectRows.length === 0) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return <ClientPage playgrounds={playgrounds} />;
}

export async function generateMetadata(props: {
  params: Promise<Params>;
}): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: projectName,
    description: `${orgName}`,
    relativeUrl: `/${params.org}/p/${params.project}`,
  });
}
