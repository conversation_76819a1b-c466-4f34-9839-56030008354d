import { buildMetadata } from "#/app/metadata";
import { decodeURIComponentPatched } from "#/utils/url";
import ClientPage, { type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  return <ClientPage params={params} />;
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Tools",
    sections: [projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/tools`,
  });
}
