"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  promptSessionSchema,
  userSchema,
  projectSchema,
  promptSchema,
} from "@braintrust/core/typespecs";

const promptSessionSummarySchema = z.object({
  id: promptSessionSchema.shape.id,
  name: promptSessionSchema.shape.name,
  created: promptSessionSchema.shape.created,
  project_id: promptSessionSchema.shape.project_id,
  created_by_name: z.string(),
  email: userSchema.shape.email,
  avatar_url: userSchema.shape.avatar_url,
});

export type PromptSessionSummary = z.infer<typeof promptSessionSummarySchema>;

export type GetPromptSessionsSummaryInput = {
  org_name: string;
  project_name: string;
};

export async function getPromptSessionSummary(
  { org_name, project_name }: GetPromptSessionsSummaryInput,
  authLookupRaw?: AuthLookup,
): Promise<PromptSessionSummary[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: allPromptSessionsQuery, queryParams } = makeFullResultSetQuery(
    {
      authLookup,
      permissionInfo: {
        aclObjectType: "prompt_session",
        aclPermission: "read",
      },
      filters: {
        org_name,
        project_name,
      },
    },
  );

  const fullQuery = `
  select
    all_prompt_sessions.id,
    all_prompt_sessions.name,
    all_prompt_sessions.created,
    all_prompt_sessions.project_id,
    concat_ws(' ', users.given_name, users.family_name) created_by_name,
    users.email created_by_email,
    users.avatar_url created_by_avatar_url
  from
    (${allPromptSessionsQuery}) "all_prompt_sessions"
    left join users on all_prompt_sessions.user_id = users.id
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(fullQuery, queryParams.params);
  return promptSessionSummarySchema.array().parse(rows ?? []);
}

const promptListingSchema = promptSchema
  .pick({ id: true, project_id: true, slug: true })
  .merge(z.strictObject({ project_name: projectSchema.shape.name }));

export type PromptListing = z.infer<typeof promptListingSchema>;

export async function fetchPromptListing(
  {
    orgName,
  }: {
    orgName: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<PromptListing[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const { query: fullResultSetQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "prompt",
      aclPermission: "read",
    },
    filters: {
      org_name: orgName,
    },
  });
  const query = `
    select prompts.id, prompts.project_id, projects.name project_name, prompts.slug
    from (${fullResultSetQuery}) prompts join projects on prompts.project_id = projects.id
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, queryParams.params);
  return z.array(promptListingSchema).parse(rows);
}
