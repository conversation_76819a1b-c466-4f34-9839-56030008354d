"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { PlainInput } from "#/ui/plain-input";
import { VizQuery } from "#/ui/viz-query";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { useMaterializedRecords } from "#/utils/duckdb";
import { useOrg, useUser } from "#/utils/user";
import { Field, Schema, TimestampMillisecond, Utf8 } from "apache-arrow";
import { useRouter } from "next/navigation";
import { useCallback, useContext, useMemo, useRef, useState } from "react";
import { CheckOrg } from "../../../clientlayout";
import { ProjectOnboardingPage } from "../../../onboarding-page";
import { useProjectRowEvents } from "../experiments-formatters";
import { useCreatePlaygroundDialog } from "../../../prompt/[prompt]/create-playground-dialog";
import { makePromptSessionDefaultName } from "../../../prompt/[prompt]/createPromptSession";
import { getPlaygroundLink } from "../../../prompt/[prompt]/getPromptLink";
import { Plus, Search, Shapes, Trash } from "lucide-react";
import {
  type getPromptSessionSummary,
  type PromptSessionSummary,
} from "./playground-actions";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { useEntityBatchActions } from "../../useEntityBatchActions";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { singleQuote } from "#/utils/sql-utils";
import { ProjectContext } from "../projectContext";
import { AccessFailed } from "#/ui/access-failed";
import { isEmpty } from "#/utils/object";
import { useQueryFunc } from "#/utils/react-query";
import { useViewStates, type ViewParams } from "#/utils/view/use-view";
import { Views } from "#/ui/views";
import { useSessionToken } from "#/utils/auth/session-token";
import { noopChecker } from "#/utils/search/search";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { CreatorFormatter } from "#/ui/table/formatters/creator-formatter";
import { decodeURIComponentPatched } from "#/utils/url";
import React from "react";
import { ProjectListLayout } from "../project-list-layout";

// Mirrors the schema for PromptSessionSummary defined in
// app/app/app/[org]/p/[project]/playgrounds/playground-actions.
const emptyPromptSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "created", type: new TimestampMillisecond() }),
  Field.new({ name: "project_id", type: new Utf8() }),
  Field.new({ name: "created_by_name", type: new Utf8() }),
  Field.new({ name: "created_by_email", type: new Utf8() }),
  Field.new({
    name: "created_by_avatar_url",
    type: new Utf8(),
  }),
]);

export interface Params {
  org: string;
  project: string;
}

const formatters = makeFormatterMap({
  creator: {
    cell: CreatorFormatter,
  },
});

export default function ClientPage({
  params,
  promptSessions: promptSessionsServer,
}: {
  params: Params;
  promptSessions: PromptSessionSummary[];
}) {
  const org = useOrg();

  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);
  const { projectId } = useContext(ProjectContext);

  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: {
      org_name: orgName,
    },
  });
  const { data: promptSessionsRaw, invalidate: refreshPromptSessions } =
    useQueryFunc<typeof getPromptSessionSummary>({
      fName: "getPromptSessionSummary",
      args: { org_name: orgName, project_name: projectName },
      serverData: promptSessionsServer,
    });
  const promptSessions = useMemo(
    () => promptSessionsRaw ?? [],
    [promptSessionsRaw],
  );

  const promptAllowed = !org.resources?.forbid_insert_prompt_sessions;

  const { onCreate: onCreatePrompt, newPromptDialog } =
    useCreateNewPlaygroundDialog({
      orgName,
      projectName,
      numPlaygrounds: promptSessions.length,
      refreshPromptSessions,
    });

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  if (!projects) {
    return <CheckOrg params={{ org: orgName }}>{null}</CheckOrg>;
  }

  if (projects.length === 0 && promptSessions.length === 0) {
    return (
      <CheckOrg params={{ org: orgName }}>
        <ProjectOnboardingPage orgName={orgName} />;
      </CheckOrg>
    );
  }

  return (
    <CheckOrg params={{ org: orgName }}>
      <ProjectListLayout
        active="playgrounds"
        orgName={orgName}
        projectName={projectName}
        scrollContainerRef={scrollContainerRef}
      >
        {promptAllowed && (
          <PromptList
            onCreatePrompt={onCreatePrompt}
            orgName={orgName}
            projectId={projectId}
            projectName={projectName}
            promptSessions={promptSessions}
            scrollContainerRef={scrollContainerRef}
            refreshPromptSessions={refreshPromptSessions}
          />
        )}
        {newPromptDialog}
      </ProjectListLayout>
    </CheckOrg>
  );
}

const PromptList = ({
  promptSessions,
  orgName,
  projectId,
  projectName,
  onCreatePrompt,
  refreshPromptSessions,
  scrollContainerRef,
}: {
  promptSessions: PromptSessionSummary[];
  refreshPromptSessions: () => void;
  orgName: string;
  projectId: string;
  projectName: string;
  onCreatePrompt: () => void;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
}) => {
  const { refreshed: promptsReady, name: promptsTable } =
    useMaterializedRecords(
      `prompt_sessions_${orgName}`,
      promptSessions,
      emptyPromptSchema,
    );

  const rowEvents = useProjectRowEvents({
    entity: "playground",
    entityNameProp: "name",
    orgName,
    projectName,
  });
  const [search, setSearch] = useState("");

  const viewParams: ViewParams | undefined = projectId
    ? {
        objectType: "project",
        objectId: projectId,
        viewType: "playgrounds",
      }
    : undefined;
  const pageIdentifier = `organization-prompts-list-${orgName}`;
  const viewProps = useViewStates({
    viewParams,
    clauseChecker: noopChecker,
    pageIdentifier,
  });

  const query = useMemo(() => {
    const orderByClauses: string[] = [];
    if (viewProps.search.sort?.length) {
      orderByClauses.push(...viewProps.search.sort.map((s) => s.text));
    }
    orderByClauses.push("created DESC NULLS LAST");
    const orderBy = orderByClauses.join(", ");

    return (
      promptsTable &&
      `SELECT
      name,
      JSON_OBJECT(
      'name', created_by_name,
      'email', created_by_email,
      'avatar', created_by_avatar_url) as "creator",
      created,
      id,
      FROM "${promptsTable}" ${
        search
          ? `WHERE contains(lower(name), ${singleQuote(search.toLowerCase())})`
          : ""
      } ORDER BY ${orderBy}`
    );
  }, [promptsTable, search, viewProps.search.sort]);

  const sizeConstraintsMap = useMemo(
    () => ({
      name: {
        minSize: 400,
      },
    }),
    [],
  );

  const visibilityMap = useMemo(
    () => ({
      id: false,
    }),
    [],
  );

  const {
    selectedRows,
    setSelectedRows,
    getSelectedRowsWithData,
    selectedRowsNumber,
    deselectAllTableRows,
    tableRef,
  } = useTableSelection();

  const { actions, modals } = useEntityBatchActions({
    entityType: "prompt_session",
    onUpdate: () => {
      refreshPromptSessions();
      deselectAllTableRows();
    },
    entityName: projectName,
  });

  return (
    <>
      <VizQuery
        viewProps={viewProps}
        className="pt-3"
        query={query}
        tableType="list"
        signals={[promptsReady]}
        rowEvents={rowEvents}
        formatters={formatters}
        sizeConstraintsMap={sizeConstraintsMap}
        initiallyVisibleColumns={visibilityMap}
        scrollContainerRef={scrollContainerRef}
        extraLeftControls={
          <>
            <Button
              variant="primary"
              size="xs"
              Icon={Plus}
              onClick={onCreatePrompt}
            >
              Playground
            </Button>
            <Views
              pageIdentifier={pageIdentifier}
              viewParams={viewParams}
              viewProps={viewProps}
            />
          </>
        }
        extraRightControls={
          <div className="relative flex flex-1">
            <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
            <PlainInput
              placeholder="Find playgrounds"
              onChange={(e) => setSearch(e.target.value)}
              className="h-7 flex-1 border-0 pl-7 text-xs outline-none transition-all bg-transparent hover:bg-primary-100 focus:bg-primary-100"
            />
          </div>
        }
        toolbarSlot={
          selectedRowsNumber > 0 ? (
            <>
              <CancelSelectionButton
                onCancelSelection={deselectAllTableRows}
                selectedRowsNumber={selectedRowsNumber}
              />
              <SelectionBarButton
                onClick={() => {
                  actions.moveEntities({
                    entityIds: getSelectedRowsWithData().map((row) => row.id),
                  });
                }}
              >
                Move
              </SelectionBarButton>
              <SelectionBarButton
                onClick={() => {
                  actions.deleteEntities({
                    entityIds: getSelectedRowsWithData().map((row) => row.id),
                  });
                }}
                Icon={Trash}
              />
            </>
          ) : undefined
        }
        hasNoRowsComponent={
          !search && (
            <TableEmptyState
              Icon={Shapes}
              className="mt-4"
              labelClassName="text-sm leading-normal"
              label={
                <>
                  <span className="mb-3 block text-base">
                    There are no playgrounds in this project yet.
                  </span>
                  Playgrounds are a powerful workspace for rapidly iterating on
                  AI engineering primitives. Tune prompts, models, scorers and
                  datasets in an editor-like interface, and run full evaluations
                  in real-time, side by side.
                </>
              }
            >
              <Button onClick={onCreatePrompt} size="sm">
                Create playground
              </Button>
            </TableEmptyState>
          )
        }
        rowSelection={selectedRows}
        setRowSelection={setSelectedRows}
        tableRef={tableRef}
      />
      {modals}
    </>
  );
};

export const useCreateNewPlaygroundDialog = ({
  orgName,
  projectName,
  numPlaygrounds,
  refreshPromptSessions,
}: {
  orgName: string;
  projectName: string;
  numPlaygrounds: number;
  refreshPromptSessions: () => void;
}) => {
  const router = useRouter();

  const { user } = useUser();
  const org = useOrg();

  const { getOrRefreshToken } = useSessionToken();

  const apiUrl = org.api_url;
  const userId = user?.id;
  const email = user?.email;

  const { setNewPromptSessionDefaultValue, makeNewPromptSessionDialog } =
    useCreatePlaygroundDialog();

  const routeToNewPrompt = useCallback(
    ({ name: promptName }: { name: string }) => {
      router.push(
        getPlaygroundLink({ orgName, projectName, playgroundName: promptName }),
      );
    },
    [orgName, projectName, router],
  );
  return {
    newPromptDialog: makeNewPromptSessionDialog({
      orgName,
      projectName,
      refreshPromptSessions,
      initialPromptArgs:
        apiUrl && userId ? { apiUrl, getOrRefreshToken, userId } : undefined,
      onSuccess: routeToNewPrompt,
    }),
    onCreate: email
      ? () => {
          setNewPromptSessionDefaultValue(
            makePromptSessionDefaultName(numPlaygrounds),
          );
        }
      : () => {},
  };
};
