import { useFeatureFlags } from "#/lib/feature-flags";
import {
  type FetchCachedBtSessionTokenFn,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { _urljoin } from "@braintrust/core";
import { useQueries } from "@tanstack/react-query";
import {
  type EvaluatorDefinitions,
  evaluatorDefinitionsSchema,
} from "braintrust";
import { createContext, useContext, useMemo, useState, useEffect } from "react";

const RemoteEvalsContext = createContext<{
  remoteEvals: Record<
    string,
    { endpointUrl: string; evals: EvaluatorDefinitions; error: Error | null }
  >;
  isLoading: boolean;
  setEnabled: (enabled: boolean) => void;
}>({
  remoteEvals: {},
  isLoading: true,
  setEnabled: () => {},
});

export const remoteEvalListQuery = async ({
  endpointUrl,
  getOrRefreshToken,
  orgName,
}: {
  endpointUrl: string;
  getOrRefreshToken: FetchCachedBtSessionTokenFn;
  orgName: string;
}) => {
  const sessionToken = await getOrRefreshToken();
  if (!endpointUrl || sessionToken === "unauthenticated") {
    return null;
  }
  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);

  const result = await fetch(_urljoin(endpointUrl, "list"), {
    method: "GET",
    mode: "cors",
    headers: {
      "content-type": "application/json",
      "x-bt-org-name": orgName,
      ...sessionHeaders,
    },
    ...sessionExtraFetchProps,
  });

  const evalsRaw = await result.json();
  const evalsParsed = evaluatorDefinitionsSchema.safeParse(evalsRaw);
  if (!evalsParsed.success) {
    throw new Error("Failed to parse evaluator list");
  }
  return evalsParsed.data;
};

export function RemoteEvalsProvider({
  remoteEndpoints,
  children,
}: {
  remoteEndpoints: string[];
  children: React.ReactNode;
}) {
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const { flags } = useFeatureFlags();
  const [enabled, setEnabled] = useState(false);

  const results = useQueries({
    queries: remoteEndpoints.map((evalUrl) => ({
      queryKey: ["remote-evals", evalUrl],
      queryFn: async () =>
        await remoteEvalListQuery({
          endpointUrl: evalUrl,
          getOrRefreshToken,
          orgName: org.name,
        }),
      meta: {
        disableGlobalErrorToast: true,
      },
      enabled: enabled && flags.remoteEvals,
    })),
  });

  // TODO: Error handling?
  const remoteEvals = useMemo(() => {
    return Object.fromEntries(
      remoteEndpoints.map((evalUrl, index) => [
        evalUrl,
        {
          endpointUrl: evalUrl,
          evals: results[index].data ?? {},
          error: results[index].error,
        },
      ]),
    );
  }, [results, remoteEndpoints]);

  const isLoading = useMemo(() => results.some((r) => r.isLoading), [results]);
  const value = useMemo(
    () => ({ remoteEvals, isLoading, setEnabled }),
    [remoteEvals, isLoading, setEnabled],
  );

  return (
    <RemoteEvalsContext.Provider value={value}>
      {children}
    </RemoteEvalsContext.Provider>
  );
}

export function useRemoteEvals({ enabled }: { enabled?: boolean } = {}) {
  const { remoteEvals, isLoading, setEnabled } = useContext(RemoteEvalsContext);

  useEffect(() => {
    if (enabled !== undefined) {
      setEnabled(enabled);
    }
  }, [enabled, setEnabled]);

  return {
    remoteEvals,
    isLoading,
  };
}
