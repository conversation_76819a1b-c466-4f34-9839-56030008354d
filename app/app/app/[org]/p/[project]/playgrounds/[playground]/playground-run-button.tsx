import { Button, type ButtonProps } from "#/ui/button";
import { Spinner } from "#/ui/icons/spinner";
import { useAtomValue } from "jotai";
import { Play } from "lucide-react";
import { type MouseEventHandler } from "react";
import { useCompletionPercentageAtom } from "./playx/atoms";
import { BasicTooltip } from "#/ui/tooltip";

export const PlaygroundRunButton = ({
  datasetRowId,
  className,
  isRunning,
  onRun,
  onStop,
  children,
  variant,
  ...props
}: ButtonProps & {
  datasetRowId?: string;
  className?: string;
  onRun: MouseEventHandler<HTMLButtonElement>;
  onStop: MouseEventHandler<HTMLButtonElement>;
  isRunning: boolean;
  children?: React.ReactNode;
  variant?: ButtonProps["variant"];
}) => {
  const completedPercentage = useAtomValue(
    useCompletionPercentageAtom(datasetRowId),
  );
  return (
    <BasicTooltip
      tooltipContent={
        isRunning &&
        (completedPercentage == null || completedPercentage === 100)
          ? "Other rows are running. Stop or wait for completion."
          : undefined
      }
    >
      <Button
        size="xs"
        className={className}
        variant={variant}
        onClick={isRunning ? onStop : onRun}
        {...props}
        disabled={props.disabled}
      >
        {isRunning ? (
          <>
            {completedPercentage === undefined ? (
              <Spinner className="!size-3 flex-none" />
            ) : (
              <>
                <span className="mr-1 text-xs tabular-nums text-primary-400">
                  {completedPercentage?.toFixed(0)}%
                </span>
                <span className="relative mr-0.5 h-2.5 w-9 flex-none overflow-hidden rounded-full border p-px bg-accent-200 border-accent-300">
                  <span
                    className="block h-full rounded-full bg-primary-700"
                    style={{ width: `${completedPercentage}%` }}
                  />
                  <span
                    className="absolute inset-0 -translate-x-full animate-shine bg-gradient-to-r from-transparent via-white/40 to-transparent"
                    style={{ transform: "translateX(-100%)" }}
                  />
                </span>
              </>
            )}
            Stop
          </>
        ) : (
          <>
            <Play className="size-3 flex-none" />
            {children ?? "Run"}
          </>
        )}
      </Button>
    </BasicTooltip>
  );
};
