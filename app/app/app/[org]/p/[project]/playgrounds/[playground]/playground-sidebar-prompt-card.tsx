import { getModelIcon } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { But<PERSON> } from "#/ui/button";
import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
} from "#/ui/charts/colors";
import { cn } from "#/utils/classnames";
import { type SavedPromptMeta } from "./use-saved-prompt-meta";
import { usePlaygroundPromptSheetIndexState } from "#/ui/query-parameters";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, Route, Unplug } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { useMemo } from "react";
import { PromptPreviewTooltip } from "./prompt-preview-tooltip";
import { type PlaygroundBlockWithId } from "#/ui/prompts/schema";
import { Skeleton } from "#/ui/skeleton";

export const PlaygroundSidebarPromptCard = ({
  prompt,
  index,
  promptMeta,
  displayName,
  isSavedPromptMetaInitializing,
}: {
  prompt: PlaygroundBlockWithId;
  index: number;
  promptMeta: SavedPromptMeta | null;
  displayName: string;
  isSavedPromptMetaInitializing?: boolean;
}) => {
  const { setPromptSheetIndex } = usePlaygroundPromptSheetIndexState();

  const { attributes, listeners, setNodeRef, transform, transition, active } =
    useSortable({ id: prompt.id });

  const style = {
    transform: CSS.Transform.toString({
      x: 0,
      y: transform?.y ?? 0,
      scaleX: 1,
      scaleY: 1,
    }),
    transition,
    cursor: "default",
  };

  const isAgent = prompt.function_data.type === "graph";
  const isRemoteEval = prompt.function_data.type === "remote_eval";
  const model = prompt.prompt_data?.options?.model ?? "";

  const swatchColorClassName = useMemo(
    () =>
      index !== undefined
        ? [
            EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
            ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
          ][index]
        : undefined,
    [index],
  );

  if (isSavedPromptMetaInitializing) {
    return <Skeleton className="h-10 w-full border-2 border-background" />;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div ref={setNodeRef} {...attributes} style={style}>
          <Button
            size="xs"
            variant="ghost"
            className="group/prompt-card h-10 w-full justify-start pl-0 text-left transition-all hover:pl-2"
            onClick={() => setPromptSheetIndex(index)}
          >
            <PlaygroundSidebarPromptCardContents
              index={index}
              isDragging={active !== null}
              model={model}
              isAgent={isAgent}
              isRemoteEval={isRemoteEval}
              displayName={displayName}
              swatchColorClassName={swatchColorClassName}
            />
            {index !== undefined && (
              <div
                className={cn(
                  "cursor-grab text-primary-400 hidden group-hover/prompt-card:block",
                )}
                {...listeners}
              >
                <GripVertical className="size-3" />
              </div>
            )}
          </Button>
        </div>
      </TooltipTrigger>
      {!isAgent && !isRemoteEval && (
        <TooltipContent
          side="right"
          className="flex max-w-sm flex-col gap-2.5 py-3"
          align="start"
        >
          <PromptPreviewTooltip
            prompt={prompt.prompt_data}
            promptMeta={promptMeta}
          />
        </TooltipContent>
      )}
    </Tooltip>
  );
};

export const PlaygroundSidebarPromptCardContents = ({
  index,
  isDragging,
  model,
  displayName,
  swatchColorClassName,
  isAgent,
  isRemoteEval,
}: {
  index?: number;
  isDragging: boolean;
  model: string;
  displayName: string;
  swatchColorClassName?: string;
  isAgent?: boolean;
  isRemoteEval?: boolean;
}) => {
  const Icon = isAgent
    ? Route
    : isRemoteEval
      ? Unplug
      : (getModelIcon(model) ?? undefined);

  return (
    <>
      {!isDragging && swatchColorClassName && (
        <div
          className={cn(
            "rounded-full size-2 flex-none mr-0.5",
            swatchColorClassName,
          )}
        />
      )}
      {Icon && (
        <div className="flex size-5 flex-none items-start justify-center">
          <Icon
            size={isAgent || isRemoteEval ? 16 : 20}
            className="flex-none"
          />
        </div>
      )}
      <div className="flex flex-1 flex-col overflow-hidden pl-0.5">
        <div className="w-full flex-none truncate text-xs font-medium">
          {displayName}
        </div>
        {index !== undefined && (
          <div className="flex-none text-[10px] font-normal text-primary-500">
            {index === 0 ? "Base" : "Comparison"}
          </div>
        )}
      </div>
    </>
  );
};
