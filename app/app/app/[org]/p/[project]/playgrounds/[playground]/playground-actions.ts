"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { promptSessionSchema } from "@braintrust/core/typespecs";
import { otelTraced, otelWrapTraced } from "#/utils/tracing";

const promptSessionMetaSchema = z.object({
  id: promptSessionSchema.shape.id,
  name: promptSessionSchema.shape.name,
  created: promptSessionSchema.shape.created,
  project_id: promptSessionSchema.shape.project_id,
});

export type PromptSessionMeta = z.infer<typeof promptSessionMetaSchema>;

export type GetPromptSessionMetaInput = {
  org_name: string;
  project_name: string;
  playground: string;
  id?: string;
};

export const getPromptSessionMeta = otelWrapTraced(
  "getPromptSessionMeta",
  async (
    { org_name, project_name, playground }: GetPromptSessionMetaInput,
    authLookupRaw?: AuthLookup,
  ): Promise<PromptSessionMeta | null> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query, queryParams } = makeFullResultSetQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "prompt_session",
        aclPermission: "read",
      },
      filters: {
        org_name,
        project_name,
        name: playground,
      },
    });

    const supabase = getServiceRoleSupabase();
    const { rows } = await otelTraced("supabase query", () =>
      supabase.query(query, queryParams.params),
    );
    if (!rows || !rows.length) {
      return null;
    } else {
      return promptSessionMetaSchema.parse(rows[0]);
    }
  },
);
