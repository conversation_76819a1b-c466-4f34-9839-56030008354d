import { Button, buttonVariants } from "#/ui/button";
import { type UIFunction } from "#/ui/prompts/schema";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type SavedScorer } from "#/utils/scorers";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, XIcon } from "lucide-react";

export const AppliedScorers = ({
  savedScorers,
  scorerFunctions,
  updateSavedScorers,
  setSelectedScorerId,
}: {
  savedScorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;
  updateSavedScorers: (scorers: SavedScorer[]) => void;
  setSelectedScorerId: (selectedScorerId: string | null) => void;
}) => {
  return (
    <div className="flex flex-none gap-2">
      {savedScorers.map((scorer, idx) => (
        <div key={idx} className="flex flex-none">
          <Button
            size="xs"
            Icon={Percent}
            className="max-w-40 flex-none pr-1 border-good-600 text-good-700 hover:bg-good-50 hover:text-good-800"
            onClick={() => {
              if (scorer.type === "function") {
                setSelectedScorerId(scorer.id);
              } else {
                setSelectedScorerId(scorer.name);
              }
            }}
          >
            <span className="flex-1 truncate">
              {scorer.type === "global"
                ? scorer.name
                : (scorerFunctions[scorer.id]?.name ?? scorer.id)}
            </span>
            <span
              className="flex-none pl-0.5 text-primary-400 hover:text-primary-800"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                updateSavedScorers(
                  savedScorers.filter((s) =>
                    s.type === "function" && scorer.type === "function"
                      ? s.id !== scorer.id
                      : scorer.type === "global" && s.type === "global"
                        ? s.name !== scorer.name
                        : true,
                  ),
                );
              }}
            >
              <XIcon className="size-3 flex-none" />
            </span>
          </Button>
          {scorer.type !== "global" && !scorerFunctions[scorer.id] && (
            <BasicTooltip
              className="text-bad-700"
              tooltipContent={`Scorer function not found - it will not be run`}
            >
              <div
                className={cn(
                  buttonVariants({
                    size: "xs",
                    variant: "ghost",
                  }),
                  "text-bad-700",
                )}
              >
                <TriangleAlert className="size-3 flex-none" />
              </div>
            </BasicTooltip>
          )}
        </div>
      ))}
    </div>
  );
};
