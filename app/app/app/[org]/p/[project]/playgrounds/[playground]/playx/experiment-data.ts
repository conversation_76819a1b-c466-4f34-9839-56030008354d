import { buildSearch<PERSON><PERSON>, type DataObjectSearch } from "#/utils/btapi/btapi";
import { dbQ<PERSON>y, InsertR<PERSON>ordsR<PERSON>ult, useParquetView } from "#/utils/duckdb";
import { singleQuote } from "#/utils/sql-utils";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  useComparisonExperimentScans,
  useExperimentScan,
} from "../../../experiments/[experiment]/(queries)/useExperiment";
import { type LoadExperimentScansParams } from "../../../experiments/[experiment]/(queries)/loaders";
import { invalidateId } from "#/utils/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { type Search } from "#/utils/search/search";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { makePreviewBtqlReplace } from "#/utils/btapi/fetch";
import { type LoadedObject } from "#/app/app/[org]/prompt/[prompt]/mounted-object";
import { type ProjectSettings } from "@braintrust/core/typespecs";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  hasStreamingCompletionsAtom,
  streamingCompletionsAtom,
  isRunningAtom,
  latestXactIdAtom,
  resetRunningStateAtom,
  streamingStateAtom,
} from "./atoms";
import { SINGLETON_DATASET_ID } from "./stream";
import { type PromptSessionData } from "../use-prompt-session-data";
import { atomEffect } from "jotai-effect";
import { useAtomCallback } from "jotai/utils";
import { getOriginDataset } from "#/ui/trace/origin-dataset/origin-dataset";
import { type RowId } from "#/utils/diffs/diff-objects";
import { type AllCompletionsStreamingCell } from "#/ui/prompts/schema";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { useDiffModeState } from "#/ui/query-parameters";

export type GenerationId = { id: string; name: string; hasRun: boolean };

export function usePlaygroundLogsDataFromBackend({
  promptSessionId,
  generationIds,
  playgroundName,
  modelSpecScan,
  search,
  setSearch,
  loadedDataset,
  datasetTableParams,
  projectSettings,
  activeRowId,
}: {
  promptSessionId: string | null;
  generationIds: GenerationId[];
  playgroundName: string;
  modelSpecScan: string | null;
  search: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
  loadedDataset: LoadedObject & { count: number };
  isEvalRunning: boolean;
  datasetTableParams: {
    scoreNames: string[];
    metricNames: string[];
    metadataFields: string[][];
  };
  projectSettings?: ProjectSettings | null;
  setPromptSessionData: (promptSessionData: Partial<PromptSessionData>) => void;
  activeRowId: RowId | null;
}) {
  const primaryGeneration: GenerationId | undefined = generationIds?.[0];
  const {
    flags: { hasIsRootField },
  } = useFeatureFlags();
  const { version } = useAPIVersion();
  // XXX When brainstore is off, we want this to run against postgres, not clickhouse.
  const primaryGenerationSearch = useMemo(
    (): DataObjectSearch | undefined =>
      promptSessionId && primaryGeneration?.id && primaryGeneration?.hasRun
        ? {
            id: promptSessionId,
            filters: {
              sql: [],
              btql: [
                {
                  op: "eq",
                  left: { btql: "span_attributes.generation" },
                  right: {
                    op: "literal",
                    value: primaryGeneration.id,
                  },
                },
              ],
            },
            replace: makePreviewBtqlReplace({
              objectType: "playground_logs",
              relaxedSearchMode: false,
              hasIsRootField,
              version,
            }),
          }
        : undefined,
    [promptSessionId, primaryGeneration, hasIsRootField, version],
  );

  const {
    scan: rowScanUnfiltered,
    refreshed: rowRefreshedSingle,
    schema: rowSchema,
    channel,
    roster,
  } = useParquetView({
    objectType: "playground_logs",
    search: primaryGenerationSearch,
  });

  // Because realtime does not filter, we need to re-apply the generation filter here
  const rowScanRaw = useMemo(() => {
    return (
      rowScanUnfiltered &&
      `
    SELECT
        *
    FROM (${rowScanUnfiltered})
    WHERE
    ${
      /*
      generationIds && generationIds.length > 0
        ? `span_attributes->>'generation' IN (${generationIds
            ?.map((id) => singleQuote(id))
            .join(",")})`
        : "FALSE"
        */
      primaryGeneration?.id
        ? `span_attributes->>'generation' = ${singleQuote(primaryGeneration?.id)}`
        : "FALSE"
    }
    `
    );
  }, [rowScanUnfiltered, primaryGeneration?.id]);

  const searchKeyName = useMemo(
    () => buildSearchKey(primaryGenerationSearch),
    [primaryGenerationSearch],
  );

  const queryClient = useQueryClient();
  useEffect(() => {
    invalidateId(queryClient, searchKeyName);
    return () => invalidateId(queryClient, searchKeyName);
  }, [searchKeyName, queryClient, rowRefreshedSingle]);
  const loadedExperiment = useMemo(() => {
    return {
      id: primaryGeneration?.id ?? "",
      searchKeyName,
      name: primaryGeneration?.name ?? "",
      scan: rowScanRaw ?? "",
      schema: rowSchema,
      refreshed: rowRefreshedSingle,
      //auditLog: { scan: null },
      auditLog: undefined,
      channel,
      roster,
    };
  }, [
    primaryGeneration,
    searchKeyName,
    rowScanRaw,
    rowSchema,
    rowRefreshedSingle,
    channel,
    roster,
  ]);

  const hasErrorField = useMemo(() => {
    return !!loadedExperiment.schema?.fields.find((f) => f.name === "error");
  }, [loadedExperiment.schema]);

  const loadExperimentScanParams: LoadExperimentScansParams = useMemo(() => {
    return {
      modelSpecScan,
      auditLogScan: null,
      scoreConfig: [],
      comparisonKey: projectSettings?.comparison_key ?? null,
      hasErrorField,
      isRootAvailable: true,
      customColumns: undefined,
    };
  }, [modelSpecScan, projectSettings?.comparison_key, hasErrorField]);

  const datasetParam = useMemo(
    () =>
      loadedDataset.scan
        ? {
            loadedDataset: {
              ...loadedDataset,
              name: primaryGeneration?.name ?? "",
              searchKeyName: loadedDataset.id,
            },
            ...datasetTableParams,
            generation: primaryGeneration,
          }
        : undefined,
    [loadedDataset, datasetTableParams, primaryGeneration],
  );
  const {
    data: experiment,
    ready,
    error: experimentError,
    projectedPaths,
  } = useExperimentScan({
    experiment: loadedExperiment,
    loadExperimentScanParams,
    search,
    datasetParam,
  });

  const comparisonExperimentSearches = useMemo(
    () =>
      promptSessionId && generationIds
        ? generationIds.flatMap(({ id, name, hasRun }, i) =>
            i === 0
              ? []
              : [
                  {
                    id,
                    name,
                    skipLoading: !id || !hasRun,
                    datasetParam: loadedDataset.scan
                      ? {
                          loadedDataset: {
                            ...loadedDataset,
                            name,
                            searchKeyName: loadedDataset.id,
                          },
                          ...datasetTableParams,
                          generation: {
                            id,
                            name,
                          },
                        }
                      : undefined,
                    search: {
                      id: promptSessionId,
                      filters: {
                        sql: [],
                        btql: [
                          {
                            op: "eq" as const,
                            left: { btql: "span_attributes.generation" },
                            right: {
                              op: "literal" as const,
                              value: id,
                            },
                          },
                        ],
                      },
                    },
                    scanFilterFn: (scan: string) => {
                      // Because realtime does not filter, we need to re-apply the generation filter here
                      return `SELECT * FROM (${scan}) WHERE span_attributes->>'generation' = ${singleQuote(id)}`;
                    },
                  },
                ],
          )
        : [],
    [promptSessionId, generationIds, loadedDataset, datasetTableParams],
  );

  const [diffMode, setDiffMode] = useDiffModeState();
  const {
    regressionFilters,
    setRegressionFilters,
    addRegressionFilter,
    comparisonExperimentData,
    errors: comparisonExperimentErrors,
  } = useComparisonExperimentScans({
    experiment: loadedExperiment,
    loadExperimentScanParams,
    selectedComparisonExperiments: comparisonExperimentSearches,
    search,
    setSearch,
    objectType: "playground_logs",
    diffMode,
    setDiffMode,
    enableSummary: false,
  });

  const queryError = useMemo(() => {
    return experimentError ?? comparisonExperimentErrors?.find(Boolean) ?? null;
  }, [experimentError, comparisonExperimentErrors]);

  const experimentScans = useMemo(() => {
    return [
      experiment?.tableScan,
      ...comparisonExperimentData.map((e) => e.tableScan),
    ].filter(<T>(v: T | null | undefined): v is T => !!v);
  }, [experiment, comparisonExperimentData]);
  const { streamingState, onDataLoaded: onStreamingDataLoaded } =
    useStreamingState({
      activeRowId,
      experimentScans,
    });

  const primaryExperimentReady = useMemo(() => {
    return [ready].concat(
      loadedDataset?.refreshed ? [loadedDataset.refreshed] : [],
    );
  }, [ready, loadedDataset?.refreshed]);

  const playgroundLogsReady =
    (!primaryGeneration?.id ||
      !primaryGeneration?.hasRun ||
      (!!experiment &&
        experiment.id === primaryGeneration.id &&
        !!experiment.tempTableName)) &&
    (generationIds.length === 0 ||
      generationIds.slice(1).every((g) => {
        if (!g.id || !g.hasRun) {
          return true;
        }
        // e.tempTableName is a proxy for experiment data being loaded
        const e = comparisonExperimentData.find(
          (e) => e.id === g.id && e.tempTableName,
        );
        return !!e;
      }));
  const [experimentsLoaded, setExperimentsLoaded] = useState(false);
  const onDataLoaded = useCallback(
    async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      onStreamingDataLoaded?.(conn, abortSignal);
      setExperimentsLoaded(playgroundLogsReady);
    },
    [onStreamingDataLoaded, playgroundLogsReady, setExperimentsLoaded],
  );

  const channels = useMemo(() => {
    return [
      channel(),
      ...comparisonExperimentData.map((e) => e.channel()),
    ].filter((v) => !!v);
  }, [channel, comparisonExperimentData]);

  const hasRealtimeError = channels.some(
    (c) =>
      c.insertRecordsError === InsertRecordsResult.FAILURE_EXCEEDS_MAX_BYTES ||
      c.hasRejoined,
  );

  return {
    experimentObject: loadedExperiment,
    experiment,
    primaryExperimentReady,
    comparisonExperimentData,
    modelSpecScan,
    projectedPaths,
    diffMode,
    setDiffMode,
    queryError,
    regressionFilters,
    setRegressionFilters,
    addRegressionFilter,
    hasErrorField,
    onDataLoaded,
    streamingState,
    completedCount: undefined,
    playgroundLogsReady,
    playgroundLogsLoaded: experimentsLoaded,
    channels,
    hasRealtimeError,
  };
}

const streamingStateEffect = atomEffect((get, set) => {
  const streamingState = get(streamingStateAtom);
  const hasStreamingData = get(hasStreamingCompletionsAtom);
  const isEvalRunning = get(isRunningAtom);
  switch (streamingState) {
    case undefined:
      if (hasStreamingData) {
        set(streamingStateAtom, "streaming");
      }
      break;
    default:
      const allPlaygroundLogsReceived = !isEvalRunning && !hasStreamingData;
      if (allPlaygroundLogsReceived) {
        set(resetRunningStateAtom);
      }
      break;
  }
});

const parseFinishedRows = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rowData: any,
  finishedRows: Record<string, Record<string, boolean>>,
  activeDatasetId?: string,
) => {
  try {
    const spanInfo = JSON.parse(rowData.span_type_info);
    const generationId = spanInfo.generation;

    const origin = JSON.parse(rowData.origin);
    const datasetRowId =
      origin?.object_type === "dataset" ? origin.id : SINGLETON_DATASET_ID;

    if (
      rowData.metrics?.end != null &&
      datasetRowId &&
      generationId &&
      // If the trace sheet is open, allow it to clear the streaming data for a row by not considering it finished here
      !(activeDatasetId && activeDatasetId === datasetRowId)
    ) {
      finishedRows[datasetRowId] = finishedRows[datasetRowId] || {};
      finishedRows[datasetRowId][generationId] = true;
    }
  } catch (e) {
    console.warn(
      "Failed to parse finished row for streaming completion removal:",
      e,
    );
  }
};

function useStreamingState({
  activeRowId,
  experimentScans,
}: {
  activeRowId: RowId | null;
  experimentScans: string[];
}) {
  const setStreamingData = useSetAtom(streamingCompletionsAtom);
  const streamingState = useAtomValue(streamingStateAtom);
  useAtom(streamingStateEffect);

  const activeDatasetId = getOriginDataset(activeRowId)?.rowId;

  // Stable getter to minimize re-queries due to onDataLoaded changes
  const getAtomValues = useAtomCallback(
    useCallback((get) => {
      return {
        hasStreamingData: get(hasStreamingCompletionsAtom),
        latestXactId: get(latestXactIdAtom),
        streamingCompletions: get(streamingCompletionsAtom),
      };
    }, []),
  );

  const onDataLoaded = useCallback(
    async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const { hasStreamingData, latestXactId } = getAtomValues();

      const tables = await Promise.all(
        experimentScans.map((scan) =>
          dbQuery(
            conn,
            abortSignal,
            scan
              ? `SELECT span_type_info, origin, metrics FROM (${scan})
                  ${latestXactId ? `WHERE playground_xact_id > ${singleQuote(latestXactId)}` : ""}`
              : null,
          ),
        ),
      );
      const finishedRows: Record<string, Record<string, boolean>> = {};

      for (const table of tables) {
        if (!table) {
          continue;
        }
        for (const row of table) {
          // Process base experiment
          parseFinishedRows(row, finishedRows, activeDatasetId);
        }
      }

      if (!hasStreamingData || Object.keys(finishedRows).length === 0) {
        return;
      }

      setStreamingData((prev) => {
        const nextStreamingCompletions: Record<
          string,
          Record<string, AllCompletionsStreamingCell>
        > = {};

        for (const [datasetRowId, _v] of Object.entries(prev)) {
          for (const [generationId, v] of Object.entries(_v)) {
            if (finishedRows[datasetRowId]?.[generationId]) {
              // Clear finished rows by excluding them from the next streaming completions state
              continue;
            }

            nextStreamingCompletions[datasetRowId] ??= {};
            nextStreamingCompletions[datasetRowId][generationId] = v;
          }
        }
        return nextStreamingCompletions;
      });
    },
    [getAtomValues, experimentScans, activeDatasetId, setStreamingData],
  );

  return {
    streamingState,
    onDataLoaded: !!streamingState ? onDataLoaded : undefined,
  };
}
