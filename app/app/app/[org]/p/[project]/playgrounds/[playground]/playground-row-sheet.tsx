import { Button, buttonVariants } from "#/ui/button";
import {
  ArrowDown,
  ArrowUp,
  ArrowUpRight,
  Plus,
  TriangleAlert,
  XIcon,
} from "lucide-react";
import { type PropsWithChildren, useEffect } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import Link from "next/link";
import { cn } from "#/utils/classnames";
import { CustomBottomSheet } from "#/ui/custom-bottom-sheet";
import { BasicTooltip } from "#/ui/tooltip";
import { PlaygroundRunButton } from "./playground-run-button";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import { TraceLayoutDropdown } from "#/ui/trace/trace-layout-dropdown";

export const PlaygroundRowSheet = ({
  headerText,
  children,
  onRunRow,
  onClose,
  onAddRow,
  hasPrevRow = true,
  hasNextRow = true,
  onPrevRow,
  onNextRow,
  datasetRowUrl,
  isReadOnly,
  isRunning,
  isOpen,
  extraRightControls,
  runLabel = "Run row",
  isDirty,
  datasetRowId,
}: PropsWithChildren<{
  headerText: string;
  isReadOnly?: boolean;
  isOpen: boolean;
  isRunning: boolean;
  onRunRow?: (colIdx?: number) => void;
  onClose: VoidFunction;
  onAddRow: VoidFunction;
  hasPrevRow?: boolean;
  hasNextRow?: boolean;
  onPrevRow: VoidFunction;
  onNextRow: VoidFunction;
  datasetRowUrl?: string;
  extraRightControls?: React.ReactNode;
  runLabel?: string;
  isDirty?: boolean;
  datasetRowId?: string;
}>) => {
  useEffect(() => {
    // Enable pointer events on the body (radix dialog disables them)
    const timer = setTimeout(() => {
      document.body.style.pointerEvents = "";
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  const { enableScope, disableScope } = useHotkeysContext();
  useEffect(() => {
    enableScope("playground-row");
    return () => {
      disableScope("playground-row");
    };
  }, [enableScope, disableScope]);

  useHotkeys(
    ["j", "ArrowDown"],
    onNextRow,
    {
      scopes: ["playground-row"],
      description: "Move to the next row in the table",
      preventDefault: true,
    },
    [onNextRow],
  );
  useHotkeys(
    ["k", "ArrowUp"],
    onPrevRow,
    {
      scopes: ["playground-row"],
      description: "Move to the previous row in the table",
      preventDefault: true,
    },
    [onPrevRow],
  );

  return (
    <CustomBottomSheet isOpen={isOpen} onClose={onClose}>
      <div
        className={cn(
          "pointer-events-auto relative flex flex-col w-full border-t bg-background",
          HEIGHT_WITH_TOP_OFFSET,
        )}
      >
        <div className="flex flex-none items-center justify-between border-b px-4 py-3">
          <div className="flex flex-none items-center gap-2">
            <div className="flex">
              <Button
                variant="ghost"
                size="xs"
                disabled={!hasPrevRow}
                onClick={onPrevRow}
                Icon={ArrowUp}
              />
              <Button
                variant="ghost"
                size="xs"
                disabled={!hasNextRow}
                onClick={onNextRow}
                Icon={ArrowDown}
              />
            </div>
            <div className="text-lg font-semibold">{headerText}</div>
            {!isReadOnly && (
              <TraceLayoutDropdown className="ml-2" appendedLabel=" layout" />
            )}
          </div>

          <div className="flex gap-2">
            {!isReadOnly && (
              <>
                {extraRightControls}
                <Button
                  size="xs"
                  Icon={Plus}
                  variant="ghost"
                  onClick={onAddRow}
                >
                  Row
                </Button>
                {datasetRowUrl && (
                  <Link
                    href={datasetRowUrl}
                    className={cn(
                      buttonVariants({ size: "xs", variant: "ghost" }),
                    )}
                  >
                    <ArrowUpRight className="size-3" />
                    Go to row in dataset
                  </Link>
                )}
                {onRunRow && (
                  <>
                    {isDirty && (
                      <BasicTooltip tooltipContent="This dataset row has changed since the playground was last run. Run the playground to view results for the latest dataset row.">
                        <div className="flex items-center gap-1 px-2 text-xs text-amber-700">
                          <TriangleAlert className="size-3" />
                          Stale
                        </div>
                      </BasicTooltip>
                    )}

                    <PlaygroundRunButton
                      datasetRowId={datasetRowId}
                      size="xs"
                      isRunning={isRunning}
                      onRun={() => onRunRow()}
                      onStop={() => onRunRow()}
                    >
                      {runLabel}
                    </PlaygroundRunButton>
                  </>
                )}
              </>
            )}
            <Button size="xs" onClick={onClose} Icon={XIcon} />
          </div>
        </div>
        {children}
      </div>
    </CustomBottomSheet>
  );
};
