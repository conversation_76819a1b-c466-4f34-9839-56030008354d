import { type getIsPublicPartial } from "#/utils/object-public-server";
import { type Permission } from "@braintrust/core/typespecs";
import { useQueryFunc } from "#/utils/react-query";
import { type PromptSessionMeta } from "./playground-actions";

export const usePlaygroundVisibility = ({
  permissions,
  promptSessionMeta,
}: {
  permissions: Permission[];
  promptSessionMeta?: PromptSessionMeta | null;
}) => {
  const {
    data: isPublicOutput,
    isLoading: isLoadingVisibility,
    invalidate: refreshIsPublic,
  } = useQueryFunc<typeof getIsPublicPartial>({
    fName: "getIsPublicPartial",
    args: {
      objectType: "prompt_session",
      objectId: promptSessionMeta?.id,
      objectPermissions: permissions,
    },
  });

  const isPublic =
    !isLoadingVisibility &&
    isPublicOutput?.status === "found" &&
    isPublicOutput.value === true;
  const isReadOnly =
    !isLoadingVisibility &&
    (!permissions.includes("update") || !permissions.includes("delete"));

  return {
    isLoadingVisibility,
    isPublic,
    isReadOnly,
    refreshIsPublic,
  };
};
