import { Button, type ButtonProps } from "#/ui/button";
import { Plus } from "lucide-react";
import {
  createContext,
  forwardRef,
  type HTMLAttributes,
  useCallback,
  useContext,
  useMemo,
} from "react";
import { type PromptListing } from "./playground-actions";
import { type UIFunction } from "#/ui/prompts/schema";
import { ProjectContext } from "../projectContext";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { isEmpty } from "#/utils/object";
import { type PromptData } from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import {
  type PromptsByProjectSortedArgs,
  usePromptsByProjectSorted,
} from "../prompts/use-prompts-by-project";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";

export const makePromptDropdownContext = ({
  name,
  args,
}: {
  name: string;
  args: Omit<PromptsByProjectSortedArgs, "orgName" | "projectName">;
}): [
  ({ children }: React.PropsWithChildren<{}>) => React.ReactNode,
  () => {
    allPrompts: PromptListing[] | undefined;
    isLoading: boolean;
    promptsByProjectSorted: {
      projectId: string;
      projectName: string;
      prompts: UIFunction[];
    }[];
    idToPrompt: Record<string, UIFunction>;
    invalidate: VoidFunction;
  },
] => {
  const Context = createContext<{
    allPrompts: PromptListing[] | undefined;
    isLoading: boolean;
    promptsByProjectSorted: {
      projectId: string;
      projectName: string;
      prompts: UIFunction[];
    }[];
    idToPrompt: Record<string, UIFunction>;
    invalidate: VoidFunction;
  }>({
    allPrompts: [],
    isLoading: false,
    promptsByProjectSorted: [],
    idToPrompt: {},
    invalidate: () => {},
  });

  function Provider({ children }: React.PropsWithChildren<{}>) {
    const { projectName } = useContext(ProjectContext);
    const org = useOrg();
    const value = usePromptsByProjectSorted({
      ...args,
      orgName: org.name,
      projectName,
    });

    const fullValue = useMemo(() => {
      return {
        ...value,
        idToPrompt: Object.fromEntries(
          value.promptsByProjectSorted.flatMap(({ prompts }) =>
            prompts.map((p) => [p.id, p]),
          ),
        ),
      };
    }, [value]);

    return <Context.Provider value={fullValue}>{children}</Context.Provider>;
  }
  Provider.displayName = `${name}DropdownProvider`;

  function useDropdown() {
    return useContext(Context);
  }
  useDropdown.displayName = `use${name}Dropdown`;

  return [Provider, useDropdown];
};

type PromptItem = UIFunction & {
  projectName?: string;
  disabled?: boolean;
};

const [ProjectPromptsDropdownProvider, useProjectPromptsDropdown] =
  makePromptDropdownContext({
    name: "ProjectPrompts",
    args: {
      functionObjectType: "prompt",
      query: "", // not used
      btqlFilter: "function_type IS NULL or function_type = 'llm'",
    },
  });

export { ProjectPromptsDropdownProvider, useProjectPromptsDropdown };

// Make sure you nest this in a ProjectPromptsDropdownProvider
export const PromptsDropdown = ({
  open,
  isInSubMenu,
  onAddPrompt,
  projectName,
  selectedPromptIds = [],
  disabledOriginIds = [],
  buttonProps,
  dropdownMenuContentProps,
  children,
  hideBlankOption,
}: React.PropsWithChildren<{
  onAddPrompt: (prompt: {
    origin?: PromptData["origin"];
    promptData?: PromptData;
  }) => void;
  open?: boolean;
  isInSubMenu?: boolean;
  projectName: string;
  selectedPromptIds?: string[];
  disabledOriginIds?: string[];
  buttonProps?: ButtonProps;
  dropdownMenuContentProps?: DropdownMenuContentProps;
  hideBlankOption?: boolean;
}>) => {
  const { projectId } = useContext(ProjectContext);
  const { promptsByProjectSorted, isLoading } = useProjectPromptsDropdown();

  const defaultPromptData = useDefaultPromptData();

  const dropdownData = useMemo(() => {
    if (isEmpty(promptsByProjectSorted)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      promptsByProjectSorted[0]?.projectName === projectName
        ? promptsByProjectSorted[0].prompts.map((p) => ({
            ...p,
            disabled: disabledOriginIds.includes(p.id),
          }))
        : undefined;

    const otherProjects = promptsByProjectSorted
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ projectName, prompts }) =>
        prompts.map((p) => ({
          ...p,
          projectName,
          disabled: disabledOriginIds.includes(p.id),
        })),
      );

    const subGroups =
      otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : undefined;

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [promptsByProjectSorted, projectName, disabledOriginIds]);

  const selectedPrompts = useMemo(
    () =>
      promptsByProjectSorted
        .flatMap((project) =>
          project.prompts.map((p) => ({
            ...p,
            projectName: project.projectName,
            disabled: disabledOriginIds.includes(p.id),
          })),
        )
        .filter((p) => selectedPromptIds.includes(p.id)),
    [promptsByProjectSorted, selectedPromptIds, disabledOriginIds],
  );

  const PromptMenuItem = forwardRef<
    HTMLDivElement,
    { item: PromptItem } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: prompt, ...rest }, ref) => {
        return (
          <DropdownMenuItem
            ref={ref}
            {...rest}
            className="flex gap-2"
            disabled={prompt.disabled}
            onSelect={() => {
              onAddPrompt({
                origin: {
                  prompt_id: prompt.id,
                  project_id: prompt.project_id,
                  prompt_version: prompt._xact_id,
                },
                promptData: prompt.prompt_data ?? undefined,
              });
            }}
            title={`${prompt.name} from ${prompt.projectName ?? "this project"}`}
          >
            <span className="flex-1">{prompt.name}</span>
            {prompt.project_id === projectId ? null : (
              <span className="max-w-24 flex-none truncate text-primary-500">
                {prompt.projectName}
              </span>
            )}
          </DropdownMenuItem>
        );
      },
      [onAddPrompt, projectId],
    ),
  );

  return (
    <NestedDropdown<PromptItem>
      isInSubMenu={isInSubMenu}
      objectType="prompt"
      align={dropdownMenuContentProps?.align}
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name?.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      open={open}
      DropdownItemComponent={PromptMenuItem}
      selectedItems={selectedPrompts}
      additionalActions={
        hideBlankOption
          ? []
          : [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Plus className="size-3" />
                    Blank prompt
                  </div>
                ),
                onSelect: () => {
                  onAddPrompt({
                    promptData: defaultPromptData,
                  });
                },
              },
            ]
      }
    >
      {isInSubMenu
        ? null
        : (children ?? (
            <Button
              size="xs"
              variant="ghost"
              className="gap-1"
              isLoading={isLoading}
              Icon={Plus}
              {...buttonProps}
            >
              Prompt
            </Button>
          ))}
    </NestedDropdown>
  );
};
