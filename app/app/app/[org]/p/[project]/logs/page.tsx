import ClientPage, { type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { cookies } from "next/headers";
import { type LogsPanelLayout } from "#/ui/logs-viewer";
export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get("react-resizable-panels:logs-layout");

  const defaultPanelLayout: LogsPanelLayout = {
    main: 80,
    trace: 20,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  return <ClientPage params={params} defaultPanelLayout={defaultPanelLayout} />;
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Logs",
    sections: [projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/logs`,
  });
}
