"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import LogsViewer, { type LogsPanelLayout } from "#/ui/logs-viewer";
import { useContext, useState } from "react";
import { type SavingState } from "#/ui/saving";
import { isEmpty } from "#/utils/object";
import { AccessFailed } from "#/ui/access-failed";
import { useFeatureFlags } from "#/lib/feature-flags";
import SummaryLogsViewer from "#/ui/summary-logs-viewer";

export interface Params {
  org: string;
  project: string;
}

export default function ClientPage({
  params,
  defaultPanelLayout,
}: {
  params: Params;
  defaultPanelLayout: LogsPanelLayout;
}) {
  const { projectId, projectName } = useContext(ProjectContext);

  useAnalytics({
    page: projectId
      ? {
          category: "project",
          props: { project_id: projectId },
        }
      : null,
  });

  const [_savingState, setSavingState] = useState<SavingState>("none");
  const {
    flags: { fastExperimentSummary },
  } = useFeatureFlags();

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return fastExperimentSummary ? (
    <SummaryLogsViewer
      logType="project_logs"
      logId={projectId}
      setSavingState={setSavingState}
      defaultPanelLayout={defaultPanelLayout}
    />
  ) : (
    <LogsViewer
      logType="project_logs"
      logId={projectId}
      setSavingState={setSavingState}
      defaultPanelLayout={defaultPanelLayout}
    />
  );
}
