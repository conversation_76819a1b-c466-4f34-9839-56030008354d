import {
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
  sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, buttonVariants } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import {
  type ChangeEventHandler,
  useCallback,
  useEffect,
  useState,
} from "react";
import { GripVertical, ThumbsDown, ThumbsUp, XIcon } from "lucide-react";
import { Input } from "#/ui/input";
import {
  type ProjectScoreCategory,
  type ProjectScore,
} from "@braintrust/core/typespecs";
import {
  type UpsertProjectScore,
  type UpsertResponse,
} from "./configuration-client-actions";
import { TextareaWithMdPreview } from "#/ui/textarea-with-md-preview";
import { Checkbox } from "#/ui/checkbox";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type DiscriminatedProjectScore } from "@braintrust/local/query";
import { cn } from "#/utils/classnames";
import { v4 as uuidv4 } from "uuid";
import StyledDndContext from "#/ui/styled-dnd-context";
import { parseStringOnlyIdentPath } from "#/utils/btql/path-helpers";
import { escapeIdentPath } from "#/utils/btql/path-helpers";
import { freeFormDataPath } from "#/ui/trace/free-form-text-area";
import { UncontrolledNumericSlider } from "#/ui/numeric-slider";

interface SortableItemProps {
  id: string;
  category: Omit<ProjectScoreCategory, "value"> & { value: string };
  isCustom: boolean;
  onLabelChange: ChangeEventHandler<HTMLInputElement>;
  onValueChange: ChangeEventHandler<HTMLInputElement>;
  onThumbChange: (newValue: string) => void;
  onRemove: () => void;
  writeToExpectedField: boolean;
}

function SortableCategoryRow({
  id,
  category,
  isCustom,
  onLabelChange,
  onValueChange,
  onThumbChange,
  onRemove,
  writeToExpectedField,
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      className="mb-1 flex w-full items-center gap-1"
      style={style}
      {...attributes}
    >
      <ToggleGroup
        type="single"
        className="h-8"
        value={isCustom ? "" : category.name}
        onValueChange={onThumbChange}
      >
        <ToggleGroupItem
          value="👍"
          className={cn(
            buttonVariants({
              variant: "border",
              size: "sm",
            }),
            "p-0 size-8",
          )}
          aria-label="Thumbs up"
        >
          {formatScoreLabel("👍")}
        </ToggleGroupItem>
        <ToggleGroupItem
          value="👎"
          className={cn(
            buttonVariants({
              variant: "border",
              size: "sm",
            }),
            "p-0 size-8",
          )}
          aria-label="Thumbs down"
        >
          {formatScoreLabel("👎")}
        </ToggleGroupItem>
      </ToggleGroup>
      <Input
        className="h-8 flex-1 text-sm"
        value={isCustom ? category.name : ""}
        onChange={onLabelChange}
        type="text"
        placeholder="Custom label"
      />
      {!writeToExpectedField && (
        <div className="relative w-20 flex-none">
          <Input
            className="h-8 w-full pr-6 text-right text-sm"
            value={category.value}
            placeholder="-"
            type="number"
            min={0}
            max={100}
            required
            onChange={onValueChange}
          />
          <div className="pointer-events-none absolute inset-y-0 right-1.5 flex items-center text-sm text-primary-600">
            %
          </div>
        </div>
      )}
      <Button className="flex-none" size="xs" onClick={onRemove} Icon={XIcon} />
      <div
        className={cn(
          buttonVariants({ size: "xs", variant: "ghost" }),
          "cursor-grab text-primary-400",
        )}
        {...listeners}
      >
        <GripVertical className="size-3" />
      </div>
    </div>
  );
}

export const ConfigureScoreModal = ({
  projectName,
  opened,
  setOpened,
  row,
  create,
  isHumanReviewScore,
}: {
  projectName: string;
  opened: boolean;
  setOpened: (opened: boolean) => void;
  row: DiscriminatedProjectScore | null;
  create: (row: UpsertProjectScore) => Promise<UpsertResponse>;
  isHumanReviewScore?: boolean;
}) => {
  const action = row?.name ? "Update" : "Create";

  const [name, setName] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [scoreType, setScoreType] =
    useState<ProjectScore["score_type"]>("categorical");
  const [categories, setCategories] = useState<
    | (Omit<ProjectScoreCategory, "value"> & { value: string; id: string })[]
    | null
  >(null);
  const [previewSliderValue, setPreviewSliderValue] = useState(50);

  const [writeToExpectedField, setWriteToExpectedField] = useState(false);
  const [isMultipleChoice, setIsMultipleChoice] = useState(false);
  const [metadataPath, setMetadataPath] = useState("");

  useEffect(() => {
    if (row) {
      setName(row.name);
      setDescription(row.description ?? "");
      setScoreType(row.score_type);
      if (row.score_type === "categorical") {
        setCategories(
          row.categories?.map((c) => ({
            ...c,
            value: `${c.value * 100}`,
            id: uuidv4(),
          })) ?? [],
        );
        setWriteToExpectedField(row.config?.destination === "expected");
        setIsMultipleChoice(!!row.config?.multi_select);
      } else if (row.score_type === "free-form") {
        const parsedPath = freeFormDataPath({
          scoreType: row.score_type,
          destination: row.config?.destination,
          name: row.name,
        }).slice(1);
        setMetadataPath(
          parsedPath.length === 1 ? parsedPath[0] : escapeIdentPath(parsedPath),
        );
      }
    }
  }, [row]);

  const clearInputs = () => {
    setName("");
    setDescription("");
    setScoreType("categorical");
    setCategories(null);
    setPreviewSliderValue(50);
    setWriteToExpectedField(false);
    setIsMultipleChoice(false);
    setMetadataPath("");
  };

  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (active.id !== over?.id) {
        setCategories((categories) => {
          if (!categories) return categories;

          const oldIndex = categories.findIndex((c) => c.id === active.id);
          const newIndex = categories.findIndex((c) => c.id === over?.id);
          return arrayMove(categories, oldIndex, newIndex);
        });
      }
    },
    [setCategories],
  );

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  return (
    <Dialog
      open={opened}
      onOpenChange={(open) => {
        !open && clearInputs();
        setOpened(open);
      }}
    >
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>{action} score</DialogTitle>
          <DialogDescription>
            This score will be available in every experiment and log in{" "}
            {projectName}.
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={async (e) => {
            e.preventDefault();
            setError(null);

            if (!name || name.length === 0) {
              setError("Name is required");
              return;
            }

            let categoriesParsed = null;
            if (scoreType === "categorical") {
              if (!categories || categories.length === 0) {
                setError("At least one category is required");
                return;
              }

              categoriesParsed = [];
              for (const category of categories) {
                if (!category.name || category.name.length === 0) {
                  setError("Category label is required");
                  return;
                }

                const categoryValue = writeToExpectedField
                  ? "0"
                  : category.value;

                if (
                  !categoryValue ||
                  categoryValue.length === 0 ||
                  isNaN(parseFloat(categoryValue))
                ) {
                  setError(
                    `Category '${category.name}' value must be a number`,
                  );
                  return;
                }

                const value = parseFloat(categoryValue);
                if (value < 0 || value > 100) {
                  setError(
                    `Category '${category.name}' value must be between 0 and 100`,
                  );
                  return;
                }
                categoriesParsed.push({
                  ...category,
                  value: value / 100,
                });
              }

              const categoryNames = new Set();
              for (const category of categories) {
                if (categoryNames.has(category.name) && !writeToExpectedField) {
                  setError(`Category '${category.name}' is duplicated`);
                  return;
                }
                categoryNames.add(category.name);
              }

              if (isHumanReviewScore) {
                // Check for duplicate category values
                const categoryValues = new Set();
                for (const category of categories) {
                  const categoryValue = writeToExpectedField
                    ? "0"
                    : category.value;
                  if (
                    categoryValues.has(categoryValue) &&
                    !writeToExpectedField
                  ) {
                    setError(`Category value ${categoryValue}% is duplicated`);
                    return;
                  }
                  categoryValues.add(categoryValue);
                }
              }
            }

            setUpdating(true);
            try {
              const result = await create({
                id: row?.id,
                name,
                description: description.length > 0 ? description : null,
                score_type: scoreType,
                categories: categoriesParsed,
                config: {
                  destination:
                    scoreType === "free-form"
                      ? metadataPath && metadataPath.trim().length > 0
                        ? escapeIdentPath([
                            "metadata",
                            ...parseStringOnlyIdentPath(metadataPath),
                          ])
                        : undefined
                      : writeToExpectedField
                        ? "expected"
                        : undefined,
                  multi_select: isMultipleChoice,
                },
              });
              if (result.kind === "duplicate") {
                setError(`Score with name ${name} already exists`);
                return;
              } else if (result.kind === "error") {
                setError(`Failed to create or update score: ${result.message}`);
                return;
              }
              setOpened(false);
              clearInputs();
            } catch (e) {
              setError(`${e}`);
            } finally {
              setUpdating(false);
            }
          }}
        >
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            type="text"
            placeholder="Score name"
            className="mb-2"
          />
          <TextareaWithMdPreview
            className="mb-2"
            value={description}
            setValue={setDescription}
          />
          <div className="mb-4 flex gap-4 py-2 text-center text-sm">
            <label
              className={cn(
                buttonVariants(),
                "flex flex-1 flex-col gap-0 items-center",
              )}
            >
              <input
                type="radio"
                id="options-score-type"
                className="my-2"
                checked={scoreType === "categorical"}
                onChange={() => setScoreType("categorical")}
              />
              <span className="font-medium">Options</span>
              <span className="text-xs font-normal text-primary-600">
                Categorical score
              </span>
            </label>
            <label
              className={cn(
                buttonVariants(),
                "flex flex-1 flex-col gap-0 items-center",
              )}
            >
              <input
                type="radio"
                id="slider-score-type"
                className="my-2"
                checked={scoreType === "slider"}
                onChange={() => setScoreType("slider")}
              />
              <span className="font-medium">Slider</span>
              <span className="text-xs font-normal text-primary-600">
                Continuous score
              </span>
            </label>
            <label
              className={cn(
                buttonVariants(),
                "flex flex-1 flex-col gap-0 items-center",
              )}
            >
              <input
                type="radio"
                id="free-form-score-type"
                className="my-2"
                checked={scoreType === "free-form"}
                onChange={() => setScoreType("free-form")}
              />
              <span className="font-medium">Text</span>
              <span className="text-xs font-normal text-primary-600">
                Free-form input
              </span>
            </label>
          </div>
          <div className="mt-2">
            {scoreType === "slider" ? (
              <div className="w-full">
                <div className="mb-2 text-sm">Preview</div>
                <UncontrolledNumericSlider
                  title={name.length > 0 ? name : "Score name"}
                  value={previewSliderValue}
                  setValue={async (v) => {
                    if (v !== undefined) {
                      setPreviewSliderValue(v);
                    }
                    return null;
                  }}
                  min={0}
                  max={100}
                  step={1}
                  unit="%"
                />
              </div>
            ) : scoreType === "free-form" ? (
              <div>
                <div className="mb-4 text-sm text-primary-600">
                  A free-form text input that writes to the{" "}
                  <code className="text-good-700">metadata</code> field.
                </div>
                <label>
                  <span className="mb-2 block text-xs">
                    Metadata path (dot-delimited)
                  </span>
                  <Input
                    type="text"
                    placeholder="Enter metadata path"
                    value={metadataPath}
                    onChange={(e) => setMetadataPath(e.target.value)}
                  />
                  <span className="mt-2 block text-xs text-primary-600">
                    {metadataPath ? (
                      <>
                        The value will be written to{" "}
                        <code>{`metadata.${escapeIdentPath(parseStringOnlyIdentPath(metadataPath))}`}</code>
                      </>
                    ) : (
                      <>
                        If not provided, the value will be written to{" "}
                        <code>{`metadata.${name.length > 0 ? name : "{name}"}`}</code>
                      </>
                    )}
                  </span>
                </label>
              </div>
            ) : (
              <StyledDndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={categories?.map((category) => category.id) || []}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="w-full">
                    {(!categories || categories?.length === 0) && (
                      <div className="text-sm text-primary-600">
                        No options defined yet
                      </div>
                    )}
                    {categories?.map((category, i) => {
                      const isCustom =
                        category.name !== "👍" && category.name !== "👎";

                      return (
                        <SortableCategoryRow
                          id={category.id}
                          key={category.id}
                          category={category}
                          isCustom={isCustom}
                          writeToExpectedField={writeToExpectedField}
                          onThumbChange={(value) => {
                            const newCategories = [...categories];
                            newCategories[i].name = value;

                            if (
                              category.value === "" ||
                              category.value === "0" ||
                              category.value === "100"
                            ) {
                              if (value === "👍") {
                                category.value = "100";
                              } else {
                                category.value = "0";
                              }
                            }
                            setCategories(newCategories);
                          }}
                          onLabelChange={(e) => {
                            const newCategories = [...categories];
                            newCategories[i].name = e.target.value;
                            setCategories(newCategories);
                          }}
                          onValueChange={(e) => {
                            const newCategories = [...categories];
                            newCategories[i].value = e.target.value;
                            setCategories(newCategories);
                          }}
                          onRemove={() => {
                            const newCategories = [...categories];
                            newCategories.splice(i, 1);
                            setCategories(newCategories);
                          }}
                        />
                      );
                    })}
                  </div>
                </SortableContext>
                <div className="mt-2 flex flex-col items-start gap-3">
                  <Button
                    size="xs"
                    variant="primary"
                    onClick={(e) => {
                      e.preventDefault();
                      setCategories(
                        (categories || []).concat([
                          {
                            name: "",
                            value: "",
                            id: uuidv4(),
                          },
                        ]),
                      );
                    }}
                  >
                    Add option
                  </Button>
                  {scoreType === "categorical" && (
                    <div className="flex gap-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="xs"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setWriteToExpectedField((o) => {
                                if (o) setIsMultipleChoice(false);
                                return !o;
                              });
                            }}
                          >
                            <Checkbox
                              className="text-normal pointer-events-none text-xs"
                              checked={writeToExpectedField}
                              label="Write to expected field instead of score"
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <span className="block text-xs font-normal">
                            A numeric score will not be assigned to the
                            categorical{" "}
                            {isMultipleChoice ? " options" : " option"}
                          </span>
                        </TooltipContent>
                      </Tooltip>
                      <Button
                        size="xs"
                        disabled={!writeToExpectedField}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setIsMultipleChoice((o) => !o);
                        }}
                      >
                        <Checkbox
                          className="text-normal pointer-events-none text-xs"
                          checked={isMultipleChoice}
                          label="Allow multiple choice"
                        />
                      </Button>
                    </div>
                  )}
                </div>
              </StyledDndContext>
            )}
          </div>
          {error && (
            <div className="mt-4 text-sm font-medium text-bad-700">{error}</div>
          )}
          <DialogFooter className="mt-4">
            <Button
              type="submit"
              isLoading={updating}
              size="sm"
              disabled={updating}
            >
              {action}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export const formatScoreLabel = (label: string) =>
  label === "👍" ? (
    <ThumbsUp className="size-3" />
  ) : label === "👎" ? (
    <ThumbsDown className="size-3" />
  ) : (
    label
  );
