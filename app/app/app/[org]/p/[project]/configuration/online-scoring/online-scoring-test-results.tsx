import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { DataTextEditor } from "#/ui/data-text-editor";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { Per<PERSON>, <PERSON><PERSON>lert } from "lucide-react";
import { useEffect, useRef } from "react";
import scrollIntoView from "scroll-into-view-if-needed";
import { onlineScoringTestResultsSchema } from "@braintrust/local/functions";
import { ErrorBanner } from "#/ui/error-banner";
import { MarkdownViewer } from "#/ui/markdown";

export const OnlineScoringTestResults = ({
  payload,
}: {
  payload: Record<string, unknown>;
}) => {
  const parsedResults = onlineScoringTestResultsSchema.safeParse(payload);
  const savedScorerObjects = useScorerFunctions({});
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current || !parsedResults.success) return;
    scrollIntoView(ref.current, {
      scrollMode: "if-needed",
      block: "nearest",
      behavior: "smooth",
    });
  }, [parsedResults.success]);

  if (!parsedResults.success) {
    return (
      <ErrorBanner>
        <div className="mb-1 font-medium">Unable to parse test results:</div>
        <pre>{JSON.stringify(payload, null, 2)}</pre>
      </ErrorBanner>
    );
  }

  return (
    <div className="flex flex-col gap-2" ref={ref}>
      <div>
        <div className="mb-1 text-sm">Test results</div>
        <div className="mb-2 text-xs text-primary-500">
          Scores for {parsedResults.data.length} matching logs
        </div>
      </div>
      {parsedResults.data.map(({ row, results }, index) => (
        <div
          key={index}
          className="flex flex-col gap-2 rounded-md border px-3 pb-3 pt-2 bg-primary-50 border-primary-100"
        >
          <DataTextEditor className="border-0 p-0" value={row} />
          {results.map((r, index) => {
            const functionId =
              "global_function" in r ? r.global_function : r.function_id;
            const functionName =
              savedScorerObjects.functions[functionId]?.name ?? functionId;

            const error = r.kind === "error" ? r.error : null;
            const result = r.kind === "success" ? r.result : null;
            const score =
              typeof result === "number" ? result : (result?.score ?? null);
            const metadata =
              typeof result === "object" ? result?.metadata : undefined;

            return (
              <div className="mt-2 flex flex-col gap-2" key={index}>
                <div className="flex items-center gap-1 rounded-xl text-sm">
                  {error ? (
                    <div className="w-14 text-sm font-semibold text-bad-500">
                      <TriangleAlert className="size-3 flex-none" />
                    </div>
                  ) : score !== null ? (
                    <div className="w-14 font-semibold tabular-nums">
                      {(Number(score) * 100).toLocaleString(undefined, {
                        maximumFractionDigits: 2,
                      })}
                      %
                    </div>
                  ) : (
                    <div className="w-14 font-semibold">
                      <NullFormatter />
                    </div>
                  )}
                  <Percent className="size-3 text-lime-600 dark:text-lime-400" />
                  <div className="truncate">{functionName}</div>
                </div>
                {error && (
                  <div className="pl-15 text-xs text-bad-600">{error}</div>
                )}
                {metadata && (
                  <div className="flex flex-col gap-2.5 pl-15 text-xs">
                    {"choice" in metadata && (
                      <div>
                        <div className="mb-1 text-primary-500">Choice</div>
                        {
                          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                          metadata.choice as string
                        }
                      </div>
                    )}
                    {"rationale" in metadata && (
                      <div>
                        <div className="mb-1 text-primary-500">Rationale</div>
                        <MarkdownViewer
                          className="prose-xs py-0 !text-xs"
                          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                          value={metadata.rationale as string}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ))}
    </div>
  );
};
