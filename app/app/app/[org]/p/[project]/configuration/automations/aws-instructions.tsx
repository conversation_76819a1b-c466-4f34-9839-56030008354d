"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import CodeToCopy from "#/ui/code-to-copy";
import { ErrorBanner } from "#/ui/error-banner";
import { Spinner } from "#/ui/icons/spinner";
import { useGetRequest } from "#/utils/btapi/get";
import { _urljoin } from "@braintrust/core";
import {
  cloudIdentitySchema,
  makeExternalId,
} from "@braintrust/local/api-schema";
import { Clipboard, ExternalLink } from "lucide-react";
import { useMemo } from "react";
import { toast } from "sonner";

interface AwsInstructionsProps {
  orgId: string;
  projectId: string;
  externalId: string;
  exportPath: string;
}

export function AwsInstructions({
  orgId,
  projectId,
  externalId,
  exportPath,
}: AwsInstructionsProps) {
  const {
    data: cloudIdentityRaw,
    error: cloudIdentityFetchError,
    isLoading,
  } = useGetRequest<{
    cloud_identity: string;
  }>("/automation/cloud-identity");
  const { cloudIdentity, cloudIdentityError } = useMemo(() => {
    if (!cloudIdentityRaw) {
      return {
        cloudIdentity: undefined,
        cloudIdentityError: cloudIdentityFetchError,
      };
    }
    const parsed = cloudIdentitySchema.safeParse(cloudIdentityRaw);
    return {
      cloudIdentity: parsed.success ? parsed.data : undefined,
      cloudIdentityError: parsed.success ? undefined : parsed.error,
    };
  }, [cloudIdentityFetchError, cloudIdentityRaw]);

  const { bucketName, bucketPrefix, invalidPath } = useMemo(() => {
    try {
      return { ...parseS3Path(exportPath), invalidPath: undefined };
    } catch (e) {
      return {
        bucketName: "",
        bucketPrefix: "",
        invalidPath: `Invalid export path: ${exportPath}`,
      };
    }
  }, [exportPath]);

  const iamPolicy = useMemo(
    () => ({
      Version: "2012-10-17",
      Statement: [
        {
          Sid: "BraintrustS3Export",
          Effect: "Allow",
          Action: [
            "s3:List*",
            "s3:DeleteObject",
            "s3:DeleteObjectVersion",
            "s3:PutObject",
            "s3:PutObjectAcl",
            "s3:AbortMultipartUpload",
          ],
          Resource: [
            `arn:aws:s3:::${_urljoin(bucketName, bucketPrefix ? `/${bucketPrefix}` : "", "*")}`,
            `arn:aws:s3:::${bucketName}`,
          ],
        },
      ],
    }),
    [bucketName, bucketPrefix],
  );

  const fullExternalId = useMemo(() => {
    return makeExternalId({
      orgId,
      projectId,
      automationExtId: externalId,
    });
  }, [orgId, projectId, externalId]);

  const trustRelationship = useMemo(
    () => ({
      Version: "2012-10-17",
      Statement: [
        {
          Effect: "Allow",
          Principal: {
            AWS: `arn:aws:iam::${cloudIdentity?.aws_account_id}:root`,
          },
          Action: "sts:AssumeRole",
          Condition: {
            StringEquals: {
              "sts:ExternalId": fullExternalId,
            },
          },
        },
      ],
    }),
    [cloudIdentity?.aws_account_id, fullExternalId],
  );

  const trustRelationshipOrgPattern = useMemo(
    () => ({
      Version: "2012-10-17",
      Statement: [
        {
          Effect: "Allow",
          Principal: {
            AWS: `arn:aws:iam::${cloudIdentity?.aws_account_id}:root`,
          },
          Action: "sts:AssumeRole",
          Condition: {
            StringLike: {
              "sts:ExternalId": `bt:${orgId}:*`,
            },
          },
        },
      ],
    }),
    [cloudIdentity?.aws_account_id, orgId],
  );

  if (isLoading) {
    return <Spinner className="size-4" />;
  } else if (cloudIdentityError) {
    return (
      <ErrorBanner skipErrorReporting className="my-0">
        Error loading cloud identity: {cloudIdentityError.message}
      </ErrorBanner>
    );
  } else if (!cloudIdentity?.aws_account_id) {
    return (
      <ErrorBanner skipErrorReporting className="my-0">
        No AWS account ID found
      </ErrorBanner>
    );
  } else if (invalidPath) {
    return (
      <ErrorBanner skipErrorReporting className="my-0">
        {invalidPath}
      </ErrorBanner>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        Follow these steps to configure an IAM role that allows Braintrust to
        export your data to S3.
      </div>

      <div className="space-y-4 rounded-lg border p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-semibold">Step 1: Create IAM Role</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() =>
              window.open(
                "https://console.aws.amazon.com/iam/home#/roles",
                "_blank",
              )
            }
            className="h-6 px-2 text-xs"
          >
            <ExternalLink className="size-3" />
            Open IAM Console
          </Button>
        </div>
        <ol className="list-inside list-decimal space-y-2 text-sm text-muted-foreground">
          <li>Go to the AWS IAM Console and click &ldquo;Create role&rdquo;</li>
          <li>Select &ldquo;AWS account&rdquo; as trusted entity type</li>
          <li>
            Choose &ldquo;Another AWS account&rdquo; and enter account ID:{" "}
            <Button
              onClick={(e) => {
                e.preventDefault();
                navigator.clipboard.writeText(
                  cloudIdentity?.aws_account_id ?? "",
                );
                toast("AWS account ID copied to clipboard", {
                  duration: 1000,
                });
              }}
              size="inline"
              className="border-0 px-1 text-xs font-normal text-primary-500"
              IconRight={Clipboard}
            >
              <code className="rounded px-1 bg-muted">
                {cloudIdentity?.aws_account_id}
              </code>
            </Button>
          </li>
          <li>
            Check &ldquo;Require external ID&rdquo; and enter the external ID:{" "}
            <Button
              onClick={(e) => {
                e.preventDefault();
                navigator.clipboard.writeText(fullExternalId ?? "");
                toast("External ID copied to clipboard", {
                  duration: 1000,
                });
              }}
              size="inline"
              className="border-0 px-1 text-xs font-normal text-primary-500"
              IconRight={Clipboard}
            >
              <code className="rounded px-1 bg-muted">{fullExternalId}</code>
            </Button>
          </li>
          <li>
            Click &ldquo;Next&rdquo; and skip the &ldquo;Add permissions&rdquo;
            step by clicking &ldquo;Next&rdquo; again.
          </li>
          <li>Name your role and create it</li>
          <li>Copy the Role ARN and paste it in the form above</li>
        </ol>
      </div>

      <div className="space-y-3 rounded-lg border p-4">
        <h3 className="text-base font-semibold">
          Step 2: Navigate to the role
        </h3>
        <p className="text-sm text-muted-foreground">
          Navigate to the role you just created in the IAM console. You can
          search for it by name.
        </p>
      </div>

      <div className="space-y-3 rounded-lg border p-4">
        <h3 className="text-base font-semibold">Step 3: Create IAM Policy</h3>
        <p className="text-sm text-muted-foreground">
          In the &ldquo;Permissions&rdquo; tab, click &ldquo;Add
          permissions&rdquo; and select &ldquo;Create inline policy&rdquo;. Pick
          &ldquo;JSON&rdquo; and enter the following policy:
        </p>
        <CodeToCopy data={JSON.stringify(iamPolicy, null, 2)} language="json" />
        <p className="text-sm text-muted-foreground">
          Click &ldquo;Next&rdquo; and give the policy a name (e.g.,
          &ldquo;BraintrustS3ExportPolicy&rdquo;). Finally, click &ldquo;Create
          policy&rdquo;.
        </p>
      </div>

      <div className="space-y-3 rounded-lg border p-4">
        <h3 className="text-base font-semibold">
          Step 4: Validate trust relationship
        </h3>
        <p className="text-sm text-muted-foreground">
          In the &ldquo;Trust relationships&ldquo; tab, validate the trust
          relationship for your IAM role:
        </p>
        <CodeToCopy
          data={JSON.stringify(trustRelationship, null, 2)}
          language="json"
        />
        <p className="text-sm text-muted-foreground">
          This trust relationship scopes the role to only export data for this
          automation. The external ID consists of your organization ID, project
          ID, and an automation-specific ID. For example, to scope the role to
          only allow any automations in your organization, you can do:
        </p>
        <CodeToCopy
          data={JSON.stringify(trustRelationshipOrgPattern, null, 2)}
          language="json"
        />
      </div>

      <div className="rounded-lg border p-4">
        <h3 className="mb-3 text-base font-semibold">
          Step 5: Copy the role ARN
        </h3>
        <p className="text-sm text-muted-foreground">
          Copy the role ARN from the role summary page and paste it in the
          &ldquo;Role ARN&rdquo; field below.
        </p>
      </div>
    </div>
  );
}

function parseS3Path(exportPath: string): {
  bucketName: string;
  bucketPrefix: string;
} {
  const url = new URL(exportPath);
  const bucketName = url.hostname;
  const bucketPrefix = url.pathname.startsWith("/")
    ? url.pathname.slice(1)
    : url.pathname;
  return { bucketName, bucketPrefix };
}
