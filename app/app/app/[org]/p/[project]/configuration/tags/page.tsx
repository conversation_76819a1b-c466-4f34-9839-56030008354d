"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { useContext, useState } from "react";
import { Plus, TagIcon } from "lucide-react";
import { Tag } from "#/ui/tag";
import { type ProjectTag } from "@braintrust/core/typespecs";
import { performUpsert, performDelete } from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { TagDialog } from "./tag-dialog";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { DraggableTable, type ColumnDefinition } from "../draggable-table";
import {
  type PositionDescriptions,
  type setAllTagsSortOrder,
  type setTagSortOrder,
} from "../configuration-actions";
import { getNewLexoRankPosition } from "#/utils/score-config";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { LexoRank } from "lexorank";

// Helper functions for tag positioning (adapted from score-config)
const getPositionDescriptionsForAllTags = (
  tags: ProjectTag[],
): PositionDescriptions => {
  const ranks: Record<string, string> = {};

  let currentRank = LexoRank.middle();
  tags.forEach((tag) => {
    ranks[tag.id] = currentRank.toString();
    currentRank = currentRank.genNext();
  });

  return ranks;
};

const getPositionDescriptionForTag = ({
  prevTag,
  tag,
  nextTag,
}: {
  prevTag?: ProjectTag;
  tag: ProjectTag;
  nextTag?: ProjectTag;
}): PositionDescriptions | null => {
  if (
    !tag.position ||
    (prevTag && !prevTag.position) ||
    (nextTag && !nextTag.position)
  ) {
    return null;
  }

  const newLexoRank = getNewLexoRankPosition({
    current: tag.position,
    prev: prevTag?.position,
    next: nextTag?.position,
  });

  if (!newLexoRank) {
    return null;
  }

  return { [tag.id]: newLexoRank };
};

// Column definitions for the tags table
const getTagColumns = (): ColumnDefinition<ProjectTag>[] => [
  {
    key: "name",
    header: "Tag name",
    width: "w-48",
    className: "w-48",
    render: (tag) => (
      <Tag
        color={tag.color}
        label={tag.name}
        className="truncate"
        description={tag.name}
      />
    ),
  },
  {
    key: "description",
    header: "Description",
    className: "flex-1",
    render: (tag) => (
      <p className="truncate text-primary-600">{tag.description}</p>
    ),
  },
];

const TagConfig = () => {
  const { getToken } = useAuth();
  const org = useOrg();
  const orgName = org.name;
  const [openedModal, setOpenedModal] = useState(false);
  const [tagConfig, setTagConfig] = useState<ProjectTag | null>(null);
  const { api_url: apiUrl } = useOrg();

  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    projectName,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { tags } = config;

  const hasAllPositionsDefined = tags.every(
    (tag) => typeof tag.position === "string",
  );

  if (!projectId) {
    throw new Error(
      "Cannot instantiate TagConfigurationSection outside project",
    );
  }

  const updateSortOrderOnServer = async (
    positionDescriptions: PositionDescriptions,
    hasAllPositionsDefined: boolean,
  ) => {
    try {
      const args = {
        org_name: orgName,
        project_name: projectName,
        position_descriptions: positionDescriptions,
      };

      if (hasAllPositionsDefined) {
        await invokeServerAction<typeof setTagSortOrder>({
          fName: "setTagSortOrder",
          args,
          getToken,
        });
      } else {
        await invokeServerAction<typeof setAllTagsSortOrder>({
          fName: "setAllTagsSortOrder",
          args,
          getToken,
        });
      }
    } catch (e) {
      console.error("Failed to update project_tags sort order", e);
      toast.error(
        "Failed to update tag sort order. Please reload the page and try again.",
      );
    }
  };

  const handleReorder = async (
    newOrder: ProjectTag[],
    draggedItem: ProjectTag,
    originalIndex: number,
    newIndex: number,
  ) => {
    let positionDescriptions: PositionDescriptions | null = null;

    // If all of the tags have Lexorank positions defined, we only need to update the moved tag
    if (hasAllPositionsDefined && draggedItem) {
      const prevTag = newOrder[newIndex - 1];
      const nextTag = newOrder[newIndex + 1];
      positionDescriptions = getPositionDescriptionForTag({
        tag: draggedItem,
        prevTag,
        nextTag,
      });
    }

    // If one or more tags do not have Lexorank positions defined, we need to update all of the tags
    // This typically happens on the very first DnD operation for a set of tags
    if (!hasAllPositionsDefined) {
      positionDescriptions = getPositionDescriptionsForAllTags(newOrder);
    }

    if (positionDescriptions) {
      await updateSortOrderOnServer(
        positionDescriptions,
        hasAllPositionsDefined,
      );
      await mutate();
    }
  };

  return (
    <ConfigurationSectionLayout
      title="Tags"
      description="Track and filter your experiments, logs, and other objects."
      action={
        tags.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setOpenedModal(true);
              setTagConfig(null);
            }}
            Icon={Plus}
          >
            Tag
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : tags.length > 0 ? (
        <DraggableTable
          items={tags}
          columns={getTagColumns()}
          onReorder={handleReorder}
          onEdit={(tag) => {
            setOpenedModal(true);
            setTagConfig(tag);
          }}
          onDelete={async (tag) => {
            return toast.promise(
              performDelete({
                apiUrl,
                sessionToken: await getOrRefreshToken(),
                objectType: "project_tag",
                rowId: tag.id,
                mutate,
              }),
            );
          }}
          getDragPreview={(tag) => tag.name}
          className="table-auto text-left"
        />
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <TagIcon className="text-primary-500" />
              <p className="text-base text-primary-500">No tags defined yet</p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setOpenedModal(true);
                  setTagConfig(null);
                }}
                Icon={Plus}
              >
                Create tag
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <TagDialog
        projectName={projectName}
        tagConfig={tagConfig}
        opened={openedModal}
        setOpened={(opened) => {
          setOpenedModal(opened);
        }}
        upsert={async (row) => {
          return await performUpsert({
            apiUrl,
            sessionToken: await getOrRefreshToken(),
            objectType: "project_tag",
            row,
            projectId,
            mutate,
          });
        }}
      />
    </ConfigurationSectionLayout>
  );
};

export default TagConfig;
