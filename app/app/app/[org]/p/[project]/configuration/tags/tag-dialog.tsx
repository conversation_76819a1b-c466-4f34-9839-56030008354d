"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useEffect, useState } from "react";
import { Tag, TagColor } from "#/ui/tag";
import { cn } from "#/utils/classnames";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { type ProjectTag } from "@braintrust/core/typespecs";
import {
  type UpsertProjectTag,
  type UpsertResponse,
} from "../configuration-client-actions";
import { Input, inputClassName } from "#/ui/input";
import { STARRED_TAG } from "#/ui/table/star-cell";

export const RESERVED_TAGS = [STARRED_TAG];

export function TagDialog({
  projectName,
  opened,
  setOpened,
  tagConfig,
  defaultName = "",
  upsert,
}: {
  projectName: string;
  opened: boolean;
  setOpened: (opened: boolean) => void;
  tagConfig: ProjectTag | null;
  defaultName?: string;
  upsert: (row: UpsertProjectTag) => Promise<UpsertResponse>;
}) {
  const action = tagConfig?.name ? "Update" : "Create";

  const [name, setName] = useState<string>(defaultName);
  const [description, setDescription] = useState<string>("");
  const [color, setColor] = useState<string>("");

  const resetFields = () => {
    setName(defaultName);
    setDescription("");
    setColor(TagColor.Red);
  };

  useEffect(() => {
    if (tagConfig) {
      setName(tagConfig.name);
      setDescription(tagConfig.description ?? "");
      setColor(tagConfig.color ?? TagColor.Red);
    } else {
      setName(defaultName);
      setDescription("");
      setColor(TagColor.Red);
    }
  }, [tagConfig, defaultName]);

  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  return (
    <Dialog open={opened} onOpenChange={setOpened}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>{action} tag</DialogTitle>
          <DialogDescription>
            This tag will be available in every experiment, dataset, and log
            event in {projectName}.
          </DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-3"
          onSubmit={async (e) => {
            e.preventDefault();
            setError(null);

            if (!name || name.length === 0) {
              setError("Name is required");
              return;
            }

            setUpdating(true);
            try {
              const result = await upsert({
                id: tagConfig?.id,
                name,
                description: description.length > 0 ? description : null,
                color: color.length > 0 ? color : null,
              });
              if (result.kind === "duplicate") {
                setError(`Tag with name ${name} already exists`);
                return;
              } else if (result.kind === "error") {
                setError(`Failed to create or update tag: ${result.message}`);
                return;
              }
              resetFields();
              setOpened(false);
            } catch (e) {
              setError(`${e}`);
            } finally {
              setUpdating(false);
            }
          }}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                value={name}
                name="name"
                autoComplete="none"
                onChange={(e) => setName(e.target.value)}
                className="w-full text-sm bg-transparent"
                type="text"
                placeholder="Enter tag name"
                disabled={action === "Update"}
                autoFocus={action === "Create"}
              />
            </TooltipTrigger>
            {action === "Update" && (
              <TooltipContent side="left" className="w-72">
                Once you create a tag, you cannot edit its name, since tags are
                stored in data. You should create a new tag instead.
              </TooltipContent>
            )}
          </Tooltip>
          <div className="-mx-0.5 flex gap-[2px]">
            {Object.values(TagColor).map((c, idx) => (
              <span
                className={cn(
                  "p-0.5 flex-1 flex rounded-md border border-transparent hover:border-primary-400 cursor-pointer",
                  {
                    "border-primary-700 pointer-events-none": color === c,
                  },
                )}
                key={idx}
                onClick={() => {
                  setColor(c);
                }}
              >
                <Tag
                  label=""
                  color={c}
                  className="h-6 flex-1 rounded-[4px] border-2"
                />
              </span>
            ))}
          </div>
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className={inputClassName}
            placeholder="Enter description (optional)"
          />
          {error && <div className="mt-4 text-sm text-bad-500">{error}</div>}
          <DialogFooter>
            <Button
              type="submit"
              isLoading={updating}
              disabled={RESERVED_TAGS.includes(name)}
            >
              {action}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
