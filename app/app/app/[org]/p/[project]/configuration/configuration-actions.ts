"use server";

import { z } from "zod";

import { makeFullResultSetQuery } from "#/pages/api/_object_crud_util";
import type { ProjectConfig } from "#/utils/score-config";
import { projectConfigSchema } from "#/utils/score-config";
import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { otelWrapTraced } from "#/utils/tracing";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

export const fetchProjectConfig = otelWrapTraced(
  "fetchProjectConfig",
  async (
    {
      org_name,
      project_name,
    }: {
      org_name: string;
      project_name: string;
    },
    authLookupRaw?: AuthLookup,
  ): Promise<ProjectConfig> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const {
      query: projectAutomationsQuery,
      queryParams: projectAutomationsQueryParams,
    } = makeFullResultSetQuery({
      authLookup,
      priorObjectTables: ["project_automation"],
      permissionInfo: {
        aclObjectType: "project",
        aclPermission: "read",
      },
      filters: {
        org_name,
        project_name,
      },
    });

    const { query: projectScoresQuery, queryParams: projectScoresQueryParams } =
      makeFullResultSetQuery({
        authLookup,
        priorObjectTables: ["project_score"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        filters: {
          org_name,
          project_name,
        },
      });

    const { query: projectTagsQuery, queryParams: projectTagsQueryParams } =
      makeFullResultSetQuery({
        authLookup,
        priorObjectTables: ["project_tag"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        filters: {
          org_name,
          project_name,
        },
      });

    const { query: projectViewerQuery, queryParams: projectViewerQueryParams } =
      makeFullResultSetQuery({
        authLookup,
        priorObjectTables: ["span_iframe"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        filters: {
          org_name,
          project_name,
        },
      });

    const supabase = getServiceRoleSupabase();
    const results = await Promise.all([
      supabase.query(
        `
        select * from (${projectAutomationsQuery}) t
        order by name asc
       `,
        projectAutomationsQueryParams.params,
      ),
      supabase.query(
        `
        select * from (${projectScoresQuery}) t
        order by position, name asc
       `,
        projectScoresQueryParams.params,
      ),
      supabase.query(
        `
        select * from (${projectTagsQuery}) t
        order by position, name asc
       `,
        projectTagsQueryParams.params,
      ),
      supabase.query(
        `
        select * from (${projectViewerQuery}) t
        order by name asc
       `,
        projectViewerQueryParams.params,
      ),
    ]);

    return projectConfigSchema.parse({
      automations: results[0].rows,
      scores: results[1].rows,
      tags: results[2].rows,
      span_iframes: results[3].rows,
    });
  },
);

const _positionDescriptions = z.record(z.string().uuid(), z.string());

export type PositionDescriptions = z.infer<typeof _positionDescriptions>;

async function updateHumanReviewScoresSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
    score_id,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
    score_id?: string;
  },
  authLookup: AuthLookup,
) {
  const { query: projectScoresQuery, queryParams: projectScoresQueryParams } =
    makeFullResultSetQuery({
      authLookup,
      priorObjectTables: ["project_score"],
      permissionInfo: {
        aclObjectType: "project",
        aclPermission: "update",
      },
      filters: {
        org_name,
        project_name,
        id: score_id ? [score_id] : undefined,
      },
    });

  const values = Object.entries(position_descriptions)
    .map(
      ([uuid, position]) =>
        `(uuid_or_null(${projectScoresQueryParams.add(
          uuid,
        )}), ${projectScoresQueryParams.add(position)})`,
    )
    .join(", ");

  const fullQuery = `
      with
      all_updates(id, position) as (values ${values}),
      matching_human_review_project_scores as (
        select * from (${projectScoresQuery}) as subquery where score_type in ('slider', 'categorical', 'free-form')
      ),
      matching_updates as (
        select id, position from all_updates
        where all_updates.id in (select id from matching_human_review_project_scores)
      )
      update project_scores
      set position = matching_updates.position
      from matching_updates
        where project_scores.id = matching_updates.id
        and (select count(*) from matching_updates) = (select count(*) from matching_human_review_project_scores)
        and (select count(*) from matching_updates) = (select count(*) from all_updates)
      returning matching_updates.id
`;

  const supabase = getServiceRoleSupabase();
  //console.log(
  //  "Running query\n",
  //  substituteParamsDebug(fullQuery, projectScoresQueryParams.params),
  //);
  const { rows } = await supabase.query(
    fullQuery,
    projectScoresQueryParams.params,
  );

  return z.array(z.object({ id: z.string().uuid() })).parse(rows);
}

export async function setAllHumanReviewScoresSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
  },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const rows = await updateHumanReviewScoresSortOrder(
    { org_name, project_name, position_descriptions },
    authLookup,
  );

  const positionDescriptionKeys = Object.keys(position_descriptions);
  if (rows.length !== positionDescriptionKeys.length) {
    throw new HTTPError(
      400,
      "Position descriptions do not cover all scores. Please reload the page and try again",
    );
  }

  return null;
}

export async function setHumanReviewScoreSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
  },
  authLookupRaw?: AuthLookup,
) {
  const positionDescriptionKeys = Object.keys(position_descriptions);

  if (positionDescriptionKeys.length !== 1) {
    throw new HTTPError(
      400,
      "position_description must include exactly one description",
    );
  }

  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const rows = await updateHumanReviewScoresSortOrder(
    {
      org_name,
      project_name,
      position_descriptions: position_descriptions,
      score_id: positionDescriptionKeys[0],
    },
    authLookup,
  );

  if (rows.length === 0) {
    throw new HTTPError(
      400,
      "Score was deleted during update. Please reload the page and try again.",
    );
  }

  return null;
}

async function updateTagsSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
    tag_id,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
    tag_id?: string;
  },
  authLookup: AuthLookup,
) {
  const { query: projectTagsQuery, queryParams: projectTagsQueryParams } =
    makeFullResultSetQuery({
      authLookup,
      priorObjectTables: ["project_tag"],
      permissionInfo: {
        aclObjectType: "project",
        aclPermission: "update",
      },
      filters: {
        org_name,
        project_name,
        id: tag_id ? [tag_id] : undefined,
      },
    });

  const values = Object.entries(position_descriptions)
    .map(
      ([uuid, position]) =>
        `(uuid_or_null(${projectTagsQueryParams.add(
          uuid,
        )}), ${projectTagsQueryParams.add(position)})`,
    )
    .join(", ");

  const fullQuery = `
      with
      all_updates(id, position) as (values ${values}),
      matching_project_tags as (
        select * from (${projectTagsQuery}) as subquery
      ),
      matching_updates as (
        select id, position from all_updates
        where all_updates.id in (select id from matching_project_tags)
      )
      update project_tags
      set position = matching_updates.position
      from matching_updates
        where project_tags.id = matching_updates.id
        and (select count(*) from matching_updates) = (select count(*) from matching_project_tags)
        and (select count(*) from matching_updates) = (select count(*) from all_updates)
      returning matching_updates.id
`;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(
    fullQuery,
    projectTagsQueryParams.params,
  );

  return z.array(z.object({ id: z.string().uuid() })).parse(rows);
}

export async function setAllTagsSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
  },
  authLookupRaw?: AuthLookup,
) {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const rows = await updateTagsSortOrder(
    { org_name, project_name, position_descriptions },
    authLookup,
  );

  const positionDescriptionKeys = Object.keys(position_descriptions);
  if (rows.length !== positionDescriptionKeys.length) {
    throw new HTTPError(
      400,
      "Position descriptions do not cover all tags. Please reload the page and try again",
    );
  }

  return null;
}

export async function setTagSortOrder(
  {
    org_name,
    project_name,
    position_descriptions,
  }: {
    org_name: string;
    project_name: string;
    position_descriptions: PositionDescriptions;
  },
  authLookupRaw?: AuthLookup,
) {
  const positionDescriptionKeys = Object.keys(position_descriptions);

  if (positionDescriptionKeys.length !== 1) {
    throw new HTTPError(
      400,
      "position_description must include exactly one description",
    );
  }

  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

  const rows = await updateTagsSortOrder(
    {
      org_name,
      project_name,
      position_descriptions: position_descriptions,
      tag_id: positionDescriptionKeys[0],
    },
    authLookup,
  );

  if (rows.length === 0) {
    throw new HTTPError(
      400,
      "Tag was deleted during update. Please reload the page and try again.",
    );
  }

  return null;
}
