import { useMemo } from "react";
import {
  type PromptListing,
  type fetchPromptListing,
} from "../playgrounds/playground-actions";
import { searchMatch } from "#/utils/string-search";
import { useQueryFunc } from "#/utils/react-query";
import { promptSchema, type UIFunction } from "#/ui/prompts/schema";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { useFunctions } from "#/ui/functions/use-functions";

export type PromptsByProjectSortedArgs = {
  functionObjectType: FunctionObjectType;
  orgName: string;
  projectName: string;
  query: string;
  btqlFilter?: string;
};

export function usePromptsByProjectSorted({
  functionObjectType,
  orgName,
  projectName,
  query,
  btqlFilter,
}: PromptsByProjectSortedArgs): {
  allPrompts: PromptListing[] | undefined;
  isLoading: boolean;
  promptsByProjectSorted: {
    projectId: string;
    projectName: string;
    prompts: UIFunction[];
  }[];
  invalidate: VoidFunction;
} {
  const {
    data: savedPrompts,
    isLoading,
    invalidate,
  } = useQueryFunc<typeof fetchPromptListing>({
    fName: "fetchPromptListing",
    args: { orgName },
  });

  const promptsByProjectSortedMeta = useMemo(() => {
    const filteredPromptsByProject = (savedPrompts ?? []).reduce<{
      [projectId: string]: {
        projectId: string;
        projectName: string;
        prompts: PromptListing[];
      };
    }>((acc, prompt) => {
      const { project_id, project_name } = prompt;
      if (!acc[project_id]) {
        acc[project_id] = {
          projectId: project_id,
          projectName: project_name,
          prompts: [],
        };
      }

      acc[project_id].prompts.push(prompt);
      return acc;
    }, {});

    return Object.values(filteredPromptsByProject).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : a.projectName.localeCompare(b.projectName),
    );
  }, [projectName, savedPrompts]);

  const promptObjects = useFunctions({
    filters: useMemo(
      () => (btqlFilter ? [{ btql: btqlFilter }] : []),
      [btqlFilter],
    ),
    functionObjectType,
  });

  const promptObjectsById = useMemo((): Record<string, UIFunction> => {
    return Object.fromEntries(
      (promptObjects.objects ?? [])
        .map((obj) => {
          const parsed = promptSchema.safeParse(obj);
          if (parsed.success) {
            return parsed.data;
          } else
            console.debug(
              "Failed to parse schematized prompt. Will not be available.",
              obj.id,
              parsed.error,
            );
          return null;
        })
        .filter((p) => p !== null)
        .map((p) => [p.id, p]),
    );
  }, [promptObjects.objects]);

  const promptsByProjectSorted = useMemo(() => {
    return promptsByProjectSortedMeta
      .map((project) => ({
        ...project,
        prompts: project.prompts
          .map((p) => promptObjectsById[p.id])
          .filter(
            (p) => p && searchMatch({ query, text: `${p.name} (${p.slug})` }),
          )
          .sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      }))
      .filter((project) => project.prompts.length > 0);
  }, [promptsByProjectSortedMeta, promptObjectsById, query]);

  return useMemo(
    () => ({
      allPrompts: savedPrompts,
      isLoading: isLoading || promptObjects.status === "loading",
      promptsByProjectSorted,
      invalidate,
    }),
    [
      savedPrompts,
      isLoading,
      promptObjects.status,
      promptsByProjectSorted,
      invalidate,
    ],
  );
}
