import { cn } from "#/utils/classnames";
import {
  type Edge,
  Handle,
  type HandleType,
  Position,
  useReactFlow,
} from "@xyflow/react";
import { Database, X } from "lucide-react";
import { getModelIcon } from "../../../prompt/[prompt]/model-icon";
import { AvailableModels } from "@braintrust/proxy/schema";
import { BasicTooltip } from "#/ui/tooltip";
import { AgentPromptPreviewTooltip } from "../playgrounds/[playground]/prompt-preview-tooltip";
import { Skeleton } from "#/ui/skeleton";
import { useOpenedPlaygroundPrompt } from "./open";
import { type PromptNodeData } from "./agent-canvas-utils";
import { useProjectPromptsDropdown } from "../playgrounds/prompts-dropdown";
import { Button } from "#/ui/button";
import { useCallback, useMemo } from "react";
import { AddAgentPromptNode } from "./add-agent-prompt-node";

export const InputNode = () => {
  const { getNodes } = useReactFlow();
  const nodes = getNodes();
  const isLeaf = nodes.length === 1;
  return (
    <div className="relative">
      <CustomHandle
        type="source"
        position={Position.Bottom}
        className={cn({
          // the handle must always be rendered, but we can hide it when the input node is a leaf
          "opacity-0": isLeaf,
        })}
      />
      <div className="flex !size-8 items-center justify-center !rounded-full border p-2 text-center text-xs bg-background hover:bg-primary-50 hover:border-primary-300">
        <Database className="size-3 text-fuchsia-500" />
      </div>
      {isLeaf && (
        <AddAgentPromptNode
          className="pr-3"
          position={nodes[0].position}
          sourceNodeId="dataInput"
        >
          Prompt
        </AddAgentPromptNode>
      )}
    </div>
  );
};

function isLeafNode(nodeId: string, edges: Edge[]): boolean {
  return !edges.some((edge) => edge.source === nodeId);
}

export const PromptNode = ({
  data,
  selected,
  selectable,
  id,
}: {
  data: PromptNodeData;
  selected?: boolean;
  selectable?: boolean;
  id: string;
}) => {
  const { idToPrompt } = useProjectPromptsDropdown();
  const promptName = data.origin?.prompt_id
    ? idToPrompt[data.origin.prompt_id]?.name
    : null;

  const openedPrompt = useOpenedPlaygroundPrompt({
    promptId: data.origin?.prompt_id,
    projectId: data.origin?.project_id,
    promptVersion: data.origin?.prompt_version,
  });

  const { deleteElements, getEdges, getNode } = useReactFlow();
  const onDelete = useCallback(() => {
    const edges = getEdges();
    const connectedEdges = edges.filter(
      (edge) => edge.source === id || edge.target === id,
    );
    deleteElements({ nodes: [{ id }], edges: connectedEdges });
  }, [deleteElements, id, getEdges]);

  const isLoadingSavedPrompt =
    data.origin && !data.options?.model && openedPrompt.status === "loading";

  // Use the model from the opened prompt if available
  const effectiveModel =
    data.options?.model ?? openedPrompt.prompt?.prompt_data?.options?.model;
  const ModelIcon = effectiveModel ? getModelIcon(effectiveModel) : undefined;
  const effectiveModelDisplayName = effectiveModel
    ? AvailableModels[effectiveModel]?.displayName
    : null;

  const edges = getEdges();
  const isLeaf = useMemo(() => {
    return isLeafNode(id, edges);
  }, [edges, id]);

  return (
    <div className="relative">
      <BasicTooltip
        side="right"
        tooltipContent={
          effectiveModel && !!data.prompt ? (
            <AgentPromptPreviewTooltip prompt={data} />
          ) : null
        }
      >
        <div
          className={cn(
            "rounded-md border bg-background items-center cursor-pointer p-2 text-xs w-48 transition-colors flex gap-1.5 relative group",
            {
              "hover:bg-primary-50 hover:border-primary-300":
                selectable && !selected,
              "bg-accent-50 border-accent-500": selected,
            },
          )}
        >
          <CustomHandle type="target" position={Position.Top} />
          {ModelIcon && <ModelIcon size={18} />}
          <div className="flex flex-1 flex-col">
            {isLoadingSavedPrompt ? (
              <Skeleton className="h-9 w-full" />
            ) : (
              <div className="font-medium">
                {promptName ??
                  effectiveModelDisplayName ??
                  effectiveModel ??
                  "Prompt"}
              </div>
            )}
            {effectiveModel && (
              <div className="text-[10px] text-primary-500">Prompt</div>
            )}
          </div>
          <CustomHandle type="source" position={Position.Bottom} />
          <Button
            size="icon"
            className="absolute -right-2 -top-2 z-10 !size-4 rounded-full border-none p-0 opacity-0 shadow-md bg-primary-300 text-primary-600 group-hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation(); // prevent node selection
              onDelete();
            }}
          >
            <X className="size-2.5" />
          </Button>
        </div>
      </BasicTooltip>
      {isLeaf && (
        <AddAgentPromptNode
          position={getNode(id)?.position ?? { x: 0, y: 0 }}
          sourceNodeId={id}
          size="icon"
          className="size-6 hover:size-7 hover:translate-y-0.5"
        />
      )}
    </div>
  );
};

const CustomHandle = ({
  type,
  position,
  className,
}: {
  type: HandleType;
  position: Position;
  className?: string;
}) => {
  return (
    <Handle
      type={type}
      position={position}
      isConnectable
      className={cn("!border !bg-background !border-primary-400", className)}
    />
  );
};
