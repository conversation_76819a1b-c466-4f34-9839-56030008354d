import { Button, type ButtonProps } from "#/ui/button";
import { Plus } from "lucide-react";
import { useCallback, useContext } from "react";
import { type PromptData } from "@braintrust/core/typespecs";
import { ProjectContext } from "../projectContext";
import { PromptsDropdown } from "../playgrounds/prompts-dropdown";
import { cn } from "#/utils/classnames";
import { useReactFlow } from "@xyflow/react";
import { nanoid } from "ai";

export const AddAgentPromptNode = ({
  className,
  children,
  position,
  sourceNodeId,
  size,
}: {
  className?: string;
  children?: React.ReactNode;
  position: { x: number; y: number };
  sourceNodeId: string;
  size?: ButtonProps["size"];
}) => {
  const project = useContext(ProjectContext);
  const { setNodes, setEdges } = useReactFlow();

  const addPromptNode = useCallback(
    (prompt: { origin?: PromptData["origin"]; promptData?: PromptData }) => {
      const newNodeId = `prompt-${nanoid(6)}`;
      setEdges((e) => [
        ...e,
        {
          id: `${sourceNodeId}->${newNodeId}`,
          source: sourceNodeId,
          target: newNodeId,
          deletable: true,
          selectable: true,
          selected: false,
          data: {
            variable: "input",
          },
        },
      ]);
      setNodes((n) => [
        ...n.map((node) => ({
          ...node,
          selected: false,
        })),
        {
          id: newNodeId,
          type: "prompt",
          position: {
            x: position.x,
            y: position.y + 100,
          },
          data: {
            ...prompt.promptData,
            ...(prompt.origin && { origin: prompt.origin }),
          },
          selectable: true,
          selected: true,
          deletable: true,
          connectable: true,
          // sourcePosition: Position.Bottom,
          // targetPosition: Position.Top,
        },
      ]);
    },
    [setNodes, setEdges, position, sourceNodeId],
  );

  return (
    <PromptsDropdown
      onAddPrompt={addPromptNode}
      projectName={project.projectName}
      selectedPromptIds={[]}
    >
      <Button
        size={size ?? "xs"}
        className={cn(
          "absolute transition-all z-10 bg-primary-200 -bottom-9 left-1/2 -translate-x-1/2 rounded-full origin-center transform-gpu",
          className,
        )}
        Icon={Plus}
      >
        {children}
      </Button>
    </PromptsDropdown>
  );
};
