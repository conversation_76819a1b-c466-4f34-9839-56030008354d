import { <PERSON><PERSON> } from "#/ui/button";
import TextEditor, { type TextEditorProps } from "#/ui/text-editor";
import { type ContentPart, type Content } from "@braintrust/core/typespecs";
import { memo, useCallback } from "react";
import { File, MessageSquare, Minus, Paperclip } from "lucide-react";
import { ImagePartThumb } from "../ImagePartThumb";
import { BasicTooltip } from "#/ui/tooltip";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useFeatureFlags } from "#/lib/feature-flags";
import { type TransactionId } from "@braintrust/core";
import { useSyncedPrompts } from "./use-synced-prompts";
import { uploadAttachment } from "#/utils/btapi/attachment";
import { FileInputButton } from "#/ui/file-input-button";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";
import { useFocusManager } from "@react-aria/focus";

export interface MessageBlockProps {
  data?: Content;
  isMultimodal: boolean;
  textPlaceholder?: string;
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  extensions: TextEditorProps["extensions"];
  isReadOnly?: boolean;
  copilotContext?: CopilotContextBuilder;
  promptId: string;
  messageIdx: number;
}

export type PartType = ContentPart["type"];

const MessageBlockSynced = memo(
  ({
    data,
    isMultimodal,
    textPlaceholder,
    triggerSave,
    runPrompts,
    extensions,
    isReadOnly,
    copilotContext,
    promptId,
    messageIdx,
  }: MessageBlockProps) => {
    const { removeMessagePart, updateMessagePart_NO_SAVE, focusedEditor } =
      useSyncedPrompts();

    const parts: ContentPart[] = Array.isArray(data)
      ? data
      : [{ type: "text", text: data ?? "" }];

    const focusManager = useFocusManager();

    const jump = useCallback(
      (up: boolean) => {
        if (up) {
          focusManager?.focusPrevious({ accept: textEditorSelector });
        } else {
          focusManager?.focusNext({ accept: textEditorSelector });
        }
      },
      [focusManager],
    );

    const { flags, isLoading: isLoadingFlags } = useFeatureFlags();
    const getAutoCompleteContext = useCallback(() => {
      return copilotContext?.makeCopilotContext({
        type: "function",
        functionType: "prompt",
      });
    }, [copilotContext]);

    const org = useOrg();
    const { getOrRefreshToken } = useSessionToken();

    return (
      <div className="flex flex-col gap-2">
        {parts.map((part, i) => (
          <div className="flex w-full flex-row gap-1.5" key={i}>
            {isMultimodal && (
              <span className="flex-none pt-[3px]">
                {part.type === "text" ? (
                  <MessageSquare className="size-3 text-primary-600" />
                ) : (
                  <File className="size-3 text-primary-600" />
                )}
              </span>
            )}
            <div className="flex flex-1 flex-col gap-1.5 overflow-hidden">
              <TextEditor
                className="flex text-sm"
                isMonospace={false}
                spellcheck={part.type !== "image_url"}
                wrap
                readOnly={isReadOnly}
                onMetaEnter={(type) => {
                  if (type === "cmd") {
                    triggerSave();
                    runPrompts();
                  }
                }}
                autoFocus={
                  focusedEditor.current?.promptId === promptId &&
                  focusedEditor.current?.messageIndex === messageIdx &&
                  focusedEditor.current?.partIndex === i
                }
                jump={jump}
                placeholder={
                  part.type === "text"
                    ? textPlaceholder || "Enter message"
                    : "Multimodal URL, base64 contents, or variable"
                }
                value={part.type === "text" ? part.text : part.image_url.url}
                onSave={triggerSave}
                onChange={(value) => {
                  updateMessagePart_NO_SAVE({
                    id: promptId,
                    index: messageIdx,
                    partIndex: i,
                    value,
                  });
                }}
                extensions={extensions}
                getAutoCompleteContext={
                  !flags.copilotPrompts ? undefined : getAutoCompleteContext
                }
                editorId={`prompt-${promptId}-message-${messageIdx}-part-${i}`}
              />
              {part.type === "image_url" && (
                <ImagePartThumb value={part.image_url.url} />
              )}
            </div>
            {part.type === "image_url" &&
              flags.attachmentsInMessages &&
              !isLoadingFlags && (
                <BasicTooltip content="Upload file">
                  <FileInputButton
                    variant="ghost"
                    size="icon"
                    className="size-5 flex-none rounded-[4px] opacity-0 transition-opacity bg-primary-200 text-primary-600 hover:bg-primary-200/50 group-hover/promptblock:opacity-100"
                    onChange={async (file) => {
                      if (!file) {
                        toast.warning("No file selected");
                        return;
                      }

                      toast.promise(
                        async () => {
                          const attachment = await uploadAttachment({
                            file,
                            sessionToken: await getOrRefreshToken(),
                            org: org,
                          });

                          updateMessagePart_NO_SAVE({
                            id: promptId,
                            index: messageIdx,
                            partIndex: i,
                            // Not sure if this is right...
                            value: JSON.stringify(attachment),
                          });
                          triggerSave();
                        },
                        {
                          loading: "Uploading file",
                          success: "File uploaded",
                          error: "Failed to upload file",
                        },
                      );
                    }}
                  >
                    <Paperclip className="size-3 opacity-80" />
                  </FileInputButton>
                </BasicTooltip>
              )}
            {parts.length > 1 && !isReadOnly && (
              <BasicTooltip content="Remove message part">
                <Button
                  variant="ghost"
                  size="icon"
                  className="-mr-0.5 size-5 flex-none rounded-[4px] opacity-0 transition-opacity bg-primary-200 text-primary-600 hover:bg-primary-200/50 group-hover/promptblock:opacity-100"
                  onClick={(e) => {
                    e.preventDefault();
                    removeMessagePart({
                      id: promptId,
                      index: messageIdx,
                      partIndex: i,
                    });
                  }}
                >
                  <Minus className="size-3 opacity-80" />
                </Button>
              </BasicTooltip>
            )}
          </div>
        ))}
      </div>
    );
  },
);

MessageBlockSynced.displayName = "MessageBlockSynced";

export default MessageBlockSynced;

const textEditorSelector = (el: Element) =>
  el instanceof HTMLElement && !!el.classList.contains("cm-content");
