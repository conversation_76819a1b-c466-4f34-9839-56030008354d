import { useCallback } from "react";

import { useState } from "react";
import { type DragEndEvent, type DragStartEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { getNewLexoRankPosition } from "#/utils/score-config";
import { getPosition, useSyncedPrompts } from "./use-synced-prompts";
import { useAtomCallback } from "jotai/utils";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import { AvailableModels } from "@braintrust/proxy/schema";

export const useDraggablePromptsSynced = (
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>,
) => {
  const [draggingPrompt, setDraggingPrompt] = useState<{
    index: number;
    displayName: string;
    model: string;
    isAgent: boolean;
    isRemoteEval: boolean;
  } | null>(null);
  const {
    sortedSyncedPromptsAtom_ROOT,
    setSyncedPrompts_ROOT,
    saveSyncedPrompt,
  } = useSyncedPrompts();

  const handleDragStart = useAtomCallback(
    useCallback(
      (get, _set, event: DragStartEvent) => {
        const { active } = event;
        if (!active) {
          setDraggingPrompt(null);
          return;
        }

        const syncedPrompts = get(sortedSyncedPromptsAtom_ROOT);

        const promptIndex = syncedPrompts.findIndex((p) => p.id === active.id);
        if (promptIndex === -1) {
          setDraggingPrompt(null);
          return;
        }

        const prompt = syncedPrompts[promptIndex];
        const meta =
          savedPromptMeta?.[prompt.prompt_data.origin?.prompt_id ?? ""] ?? null;
        const model = prompt.prompt_data.options?.model ?? "";

        setDraggingPrompt({
          index: promptIndex,
          displayName:
            meta?.name ?? AvailableModels[model]?.displayName ?? "Prompt",
          model,
          isAgent: prompt.function_data.type === "graph",
          isRemoteEval: prompt.function_data.type === "remote_eval",
        });
      },
      [sortedSyncedPromptsAtom_ROOT, savedPromptMeta],
    ),
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      if (!over || active.id === over?.id) return;

      setSyncedPrompts_ROOT((prompts) => {
        // prompts immer draft isn't sorted, so we need to sort it
        prompts.sort((a, b) =>
          (getPosition(a) ?? "").localeCompare(getPosition(b) ?? ""),
        );
        const prevIndex = prompts.findIndex((p) => p.id === active.id);
        if (prevIndex === -1) return;

        const prompt = prompts[prevIndex];
        const nextIndex = prompts.findIndex((p) => p.id === over.id);
        const newOrder = arrayMove(prompts, prevIndex, nextIndex);

        if (!prompt?.prompt_data.options?.position) return;

        const newPosition = getNewLexoRankPosition({
          current: prompt.prompt_data.options.position,
          prev: newOrder[nextIndex - 1]?.prompt_data?.options?.position,
          next: newOrder[nextIndex + 1]?.prompt_data?.options?.position,
        });

        if (!newPosition) return;

        prompt.prompt_data.options.position = newPosition;
      });

      saveSyncedPrompt(active.id.toString());
    },
    [saveSyncedPrompt, setSyncedPrompts_ROOT],
  );

  return { draggingPrompt, handleDragStart, handleDragEnd };
};
