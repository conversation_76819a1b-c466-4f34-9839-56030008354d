import { DataTextEditor } from "#/ui/data-text-editor";
import { type RenderOption } from "#/utils/parse";
import { atom, useAtomValue } from "jotai";
import { excludeKeys } from "../utils";
import { useSyncedPrompts } from "./use-synced-prompts";
import { useMemo } from "react";

const ALLOWED_RENDER_OPTIONS: RenderOption[] = ["json", "yaml"];

export const DiffedPromptDataSynced = ({
  id,
  diffId,
}: {
  id: string;
  diffId: string;
}) => {
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const diffAtom = useMemo(
    () =>
      atom((get) => {
        const prompts = get(sortedSyncedPromptsAtom_ROOT);
        const prompt = prompts.find((p) => p.id === id);
        const diffPrompt = prompts.find((p) => p.id === diffId);

        if (!prompt || !diffPrompt) {
          return {};
        }

        return {
          value: {
            ...prompt.prompt_data,
            options: excludeKeys(prompt.prompt_data.options, ["position"]),
          },
          diffValue: {
            ...diffPrompt.prompt_data,
            options: excludeKeys(diffPrompt.prompt_data.options, ["position"]),
          },
        };
      }),
    [sortedSyncedPromptsAtom_ROOT, id, diffId],
  );
  const { value, diffValue } = useAtomValue(diffAtom);

  return (
    <>
      <DataTextEditor
        allowedRenderOptions={ALLOWED_RENDER_OPTIONS}
        diffValue={diffValue}
        readOnly
        value={value}
      />
      <div className="text-pretty py-2 text-xs text-primary-400">
        This is a diff of this prompt compared to the base prompt. To edit this
        prompt, disable diff mode.
      </div>
    </>
  );
};
