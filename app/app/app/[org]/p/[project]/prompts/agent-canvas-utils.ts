import { type Node, type Edge, Position } from "@xyflow/react";
import {
  type GraphEdge,
  type GraphData,
  type GraphNode,
  type PromptData,
  functionIdSchema,
} from "@braintrust/core/typespecs";
import { InputNode } from "./agent-nodes";
import { PromptNode } from "./agent-nodes";

export const nodeTypes = {
  prompt: PromptNode,
  dataInput: InputNode,
} as const;

export type PromptNodeData = PromptData & {
  functionId?: string;
  functionVersion?: string;
};

interface InputNodeData extends Record<string, unknown> {}

export type PromptNodeType = Node<PromptNodeData>;
export type InputNodeType = Node<InputNodeData>;

export type AllNodeDataTypes = InputNodeData | PromptNodeData;
export type NodeTypeNames = keyof typeof nodeTypes;
export type AgentFlowNode = Node<AllNodeDataTypes, NodeTypeNames>;

export type EdgeData = {
  variable: string;
};

export const DEFAULT_GRAPH: GraphData = {
  type: "graph",
  nodes: {
    dataInput: {
      type: "input",
      position: {
        x: 120,
        y: 100,
      },
    },
  },
  edges: {},
};

export function makeGraphData(
  nodes: AgentFlowNode[],
  edges: Edge<EdgeData>[],
): GraphData {
  return {
    type: "graph",
    nodes: makeGraphNodes(nodes),
    edges: makeGraphEdges(edges),
  };
}

export function makeGraphNodes(
  nodes: AgentFlowNode[],
): Record<string, GraphNode> {
  return Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    nodes
      .map((node): [string, GraphNode | null] => {
        return [node.id, nodeToGraphNode(node)];
      })
      .filter(([_, graphNode]) => graphNode !== null) as [string, GraphNode][],
  );
}

export function makeGraphEdges(
  edges: Edge<EdgeData>[],
): Record<string, GraphEdge> {
  return Object.fromEntries(
    edges.map((edge): [string, GraphEdge] => {
      return [edge.id, edgeToGraphEdge(edge)];
    }),
  );
}

function nodeToGraphNode(node: AgentFlowNode): GraphNode | null {
  if (!node.type) {
    return null;
  }
  const baseNode = {
    position: node.position,
  };
  switch (node.type) {
    case "prompt":
      return {
        ...baseNode,
        type: "function",
        function: {
          inline_prompt: node.data,
        },
      };
    case "dataInput":
      return {
        ...baseNode,
        type: "input",
      };
    default: {
      const _t: never = node.type;
      throw new Error(`Unknown node type: ${_t}`);
    }
  }
}

function edgeToGraphEdge(edge: Edge<EdgeData>): GraphEdge {
  return {
    source: {
      node: edge.source,
      variable: "output", // TODO: Support this in the UI
    },
    target: {
      node: edge.target,
      variable: edge.data?.variable ?? "input",
    },
    purpose: "data",
  };
}

export function deriveAgentFlowState(graph: GraphData): {
  nodes: AgentFlowNode[];
  edges: Edge<EdgeData>[];
} {
  return {
    nodes: deriveAgentFlowNodes(graph.nodes),
    edges: deriveAgentFlowEdges(graph.edges),
  };
}

export function deriveAgentFlowNodes(
  nodes: GraphData["nodes"],
): AgentFlowNode[] {
  return Object.entries(nodes).map(([id, node]) =>
    graphNodeToAgentFlowNode({
      id,
      node,
    }),
  );
}

export function deriveAgentFlowEdges(
  edges: GraphData["edges"],
): Edge<EdgeData>[] {
  return Object.entries(edges).map(([id, edge]) => ({
    id,
    source: edge.source.node,
    target: edge.target.node,
    data: { variable: edge.target.variable },
    deletable: true,
    selectable: true,
  }));
}

function graphNodeToAgentFlowNode({
  id,
  node,
}: {
  id: string;
  node: GraphNode;
}): AgentFlowNode {
  const baseNode = {
    id,
    position: node.position ?? {
      x: Math.random() * 1000,
      y: Math.random() * 1000,
    },
  };
  switch (node.type) {
    case "function": {
      const functionId = functionIdSchema.parse(node.function);
      return {
        ...baseNode,
        type: "prompt",
        data: {
          ...("inline_prompt" in functionId ? functionId.inline_prompt : {}),
          functionId:
            "function_id" in functionId ? functionId.function_id : undefined,
          functionVersion:
            "version" in functionId ? functionId.version : undefined,
        },
        selectable: true,
        connectable: true,
      };
    }
    case "input":
      return {
        ...baseNode,
        type: "dataInput",
        data: {},
        selectable: false,
        deletable: false,
        connectable: true,
        sourcePosition: Position.Bottom,
      };
    case "output":
    case "gate":
    case "btql":
    case "literal":
    case "aggregator":
    case "prompt_template":
      throw new Error("TODO");
    default: {
      const _t: never = node;
      throw new Error(`Unknown node type: ${_t}`);
    }
  }
}

export const defaultNodes: AgentFlowNode[] = [
  {
    id: "dataInput",
    type: "dataInput",
    position: { x: 120, y: 100 },
    data: {},
    selectable: false,
    deletable: false,
    connectable: true,
    sourcePosition: Position.Bottom,
  },
];

export const defaultEdges: Edge<EdgeData>[] = [];
