import { Button, type ButtonProps } from "#/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import React from "react";

export const MessageActionButton = ({
  onClick,
  tooltip,
  children,
  ...rest
}: React.PropsWithChildren<
  ButtonProps & {
    onClick: VoidFunction;
    tooltip: string;
  }
>) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className="!size-6 min-w-6 p-0 text-primary-500 hover:bg-primary-200"
          size="xs"
          variant="ghost"
          onClick={(e) => {
            e.preventDefault();
            onClick();
          }}
          {...rest}
        />
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent className="text-xs">{tooltip}</TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};
