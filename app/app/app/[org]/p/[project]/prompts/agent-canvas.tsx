import { useDarkMode } from "#/utils/useDarkMode";
import { Background, ReactFlow } from "@xyflow/react";
import {
  memo,
  type RefObject,
  useCallback,
  useContext,
  useMemo,
  useRef,
} from "react";
import { type AgentFlowNode, nodeTypes } from "./agent-canvas-utils";
import { useAgentCanvasState } from "./agent-canvas-store";
import {
  type SyncedPlaygroundBlock,
  type UIFunctionData,
} from "#/ui/prompts/schema";
import { type PromptData } from "@braintrust/core/typespecs";
import { type ModelDetails } from "#/ui/prompts/models";
import { type ModelSpec } from "@braintrust/proxy/schema";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { type SavedPromptMeta } from "../playgrounds/[playground]/use-saved-prompt-meta";
import { ProjectContext } from "../projectContext";
import { InfoBanner } from "#/ui/info-banner";
import Link from "next/link";
import { PromptEditorSynced } from "../../../prompt/[prompt]/prompt-editor-synced";
import { SyncedPromptsScoperProvider } from "./synced/use-synced-prompts";
import { SavedTaskBarSynced } from "./synced/saved-task-bar-synced";
import { type Extension } from "@codemirror/state";
import {
  convertJSONSchemaToStructure,
  type JSONStructure,
  usePromptExtensions,
} from "#/ui/prompts/hooks";
import { parseResponseFormat } from "./output-format";
import { useFeatureFlags } from "#/lib/feature-flags";

const GRID_SIZE = 20;
const EMPTY_OUTPUT_NAMES: string[] = [];

export type PromptEditorProps = {
  orgName: string;
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  allAvailableModels: Record<string, ModelSpec>;
  copilotContext?: CopilotContextBuilder;
  extensions?: Extension[];
};

// TODO:
// - Progress bar doesn't seem to work (gets stuck at 50%?).
export const AgentCanvas = memo(
  ({
    containerRef,
    id,
    functionData,
    origin,
    isFullscreen,
    setFullscreen,
    savedPromptMeta,
    datasetJsonStructure,
    ...promptEditorProps
  }: {
    containerRef: RefObject<HTMLDivElement | null>;
    id: string;
    functionData: UIFunctionData | undefined;
    origin?: PromptData["origin"];
    isFullscreen: boolean;
    setFullscreen: (isFullscreen: boolean) => void;
    savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
    datasetJsonStructure: JSONStructure;
  } & Omit<PromptEditorProps, "extensions">) => {
    const project = useContext(ProjectContext);
    const {
      nodes,
      edges,
      onNodesChange,
      onEdgesChange,
      onConnect,
      onReconnect,
    } = useAgentCanvasState({
      functionData,
      id,
      isFullscreen,
    });

    const isDarkMode = useDarkMode();

    const selectedNode = useMemo(() => {
      return nodes.find((node) => node.selected);
    }, [nodes]);
    const inboundNode = useMemo(() => {
      const inboundNodeId = edges.find(
        (edge) => edge.target === selectedNode?.id,
      )?.source;
      return inboundNodeId
        ? nodes.find((node) => node.id === inboundNodeId)
        : null;
    }, [edges, nodes, selectedNode?.id]);

    return (
      <div className="flex size-full overflow-hidden border-t">
        <div className="relative flex flex-1 overflow-hidden rounded-b-md">
          <div className="flex flex-1 flex-col overflow-hidden">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onReconnect={onReconnect}
              nodeTypes={nodeTypes}
              fitView
              maxZoom={1.1}
              defaultEdgeOptions={{
                animated: true,
              }}
              minZoom={0.5}
              proOptions={{ hideAttribution: true }}
              colorMode={isDarkMode ? "dark" : "light"}
              snapToGrid
              selectNodesOnDrag={false}
              multiSelectionKeyCode={null}
              selectionKeyCode={null}
              snapGrid={[GRID_SIZE, GRID_SIZE]}
              onNodeClick={() => {
                setFullscreen(true);
              }}
            >
              <Background
                id={id}
                gap={GRID_SIZE}
                className="!bg-primary-100/80 dark:!bg-primary-50"
              />
            </ReactFlow>
            <InfoBanner className="my-0 rounded-none border-x-0 border-b-0 px-3 border-primary-200">
              <div className="flex justify-between">
                Agents are in beta
                <Link
                  href="/docs/guides/functions/agents"
                  className="font-medium hover:underline"
                  target="_blank"
                >
                  Learn more
                </Link>
              </div>
            </InfoBanner>
            <SavedTaskBarSynced
              containerRef={containerRef}
              functionData={functionData}
              origin={origin}
              isReadOnly={false}
              orgName={project.orgName}
              promptId={id}
              projectId={project.projectId}
              savedPromptMetaName={savedPromptMeta[id]?.name}
              type="agent"
            />
          </div>
        </div>
        {selectedNode?.type === "prompt" && isFullscreen && (
          <div className="flex w-[400px] flex-none flex-col overflow-hidden border-l">
            <NodeDataEditor
              agentId={id}
              node={selectedNode}
              inboundNode={inboundNode}
              savedPromptMeta={savedPromptMeta}
              datasetJsonStructure={datasetJsonStructure}
              {...promptEditorProps}
            />
          </div>
        )}
      </div>
    );
  },
);
AgentCanvas.displayName = "AgentCanvas";

function NodeDataEditor({
  agentId,
  node,
  ...promptEditorProps
}: {
  agentId: string;
  node: AgentFlowNode;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
  inboundNode?: AgentFlowNode | null;
  datasetJsonStructure: JSONStructure;
} & PromptEditorProps) {
  // This is used to point the prompt state callbacks exposed by usedSyncedPrompts to the correct nested prompt, rather than the root-level task (i.e. the agent).
  const scoper = useCallback(
    (prompt: SyncedPlaygroundBlock) => {
      if (prompt.function_data.type !== "graph") return undefined;
      const graphNode = prompt.function_data.nodes[node.id];
      return graphNode.type === "function" &&
        "inline_prompt" in graphNode.function
        ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          (graphNode.function.inline_prompt as PromptData)
        : undefined;
    },
    [node.id],
  );

  switch (node.type) {
    case "prompt":
      return (
        <SyncedPromptsScoperProvider persistenceId={agentId} scoper={scoper}>
          <PromptNodeEditor
            nodeId={node.id}
            data={node.data}
            {...promptEditorProps}
          />
        </SyncedPromptsScoperProvider>
      );
  }
}

function PromptNodeEditor({
  nodeId,
  data,
  orgName,
  modelOptionsByProvider,
  allAvailableModels,
  copilotContext,
  savedPromptMeta,
  inboundNode,
  datasetJsonStructure,
}: {
  nodeId: string;
  data: PromptData;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
  inboundNode?: AgentFlowNode | null;
  datasetJsonStructure: JSONStructure;
} & PromptEditorProps) {
  const project = useContext(ProjectContext);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const {
    flags: { datasetAgentVar },
  } = useFeatureFlags();

  const isFirstNodeSelected = inboundNode?.id === "dataInput";
  const inboundNodeResponseFormat = parseResponseFormat(inboundNode?.data);
  const jsonStructure = useMemo(() => {
    const datasetStructure = datasetAgentVar
      ? { dataset: datasetJsonStructure }
      : {};
    if (isFirstNodeSelected) {
      return { ...datasetJsonStructure, ...datasetStructure };
    }
    if (inboundNodeResponseFormat.type === "json_schema") {
      return {
        ...convertJSONSchemaToStructure(
          inboundNodeResponseFormat.schema?.schema,
        ),
        ...datasetStructure,
      };
    }
    return { input: "VARCHAR", ...datasetStructure };
  }, [
    datasetAgentVar,
    datasetJsonStructure,
    isFirstNodeSelected,
    inboundNodeResponseFormat.type,
    inboundNodeResponseFormat.schema?.schema,
  ]);

  const previousNodeHasStructuredOutput =
    inboundNodeResponseFormat.type === "json_schema";
  const getMissingVariableMessage = useCallback(
    (variableName: string) => {
      const dataLocation = isFirstNodeSelected
        ? "your dataset."
        : "the previous prompt.";
      const suffix =
        isFirstNodeSelected || previousNodeHasStructuredOutput
          ? "You may encounter unexpected results."
          : "Consider adding a structured output schema.";
      return `Variable '${variableName}' is not defined in ${dataLocation} ${suffix}`;
    },
    [isFirstNodeSelected, previousNodeHasStructuredOutput],
  );

  const { extensions } = usePromptExtensions({
    jsonStructure,
    outputNames: EMPTY_OUTPUT_NAMES,
    expandInputVariables: true,
    getMissingVariableMessage,
  });

  return (
    <div className="flex flex-1 flex-col overflow-hidden" ref={containerRef}>
      <div className="flex flex-1 flex-col overflow-auto p-2">
        <PromptEditorSynced
          orgName={orgName}
          modelOptionsByProvider={modelOptionsByProvider}
          allAvailableModels={allAvailableModels}
          extensions={extensions}
          promptData={data}
          onRun={() => {
            console.warn("running not supported");
          }}
          copilotContext={copilotContext}
          promptId={nodeId}
          agentPosition={isFirstNodeSelected ? "first" : "later"}
        />
      </div>
      {/* TODO: hide when readonly */}
      <SavedTaskBarSynced
        containerRef={containerRef}
        isReadOnly={false}
        orgName={project.orgName}
        promptData={data}
        origin={data.origin}
        promptId={nodeId}
        projectId={project.projectId}
        savedPromptMetaName={
          savedPromptMeta[data.origin?.prompt_id ?? ""]?.name
        }
        type="prompt"
      />
    </div>
  );
}
