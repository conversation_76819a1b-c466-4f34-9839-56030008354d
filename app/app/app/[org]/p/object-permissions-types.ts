// Permissions checkboxes for an individual object-level modal (individual
// experiment, dataset, etc).

import { z } from "zod";

import { type AclObjectType } from "@braintrust/core/typespecs";
import {
  type PermissionsRequirement,
  crudPermissions,
  manageAccessPermissions,
  permissionsCheckboxSchema,
} from "#/ui/permissions/permissions-types";

export const objectPermissionsCheckboxesSchema = z.strictObject({
  create: permissionsCheckboxSchema,
  read: permissionsCheckboxSchema,
  update: permissionsCheckboxSchema,
  delete: permissionsCheckboxSchema,
  manageAccess: permissionsCheckboxSchema,
});

export type ObjectPermissionsCheckboxes = z.infer<
  typeof objectPermissionsCheckboxesSchema
>;

export function makeObjectPermissionsRequirements(
  objectType: AclObjectType,
): Record<string, PermissionsRequirement> {
  return Object.fromEntries([
    ...crudPermissions.map((p) => [
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      p as string,
      {
        objectType: objectType,
        permissions: [{ permission: p, restrictObjectType: objectType }],
      },
    ]),
    [
      "manageAccess",
      {
        objectType: objectType,
        permissions: manageAccessPermissions.map((p) => ({
          permission: p,
          restrictObjectType: objectType,
        })),
      },
    ],
  ]);
}

export interface AccessSummary {
  userOrGroupId: string;
  label: string;
  accessLabel?: string;
  inheritanceLabels?: string[];
}
