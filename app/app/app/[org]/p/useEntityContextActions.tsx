import { type Analytics, useAnalytics } from "#/ui/use-analytics";
import { ChangeNameDialog } from "#/ui/dialogs/change-name";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { useRouter } from "next/navigation";
import { type JSX, useCallback, useState } from "react";
import { toast } from "sonner";
import React from "react";

export type EntityType =
  | "experiment"
  | "dataset"
  | "prompt_session"
  | "project";

function entityTypeLabel(x: EntityType): string {
  switch (x) {
    case "prompt_session":
      return "playground";
    default:
      return x;
  }
}

type TrackAnalytics = {
  source: string;
};

type ActiveAction = {
  type: "edit" | "delete";
  data: {
    entityName?: string;
    entityId: string;
  };
  trackAnalytics?: TrackAnalytics;
};

function doTrack(
  analytics: Analytics,
  entityType: EntityType,
  activeAction: ActiveAction,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  extraProperties?: Record<any, unknown>,
) {
  if (analytics && activeAction.trackAnalytics) {
    analytics.track(activeAction.trackAnalytics.source, {
      entityType,
      actionType: activeAction.type,
      entityInfo: activeAction.data,
      ...extraProperties,
    });
  }
}

export type EntityContextActions = {
  deleteEntity: (args: {
    entityName?: string;
    entityId?: string;
    trackAnalytics?: TrackAnalytics;
  }) => void;
  editEntityName: (args: {
    entityName?: string;
    entityId?: string;
    trackAnalytics?: TrackAnalytics;
  }) => void;
  copyEntityId: (args: { entityId?: string }) => void;
  copyEntityName: (args: { entityName?: string }) => void;
  onSubmitEdit: (args: {
    v: string;
    entityName: string;
    entityId: string;
    trackAnalytics?: TrackAnalytics;
  }) => void;
};

export const useEntityContextActions = ({
  onUpdate,
  entityType,
  reloadPageOnUpdateArgs,
  additionalDeleteConfirmationDescription,
}: {
  onUpdate?: () => Promise<void>;
  entityType: EntityType;
  reloadPageOnUpdateArgs?: {
    getEditedEntityLink: (entityName: string) => string;
    getDeletedEntityLink: () => string;
  };
  additionalDeleteConfirmationDescription?: string;
}): { actions: EntityContextActions; modals: JSX.Element } => {
  const router = useRouter();
  const { analytics } = useAnalytics();

  const [activeAction, setActiveAction] = useState<ActiveAction | null>(null);
  const { entityName, entityId } = activeAction?.data || {};

  const toastError = useCallback(
    (action: "update" | "delete", error: string | React.ReactNode) => {
      toast.error(`Could not ${action} ${entityTypeLabel(entityType)}`, {
        description:
          typeof error === "string" ? (
            <>
              {error}. Please contact{" "}
              <a href="mailto:<EMAIL>" className="underline">
                <EMAIL>
              </a>{" "}
              for help.
            </>
          ) : (
            error
          ),
      });
    },
    [entityType],
  );

  const deleteEntity = async () => {
    if (!entityId) {
      toastError("delete", "Inputs unavailable");
      return;
    }

    const resp = await fetch(`/api/${entityType}/delete_id`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: entityId,
      }),
    });
    if (!resp.ok) {
      toastError("delete", await resp.text());
      return;
    }

    toast(`Deleted ${entityTypeLabel(entityType)}`);
    await onUpdate?.();

    if (reloadPageOnUpdateArgs) {
      router.replace(reloadPageOnUpdateArgs.getDeletedEntityLink());
    }
  };

  const updateEntity = async (
    newName: string,
    entityType: EntityType,
    entityId: string,
  ) => {
    if (!entityId) {
      toastError("update", "Inputs unavailable");
      return;
    }
    const resp = await fetch(`/api/${entityType}/patch_id`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        id: entityId,
        name: newName,
      }),
    });
    if (!resp.ok) {
      const respText = await resp.text();
      if (resp.status === 400 && respText.includes("Unique key violation")) {
        toastError(
          "update",
          <>
            <span className="capitalize">{entityTypeLabel(entityType)}</span>{" "}
            with the name <b>{newName}</b> already exists.
          </>,
        );
      } else {
        toastError("update", respText);
      }
      return;
    }

    if (reloadPageOnUpdateArgs) {
      router.replace(reloadPageOnUpdateArgs.getEditedEntityLink(newName));
    }

    toast(`Updated ${entityTypeLabel(entityType)}`);
    await onUpdate?.();
  };

  const onSubmitEdit = ({
    v,
    entityName,
    entityId,
    trackAnalytics,
  }: {
    v: string;
    entityName: string;
    entityId: string;
    trackAnalytics?: TrackAnalytics;
  }) => {
    doTrack(
      analytics,
      entityType,
      {
        type: "edit",
        data: {
          entityName: entityName,
          entityId: entityId,
        },
        trackAnalytics,
      },
      {
        newEntityName: v,
      },
    );
    updateEntity(v, entityType, entityId);
  };

  const modals = (
    <>
      {activeAction?.type == "edit" && (
        <ChangeNameDialog
          key={entityId}
          fieldName="Name"
          title={`Rename ${entityTypeLabel(entityType)}`}
          defaultValue={entityName}
          open={activeAction?.type == "edit"}
          onOpenChange={(open) => {
            if (!open) setActiveAction(null);
          }}
          onSubmit={(newValue: string) => {
            if (!activeAction.data.entityName) {
              toastError("update", "Internal error");
              return;
            }
            onSubmitEdit({
              v: newValue,
              entityName: activeAction.data.entityName,
              entityId: activeAction.data.entityId,
            });
          }}
        />
      )}
      {activeAction?.type == "delete" && (
        <ConfirmationDialog
          key={entityId}
          onConfirm={() => {
            doTrack(analytics, entityType, activeAction);
            deleteEntity();
          }}
          open={activeAction?.type == "delete"}
          onOpenChange={(open) => {
            if (!open) setActiveAction(null);
          }}
          title={`Delete ${entityTypeLabel(entityType)}`}
          description={`Are you sure you want to delete ${entityTypeLabel(
            entityType,
          )} "${entityName}"?${
            additionalDeleteConfirmationDescription
              ? ` ${additionalDeleteConfirmationDescription}`
              : ""
          }`}
          confirmText={"Delete"}
        />
      )}
    </>
  );

  return {
    actions: {
      editEntityName: ({
        entityName,
        entityId,
        trackAnalytics,
      }: {
        entityName?: string;
        entityId?: string;
        trackAnalytics?: TrackAnalytics;
      }) => {
        if (!entityName || !entityId) {
          toastError("update", "Internal error");
          return;
        }
        setActiveAction({
          type: "edit",
          data: {
            entityName: entityName,
            entityId: entityId,
          },
          trackAnalytics,
        });
      },
      deleteEntity: ({
        entityName,
        entityId,
        trackAnalytics,
      }: {
        entityName?: string;
        entityId?: string;
        trackAnalytics?: TrackAnalytics;
      }) => {
        if (!entityName || !entityId) {
          toastError("delete", "Internal error");
          return;
        }
        setActiveAction({
          type: "delete",
          data: {
            entityName: entityName,
            entityId: entityId,
          },
          trackAnalytics,
        });
      },
      copyEntityId: ({ entityId }: { entityId?: string }) => {
        if (!entityId) {
          return;
        }
        navigator.clipboard.writeText(entityId);
        toast(
          `${
            entityType.substring(0, 1).toUpperCase() + entityType.substring(1)
          } ID copied to clipboard`,
        );
      },
      copyEntityName: ({ entityName }: { entityName?: string }) => {
        if (!entityName) {
          return;
        }
        navigator.clipboard.writeText(entityName);
        toast(
          `${
            entityType.substring(0, 1).toUpperCase() + entityType.substring(1)
          } name copied to clipboard`,
        );
      },
      onSubmitEdit,
    },
    modals: modals,
  };
};
