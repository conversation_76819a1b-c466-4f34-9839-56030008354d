import { type ObjectRedirectLinkParams } from "#/utils/object-link";
import { getOrgLink } from "../getOrgLink";

export function getObjectLink({
  orgName,
  object_type,
  object_id,
  id,
}: ObjectRedirectLinkParams): string {
  const queryParams = new URLSearchParams({
    object_type,
    object_id,
    ...(id ? { id } : {}),
  }).toString();
  return `${getOrgLink({ orgName })}/object?${queryParams}`;
}
