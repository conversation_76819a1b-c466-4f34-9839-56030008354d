import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import { useMemo } from "react";
import { type ChartTimeFrame, TIME_RANGE_OPTIONS } from "./time-range";
import { type TimeSpan } from "./time-range";
import { Calendar } from "#/ui/calendar";
import { endOfDay, startOfDay, format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";

const formatCustomDate = (date: Date): string => {
  if (!date) {
    return "";
  }
  const isStart = startOfDay(date).getTime() === date.getTime();
  const isEnd = endOfDay(date).getTime() === date.getTime(); // rm once end is made exclusive
  if (isStart || isEnd) {
    return format(date, "MMM dd");
  }
  return format(date, "MMM dd p");
};

const formatCustomDateRange = (chartTimeFrame: ChartTimeFrame): string => {
  const { start, end } = chartTimeFrame;
  const from = formatCustomDate(new Date(start));
  const to = formatCustomDate(new Date(end));
  return [from, to].join(" - ");
};

function TimeRangeSelect({
  timeSpan,
  onChange,
  chartTimeFrame,
  isLive,
}: {
  chartTimeFrame: ChartTimeFrame;
  isLive: boolean;
  timeSpan: TimeSpan;
  onChange: (range: TimeSpan) => void;
}) {
  const isNotCustom = isLive && typeof timeSpan === "string";
  const dateRange = useMemo(() => {
    return typeof timeSpan === "object"
      ? {
          from: new Date(timeSpan.start),
          to: timeSpan.end ? new Date(timeSpan.end) : undefined,
        }
      : undefined;
  }, [timeSpan]);

  const dateRangeLabel = isNotCustom
    ? "Custom"
    : formatCustomDateRange(chartTimeFrame);

  return (
    <ToggleGroup
      className="h-7 rounded-md border p-0.5 bg-primary-50 border-primary-200"
      type="single"
      value={isNotCustom ? timeSpan : "custom"}
    >
      {TIME_RANGE_OPTIONS.map((r) => (
        <ToggleGroupItem
          key={r.value}
          value={r.value}
          size="sm"
          className="h-full rounded-[3px] text-xs font-normal data-[state=off]:text-primary-600"
          onClick={() => {
            onChange(r.value);
          }}
        >
          {r.value}
        </ToggleGroupItem>
      ))}
      <ToggleGroupItem
        key="custom"
        value="custom"
        size="sm"
        className="h-full rounded-[3px] text-xs font-normal data-[state=off]:text-primary-600"
      >
        <Popover>
          <PopoverTrigger asChild>
            <div>{dateRangeLabel || "Custom"}</div>
          </PopoverTrigger>
          <PopoverContent
            align="end"
            onOpenAutoFocus={(e) => e.preventDefault()}
            className="w-auto p-0"
          >
            <Calendar
              mode="range"
              numberOfMonths={2}
              selected={dateRange}
              onSelect={(dateRange) => {
                const range = {
                  from: dateRange?.from && startOfDay(dateRange?.from),
                  to: dateRange?.to
                    ? endOfDay(dateRange?.to)
                    : dateRange?.from
                      ? // if there is only a "from" date selected, use the end of the day time for the "to" date
                        endOfDay(dateRange?.from)
                      : undefined,
                };
                range.from &&
                  range.to &&
                  onChange({
                    start: range.from.getTime(),
                    end: range.to.getTime(),
                  });
              }}
            />
          </PopoverContent>
        </Popover>
      </ToggleGroupItem>
    </ToggleGroup>
  );
}

export { TimeRangeSelect };
