import { type TIME_BUCKETS } from "#/ui/charts/scale-time";

export const DEFAULT_TIME_RANGE = {
  value: "1h",
  label: "1 hour",
  isLive: true,
};

export const TIME_RANGE_OPTIONS = [
  DEFAULT_TIME_RANGE,
  { value: "6h", label: "6 hours", isLive: false },
  { value: "12h", label: "12 hours", isLive: false },
  { value: "1d", label: "1 day", isLive: false },
  { value: "3d", label: "3 days", isLive: false },
  { value: "7d", label: "7 days", isLive: false },
  { value: "14d", label: "14 days", isLive: false },
  { value: "30d", label: "30 days", isLive: false },
] as const;

export type TimeRange = (typeof TIME_RANGE_OPTIONS)[number]["value"];

export const TIME_RANGE_TO_TIME_BUCKET: {
  [key in TimeRange]: TIME_BUCKETS;
} = {
  "1h": "minute",
  "6h": "hour",
  "12h": "hour",
  "1d": "hour",
  "3d": "hour",
  "7d": "hour",
  "14d": "day",
  "30d": "day",
};

export const TIME_RANGE_TO_INTERVAL: {
  [key in TimeRange]: string;
} = {
  "1h": "1 hour",
  "6h": "6 hour",
  "12h": "12 hour",
  "1d": "1 day",
  "3d": "3 day",
  "7d": "7 day",
  "14d": "14 day",
  "30d": "30 day",
};

export const TIME_RANGE_TO_MILLISECONDS: {
  [key in TimeRange]: number;
} = {
  "1h": 60 * 60 * 1000,
  "6h": 6 * 60 * 60 * 1000,
  "12h": 12 * 60 * 60 * 1000,
  "1d": 24 * 60 * 60 * 1000,
  "3d": 3 * 24 * 60 * 60 * 1000,
  "7d": 7 * 24 * 60 * 60 * 1000,
  "14d": 14 * 24 * 60 * 60 * 1000,
  "30d": 30 * 24 * 60 * 60 * 1000,
};

export type TimeFrame = {
  start: number;
  end?: number;
};

export type TimeSpan = TimeRange | TimeFrame;

// always populate end once passed into chart
export type ChartTimeFrame = {
  start: number;
  end: number;
};

export const getNextLETimeRange = (durationMs: number): TimeRange => {
  const nextLE =
    TIME_RANGE_OPTIONS.find((opt) => {
      return TIME_RANGE_TO_MILLISECONDS[opt.value] >= durationMs;
    }) ?? TIME_RANGE_OPTIONS.at(-1);

  return nextLE?.value ?? DEFAULT_TIME_RANGE.value;
};

export function timeFrameToChartTimeFrame(
  timeFrame: TimeFrame,
): ChartTimeFrame {
  return {
    start: timeFrame.start,
    end: timeFrame.end ?? new Date().getTime(),
  };
}

export function timeSpanToTimeFrame(timeSpan: TimeSpan): TimeFrame {
  if (typeof timeSpan === "string") {
    const timeRange = TIME_RANGE_TO_MILLISECONDS[timeSpan];
    if (!timeRange) {
      throw new Error(`Invalid time range: ${timeSpan}`);
    }
    return {
      start: new Date().getTime() - timeRange,
      end: new Date().getTime(),
    };
  } else {
    return timeSpan;
  }
}

export function timeFrameToTimeSpan(timeFrame: TimeFrame): TimeSpan {
  return {
    start: timeFrame.start,
    end: timeFrame.end,
  };
}

export function timeFrameToString(timeFrame: TimeFrame): string {
  const start = new Date(timeFrame.start).getTime();
  const end = timeFrame.end ? new Date(timeFrame.end).getTime() : "";
  return `${start}${end ? `_${end}` : ""}`;
}

export function timeFrameFromStrings(
  startString: string,
  endString?: string,
): TimeFrame {
  const now = Date.now();

  const startValue = parseInt(startString);
  const start = !Number.isNaN(startValue)
    ? new Date(startValue).getTime()
    : now - TIME_RANGE_TO_MILLISECONDS["3d"];

  let end: number | undefined = undefined;
  if (endString && endString.length > 0) {
    const endValue = parseInt(endString);
    end = !Number.isNaN(endValue) ? new Date(endValue).getTime() : undefined;
  }

  return { start, end };
}

export function selectGranularityBucket(timeFrame: TimeFrame): TIME_BUCKETS {
  const duration = (timeFrame.end ?? Date.now()) - timeFrame.start;
  if (duration <= 2 * 60 * 60 * 1000) {
    return "minute";
  } else if (duration <= 7 * 24 * 60 * 60 * 1000) {
    return "hour";
  } else {
    return "day";
  }
}
