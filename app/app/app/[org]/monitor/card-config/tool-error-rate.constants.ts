import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const TOOL_ERROR_RATE_CARD_CONFIG: MonitorCardConfig = {
  title: "Tool error rate",
  name: "tool errors query",
  idName: "tools",
  iconName: "alert-circle",
  vizType: "lines",
  pivot: "span_attributes.name",
  measures: [
    {
      alias: "last_updated",
      displayName: "last_updated",
      btql: "max(created)",
    },
    {
      alias: "error_rate",
      displayName: "Error rate",
      btql: "count(error)/count(1)",
    },
    {
      alias: "count",
      displayName: "Total",
      btql: "count(1)",
    },
  ],
  unitType: "percent",
  toolMetric: "error_rate",
};
