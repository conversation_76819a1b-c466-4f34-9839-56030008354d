import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const TOOL_DURATION_CARD_CONFIG: MonitorCardConfig = {
  title: "Tool duration (p50)",
  name: "tool duration query",
  idName: "tools",
  iconName: "timer",
  vizType: "lines",
  pivot: "span_attributes.name",
  measures: [
    {
      alias: "last_updated",
      displayName: "last_updated",
      btql: "max(created)",
    },
    {
      alias: "p50_duration",
      displayName: "P50",
      btql: "percentile(metrics.end-metrics.start, 0.5)",
    },
    {
      alias: "count",
      displayName: "Total",
      btql: "count(1)",
    },
  ],
  unitType: "duration",
  toolMetric: "p50_duration",
};
