import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const TOKEN_COUNT_CARD_CONFIG: MonitorCardConfig = {
  title: "Token count",
  name: "Token count query",
  idName: "token",
  vizType: "bars",
  iconName: "blocks",
  measures: [
    {
      alias: "sum_prompt_uncached_tokens",
      displayName: "Prompt (uncached)",
      btql: "sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)",
    },
    {
      alias: "sum_prompt_cached_tokens",
      displayName: "Prompt (cache read)",
      btql: "sum(metrics.prompt_cached_tokens)",
    },
    {
      alias: "sum_prompt_cache_creation_tokens",
      displayName: "Prompt (cache write)",
      btql: "sum(metrics.prompt_cache_creation_tokens)",
    },
    {
      alias: "sum_completion_tokens",
      displayName: "Completion",
      btql: "sum(metrics.completion_tokens)",
    },
  ],
  unitType: "count",
};
