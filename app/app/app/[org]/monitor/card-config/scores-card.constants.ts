import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const SCORES_CARD_CONFIG: MonitorCardConfig = {
  title: "Scores",
  name: "Scores query",
  idName: "scores",
  iconName: "percent",
  vizType: "lines",
  pivot: "score",
  unpivot: ["score", "value"],
  measures: [
    {
      alias: "last_updated",
      displayName: "last_updated",
      btql: "max(created)",
    },
    {
      alias: "avg",
      displayName: "avg",
      btql: "avg(value)",
    },
  ],
  unitType: "percent",
};
