import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const COST_CARD_CONFIG: MonitorCardConfig = {
  title: "Total LLM cost",
  name: "Cost query",
  idName: "cost",
  vizType: "bars",
  iconName: "dollar",
  applyCrunchCosts: true,
  measures: [
    {
      alias: "prompt_uncached_tokens",
      displayName: "Prompt (uncached)",
      btql: "sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)",
    },
    {
      alias: "prompt_cached_tokens",
      displayName: "Prompt (cache read)",
      btql: "sum(metrics.prompt_cached_tokens)",
    },
    {
      alias: "prompt_cache_creation_tokens",
      displayName: "Prompt (cache write)",
      btql: "sum(metrics.prompt_cache_creation_tokens)",
    },
    {
      alias: "completion_tokens",
      displayName: "Completion",
      btql: "sum(metrics.completion_tokens)",
    },
  ],
  unitType: "cost",
  additionalGroupBy: { alias: "model", btql: "metadata.model" },
};
