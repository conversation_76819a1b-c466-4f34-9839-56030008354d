import { COST_CARD_CONFIG } from "./cost-card.constants";
import { LATENCY_CARD_CONFIG } from "./latency-card.constants";
import { REQUEST_COUNT_CARD_CONFIG } from "./request-count-card.constants";
import { SCORES_CARD_CONFIG } from "./scores-card.constants";
import { TOKEN_COUNT_CARD_CONFIG } from "./token-count-card.constants";
import { TOOL_DURATION_CARD_CONFIG } from "./tool-duration-card.constants";
import { TOOL_ERROR_RATE_CARD_CONFIG } from "./tool-error-rate.constants";
import { TOOL_REQUESTS_CARD_CONFIG } from "./tool-requests-card.constants";
import { TTFT_CARD_CONFIG } from "./ttft-card.constants";

export const MONITOR_CARDS = [
  REQUEST_COUNT_CARD_CONFIG,
  LATENCY_CARD_CONFIG,
  COST_CARD_CONFIG,
  TOKEN_COUNT_CARD_CONFIG,
  TTFT_CARD_CONFIG,
  SCORES_CARD_CONFIG,
  TOOL_REQUESTS_CARD_CONFIG,
  TO<PERSON>_ERROR_RATE_CARD_CONFIG,
  TOOL_DURATION_CARD_CONFIG,
];
