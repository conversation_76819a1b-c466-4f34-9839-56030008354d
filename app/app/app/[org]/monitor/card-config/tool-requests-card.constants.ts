import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const TOOL_REQUESTS_CARD_CONFIG: MonitorCardConfig = {
  title: "Tool executions",
  name: "tool executions query",
  idName: "tools",
  iconName: "bolt",
  vizType: "bars",
  pivot: "span_attributes.name",
  measures: [
    {
      alias: "last_updated",
      displayName: "last_updated",
      btql: "max(created)",
    },
    {
      alias: "count",
      displayName: "Total",
      btql: "count(1)",
    },
  ],
  unitType: "count",
  toolMetric: "count",
};
