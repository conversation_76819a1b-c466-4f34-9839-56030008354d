import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const REQUEST_COUNT_CARD_CONFIG: MonitorCardConfig = {
  title: "Request count",
  name: "Request query",
  idName: "request",
  vizType: "bars",
  iconName: "message-circle",
  measures: [
    {
      alias: "llm_count",
      displayName: "LLM calls",
      btql: "sum(span_attributes.type = 'llm' ? 1 : 0)",
    },
    {
      alias: "tool_count",
      displayName: "Tool calls",
      btql: "sum(span_attributes.type = 'tool' ? 1 : 0)",
    },
    {
      alias: "traces",
      displayName: "Traces",
      btql: "sum(is_root ? 1 : 0)",
    },
    {
      alias: "spans",
      displayName: "Spans",
      btql: "count(1)",
    },
  ],
  unitType: "count",
};
