import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const TTFT_CARD_CONFIG: MonitorCardConfig = {
  title: "Time to first token",
  name: "Ttft query",
  idName: "ttft",
  iconName: "clock",
  vizType: "lines",
  measures: [
    {
      alias: "p50_time_to_first_token",
      displayName: "P50",
      btql: "percentile(metrics.time_to_first_token, 0.5)",
    },
    {
      alias: "p95_time_to_first_token",
      displayName: "P95",
      btql: "percentile(metrics.time_to_first_token, 0.95)",
    },
  ],
  unitType: "duration",
};
