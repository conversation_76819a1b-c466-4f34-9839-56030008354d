import {
  AlertCircle,
  <PERSON>s,
  Bolt,
  Clock,
  DollarSign,
  MessageCircle,
  Percent,
  Snail,
  Timer,
} from "lucide-react";
import type { CardIconName } from "./monitor-card-config.types";

export const MonitorCardIcon = (name: CardIconName) => {
  switch (name) {
    case "alert-circle":
      return AlertCircle;
    case "message-circle":
      return MessageCircle;
    case "blocks":
      return Blocks;
    case "bolt":
      return Bolt;
    case "clock":
      return Clock;
    case "dollar":
      return DollarSign;
    case "percent":
      return Percent;
    case "snail":
      return Snail;
    case "timer":
      return Timer;
    default:
      return Bolt;
  }
};
