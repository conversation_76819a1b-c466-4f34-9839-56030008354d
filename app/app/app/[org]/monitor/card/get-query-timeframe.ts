import type { ChartTimeFrame } from "../time-controls/time-range";

const stepForBucket = (timeBucket: "minute" | "hour" | "day") => {
  if (timeBucket === "minute") {
    return 20_000; // 20s
  }
  if (timeBucket === "hour") {
    return 600_000; // 10m
  }

  // we want to avoid day step level discretization
  // because naive division of unix epoch will misalign cal alignment
  // also we want to avoid very stale data
  return 3600_000; // 1 hr
};

// Discretize timeframe for query to more likely hit browser cache
export const getQueryTimeframe = (
  timeframe: ChartTimeFrame,
  timeBucket: "minute" | "hour" | "day",
) => {
  const { start, end } = timeframe;

  // because of cal alignment, we want to avoid day level discretization
  // use hour as the max step
  const step = stepForBucket(timeBucket);

  // expand the timeframe on both sides. So we always include original timespan
  const floorStart = Math.floor(start / step) * step;
  const ceilEnd = Math.ceil(end / step) * step;

  return { start: floorStart, end: ceilEnd };
};
