import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

interface MeasureConfig {
  btql: string;
  durationPercentile?: number;
  alias: string;
  displayName?: string;
}

export type CardIconName =
  | "dollar"
  | "snail"
  | "message-circle"
  | "blocks"
  | "percent"
  | "clock"
  | "bolt"
  | "alert-circle"
  | "timer";

export type UnitType = "duration" | "percent" | "count" | "cost";

export interface MonitorCardConfig {
  /** Title in the card header */
  title: string;

  /** Name used for react key  */
  name: string;

  /** Name used for pivot key */
  idName: string;

  /** Icon on top left of card header */
  iconName: CardIconName;

  /** Timeseries viz type */
  vizType: TimeseriesVizType;

  /** Measures to query for */
  measures: MeasureConfig[];

  /** Unit to format numbers appropriately */
  unitType?: UnitType;

  /** Optionally append group by to query */
  additionalGroupBy?: { alias: string; btql: string };

  /** Calculations specific for cost data */
  applyCrunchCosts?: boolean;

  /** pivot query */
  pivot?: string;

  /** unpivot query */
  unpivot?: [string, string];

  /** tool metric specific category */
  toolMetric?: "count" | "error_rate" | "p50_duration";
}
