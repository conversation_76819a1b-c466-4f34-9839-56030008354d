import type { ROW_TYPE_OPTION_VALUES } from "./row-type-select";
import {
  type ViewData,
  type ViewOptions,
  type MonitorViewOptions,
} from "@braintrust/core/typespecs";
import { type View } from "#/utils/view/use-view-generic";

function isMonitorViewOptions(
  options: ViewOptions,
): options is { viewType: "monitor"; options: MonitorViewOptions } {
  return "viewType" in options && options.viewType === "monitor";
}

export function getMetricsLabel(metric: string) {
  switch (metric) {
    case "p50_duration":
    case "p50_time_to_first_token":
      return "P50";
    case "p95_duration":
    case "p95_time_to_first_token":
      return "P95";
    case "sum_tokens":
    case "totalCost":
      return "Total";
    case "sum_prompt_uncached_tokens":
    case "promptUncachedTokensCost":
      return "Prompt (uncached)";
    case "sum_completion_tokens":
    case "completionTokensCost":
      return "Completion";
    case "sum_prompt_cached_tokens":
    case "promptCachedTokensCost":
      return "Prompt (cache read)";
    case "sum_prompt_cache_creation_tokens":
    case "promptCacheCreationTokensCost":
      return "Prompt (cache write)";
    case "count":
      return "Traces";
    case "spans":
      return "Spans";
    case "llm_count":
      return "LLM calls";
    case "tool_count":
      return "Tool calls";
    default:
      return metric;
  }
}

// Convert between long types and view types for clientpage
export function rowTypeToViewType(
  rowType: (typeof ROW_TYPE_OPTION_VALUES)[number],
): "project" | "experiment" {
  if (rowType === "experiment") {
    return "experiment";
  } else if (rowType === "logs" || rowType === "org_logs") {
    return "project";
  } else {
    throw new Error(`Unknown row type: ${rowType}`);
  }
}

export function viewTypeToRowType(
  viewType: "project" | "experiment" | undefined | null,
): (typeof ROW_TYPE_OPTION_VALUES)[number] {
  if (viewType === "experiment") {
    return "experiment";
  }
  return "logs"; // Default to logs for project views
}

export function createViewAutosaveHandler({
  selectedView,
  updateView,
  currentViewData,
  currentViewOptions,
}: {
  selectedView: View | null;
  updateView: (params: {
    viewId: string;
    viewData?: ViewData;
    options?: ViewOptions;
  }) => void;
  currentViewData: ViewData;
  currentViewOptions: ViewOptions;
}) {
  return (overrides?: {
    options?: Partial<MonitorViewOptions>;
    viewData?: Partial<ViewData>;
  }) => {
    // Don't save if no view is selected or if default view is selected
    if (!selectedView || selectedView.builtin || !selectedView.id) {
      return;
    }

    const optionsToSave = overrides?.options
      ? (() => {
          if (!isMonitorViewOptions(currentViewOptions)) {
            throw new Error("Expected monitor view options");
          }
          return {
            ...currentViewOptions,
            options: {
              ...currentViewOptions.options,
              ...overrides.options,
            },
          };
        })()
      : currentViewOptions;

    const viewDataToSave = overrides?.viewData
      ? {
          ...currentViewData,
          ...overrides.viewData,
        }
      : currentViewData;

    updateView({
      viewId: selectedView.id,
      viewData: viewDataToSave,
      options: optionsToSave,
    });
  };
}
