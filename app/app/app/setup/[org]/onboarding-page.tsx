"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { getExperimentLink } from "#/app/app/[org]/p/[project]/experiments/[experiment]/getExperimentLink";
import { useAnalytics } from "#/ui/use-analytics";

import { useUser } from "#/utils/user";
import { type getOnboardingExperimentInfo } from "#/app/app/[org]/org-actions";
import { RealtimeChannel } from "#/utils/realtime-data";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { z } from "zod";
import { toast } from "sonner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useSessionToken } from "#/utils/auth/session-token";
import { useAuth } from "@clerk/nextjs";

import { cn } from "#/utils/classnames";

import { GetStarted } from "./getStarted";
import { RunSDK } from "./runSDK";
import { AddProvider } from "./addProvider";
import { AnimatePresence, motion, useReducedMotion } from "motion/react";
import { MiniTutorial } from "./miniTutorial";
import { Button } from "#/ui/button";
import { type OrgContextT } from "#/utils/user-types";
import { InviteTeam } from "./invite-team";

const orgProjectMetadataPayloadSchema = z.record(z.record(z.any()));

export function ProjectOnboardingPage({
  org,
  hasApiSecrets,
  refreshAvailableModels,
  refreshProjects,
  initialStep,
  hideSDK,
}: {
  org: OrgContextT;
  hasApiSecrets: boolean;
  refreshAvailableModels: () => void;
  refreshProjects: () => void;
  initialStep: number;
  hideSDK: boolean;
}) {
  const { getToken } = useAuth();
  const { analytics } = useAnalytics();
  const { user } = useUser();
  const shouldReduceMotion = useReducedMotion();

  const { getOrRefreshToken } = useSessionToken();
  const [activeStep, setActiveStep] = useState(initialStep);
  const [flowType, setFlowType] = useState<"ui" | "sdk">("ui");
  useEffect(() => {
    analytics?.track("onboarding_page_viewed", {
      orgName: org.name,
    });
  }, [analytics, org.name]);

  const router = useRouter();
  const finishedOnboarding = useRef<boolean>(false);

  const processNewExperiment = useCallback(
    async (experimentId: string) => {
      if (finishedOnboarding.current) return;
      try {
        const experimentInfo = await invokeServerAction<
          typeof getOnboardingExperimentInfo
        >({
          fName: "getOnboardingExperimentInfo",
          args: { experimentId },
          getToken,
        });
        if (!experimentInfo) {
          console.warn("No experiments");
          return; // Could not find any experiments
        }
        // Don't redirect if other experiments were created by the time we got
        // around to processing this one.
        if (experimentInfo.exist_other_experiments_in_org) {
          console.warn(
            "Skipping onboarding redirect because there are other experiments in the org",
          );
          finishedOnboarding.current = true;
        }
        if (finishedOnboarding.current) return;
        router.push(
          getExperimentLink({
            orgName: org.name,
            projectName: experimentInfo.project_name,
            experimentName: experimentInfo.experiment_name,
          }),
        );
        finishedOnboarding.current = true;
      } catch (e) {
        toast.error("Failed to query tutorial experiment");
      }
    },
    [org.name, router, getToken],
  );
  const processOrgProjectMetadataEvent = useCallback(
    async (eventRaw: unknown) => {
      if (finishedOnboarding.current) return;
      const event = orgProjectMetadataPayloadSchema.parse(eventRaw);
      const experimentData = event["experiment"];
      if (!experimentData) return;
      for (const experimentId of Object.keys(experimentData)) {
        await processNewExperiment(experimentId);
      }
    },
    [processNewExperiment],
  );

  // Subscribe to updates to this org. Trigger processOrgProjectMetadataEvent on
  // each event.
  const channelStatus = useRef<"empty" | "creating" | "created">("empty");
  const fetchChannelUrl = useMemo(
    () =>
      org.api_url && org.id
        ? `${org.api_url}/broadcast-key?` +
          new URLSearchParams({
            object_type: "org_project_metadata",
            id: org.id,
            audit_log: "0",
          })
        : undefined,
    [org.api_url, org.id],
  );
  useEffect(() => {
    (async () => {
      if (
        !(
          fetchChannelUrl &&
          org.api_url &&
          user &&
          channelStatus.current === "empty"
        )
      ) {
        return;
      }
      channelStatus.current = "creating";
      try {
        const channelName = await (
          await apiFetchGet(fetchChannelUrl, await getOrRefreshToken())
        ).json();
        if (!(channelName.channel && channelName.token)) {
          throw new Error(
            `Invalid channel info: ${JSON.stringify(channelName)}`,
          );
        }
        new RealtimeChannel(
          channelName.channel,
          channelName.token,
          {
            user_id: user.id,
            email: user.email,
            avatar_url: user.avatar_url,
          },
          org,
          () => true,
          (payload) => {
            processOrgProjectMetadataEvent(payload);
          },
        );
        channelStatus.current = "created";
      } catch (e) {
        console.error("Failed to create channel\n", e);
        channelStatus.current = "empty";
      }
    })();
  }, [
    fetchChannelUrl,
    user,
    org,
    processOrgProjectMetadataEvent,
    getOrRefreshToken,
  ]);

  const renderStep = () => {
    switch (flowType) {
      case "sdk":
        switch (activeStep) {
          case 0:
            return (
              <GetStarted
                orgName={org.name}
                setActiveStep={setActiveStep}
                setFlowType={setFlowType}
                hasApiSecrets={hasApiSecrets}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
          case 1:
            return (
              <RunSDK
                orgId={org.id}
                orgName={org.name}
                analytics={analytics}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
        }
      case "ui":
        switch (activeStep) {
          case 0:
            return (
              <GetStarted
                orgName={org.name}
                setActiveStep={setActiveStep}
                setFlowType={setFlowType}
                hasApiSecrets={hasApiSecrets}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
          case 1:
            return (
              <AddProvider
                orgName={org.name}
                onProviderAdded={() => {
                  refreshAvailableModels();
                  setActiveStep(2);
                }}
                onSkip={async () => {
                  refreshAvailableModels();
                  setActiveStep(2);
                }}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
          case 2:
            return (
              <InviteTeam
                orgName={org.name}
                orgId={org.id ?? ""}
                onSkip={async () => {
                  setActiveStep(3);
                }}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
          case 3:
            return (
              <MiniTutorial
                orgId={org.id}
                orgName={org.name}
                apiUrl={org.api_url}
                onFinish={refreshProjects}
                shouldReduceMotion={shouldReduceMotion ?? false}
              />
            );
          default:
            return null;
        }
      default:
        return null;
    }
  };

  const animationX = shouldReduceMotion ? 0 : -10;
  return (
    <div className="flex h-screen w-screen overflow-y-auto bg-primary-50">
      <div className="relative mx-auto flex size-full max-w-screen-sm px-4">
        <motion.div className="relative flex size-full pt-40">
          <AnimatePresence>
            {activeStep > 0 && (
              <div className="absolute left-0 top-30 z-[999] flex w-full items-center justify-between">
                <motion.div
                  initial={{ opacity: 0, x: animationX }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: animationX }}
                  className="flex gap-2"
                >
                  {flowType === "ui" && (
                    <>
                      {initialStep === 0 && (
                        <button
                          className={cn(
                            "transition-all h-2 w-3 rounded-full bg-primary-200",
                            !(initialStep > 0) && "hover:bg-primary-300",
                          )}
                          onClick={() => setActiveStep(0)}
                          aria-label="Go to step 1"
                        />
                      )}
                      {initialStep <= 1 && (
                        <button
                          className={cn(
                            "transition-all h-2 w-3 rounded-full bg-primary-200 ",
                            !(initialStep > 1) && "hover:bg-primary-300",
                            activeStep === 1 &&
                              "w-6 bg-gradient-to-b from-accent-500 to-accent-600",
                          )}
                          onClick={() => setActiveStep(1)}
                          aria-label={
                            activeStep === 1
                              ? "Step 2 is active"
                              : "Go to step 2"
                          }
                        />
                      )}
                      <button
                        className={cn(
                          "transition-all h-2 w-3 rounded-full bg-primary-200 ",
                          !hasApiSecrets && "hover:bg-primary-300",
                          activeStep === 2 &&
                            "w-6 bg-gradient-to-b from-accent-500 to-accent-600",
                        )}
                        onClick={() => setActiveStep(2)}
                        aria-label={
                          activeStep === 2 ? "Step 3 is active" : "Go to step 3"
                        }
                      />
                      <button
                        className={cn(
                          "transition-all h-2 w-3 rounded-full bg-primary-200 hover:bg-primary-300",
                          activeStep === 3 &&
                            "w-6 bg-gradient-to-b from-accent-500 to-accent-600",
                        )}
                        onClick={() => setActiveStep(3)}
                        aria-label={
                          activeStep === 3 ? "Step 4 is active" : "Go to step 4"
                        }
                      />
                    </>
                  )}
                  {flowType === "sdk" && (
                    <>
                      <button
                        className={cn(
                          "transition-all h-2 w-3 rounded-full bg-primary-200 hover:bg-primary-300",
                          activeStep === 0 &&
                            "w-6 bg-gradient-to-b from-accent-400 to-accent-600",
                        )}
                        onClick={() => setActiveStep(0)}
                        aria-label="Go to step 1"
                      />
                      <button
                        className={cn(
                          "transition-all h-2 w-3 rounded-full bg-primary-200 hover:bg-primary-300",
                          activeStep === 1 &&
                            "w-6 bg-gradient-to-b from-accent-400 to-accent-600",
                        )}
                        aria-label={activeStep === 1 ? "Step 2 is active" : ""}
                      />
                    </>
                  )}
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: animationX }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: animationX }}
                >
                  {flowType === "ui" && !hideSDK && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="pr-0 transition-all text-primary-500 hover:pr-3"
                      onClick={() => {
                        setFlowType("sdk");
                        setActiveStep(1);
                      }}
                    >
                      Switch to SDK onboarding
                    </Button>
                  )}
                  {flowType === "sdk" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="pr-0 transition-all hover:pr-3"
                      onClick={() => {
                        setFlowType("ui");
                        setActiveStep(hasApiSecrets ? 2 : 1);
                      }}
                    >
                      Switch to UI onboarding
                    </Button>
                  )}
                </motion.div>
              </div>
            )}
          </AnimatePresence>
          {renderStep()}
        </motion.div>
      </div>
    </div>
  );
}
