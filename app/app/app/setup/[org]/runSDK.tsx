"use client";
import { Python<PERSON>ogo } from "../../[org]/onboarding-logos";
import { TypescriptLogo } from "../../[org]/onboarding-logos";

import { <PERSON><PERSON> } from "#/ui/button";
import { useState } from "react";
import CodeToCopy from "#/ui/code-to-copy";
import { Spinner } from "#/ui/icons/spinner";
import { createApiKey } from "#/app/app/[org]/settings/api-keys/api-key";
import { toast } from "sonner";
import { type Analytics } from "#/ui/use-analytics";
import { motion } from "motion/react";
import { Tabs, TabsList, TabsTrigger } from "#/ui/tabs";

export const RunSDK = ({
  orgId,
  orgName,
  analytics,
  shouldReduceMotion,
}: {
  orgId: string | undefined;
  orgName: string;
  analytics: Analytics;
  shouldReduceMotion: boolean;
}) => {
  const [lang, setLang] = useState<"ts" | "py">("ts");
  const [jsPackageManager, setJsPackageManager] = useState<
    "npm" | "pnpm" | "yarn"
  >("npm");
  const [creatingApiKey, setCreatingApiKey] = useState(false);
  const [apiKey, setApiKey] = useState<string | null>(null);

  const apiKeyEnv = `BRAINTRUST_API_KEY=${apiKey || "YOUR_API_KEY"}`;

  const createKey = async () => {
    if (!orgId) {
      toast.error("Cannot find org id");
      return;
    }
    setCreatingApiKey(true);
    try {
      setApiKey(await createApiKey({ name: "tutorial", orgId: orgId }));
      analytics?.track("onboarding_api_key_created", {
        orgName: orgName,
      });
    } catch (error) {
      toast.error(`Failed to create API key`, {
        description: `${error}`,
      });
    } finally {
      setCreatingApiKey(false);
    }
  };

  return (
    <div className="mx-auto flex size-full flex-col">
      <div className="mb-6 flex flex-col">
        <motion.h1
          initial={{ opacity: 0, filter: "blur(10px)" }}
          animate={{ opacity: 1, filter: "blur(0px)" }}
          exit={{ opacity: 0, filter: "blur(10px)" }}
          className="mb-2 font-planar text-3xl font-medium"
        >
          Run your first eval
        </motion.h1>
        <p className="text-base text-primary-600">
          Evaluations are a method to measure the performance of your AI
          application
        </p>
      </div>
      <Tabs defaultValue="ts" className="mb-6">
        <TabsList className="h-fit w-full p-1">
          <TabsTrigger
            value="ts"
            className="flex w-full items-center gap-2 rounded-md py-2"
            onClick={() => setLang("ts")}
          >
            <TypescriptLogo />
            TypeScript
          </TabsTrigger>
          <TabsTrigger
            value="py"
            className="flex w-full items-center gap-2 rounded-md py-2"
            onClick={() => setLang("py")}
          >
            <PythonLogo />
            Python
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="mb-2 flex items-end gap-3 text-base">
        <p className="h-[28px] flex-1">Install the Braintrust SDK</p>
        {lang === "ts" && (
          <Tabs defaultValue="pnpm">
            <TabsList className="h-8">
              <TabsTrigger
                value="pnpm"
                className="h-6 text-xs"
                onClick={() => setJsPackageManager("pnpm")}
              >
                pnpm
              </TabsTrigger>
              <TabsTrigger
                value="npm"
                className="h-6 text-xs"
                onClick={() => setJsPackageManager("npm")}
              >
                npm
              </TabsTrigger>
              <TabsTrigger
                value="yarn"
                className="h-6 text-xs"
                onClick={() => setJsPackageManager("yarn")}
              >
                yarn
              </TabsTrigger>
            </TabsList>
          </Tabs>
        )}
      </div>
      <CodeToCopy
        highlighterClassName="bg-background"
        data={
          lang === "py"
            ? "pip install braintrust autoevals"
            : `${
                jsPackageManager === "npm"
                  ? "npm install"
                  : jsPackageManager === "pnpm"
                    ? "pnpm add"
                    : "yarn add"
              } braintrust autoevals`
        }
        language="bash"
      />

      <p className="mb-2 mt-6 text-base">
        Then, create a file named{" "}
        <code className="font-mono font-medium text-accent-600">
          {lang === "py" ? "eval_tutorial.py" : "tutorial.eval.ts"}
        </code>{" "}
        with the following contents
      </p>
      <CodeToCopy
        highlighterClassName="bg-background"
        language={lang === "py" ? "python" : "javascript"}
        data={
          lang === "py"
            ? `from braintrust import Eval
from autoevals import LevenshteinScorer

Eval(
  "Say Hi Bot",
  data=lambda: [
      {
          "input": "Foo",
          "expected": "Hi Foo",
      },
      {
          "input": "Bar",
          "expected": "Hello Bar",
      },
  ],  # Replace with your eval dataset
  task=lambda input: "Hi " + input,  # Replace with your LLM call
  scores=[LevenshteinScorer],
)`
            : `import { Eval } from "braintrust";
import { LevenshteinScorer } from "autoevals";

Eval("Say Hi Bot", {
  data: () => {
    return [
      {
        input: "Foo",
        expected: "Hi Foo",
      },
      {
        input: "Bar",
        expected: "Hello Bar",
      },
    ]; // Replace with your eval dataset
  },
  task: async (input) => {
    return "Hi " + input; // Replace with your LLM call
  },
  scores: [LevenshteinScorer],
});`
        }
      />

      <p className="mb-2 mt-6 text-base">Create an API key and run your eval</p>
      <div className="pb-20">
        <APIKeyOrCommand
          apiKey={apiKey}
          creatingApiKey={creatingApiKey}
          createKey={createKey}
        >
          <CodeToCopy
            data={
              lang === "py"
                ? `${apiKeyEnv} \\
  braintrust eval eval_tutorial.py`
                : `${apiKeyEnv} \\
  npx braintrust eval tutorial.eval.ts`
            }
            language="bash"
          />
        </APIKeyOrCommand>
      </div>
    </div>
  );
};

function APIKeyOrCommand({
  apiKey,
  creatingApiKey,
  createKey,
  children,
}: {
  apiKey: string | null;
  creatingApiKey: boolean;
  createKey: () => Promise<void>;
  children: React.ReactNode;
}) {
  return !apiKey ? (
    <Button
      onClick={(e) => {
        e.preventDefault();
        createKey();
      }}
      variant="primary"
      className="gap-2 px-4 text-base bg-primary-950 text-primary-100 hover:bg-primary-800"
    >
      Create API key
      {creatingApiKey && <Spinner className="size-4" />}
    </Button>
  ) : (
    <>{children}</>
  );
}
