"use client";

import { useAvailableModels } from "#/ui/prompts/models";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "../../[org]/org-actions";
import { ProjectOnboardingPage } from "./onboarding-page";
import { Spinner } from "#/ui/icons/spinner";
import { useUser } from "#/utils/user";
import { AccessFailed } from "#/ui/access-failed";
import Header from "#/ui/layout/header";
import { useSearchParams } from "next/navigation";

export function ClientPage({ orgName }: { orgName: string }) {
  const { orgs } = useUser();
  const searchParams = useSearchParams();
  const org = orgs[orgName];
  const { apiSecrets: apiSecretsDB, refresh: refreshAvailableModels } =
    useAvailableModels({ orgName });

  const { invalidate: refreshProjects } = useQueryFunc<
    typeof getProjectSummary
  >({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  if (apiSecretsDB === undefined) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <Spinner />
      </div>
    );
  }

  if (org === undefined) {
    return (
      <div className="h-screen w-screen items-center justify-center">
        <Header noCTA />
        <AccessFailed objectType="organization" objectName={orgName} />
      </div>
    );
  }

  const hasApiSecrets = apiSecretsDB && apiSecretsDB.length > 0;
  const fromPlayground = searchParams?.get("referrer") === "playground";
  const initialStep = hasApiSecrets ? 2 : fromPlayground ? 1 : 0;

  return (
    <ProjectOnboardingPage
      org={org}
      hasApiSecrets={hasApiSecrets}
      refreshAvailableModels={refreshAvailableModels}
      refreshProjects={refreshProjects}
      initialStep={initialStep}
      hideSDK={fromPlayground}
    />
  );
}
