import { getProjectSummary } from "#/app/app/[org]/org-actions";
import { buildMetadata } from "#/app/metadata";
import { redirect } from "next/navigation";
import { decodeURIComponentPatched } from "#/utils/url";
import { ClientPage } from "./clientpage";
import SessionRoot from "#/ui/root";

export interface Params {
  org: string;
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const projectSummary = await getProjectSummary({ org_name });
  if (projectSummary.length > 0) {
    redirect(`/app/${org_name}/p/${projectSummary[0].project_name}`);
  }
  return (
    <SessionRoot loginRequired>
      <ClientPage orgName={org_name} />
    </SessionRoot>
  );
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Onboarding",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/setup/${params.org}`,
  });
}
