"use client";

import { useAnalytics } from "#/ui/use-analytics";
import { Button } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { useSubscribeFn } from "#/utils/mailing-list";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { useUser } from "#/utils/user";
import { useRef, useState } from "react";
import { Input } from "#/ui/input";
import { Spinner } from "#/ui/icons/spinner";
import { BadgeCheck, XCircle } from "lucide-react";
import { checkOrgNameConflict } from "#/app/app/setup/[org]/actions";
import { toast } from "sonner";
import { Label } from "#/ui/label";
import { AnimatePresence, motion } from "motion/react";
import { useRouter, useSearchParams } from "next/navigation";
import { BasicTooltip } from "#/ui/tooltip";

export default function ClientPage() {
  const { user, invalidate: refreshUser } = useUser();
  const { subscribeEmail } = useSubscribeFn();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { analytics } = useAnalytics({ page: { category: "org_setup" } });

  const orgNameRef = useRef<HTMLInputElement>(null);

  const [checkedName, setCheckedName] = useState<string | null>("gooble");
  const [conflictState, setConflictState] = useState<
    "empty" | "loading" | "unavailable" | "available"
  >("empty");
  const [submitting, setSubmitting] = useState(false);

  const checkConflict = async () => {
    const orgName = orgNameRef.current?.value?.trim();
    if (!orgName) {
      setConflictState("empty");
      return;
    }

    setCheckedName(orgName);
    setConflictState("loading");
    try {
      const conflict = await checkOrgNameConflict({ org_name: orgName });
      analytics?.track("org_name_check", {
        name: orgName,
        conflict: !!conflict,
      });
      setConflictState(conflict ? "unavailable" : "available");
    } catch (e) {
      toast.error("Failed to check org name. Please try again.");
      setConflictState("empty");
    }
  };

  const debouncedCheck = useDebouncedCallback(checkConflict, 500);

  const registerOrg = async () => {
    const orgName = orgNameRef.current?.value?.trim();
    if (!orgName) {
      toast.error("Please enter an org name");
      return;
    }

    if (submitting) {
      toast("Working on it... Hang tight!");
      return;
    }

    const email = user?.email;
    const subscription = email ? subscribeEmail(email, false) : null;

    setSubmitting(true);

    const result = await fetch("/api/organization/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        org_name: orgName,
      }),
    });

    let error = null;
    if (!result.ok) {
      error = await result.text();
    }

    analytics?.track("register_org", {
      name: orgName,
      error: error && `${error}`,
      subscription,
    });

    if (error) {
      setSubmitting(false);
      console.error(error);
      toast.error("Failed to create org. Please try again.");
      return;
    }

    // We may want to remove this -- I am nervous about holding up onboarding
    // if subscription via 3rd party (eg resend) is slow. On the flipside,
    // we don't want the request to get prematurely cancelled.
    if (subscription) {
      await subscription;
    }
    await refreshUser();
    let path = `/app/setup/${orgName}`;
    const referrer = searchParams?.get("referrer");
    if (referrer) {
      path += `?referrer=${referrer}`;
    }
    router.replace(path);
  };

  if (!user?.id) {
    return null;
  }
  return (
    <div className="relative flex flex-1 flex-col">
      <div className="z-10 flex h-screen w-screen flex-col items-center  overflow-hidden bg-primary-50">
        <div className="z-20 flex w-full max-w-screen-sm flex-col px-4 pt-40">
          <h1 className="mb-3 font-planar text-3xl font-medium">
            Create an organization
          </h1>
          <p className="text-base text-primary-600">
            Organizations in Braintrust represent a collection of projects and
            users. Most commonly, an organization is a business or team.
          </p>
          <div className={cn("mt-6 flex flex-col rounded-md")}>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                registerOrg();
              }}
              className="flex w-full flex-col"
            >
              <div className="flex flex-col gap-2">
                <Label
                  htmlFor="org_name"
                  className="relative flex w-full gap-2"
                >
                  <Input
                    id="org_name"
                    type="text"
                    name="org_name"
                    ref={orgNameRef}
                    className="h-auto py-2 pl-4 pr-8 text-lg"
                    placeholder="Enter organization name"
                    onChange={debouncedCheck}
                    autoComplete="off"
                    autoFocus
                  />
                  <AnimatePresence mode="popLayout">
                    {conflictState !== "empty" && (
                      <BasicTooltip
                        tooltipContent={
                          conflictState === "available"
                            ? `${checkedName} is available`
                            : conflictState === "unavailable"
                              ? `${checkedName} is taken. Please choose a different organization name.`
                              : "Checking..."
                        }
                      >
                        <motion.div
                          className={cn(
                            "absolute right-0 top-0 flex size-11 items-center justify-center gap-1 text-sm",
                            conflictState === "available"
                              ? "text-good-700"
                              : conflictState === "unavailable"
                                ? "text-bad-700"
                                : "text-primary-500",
                          )}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{
                            type: "ease-in",
                            duration: 0.15,
                            bounce: 0,
                          }}
                        >
                          {conflictState === "available" ? (
                            <BadgeCheck className="size-4" />
                          ) : conflictState === "unavailable" ? (
                            <XCircle className="size-4" />
                          ) : (
                            <Spinner className="size-4" />
                          )}
                        </motion.div>
                      </BasicTooltip>
                    )}
                  </AnimatePresence>
                </Label>
              </div>

              <div className="flex items-center justify-between py-4">
                <Button
                  variant="primary"
                  size="default"
                  className="text-base"
                  disabled={conflictState !== "available" || submitting}
                  isLoading={submitting}
                >
                  Continue
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
