import { cn } from "#/utils/classnames";

import mikeKnoop from "./img/mike-knoop.png";
import micheleCatasta from "./img/michele-catasta.png";
import leeWeisberger from "./img/lee-weisberger.png";
import simonLast from "./img/simon-last.jpg";
import malteUbl from "./img/malte-ubl.jpeg";
// import sharan<PERSON><PERSON><PERSON> from "./img/sharan-ramjee.png";
import Image, { type ImageProps } from "next/image";
import {
  Airtable,
  Replit,
  Vercel,
  Zapier,
  Notion,
  // Stripe
} from "./logos";
import { Title } from "./common";

import { Fade } from "react-awesome-reveal";
import { ProgressiveBlurShape } from "./progressive-blur-shape";

export const Testimonials = () => {
  return (
    <div className="relative z-30 mt-24 px-4 sm:px-8 md:mb-24">
      <Title>Join industry leaders</Title>
      <div className="grid grid-cols-1 gap-8 text-balance pt-3 md:grid-cols-2 lg:gap-12 xl:grid-cols-3">
        <Testimonial
          name="<PERSON> Knoop"
          position="Cofounder/Head of AI"
          isBigger
          imageSrc={mikeKnoop}
          // quote="Braintrust fills the missing (and critical!) gap of evaluating non-deterministic AI systems. We've used it to successfully measure and improve our AI-first products."
          quote="Braintrust fills the missing (and critical!) gap of evaluating non-deterministic AI systems."
        >
          <Zapier isMono />
        </Testimonial>

        <Testimonial
          name="Malte Ubl"
          position="CTO"
          isBigger
          imageSrc={malteUbl}
          // quote="We deeply appreciate the collaboration. I’ve never seen a workflow transformation like the one that incorporates evals into ‘mainstream engineering’ processes before. It’s astonishing."
          quote="I’ve never seen a workflow transformation like the one that incorporates evals into ‘mainstream engineering’ processes before. It’s astonishing."
        >
          <Vercel isMono />
        </Testimonial>

        <Testimonial
          name="Michele Catasta"
          position="President"
          isBigger
          imageSrc={micheleCatasta}
          // quote="Testing in production is painfully familiar to many AI engineers developing with LLMs. Braintrust finally brings end-to-end testing to AI products, helping companies produce meaningful quality metrics."
          quote="Braintrust finally brings end-to-end testing to AI products, helping companies produce meaningful quality metrics."
        >
          <Replit isMono />
        </Testimonial>

        <Testimonial
          name="Simon Last"
          position="Cofounder"
          isBigger
          imageSrc={simonLast}
          quote="We log everything to Braintrust. They make it very easy to find and fix issues."
          // quote="We're now using Braintrust to monitor prompt quality over time, and to evaluate whether one prompt or model is better than another. It's made it easy to turn iteration and optimization into a science."
        >
          <Notion isMono />
        </Testimonial>

        <Testimonial
          name="Lee Weisberger"
          position="Eng. Manager, AI"
          isBigger
          imageSrc={leeWeisberger}
          quote="Every new AI project starts with evals in Braintrust—it’s a game changer."
          // quote="After a simple integration, Braintrust has become essential to our AI development process and helps us ensure that our products constantly improve through observability & evaluation."
        >
          <Airtable isMono />
        </Testimonial>

        {/* <Testimonial
          name="Sharan Ramjee"
          position="Machine Learning Engineer"
          isBigger
          imageSrc={sharanRamjee}
          quote="A much nicer developer experience and cleaner mental models than alternatives. I love this product."
          // quote="After a simple integration, Braintrust has become essential to our AI development process and helps us ensure that our products constantly improve through observability & evaluation."
        >
          <Stripe isMono />
        </Testimonial> */}
      </div>
    </div>
  );
};

const Testimonial = ({
  quote,
  name,
  position,
  isBigger,
  imageSrc,
  children,
}: {
  isBigger?: boolean;
  quote: string;
  name: string;
  position: string;
  imageSrc: ImageProps["src"];
  children?: React.ReactNode;
}) => (
  <Fade triggerOnce>
    <div className="relative flex h-full flex-col p-6">
      <div className="absolute inset-0 z-[-1] opacity-5 dark:opacity-20">
        <ProgressiveBlurShape
          strength={0.05}
          direction="top"
          disableBlurInSafari
          shapeClassName="border-2 border-black dark:border-white rounded-xl"
          className="pointer-events-none absolute inset-0"
        />
      </div>
      <div
        className={cn("text-xl mb-6 flex-1", {
          "text-2xl lg:text-3xl": isBigger,
        })}
        style={{ textIndent: "-0.4em" }}
      >
        &ldquo;{quote}&rdquo;
      </div>

      <div className="mt-6 flex items-center gap-3">
        {children}
        <div className="flex-1 text-right font-inter text-sm">
          {name}
          <br />
          {position}
        </div>
        <Image
          src={imageSrc}
          width={40}
          height={40}
          alt={name}
          className="rounded-full border saturate-0 border-primary-200"
        />
      </div>
    </div>
  </Fade>
);
