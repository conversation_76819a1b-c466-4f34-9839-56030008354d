import { cn } from "#/utils/classnames";

const SvgShell = ({
  size = 60,
  className,
  children,
}: React.PropsWithChildren<{ size?: number; className?: string }>) => (
  <svg
    width={size}
    height={size}
    className={cn("text-background dark:text-white/20", className)}
    viewBox="0 0 60 60"
    fill="none"
  >
    {children}
  </svg>
);

export const Star = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M23.2156 6.99653C25.7517 1.00115 34.2483 1.00116 36.7844 6.99653L40.4421 15.6434C41.1883 17.4076 42.5924 18.8117 44.3566 19.558L53.0035 23.2157C58.9988 25.7518 58.9988 34.2483 53.0035 36.7844L44.3566 40.4421C42.5924 41.1883 41.1883 42.5924 40.4421 44.3566L36.7844 53.0035C34.2483 58.9989 25.7517 58.9989 23.2156 53.0035L19.5579 44.3566C18.8117 42.5924 17.4076 41.1883 15.6434 40.4421L6.99653 36.7844C1.00116 34.2483 1.00116 25.7518 6.99653 23.2157L15.6434 19.5579C17.4076 18.8117 18.8117 17.4076 19.5579 15.6434L23.2156 6.99653Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Bubble = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M4 30C4 15.6406 15.6406 4 30 4C44.3594 4 56 15.6406 56 30C56 44.3594 44.3594 56 30 56H4V30Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Data = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M36 38.8203C41.5228 38.8203 46 34.3432 46 28.8203V16.983C44.0281 20.7434 40.637 23.3574 36 23.3574H10C5.36301 23.3574 1.97187 20.7434 0 16.983V28.8203C0 34.3432 4.47715 38.8203 10 38.8203L36 38.8203Z"
      fill="currentColor"
    />
    <path
      d="M0 35.4459C1.97187 39.2063 5.36301 41.8203 10 41.8203L36 41.8203C40.637 41.8203 44.0281 39.2063 46 35.4459V46.2656C46 51.7885 41.5228 56.2656 36 56.2656H10C4.47715 56.2656 0 51.7885 0 46.2656V35.4459Z"
      fill="currentColor"
    />
    <path
      d="M0 10.3618C0.00238514 15.8827 4.47862 20.3574 10 20.3574H36C41.5228 20.3574 46 15.8803 46 10.3574V10C46 4.47715 41.5228 0 36 0H10C4.47862 0 0.00238514 4.47477 0 9.99558V10.3618Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Grid = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M15.6875 28.6328C22.8672 28.6328 28.6875 22.8125 28.6875 15.6328C28.6875 8.45311 22.8672 2.63281 15.6875 2.63281C8.5078 2.63281 2.6875 8.45311 2.6875 15.6328C2.6875 22.8125 8.5078 28.6328 15.6875 28.6328ZM44.3125 28.6328C51.4922 28.6328 57.3125 22.8125 57.3125 15.6328C57.3125 8.45311 51.4922 2.63281 44.3125 2.63281C37.1328 2.63281 31.3125 8.45311 31.3125 15.6328C31.3125 22.8125 37.1328 28.6328 44.3125 28.6328ZM28.6875 44.3672C28.6875 51.5469 22.8672 57.3672 15.6875 57.3672C8.5078 57.3672 2.6875 51.5469 2.6875 44.3672C2.6875 37.1875 8.5078 31.3672 15.6875 31.3672C22.8672 31.3672 28.6875 37.1875 28.6875 44.3672ZM44.3125 57.3672C51.4922 57.3672 57.3125 51.5469 57.3125 44.3672C57.3125 37.1875 51.4922 31.3672 44.3125 31.3672C37.1328 31.3672 31.3125 37.1875 31.3125 44.3672C31.3125 51.5469 37.1328 57.3672 44.3125 57.3672Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Arrow = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M6.41406 4.38477H41.1314H56.4141V19.6674L56.4141 54.3848H41.1314L41.1314 28.8781L14.3924 55.617L3.58594 44.8105L28.729 19.6674L6.41406 19.6674V4.38477Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Score = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M28 15C28 22.1797 22.1797 28 15 28C7.8203 28 2 22.1797 2 15C2 7.8203 7.8203 2 15 2C22.1797 2 28 7.8203 28 15ZM7.49699 52.9013C4.51286 49.9172 4.51286 45.0789 7.49699 42.0948L42.0743 7.5175C45.0584 4.53337 49.8967 4.53337 52.8808 7.5175C55.8649 10.5016 55.8649 15.3398 52.8808 18.324L18.3035 52.9013C15.3193 55.8854 10.4811 55.8854 7.49699 52.9013ZM45.2852 58.3047C52.4649 58.3047 58.2852 52.4844 58.2852 45.3047C58.2852 38.125 52.4649 32.3047 45.2852 32.3047C38.1055 32.3047 32.2852 38.125 32.2852 45.3047C32.2852 52.4844 38.1055 58.3047 45.2852 58.3047Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Snake = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M20.0039 23C20.0039 21.3431 21.3471 20 23.0039 20L37.0039 20C38.6608 20 40.0039 21.3431 40.0039 23V51C40.0039 52.6569 41.347 54 43.0039 54L51.0039 54C52.6608 54 54.0039 52.6569 54.0039 51L54.0039 16C54.0039 10.4772 49.5268 6 44.0039 6L16.0039 6C10.4811 6 6.00391 10.4772 6.00391 16L6.00391 44C6.00391 49.5229 10.4811 54 16.0039 54H27.0039C32.5268 54 37.0039 49.5229 37.0039 44L37.0039 25.2285C37.0039 23.5717 35.6608 22.2285 34.0039 22.2285L26.0039 22.2285C24.3471 22.2285 23.0039 23.5717 23.0039 25.2285L23.0039 38.5C23.0039 39.3284 22.3323 40 21.5039 40C20.6755 40 20.0039 39.3284 20.0039 38.5L20.0039 23Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const X = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M19.1973 30.3968L4.09554 45.4985L14.902 56.305L30.0037 41.2033L45.1059 56.3055L55.9124 45.499L40.8102 30.3968L55.9142 15.2928L45.1077 4.48633L30.0037 19.5903L14.9002 4.4868L4.09375 15.2933L19.1973 30.3968Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Tool = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M60 30.0003L45 55.9811H15L0 30.0003L15 4.01953L45 4.01953L60 30.0003ZM30 43C37.1797 43 43 37.1797 43 30C43 22.8203 37.1797 17 30 17C22.8203 17 17 22.8203 17 30C17 37.1797 22.8203 43 30 43Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Square = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <rect x="5" y="5" width="50" height="50" fill="currentColor" />
  </SvgShell>
);

export const Log = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path d="M0 5H48V19H0V5Z" fill="currentColor" />
    <path d="M12 41H60V55H12V41Z" fill="currentColor" />
    <path d="M54 23H6V37H54V23Z" fill="currentColor" />
  </SvgShell>
);

export const Play = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M51.5 27.402C53.5 28.5567 53.5 31.4434 51.5 32.5981L15.5 53.3827C13.5 54.5374 11 53.094 11 50.7846L11 9.21541C11 6.90601 13.5 5.46264 15.5 6.61734L51.5 27.402Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Check = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M22.4901 55.3064L2 34.8162L14.8065 22.0098L23.4447 30.648L47.1279 6.96484L58.9344 18.7713L22.4901 55.3064Z"
      fill="currentColor"
    />
  </SvgShell>
);
