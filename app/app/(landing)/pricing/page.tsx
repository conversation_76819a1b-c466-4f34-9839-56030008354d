import { PageTracker } from "#/ui/use-analytics";
import { type Metadata } from "next";
import { PricingClientPage } from "./clientpage";
import { buildMetadata } from "#/app/metadata";

export default function PricingPage() {
  return (
    <PageTracker category="pricing">
      <PricingClientPage />
    </PageTracker>
  );
}

export const metadata: Metadata = buildMetadata({
  title: "Pricing",
  description:
    "Start building AI products for free with Braintrust. Transparent pricing for AI evaluation, monitoring, and observability. No hidden fees, pay as you scale.",
  relativeUrl: "/pricing",
  tags: ["pricing", "AI pricing", "LLM pricing", "free tier", "enterprise"],
});
