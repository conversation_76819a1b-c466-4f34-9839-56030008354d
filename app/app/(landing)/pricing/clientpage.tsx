import { cn } from "#/utils/classnames";
import CustomerLogos from "../customer-logos";
import { FAQ } from "./faq";
import { PricingSection } from "./pricing";
import { PricingCalculator } from "./pricing-calculator";

export const PricingClientPage = () => {
  return (
    <div className="mb-44 px-4 sm:px-8">
      <section className="mb-0 mt-16 text-center">
        <h1 className="mb-6 text-balance text-5xl tracking-tight">
          Predictable pricing,
          <br />
          designed to scale
        </h1>
        <p
          className={cn(
            "max-w-screen-md mx-auto text-center text-primary-900 text-2xl mb-8 text-balance",
          )}
        >
          Start building for free, collaborate with your team, and ship quality
          AI products.
        </p>
      </section>
      <div className="flex justify-center">
        <PricingSection />
      </div>
      <PricingCalculator />
      <FAQ />

      <div className="relative z-10 mx-auto mb-8 w-full pt-36 lg:max-w-[460px] xl:max-w-screen-md">
        <div className="relative z-20 mb-6 text-center text-sm font-medium uppercase tracking-wider">
          Trusted by AI teams at
        </div>
        <CustomerLogos />
      </div>
    </div>
  );
};
