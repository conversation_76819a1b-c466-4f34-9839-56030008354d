@keyframes slide-up {
  0% {
    transform: translateY(5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.slideUp {
  animation: slide-up 0.5s ease-in forwards;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.fadeIn {
  animation: fade-in 4s ease-in forwards;
}

@keyframes rotate-tool {
  0% {
    transform: rotate(0deg);
  }
  10% {
    transform: rotate(60deg);
  }
  100% {
    transform: rotate(60deg);
  }
}

.rotateTool {
  animation: rotate-tool 4s ease-out infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.blink {
  animation: blink 1.5s step-end infinite;
}

@keyframes scale-up {
  0% {
    transform: scale(0.4);
    transform-origin: bottom left;
  }
  100% {
    transform: scale(1);
    transform-origin: bottom left;
  }
}

.bubbleScaleUp {
  animation: scale-up 0.5s ease-out forwards;
}

@keyframes sphere {
  0% {
    transform: translateY(16px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.sphere {
  animation: sphere 4s ease-out forwards;
}

@keyframes move-left {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0px);
    opacity: 1;
  }
}

.moveLeft {
  opacity: 0;
  animation: move-left 0.2s cubic-bezier(0, 0.07, 0, 0.88) forwards;
  animation-delay: 2.5s;
}

@keyframes move-up-right {
  0% {
    transform: translate(-60px, 60px);
    opacity: 0;
  }
  100% {
    transform: translate(0px, 0px);
    opacity: 1;
  }
}

.moveUpRight {
  opacity: 0;
  animation: move-up-right 0.4s cubic-bezier(0, 0.07, 0, 0.88) forwards;
  animation-delay: 0.5s;
}

@keyframes marquee {
  0% {
    transform: translate3d(0%, 0%, 0);
  }
  100% {
    transform: translate3d(-100%, 0%, 0);
  }
}
@keyframes marquee2 {
  0% {
    transform: translate3d(100%, 0%, 0);
  }
  100% {
    transform: translate3d(0%, 0%, 0);
  }
}

.cardMarquee {
  animation: marquee 90s linear infinite;
}
.cardMarquee2 {
  animation: marquee2 90s linear infinite;
}
