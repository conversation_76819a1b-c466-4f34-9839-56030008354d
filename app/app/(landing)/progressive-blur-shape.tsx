import React from "react";
import { cn } from "#/utils/classnames";
import { useIsClient } from "#/utils/use-is-client";

export const isSafari = () => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

const stops = [
  { blur: 0, mask: 100, opacity: 1 },
  { blur: 3.3, mask: 90, opacity: 0.95 },
  { blur: 7.4, mask: 80, opacity: 0.9 },
  { blur: 13.1, mask: 70, opacity: 0.85 },
  { blur: 20.5, mask: 60, opacity: 0.8 },
  { blur: 29.5, mask: 50, opacity: 0.7 },
  { blur: 40.2, mask: 40, opacity: 0.6 },
  { blur: 52.5, mask: 30, opacity: 0.5 },
  { blur: 66.4, mask: 20, opacity: 0.4 },
  { blur: 82, mask: 10, opacity: 0.3 },
];

export const ProgressiveBlurShape = ({
  className,
  direction = "right",
  shapeClassName,
  maskClassName,
  style = {},
  strength = 1,
  padding = 200,
  children,
  disableBlurInSafari,
}: {
  className?: string;
  direction?:
    | "right"
    | "left"
    | "top"
    | "bottom"
    | "top-right"
    | "bottom-right";
  shapeClassName: string;
  maskClassName?: string;
  children?: React.ReactNode;
  strength?: number;
  padding?: number;
  style?: React.CSSProperties;
  disableBlurInSafari?: boolean;
}) => {
  const getStop = (mask: number) =>
    `calc((100% - ${padding * 2}px) * ${mask / 100} + ${padding}px)`;

  const isClient = useIsClient();

  return (
    <div className={cn("relative progressive-blur", className)} style={style}>
      {isClient && disableBlurInSafari && isSafari() ? (
        <>
          {children ? (
            <div className="progressive-blur-shape">{children}</div>
          ) : (
            <div className="relative size-full">
              <div
                className={cn(
                  "absolute inset-0 progressive-blur-shape ease-out",
                  shapeClassName,
                  {
                    "bg-gradient-to-b": direction === "bottom",
                    "bg-gradient-to-t": direction === "top",
                    "bg-gradient-to-r": direction === "right",
                    "bg-gradient-to-l": direction === "left",
                    "bg-gradient-to-tr": direction === "top-right",
                    "bg-gradient-to-br": direction === "bottom-right",
                  },
                )}
              />
            </div>
          )}
        </>
      ) : (
        stops.map(({ blur, mask, opacity }, idx) => (
          <div
            className={cn("progressive-blur-mask absolute", {
              maskClassName,
            })}
            style={{
              inset: -padding,
              padding,
              zIndex: 100 - mask,
              opacity,
              maskImage: `linear-gradient(to ${direction}, rgba(0,0,0,0) ${getStop(mask - 40)}, rgba(0,0,0,1) ${getStop(mask - 20)}, rgba(0,0,0,1) ${getStop(mask)}, rgba(0,0,0,0) ${getStop(mask + 10)})`,
            }}
            key={idx}
          >
            {children ? (
              <div
                className="progressive-blur-shape"
                style={{
                  filter: `blur(${blur * strength}px)`,
                  // force safari to use gpu for perf
                  transform:
                    isClient && isSafari() ? "translate3d(0,0,0)" : undefined,
                }}
              >
                {children}
              </div>
            ) : (
              <div className="relative size-full">
                <div
                  className={cn(
                    "absolute inset-0 progressive-blur-shape ease-out",
                    shapeClassName,
                    {
                      "bg-gradient-to-b": direction === "bottom",
                      "bg-gradient-to-t": direction === "top",
                      "bg-gradient-to-r": direction === "right",
                      "bg-gradient-to-l": direction === "left",
                      "bg-gradient-to-tr": direction === "top-right",
                      "bg-gradient-to-br": direction === "bottom-right",
                    },
                  )}
                  style={{
                    filter:
                      isClient && disableBlurInSafari && isSafari()
                        ? "none"
                        : `blur(${blur * strength}px)`,
                    // force safari to use gpu for perf
                    transform:
                      isClient && isSafari() ? "translate3d(0,0,0)" : undefined,
                  }}
                />
              </div>
            )}
          </div>
        ))
      )}
    </div>
  );
};
