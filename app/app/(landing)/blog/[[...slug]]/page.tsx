import { getPages, getPage } from "../source";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { ArrowLeft, ArrowRight, Rss } from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { InlineTOC } from "fumadocs-ui/components/inline-toc";
import "./blog-style.css";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { buildMetadata } from "#/app/metadata";
import Image from "next/image";
import { buttonVariants } from "#/ui/button";
import { StructuredData } from "#/ui/structured-data";
import {
  generateArticleSchema,
  generateBreadcrumbSchema,
} from "#/lib/structured-data";

export default async function Page(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  if (!params.slug) {
    return (
      <div className="mb-44 px-4 sm:px-8">
        <section className="my-16">
          <div className="mb-12 flex max-w-screen-lg items-center justify-between">
            <h1 className="text-balance text-4xl tracking-tight">Blog</h1>
            <Link
              href="/blog/atom"
              className={buttonVariants({
                size: "xs",
                variant: "ghost",
              })}
            >
              <Rss className="size-4 text-primary-400" />
            </Link>
          </div>
          <div className="-mx-5 sm:mx-0">
            {getPages()
              .filter((page) => !page.data.draft)
              .sort(
                (a, b) =>
                  new Date(b.data.date).getTime() -
                  new Date(a.data.date).getTime(),
              )
              .map((child, idx) => {
                return (
                  <Link
                    key={idx}
                    href={child.url}
                    className="mb-4 flex max-w-screen-lg overflow-auto rounded-md border text-lg font-medium transition-colors bg-transparent hover:bg-primary-100 hover:border-primary-200"
                  >
                    <div className="relative hidden aspect-video w-60 flex-none border-r bg-primary-100 border-primary-100 sm:block">
                      <Image
                        src={child.data.image ?? ""}
                        alt={child.data.title}
                        fill
                        sizes="480px"
                        quality={90}
                        className="object-cover"
                      />
                    </div>
                    <div className="flex flex-1 p-5">
                      <div className="flex-1">
                        <div className="mb-1 max-w-screen-sm text-2xl font-medium">
                          {child.data.title}
                        </div>
                        <div className="max-w-screen-sm font-inter text-sm font-normal text-primary-700">
                          {child.data.description}
                        </div>
                      </div>
                      <ArrowRight className="size-4 flex-none text-primary-500" />
                    </div>
                  </Link>
                );
              })}
          </div>
        </section>
      </div>
    );
  }

  const page = getPage(params.slug);

  if (!page || page.data.draft) notFound();

  const breadcrumbs = [
    { name: "Home", url: "https://www.braintrust.dev" },
    { name: "Blog", url: "https://www.braintrust.dev/blog" },
    { name: page.data.title, url: `https://www.braintrust.dev${page.url}` },
  ];

  return (
    <>
      <StructuredData
        data={[
          generateArticleSchema({
            title: page.data.title,
            description: page.data.description || "",
            url: `https://www.braintrust.dev${page.url}`,
            publishedTime: String(page.data.date),
            modifiedTime: page.data.lastModified
              ? String(page.data.lastModified)
              : String(page.data.date),
            authors: page.data.authors || ["Braintrust Team"],
            tags: page.data.tags || [],
          }),
          generateBreadcrumbSchema(breadcrumbs),
        ]}
      />
      <div className="px-4 pt-12 sm:px-8">
        <Link
          href="/blog"
          className="mb-2 flex items-center gap-1 text-sm underline-offset-4 text-primary-600 hover:underline"
        >
          <ArrowLeft className="size-3" /> Blog
        </Link>
        <div className="blog-article flex">
          <div
            className={cn(
              proseMDXBaseClassName,
              "mb-44 prose-h1:text-4xl prose-h1:font-normal flex-1 overflow-hidden sm:prose-h1:font-normal sm:prose-h1:leading-tight sm:prose-h1:text-5xl md:prose-h1:text-5xl prose-h1:text-balance max-w-screen-xl md:prose-h1:leading-tight prose-xl text-lg leading-[1.8] prose-headings:font-medium prose-headings:font-planar font-inter",
            )}
          >
            <page.data.body components={defaultMdxComponents} />
          </div>
          <div className="blog-toc sticky top-0 hidden max-h-screen flex-none overflow-auto py-9 pl-8 lg:block">
            <InlineTOC defaultOpen items={page.data.toc} />
          </div>
        </div>
      </div>
    </>
  );
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  if (!params.slug) {
    return {
      ...buildMetadata({
        title: "Blog",
        description:
          "Latest insights, tutorials, and updates from the Braintrust team. Learn about AI evaluation, LLM observability, and best practices for building reliable AI products.",
        relativeUrl: "/blog",
        tags: [
          "AI blog",
          "LLM tutorials",
          "AI evaluation",
          "machine learning",
          "developer resources",
        ],
      }),
      head: [
        {
          tag: "link",
          props: {
            rel: "alternate",
            type: "application/rss+xml",
            title: "Braintrust blog RSS feed",
            href: "/blog/atom",
          },
        },
      ],
    };
  }
  const page = getPage(params.slug);

  if (page == null) notFound();

  const imageUrl = page.data.image;
  const twimageUrl = imageUrl ?? page.data.twimage;

  return buildMetadata({
    title: `${page.data.title}`,
    sections: ["Blog"],
    description: page.data.description,
    relativeUrl: `/blog/${params.slug.join("/")}`,
    ogImageUrl: imageUrl,
    twitterImageUrl: twimageUrl,
    type: "article",
    publishedTime: String(page.data.date),
    modifiedTime: page.data.lastModified
      ? String(page.data.lastModified)
      : page.data.date,
    authors: page.data.authors || ["Braintrust Team"],
    tags: page.data.tags || ["AI", "machine learning", "evaluation"],
  });
}
