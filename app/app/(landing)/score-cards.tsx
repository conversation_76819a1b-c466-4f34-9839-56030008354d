"use client";

import { ProgressiveBlurShape } from "./progressive-blur-shape";
import {
  AnthropicImage,
  GoogleImage,
  MetaImage,
  MistralImage,
  OpenAIImage,
  PerplexityImage,
} from "#/ui/icons/providers";
import { Score } from "./shapes";
import styles from "./landing.module.css";

import { LandingDocsLink, Title } from "./common";
import { cn } from "#/utils/classnames";
import { Fade } from "react-awesome-reveal";

const cards = [
  {
    strength: 0.4,
    score: 51,
    name: "Translation",
    Icon: OpenAIImage,
    model: "o1 mini",
    tok: "1,539",
    secs: 8.07,
    cost: "0.018",
  },
  {
    strength: 0.2,
    score: 67,
    name: "Levenshtein distance",
    Icon: MistralImage,
    model: "Mistral Nemo",
    tok: "1,021",
    secs: 4.83,
    cost: "0.002",
  },
  {
    strength: 0.1,
    score: 89,
    name: "Similarity",
    Icon: OpenAIImage,
    model: "GPT-4o",
    tok: "2,992",
    secs: 12.44,
    cost: "0.010",
  },

  {
    strength: 0.2,
    score: 60,
    name: "Moderation",
    Icon: AnthropicImage,
    model: "Claude 3.5 Sonnet",
    tok: "1,958",
    secs: 11.24,
    cost: "0.008",
  },

  {
    strength: 0.5,
    score: 54,
    name: "Security",
    Icon: GoogleImage,
    model: "Gemini Pro",
    tok: "2,610",
    secs: 9.23,
    cost: "0.008",
  },
  {
    strength: 0.8,
    score: 33,
    name: "Hallucination",
    Icon: MetaImage,
    model: "Llama 3.5",
    tok: "1,620",
    secs: 10.2,
    cost: "0.014",
  },
  {
    strength: 1,
    score: 29,
    name: "Summary",
    Icon: PerplexityImage,
    model: "Sonar large online",
    tok: "1,004",
    secs: 12.2,
    cost: "0.004",
  },
];

export const ScoreCards = () => {
  return (
    <Fade>
      <div className="p-4 sm:p-8 md:mb-18">
        <Title>Evaluate your prompts and models</Title>
        <div className="mb-12 flex flex-col gap-8 lg:flex-row lg:items-end lg:justify-between">
          <div className="flex max-w-screen-sm flex-col gap-4 text-pretty font-inter text-xl text-primary-900">
            <p className="text-pretty font-planar text-4xl font-normal leading-[1.1] text-black dark:text-white xl:text-4xl">
              Non-deterministic models and unpredictable natural language inputs
              make building robust LLM applications difficult. Adapt your
              development lifecycle for the AI era with Braintrust&apos;s
              iterative LLM workflows.
            </p>
            <p className="text-base">
              Easily answer questions like{" "}
              <span className="font-medium">
                “which examples regressed when we changed the prompt?”
              </span>{" "}
              or{" "}
              <span className="font-medium">
                “what happens if I try this new model?”
              </span>
            </p>
          </div>
          <div className="-ml-2 flex flex-col gap-2 font-inter lg:m-0 lg:items-end">
            <div className="px-2 text-sm text-black/50 dark:text-white/50">
              Learn more in docs
            </div>
            <div className="flex gap-3">
              <LandingDocsLink href="/docs/guides/playground">
                Playground
              </LandingDocsLink>
              <LandingDocsLink href="/docs/start/eval-ui">
                Eval via UI
              </LandingDocsLink>
              <LandingDocsLink href="/docs/start/eval-sdk">
                Eval via SDK
              </LandingDocsLink>
            </div>
          </div>
        </div>
        <div className="relative mx-[-320px] flex h-96 flex-wrap justify-center overflow-hidden">
          <div className="pointer-events-none absolute inset-y-0 left-0 z-20 w-6 bg-gradient-to-r from-warm to-transparent dark:from-black" />
          <div className="pointer-events-none absolute inset-y-0 right-0 z-20 w-6 bg-gradient-to-l from-warm to-transparent dark:from-black" />
          <div
            className={cn("absolute top-0 flex gap-6", styles.cardMarquee, {
              // [styles.cardMarquee]: isClient && !isSafari(),
            })}
          >
            {cards.map((card, idx) => (
              <ScoreCard key={idx} {...card} />
            ))}
          </div>
          {/* {isClient && isSafari() && ( */}
          <div
            className={cn(
              "absolute top-0 pl-6 flex gap-6",
              styles.cardMarquee2,
            )}
          >
            {cards.map((card, idx) => (
              <ScoreCard key={idx} {...card} />
            ))}
          </div>
          {/* )} */}
        </div>
      </div>
    </Fade>
  );
};

interface ScoreCardProps {
  strength?: number;
  score: number;
  name: string;
  Icon: React.ElementType;
  model: string;
  tok: string;
  secs: string | number;
  cost: string;
}
const ScoreCard = ({
  strength,
  score,
  name,
  Icon,
  model,
  tok,
  secs,
  cost,
}: ScoreCardProps) => {
  return (
    <div className="relative h-72 w-60 flex-none font-inter">
      <div className="absolute inset-0 z-10 p-2">
        <div className="flex h-full flex-col justify-end rounded-lg p-4 text-primary-900 dark:text-primary-900">
          <div className="mb-3 flex-none text-base">{name}</div>
          <div className="mb-3 flex flex-none items-baseline gap-1 font-mono text-7xl font-semibold tabular-nums">
            {score}
            <Score
              size={32}
              className="text-primary-900 dark:text-primary-700"
            />
          </div>
          <div className="relative mb-3 h-2 w-full flex-none rounded-lg">
            <div className="h-2 w-full rounded-lg opacity-10 bg-primary-900" />
            <div
              className="absolute inset-y-0 left-0 rounded-xl bg-primary-900"
              style={{ width: `${score}%` }}
            />
          </div>
          <div className="mb-3 flex items-center gap-1.5 font-medium">
            <Icon size={40} />
            {model}
          </div>
          <div className="flex flex-none justify-between font-mono text-xs opacity-50 text-primary-900">
            <div>{tok} TOK</div>
            <div>{secs}s</div>
            <div>~${cost}</div>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 z-0 opacity-5 dark:opacity-10">
        <ProgressiveBlurShape
          strength={strength}
          direction="top"
          padding={120}
          disableBlurInSafari
          shapeClassName="bg-black dark:bg-white rounded-lg"
          className="pointer-events-none h-72 w-60"
        />
      </div>
    </div>
  );
};
