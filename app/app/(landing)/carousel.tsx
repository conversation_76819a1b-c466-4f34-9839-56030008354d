"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { ProgressiveBlurShape } from "./progressive-blur-shape";
import { Score, Play, Log, Check } from "./shapes";

import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "#/ui/tabs";
import Image from "next/image";
import evalImage from "./img/Eval.png";
import logImage from "./img/Log.png";
import playImage from "./img/Play.png";
import reviewImage from "./img/Review.png";
import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "#/utils/classnames";
import { Fade } from "react-awesome-reveal";

const imageClassName = "rounded-md border bg-white border-primary-200";
const buttonClassName =
  "rounded-md px-6 py-2 text-xl text-primary-900 data-[state=active]:bg-primary-100 data-[state=active]:border-primary-800";
const iconClassName = "text-primary-900 dark:text-primary-900";
const tabContentClassName =
  "absolute inset-0.5 transition-opacity duration-300";

const imageWidth = 2800;
const imageHeight = 1512;

const tabConfig = [
  {
    value: "eval",
    image: evalImage,
    title: "Eval",
    Icon: Score,
  },
  {
    value: "log",
    image: logImage,
    title: "Log",
    Icon: Log,
  },
  {
    value: "play",
    image: playImage,
    title: "Play",
    Icon: Play,
  },
  {
    value: "review",
    image: reviewImage,
    title: "Review",
    Icon: Check,
  },
];

export const Carousel = () => {
  const [tab, setTab] = useState("eval");
  const [inView, setInView] = useState(false);

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const hasUserInteracted = useRef(false);

  // Function to go to the next tab
  const goToNextTab = useCallback(() => {
    setTab((prevTab) => {
      const currentIndex = tabConfig.findIndex((t) => t.value === prevTab);
      const nextIndex = (currentIndex + 1) % tabConfig.length;
      return tabConfig[nextIndex].value;
    });
  }, []);

  // Function to start the carousel timer
  const startCarousel = useCallback(() => {
    if (timerRef.current === null && !hasUserInteracted.current) {
      timerRef.current = setInterval(goToNextTab, 5000); // Change tab every 5 seconds
    }
  }, [goToNextTab]);

  // Function to stop the carousel timer
  const stopCarousel = useCallback(() => {
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Handle visibility changes to start/stop the carousel
  useEffect(() => {
    if (inView) {
      startCarousel();
    } else {
      stopCarousel();
    }
    return () => {
      stopCarousel();
    };
  }, [inView, startCarousel, stopCarousel]);

  // Handle user interaction to stop the carousel
  const handleTabChange = (value: string) => {
    setTab(value);
    hasUserInteracted.current = true;
    stopCarousel();
  };

  return (
    <Fade triggerOnce onVisibilityChange={setInView}>
      <div className="mx-auto mb-24 hidden px-8 py-4 md:block">
        <Tabs value={tab} onValueChange={handleTabChange}>
          <div className="relative z-10 mb-6">
            <div className="absolute inset-0 z-10 opacity-20 dark:opacity-70">
              <ProgressiveBlurShape
                strength={0.6}
                direction="bottom"
                shapeClassName="to-limeOklch dark:to-purpleOklch to-10% from-transparent rounded-lg"
                className="pointer-events-none size-full lg:aspect-[1.616]"
              />
            </div>
            <div className="relative z-20 p-0.5">
              {tabConfig.map(({ value, image, title }, idx) => (
                <TabsContent
                  key={value}
                  value={value}
                  forceMount
                  className={
                    idx === 0
                      ? undefined
                      : cn(tabContentClassName, "z-10 opacity-0", {
                          "opacity-100": tab === value,
                        })
                  }
                >
                  <Image
                    src={image}
                    alt={title}
                    className={imageClassName}
                    width={imageWidth}
                    height={imageHeight}
                    priority={idx === 0}
                    quality={100}
                  />
                </TabsContent>
              ))}
            </div>
          </div>
          <TabsList className="flex justify-center gap-2 bg-transparent">
            {tabConfig.map(({ value, Icon, title }) => (
              <TabsTrigger key={value} value={value} asChild>
                <Button className={buttonClassName}>
                  <Icon size={18} className={iconClassName} />
                  {title}
                </Button>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
    </Fade>
  );
};
