"use client";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ArrowUpRight } from "lucide-react";
import Link from "next/link";

export const Title = ({ children }: { children: React.ReactNode }) => {
  return (
    <h2 className="mb-4 max-w-screen-sm text-pretty text-lg font-normal leading-[1.1] xl:text-xl">
      {children}
    </h2>
  );
};

export const LandingDocsLink = ({
  href,
  className,
  children,
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Link
      href={href}
      className={cn(
        buttonVariants({ size: "sm" }),
        "gap-1.5 px-2 font-inter border-primary-200",
        className,
      )}
    >
      <ArrowUpRight className="size-3" />
      {children}
    </Link>
  );
};
