"use client";

import { <PERSON><PERSON><PERSON>, Sun, Moon } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "#/ui/button";
import { useIsClient } from "#/utils/use-is-client";

export const ThemeToggle = ({ className }: { className?: string }) => {
  const isClient = useIsClient();
  const { theme, setTheme } = useTheme();

  if (!isClient) {
    return null;
  }

  return (
    <div className={className}>
      <div className="inline-flex gap-1 rounded-lg border p-1 border-black/50 dark:border-white/50">
        <Button
          size="sm"
          aria-label="Light mode"
          className="transition-none duration-0"
          variant={theme === "light" ? "default" : "ghost"}
          Icon={Sun}
          onClick={() => setTheme("light")}
        />
        <Button
          size="sm"
          className="transition-none duration-0"
          aria-label="Dark mode"
          Icon={Moon}
          variant={theme === "dark" ? "default" : "ghost"}
          onClick={() => setTheme("dark")}
        />
        <Button
          size="sm"
          aria-label="System mode"
          className="transition-none duration-0"
          Icon={Laptop}
          variant={theme === "system" ? "default" : "ghost"}
          onClick={() => setTheme("system")}
        />
      </div>
    </div>
  );
};
