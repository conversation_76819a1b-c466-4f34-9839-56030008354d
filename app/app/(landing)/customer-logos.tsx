import { type PropsWithChildren } from "react";
import {
  Airtable,
  Zapier,
  Notion,
  Instacart,
  Brex,
  Stripe,
  Replit,
  Vercel,
  BrowserCompany,
  <PERSON><PERSON>,
} from "./logos";
import { cn } from "#/utils/classnames";

const CustomerLogos = () => {
  return (
    <div className="customer-logos grid grid-cols-2 justify-between justify-items-center gap-x-12 gap-y-6 sm:grid-cols-3 sm:gap-x-4 md:grid-cols-5 min-[880px]:gap-5 lg:grid-cols-3 lg:gap-x-9 lg:gap-y-5 xl:grid-cols-5 xl:gap-y-9">
      <Logo>
        <Instacart className={logoClassName} />
      </Logo>
      <Logo>
        <Stripe className={logoClassName} />
      </Logo>
      <Logo>
        <Zapier className={logoClassName} />
      </Logo>
      <Logo>
        <Airtable className={logoClassName} />
      </Logo>
      <Logo>
        <Notion className={logoClassName} />
      </Logo>
      <Logo>
        <Replit className={logoClassName} />
      </Logo>
      <Logo>
        <Brex className={logoClassName} />
      </Logo>
      <Logo>
        <Vercel className={logoClassName} />
      </Logo>
      <Logo>
        <Ramp className={logoClassName} />
      </Logo>
      <Logo>
        <BrowserCompany
          className={cn(logoClassName, "sm:hidden md:block lg:hidden xl:block")}
        />
      </Logo>
    </div>
  );
};

const logoClassName = "h-full scale-95 xl:scale-100";

const Logo = ({ children }: PropsWithChildren<{}>) => (
  <div className="h-9">{children}</div>
);

export default CustomerLogos;
