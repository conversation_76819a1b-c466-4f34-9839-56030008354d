"use client";
import { PageTracker } from "#/ui/use-analytics";
import { ArrowRight } from "lucide-react";
import CustomerLogos from "./customer-logos";
import { Testimonials } from "./Testimonials";
import { cn } from "#/utils/classnames";

import Link from "next/link";
import { buttonVariants } from "#/ui/button";
import { ProgressiveBlurShape } from "./progressive-blur-shape";
import { Fade } from "react-awesome-reveal";
import {
  Arrow,
  Bubble,
  Data,
  Grid,
  Score,
  Snake,
  Square,
  Star,
  Tool,
  X,
  Play,
} from "./shapes";

import styles from "./landing.module.css";
import { FeatureCards } from "./feature-cards";
// import { Carousel } from "./carousel";

import promptImg from "./img/prompt.png";
import scorersImg from "./img/scorers.png";
import datasetImg from "./img/dataset.png";
import Image, { type StaticImageData } from "next/image";
import { ScoreCards } from "./score-cards";
import { CtaLink } from "./cta-button";
import { Carousel } from "./carousel";
import { LandingDocsLink, Title } from "./common";

const Shape = ({
  row,
  col,
  children,
}: {
  row: number;
  col: number;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={cn("absolute transition-transform")}
      style={{ transform: `translate(${col * 60}px, ${row * 60}px)` }}
    >
      {children}
    </div>
  );
};

export const Landing = () => {
  return (
    <PageTracker category="landing">
      <div className="relative z-10 mb-16 px-4 pt-36 sm:px-8 sm:pt-16 md:mb-0 lg:mb-16">
        <div className="relative z-30 flex w-full flex-none flex-col items-start lg:pb-20">
          <h1 className="z-10 mb-6 max-w-screen-sm text-6xl font-medium tracking-tight sm:text-7xl md:text-8xl lg:mb-10 lg:max-w-[500px] lg:text-8xl xl:max-w-[600px] xl:text-9xl">
            Ship LLM products that&nbsp;work.
          </h1>
          <p className="z-10 mb-8 max-w-screen-sm text-balance pt-2 text-2xl font-normal text-primary-950 lg:absolute lg:right-0 lg:top-0 lg:max-w-[500px] lg:text-right lg:text-3xl xl:text-4xl">
            Braintrust is the end-to-end platform for building world-class AI
            apps.
          </p>
          <div className="z-10 flex gap-3">
            <CtaLink
              variant="ghost"
              className="flex gap-2 px-4 text-lg font-normal bg-primary-950 text-warm hover:bg-primary-700 hover:text-warm dark:bg-white dark:text-black dark:hover:bg-primary-700"
              size="lg"
              cta="Get started"
            />
            <Link
              href="/contact"
              className={cn(
                buttonVariants({
                  size: "lg",
                  variant: "ghost",
                }),
                "text-lg font-normal bg-black/5 dark:bg-white/10 hover:bg-black/10 dark:hover:bg-white/20 text-primary-950 px-4 gap-2",
              )}
            >
              Chat with us <ArrowRight size={18} />
            </Link>
          </div>
          <div className="relative z-10 mb-8 mt-20 w-full lg:absolute lg:right-0 lg:top-[180px] lg:mb-20 lg:max-w-[460px] xl:top-[340px] xl:max-w-screen-md">
            <div className="relative z-20 mb-6 text-sm font-medium uppercase tracking-wider lg:text-right">
              Trusted by AI teams at
            </div>
            <CustomerLogos />
          </div>
          <div className="absolute top-[-300px] z-[1] -ml-16 -mt-20 min-h-[380px] scale-75 opacity-90 dark:opacity-40 sm:-mt-32 sm:ml-0 md:bottom-8 md:left-[180px] md:top-auto md:mt-0 md:scale-100 xl:left-[380px]">
            <Shape row={2} col={5}>
              <Square className="border border-white text-white/50 dark:border-white/30 dark:text-white/30" />
            </Shape>
            <Shape row={5} col={6}>
              <Square />
            </Shape>
            <Shape row={5} col={4}>
              <Square />
            </Shape>

            <Shape row={5} col={8}>
              <Square className={cn(styles.blink, "transition-none")} />
            </Shape>
            <Shape row={6} col={7}>
              <Square />
            </Shape>

            <Shape row={5} col={10}>
              <Star />
            </Shape>
            <Shape row={1} col={6}>
              <Star />
            </Shape>
            <Shape row={4} col={7}>
              <X />
            </Shape>
            <Shape row={4} col={9}>
              <Square />
            </Shape>
            <Shape row={3} col={6}>
              <Tool className={cn(styles.rotateTool, "transition-transform")} />
            </Shape>
            <Shape row={2} col={8}>
              <Tool />
            </Shape>
            <Shape row={3} col={8}>
              <Snake />
            </Shape>
            <Shape row={3} col={4}>
              <Data />
            </Shape>
            <Shape row={4} col={3}>
              <Score className={styles.moveLeft} />
            </Shape>
            <Shape row={2} col={10}>
              <Score />
            </Shape>
            <Shape row={2} col={3}>
              <Bubble className={cn(styles.bubbleScaleUp, "opacity-80")} />
            </Shape>
            <Shape row={7} col={2}>
              <Bubble />
            </Shape>
            <Shape row={7} col={3}>
              <X />
            </Shape>
            <Shape row={6} col={5}>
              <Arrow />
            </Shape>
            <Shape row={7} col={6}>
              <Play />
            </Shape>
            <Shape row={5} col={1}>
              <Grid />
            </Shape>
          </div>
          <div className="absolute left-[200px] top-[-250px] z-[-1] md:left-[380px] md:top-[-250px] lg:top-[-500px]">
            <ProgressiveBlurShape
              strength={1.5}
              padding={340}
              direction="bottom"
              shapeClassName="from-yellowOklch to-limeOklch to-80% dark:to-purpleOklch dark:to-80% dark:from-blueOklch rounded-tr-full rounded-br-full rounded-tl-full rounded-bl-md"
              className={cn(
                styles.sphere,
                "size-[400px] md:size-[600px] lg:size-[800px] delay-1000",
              )}
            />
          </div>
        </div>
      </div>

      <Carousel />
      <ScoreCards />

      <div className="relative z-10 -mx-20 mb-24 p-24 sm:-mx-16">
        <div className="opacity-10 dark:opacity-20">
          <ProgressiveBlurShape
            strength={0.15}
            disableBlurInSafari
            direction="left"
            shapeClassName="border-2 border-black dark:border-white rounded-xl"
            className="pointer-events-none absolute inset-0 z-[-1]"
          />
        </div>
        <Fade triggerOnce>
          <Title>Anatomy of an eval</Title>
          <p className="text mb-14 max-w-screen-sm text-pretty font-planar text-4xl font-normal leading-[1.1] text-black dark:text-white xl:text-4xl">
            Braintrust evals are composed of three components—a prompt, scorers,
            and a dataset of examples.
          </p>
          <div className="flex flex-col gap-12 lg:flex-row">
            <Fade triggerOnce cascade damping={0.1}>
              <EvalComponent
                Icon={Bubble}
                name="Prompt"
                description="Tweak LLM prompts from any AI provider, run them, and track their performance over time. Seamlessly and securely sync your prompts with your code."
                image={promptImg}
                docsHref="/docs/guides/prompts"
                docsLabel="Prompts guide"
                shapeClassName="from-cyan-50 to-cyan-500 dark:from-cyan-950 dark:to-cyan-900"
              />
              <EvalComponent
                Icon={Score}
                name="Scorers"
                description="Use industry standard autoevals or write your own using code or natural language. Scorers take an input, the LLM output, and an expected value to generate a score."
                image={scorersImg}
                docsHref="/docs/guides/evals/write#scorers"
                docsLabel="Scorers guide"
                shapeClassName="from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900"
              />
              <EvalComponent
                Icon={Data}
                name="Dataset"
                description="Capture rated examples from staging and production and incorporate them into “golden” datasets. Datasets are integrated, versioned, scalable, and secure."
                image={datasetImg}
                docsHref="/docs/guides/datasets"
                docsLabel="Datasets guide"
                shapeClassName="from-fuchsia-50 to-fuchsia-100 dark:from-fuchsia-950 dark:to-fuchsia-900"
              />
            </Fade>
          </div>
        </Fade>
      </div>
      <FeatureCards />
      <Testimonials />
    </PageTracker>
  );
};

const EvalComponent = ({
  Icon,
  image,
  iconClassName,
  name,
  description,
  docsHref,
  docsLabel,
}: {
  Icon: React.ComponentType<{ className?: string }>;
  image: StaticImageData;
  iconClassName?: string;
  name: string;
  description: string;
  docsHref: string;
  docsLabel: string;
  shapeClassName: string;
}) => {
  return (
    <div className="relative z-10 flex-1">
      <div className="relative z-10">
        <Image
          width={580}
          height={324}
          src={image}
          alt={name}
          className="rounded-md border"
        />
        <h3 className="mb-3 mt-6 flex items-center gap-2 text-2xl font-medium">
          <Icon
            className={cn(
              "size-5 text-primary-950 dark:text-primary-950",
              iconClassName,
            )}
          />

          {name}
        </h3>
        <p className="mb-3 max-w-screen-sm text-pretty font-inter text-base text-primary-900">
          {description}
        </p>
        <LandingDocsLink href={docsHref}>{docsLabel}</LandingDocsLink>
      </div>
    </div>
  );
};
