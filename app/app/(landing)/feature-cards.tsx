"use client";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react";
import { cn } from "#/utils/classnames";

import { But<PERSON> } from "#/ui/button";
import { isS<PERSON><PERSON>, ProgressiveBlurShape } from "./progressive-blur-shape";

import traces from "./img/traces.png";
import monitoring from "./img/monitoring.png";
import selfHosting from "./img/self-hosting.png";
import onlineEvals from "./img/online-evals.png";
import functions from "./img/functions.png";

import Image, { type StaticImageData } from "next/image";
import { LandingDocsLink, Title } from "./common";
import { useState } from "react";

import { Fade } from "react-awesome-reveal";
import { useIsClient } from "#/utils/use-is-client";

export const FeatureCards = () => {
  const [index, setIndex] = useState(0);

  const containerWidth =
    typeof window !== "undefined" ? window.innerWidth : 960;

  const arrowControls = (
    <>
      <Button
        disabled={index === 0}
        transparent
        aria-label="Previous feature"
        onClick={() => setIndex(index - 1)}
        className="h-auto rounded-full p-5 transition-opacity disabled:opacity-40"
      >
        <ArrowLeft className="size-6" />
      </Button>
      <Button
        disabled={containerWidth > 1150 ? index === 3 : index === 4}
        transparent
        aria-label="Next feature"
        onClick={() => setIndex(index + 1)}
        className="h-auto rounded-full p-5 transition-opacity disabled:opacity-40"
      >
        <ArrowRight className="size-6" />
      </Button>
    </>
  );

  return (
    <Fade triggerOnce>
      <div className="mb-24 p-4 sm:p-8 md:mb-28">
        <div className="-ml-5 mb-10 hidden items-center gap-2 md:flex">
          {arrowControls}
        </div>
        <div className="flex flex-col gap-8 md:flex-row">
          <div
            className={cn(
              "w-full flex-none md:w-[343px] transition-all duration-500 ease-in-out",
              {
                "md:blur-sm": index !== 0,
              },
            )}
            // force safari to use gpu for perf
            style={{ transform: "translate3d(0,0,0)" }}
          >
            <Title>Features for everyone</Title>
            <p className="text-pretty font-planar text-4xl font-normal leading-[1.1] text-black dark:text-white xl:text-4xl">
              Intuitively designed for both technical and non-technical team
              members, and synced between code and UI.
            </p>
          </div>
          <div
            className="flex gap-4 transition-transform duration-500 ease-in-out will-change-transform"
            style={{ transform: `translate3d(-${index * 360}px, 0, 0)` }}
          >
            <Feature
              img={traces}
              imgAlt="Traces"
              title="Traces"
              description="Visualize and analyze LLM execution traces in real-time to debug and optimize your AI apps."
              className="bg-blue-500"
              docsLabel="Tracing guide"
              docsUrl="/docs/guides/tracing"
            >
              <ProgressiveBlurShape
                strength={0.6}
                direction="top"
                shapeClassName="bg-blueOklch"
                className={cn(
                  "pointer-events-none absolute top-0 h-[300px] -left-5 z-[-1] w-10 transition-opacity",
                )}
              />
            </Feature>
            <Feature
              img={monitoring}
              imgAlt="Monitoring"
              title="Monitoring"
              className="bg-orange-500"
              description="Monitor real-world AI interactions with insights to ensure your models perform optimally in production."
              docsUrl="/docs/guides/logging"
              docsLabel="Logging and monitoring"
            >
              <ProgressiveBlurShape
                strength={0.2}
                direction="top"
                shapeClassName="bg-orangeOklch rounded-full"
                className={cn(
                  "pointer-events-none absolute left-[60px] top-[94px] z-[-1] size-[110px] transition-opacity",
                )}
              />
            </Feature>
            <Feature
              img={onlineEvals}
              imgAlt="Online evaluations"
              title="Online evals"
              description="Continuously evaluate with automatic, asynchronous server-side scoring as you upload logs."
              className="bg-lime-400"
              docsUrl="/docs/guides/evals/write#online-evaluation"
              docsLabel="Online evaluation docs"
            >
              <ProgressiveBlurShape
                strength={1}
                direction="top"
                shapeClassName="bg-limeOklch rounded-[50%]"
                className={cn(
                  "pointer-events-none absolute left-[-40px] top-[94px] z-[-1] h-[200px] w-[400px] transition-opacity",
                )}
              />
            </Feature>
            <Feature
              img={functions}
              imgAlt="Functions"
              title="Functions"
              className="bg-rose-500"
              description="Define functions in TypeScript and Python, and use as custom scorers or callable tools."
              docsUrl="/docs/reference/functions"
              docsLabel="Functions reference"
            >
              <ProgressiveBlurShape
                strength={1}
                direction="left"
                shapeClassName="bg-roseOklch rounded-md"
                className={cn(
                  "pointer-events-none absolute inset-x-4 top-4 z-[-1] h-[280px] transition-opacity",
                )}
              />
            </Feature>
            <Feature
              img={selfHosting}
              imgAlt="Self-hosting"
              title="Self-hosting"
              className="bg-cyan-500"
              description="Deploy and run Braintrust on your own infrastructure for full control over your data and compliance requirements."
              docsUrl="/docs/guides/self-hosting"
              docsLabel="Self-hosting guide"
            >
              <ProgressiveBlurShape
                strength={1}
                direction="right"
                shapeClassName="bg-cyanOklch rounded-md"
                className={cn(
                  "pointer-events-none absolute left-[32px] top-[69px] z-[-1] h-[203px] w-[278px] transition-opacity",
                )}
              />
            </Feature>
          </div>
          <div className="-ml-5 mb-10 items-center gap-2 md:hidden">
            {arrowControls}
          </div>
        </div>
      </div>
    </Fade>
  );
};

const width = 343;
const height = 300;

const Feature = ({
  img,
  imgAlt,
  title,
  description,
  className,
  children,
  docsUrl,
  docsLabel,
}: React.PropsWithChildren<{
  img: StaticImageData;
  imgAlt: string;
  title: string;
  description: string;
  className?: string;
  docsUrl: string;
  docsLabel: string;
}>) => {
  const isClient = useIsClient();

  return (
    <div className="w-[343px] flex-none overflow-hidden">
      <div
        className={cn(
          `w-full z-10 flex-none rounded-lg flex flex-col items-center bg-primary-100 overflow-hidden relative`,
          className,
        )}
      >
        {isClient && !isSafari() && children}
        <Image
          src={img}
          alt={imgAlt}
          width={width}
          className="z-20"
          height={height}
        />
        <div className="p-5 pt-3 text-black">
          <h3 className="mb-3 text-2xl font-medium">{title}</h3>
          <p className="mb-5 text-pretty font-inter text-base opacity-80">
            {description}
          </p>
          <LandingDocsLink
            className="border-black/10 text-black hover:bg-black/10 hover:text-black"
            href={docsUrl}
          >
            {docsLabel}
          </LandingDocsLink>
        </div>
      </div>
    </div>
  );
};
