import { getPage } from "../source";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { notFound } from "next/navigation";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { buildMetadata } from "#/app/metadata";

export default async function LegalPage(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);

  if (!page) notFound();

  return (
    <div className="px-4 pt-12 sm:px-8">
      <div
        className={cn(
          proseMDXBaseClassName,
          "mb-44 prose-headings:font-planar prose-headings:font-medium font-inter max-w-screen-md",
        )}
      >
        <page.data.body components={defaultMdxComponents} />
      </div>
    </div>
  );
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);

  if (!page) notFound();

  return buildMetadata({
    title: page.data.title,
    description: page.data.description,
    relativeUrl: `/legal/${params.slug?.join("/") ?? ""}`,
  });
}
