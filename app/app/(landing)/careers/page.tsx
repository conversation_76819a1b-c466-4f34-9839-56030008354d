import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { ArrowLeft, ArrowRight } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

import "./careers-style.css";
import { Apply<PERSON>utton } from "#/ui/careers/apply-button";
import { buildMetadata } from "#/app/metadata";

const headers = {
  accept: "application/json; version=1",
  "Content-Type": "application/json",
  Authorization: `Basic ${btoa(`${process.env.NEXT_PUBLIC_ASHBY_API_KEY ?? ""}:`)}`,
};

type Job = {
  id: string;
  title: string;
  descriptionHtml: string;
  applyLink: string;
};

const getJob = async ({ ashby_jid }: { ashby_jid: string }): Promise<Job> => {
  const res = await fetch("https://api.ashbyhq.com/jobPosting.info", {
    method: "POST",
    headers,
    body: JSON.stringify({ jobPostingId: ashby_jid }),
  });
  const json = await res.json();
  return json.results;
};

export default async function CareersPage({
  searchParams,
}: {
  searchParams: Promise<{ ashby_jid?: string }>;
}) {
  const { ashby_jid } = await searchParams;

  if (!ashby_jid) {
    let jobs: { id: string; title: string }[] = [];
    try {
      const res = await fetch("https://api.ashbyhq.com/jobPosting.list", {
        method: "POST",
        headers,
        body: JSON.stringify({ listedOnly: true }),
      });
      const json = await res.json();
      jobs = json.results;
    } catch (err) {
      console.error(err);
      // redirect to ashby hosted page in case something goes wrong
      return redirect("https://jobs.ashbyhq.com/Braintrust");
    }

    return (
      <div className="mb-44 px-4 sm:px-8">
        <section className="my-16">
          <h1 className="mb-6 text-balance text-6xl font-medium tracking-tight">
            Build with us
          </h1>
          <p className="mb-12 max-w-screen-md text-2xl text-primary-900">
            Braintrust is a small team of builders passionate about empowering
            developers working with AI. We’re looking for highly independent,
            self-motivated, and creative people to join us.
          </p>
          <div className="-mx-3 sm:mx-0">
            {jobs.map((child, idx) => {
              return (
                <Link
                  key={idx}
                  href={`/careers?ashby_jid=${child.id}`}
                  className="mb-2 flex max-w-screen-md items-center justify-between rounded-md border p-3 text-lg font-medium transition-colors bg-transparent hover:bg-primary-100 hover:border-primary-200"
                >
                  {child.title}
                  <ArrowRight className="size-4 text-primary-500" />
                </Link>
              );
            })}
          </div>
        </section>
      </div>
    );
  }

  let job: Job | null = null;
  try {
    job = await getJob({ ashby_jid });
  } catch (err) {
    console.error(err);
    // redirect to ashby hosted page in case something goes wrong
    return redirect("https://jobs.ashbyhq.com/Braintrust");
  }

  if (!job) return notFound();

  return (
    <div className="px-4 pt-12 sm:px-8">
      <Link
        href="/careers"
        className="mb-2 flex items-center gap-1 text-sm underline-offset-4 text-primary-600 hover:underline"
      >
        <ArrowLeft className="size-3" /> Careers
      </Link>
      <div className="careers-page">
        <article
          className={cn(
            proseMDXBaseClassName,
            "mb-44 prose-h1:font-normal max-w-screen-md prose-xl font-inter prose-headings:font-medium prose-headings:font-planar",
          )}
        >
          <h1>{job.title}</h1>
          <div dangerouslySetInnerHTML={{ __html: job.descriptionHtml }} />
          <ApplyButton url={job.applyLink} />
        </article>
      </div>
    </div>
  );
}

export async function generateMetadata({
  searchParams,
}: {
  searchParams: Promise<{ ashby_jid?: string }>;
}) {
  const { ashby_jid } = await searchParams;

  let job: Job | null = null;
  if (ashby_jid) {
    try {
      job = await getJob({ ashby_jid });
    } catch (err) {
      // swallow
    }
  }

  if (!job) {
    return buildMetadata({
      title: "Careers",
      description:
        "Builders passionate about empowering developers working with AI",
      relativeUrl: "/careers",
    });
  }

  return buildMetadata({
    title: job.title,
    sections: ["Careers"],
    relativeUrl: `/careers?ashby_jid=${ashby_jid}`,
  });
}
