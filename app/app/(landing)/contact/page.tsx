import { PageTracker } from "#/ui/use-analytics";
import { type Metadata } from "next";
import { ContactClientPage } from "./clientpage";
import { buildMetadata } from "#/app/metadata";

export default function ContactPage() {
  return (
    <PageTracker category="contact">
      <ContactClientPage />
    </PageTracker>
  );
}

export const metadata: Metadata = buildMetadata({
  title: "Contact us",
  description:
    "Get in touch with the Braintrust team. Talk to our sales team about enterprise solutions or join our Discord community for support and discussions about AI development.",
  relativeUrl: "/contact",
  tags: ["contact", "support", "sales", "discord", "community"],
});
