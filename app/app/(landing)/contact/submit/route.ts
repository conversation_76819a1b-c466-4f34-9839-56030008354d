import { resend } from "#/lib/resend";
import { sendClayWebhookMessage } from "#/utils/clay/clay";

export async function POST(req: Request) {
  if (!resend) {
    return Response.json(
      { error: "Resend is not initialized" },
      { status: 500 },
    );
  }

  const body = await req.json();

  try {
    const { data, error } = await resend.emails.send({
      from: "Braintrust <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `Contact form submission from ${body.name} (${body.email})`,
      text: `
        ${body.name} (${body.email}) submitted a contact form with:
        ${body.comment}
      `,
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    await sendClayWebhookMessage({
      name: body.name,
      email: body.email,
      comment: body.comment,
      source: "contact-form",
    });

    return Response.json({ ok: true, ...data });
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}
