import AnalyticsProvider, { GoogleAnalytics } from "#/ui/analytics-provder";
import Header from "#/ui/landing/header";
import type { Viewport } from "next";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { ThemeProvider } from "#/ui/theme-provider";
import { LandingFooter } from "./landing-footer";
import { Toaster } from "sonner";
import { TooltipProvider } from "#/ui/tooltip";
import Script from "#/ui/script";
import { getNonce } from "#/security/csp";
import { ReactQueryProvider } from "#/ui/query-provider";

export default async function LandingLayout({
  children,
}: React.PropsWithChildren) {
  const session = await getServerAuthSession();

  return (
    <div
      id="landing-layout"
      className="h-full overflow-x-hidden font-planar antialiased transition-colors duration-500 bg-warm dark:bg-black"
    >
      <div className="flex min-h-full w-full flex-col">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          nonce={await getNonce()}
        >
          <TooltipProvider>
            <div
              className="mx-auto w-full max-w-landing"
              suppressHydrationWarning
            >
              <Header session={session} />
              <main className="z-10">
                <AnalyticsProvider>
                  <ReactQueryProvider>{children}</ReactQueryProvider>
                </AnalyticsProvider>
              </main>
            </div>
            <LandingFooter />
          </TooltipProvider>
        </ThemeProvider>
        <GoogleAnalytics />
        <Toaster />

        {/* Script for https://www.unifygtm.com/ tracking */}
        <Script id="unifygtm">
          {`!function(){window.unify||(window.unify=Object.assign([],["identify","page","startAutoPage","stopAutoPage","startAutoIdentify","stopAutoIdentify"].reduce((function(t,e){return t[e]=function(){return unify.push([e,[].slice.call(arguments)]),unify},t}),{})));var t=document.createElement("script");t.async=!0,t.setAttribute("src","https://tag.unifyintent.com/v1/P4HzpMVD461r39FmtUovLU/script.js"),t.setAttribute("data-api-key","wk_HrJcVefm_CyMhzwRBH6C2WXPMSARbWGQwbPzu9wqq"),t.setAttribute("id","unifytag"),(document.body||document.head).appendChild(t)}();`}
        </Script>
      </div>
    </div>
  );
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#000000" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};
