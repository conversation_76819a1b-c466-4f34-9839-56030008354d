"use client";
import { useEffect, useRef, useState } from "react";
import { <PERSON>con, Logo } from "#/ui/landing/logo";
import { ThemeToggle } from "./theme-toggle";
import { Fade } from "react-awesome-reveal";
import { cn } from "#/utils/classnames";
import { ArrowUpRight } from "lucide-react";
import Link from "next/link";

const footerConfig = [
  {
    title: "Resources",
    links: [
      {
        title: "Documentation",
        href: "/docs",
        description: "Complete guide to Braintrust AI evaluation platform",
      },
      {
        title: "Eval via UI",
        href: "/docs/start/eval-ui",
        description: "Get started with evaluations using our web interface",
      },
      {
        title: "Eval via SDK",
        href: "/docs/start/eval-sdk",
        description: "Integrate evaluations programmatically with our SDK",
      },
      {
        title: "Guides",
        href: "/docs/guides",
        description: "Step-by-step guides for advanced features",
      },
      {
        title: "Cookbook",
        href: "/docs/cookbook",
        description: "Code examples and practical recipes",
      },
      {
        title: "Changelog",
        href: "/docs/changelog",
        description: "Latest updates and feature releases",
      },
    ],
  },
  {
    title: "Company",
    links: [
      {
        title: "Pricing",
        href: "/pricing",
        description: "Flexible pricing plans for teams of all sizes",
      },
      {
        title: "Blog",
        href: "/blog",
        description: "Latest insights on AI evaluation and LLM best practices",
      },
      {
        title: "Careers",
        href: "/careers",
        description: "Join our team building the future of AI evaluation",
      },
      {
        title: "Contact us",
        href: "/contact",
        description: "Get in touch with our team",
      },
      {
        title: "Terms of Service",
        href: "/legal/terms-of-service",
        description: "Legal terms and conditions",
      },
      {
        title: "Privacy Policy",
        href: "/legal/privacy-policy",
        description: "How we protect your data",
      },
    ],
  },
  {
    title: "Community",
    links: [
      {
        title: "GitHub",
        href: "https://github.com/braintrustdata/",
        description: "Open source libraries and tools",
      },
      {
        title: "Discord",
        href: "https://discord.gg/6G8s47F44X",
        description: "Join our developer community",
      },
      {
        title: "LinkedIn",
        href: "https://www.linkedin.com/company/braintrust-data",
        description: "Follow us for company updates",
      },
      {
        title: "YouTube",
        href: "https://www.youtube.com/@BraintrustData",
        description: "Video tutorials and demos",
      },
      {
        title: "X",
        href: "https://twitter.com/braintrustdata",
        description: "Latest news and updates",
      },
    ],
  },
];

export const LandingFooter = () => {
  const [inView, setInView] = useState(false);

  const footerRef = useRef<HTMLDivElement>(null);
  const iconsRef = useRef<HTMLDivElement[]>([]);
  const mousePosition = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const requestRef = useRef<number | undefined>(undefined);

  const [disableBlur, setDisableBlur] = useState(true);

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Braintrust",
    url: "https://braintrust.dev",
    logo: "https://braintrust.dev/logo.png",
    description:
      "The enterprise-grade AI evaluation platform for building reliable LLM applications",
    foundingDate: "2023",
    industry: "Software Development",
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "AI Evaluation Services",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "AI Model Evaluation",
            description: "Comprehensive evaluation tools for LLM applications",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Dataset Management",
            description:
              "Scalable dataset storage and versioning for AI evaluations",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Prompt Engineering",
            description:
              "Interactive playgrounds for prompt development and optimization",
          },
        },
      ],
    },
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      url: "https://braintrust.dev/contact",
    },
    sameAs: [
      "https://github.com/braintrustdata/",
      "https://discord.gg/6G8s47F44X",
      "https://www.linkedin.com/company/braintrust-data",
      "https://www.youtube.com/@BraintrustData",
      "https://twitter.com/braintrustdata",
    ],
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Braintrust",
    url: "https://braintrust.dev",
    description:
      "The enterprise-grade AI evaluation platform for building reliable LLM applications",
    potentialAction: {
      "@type": "SearchAction",
      target: "https://braintrust.dev/docs?q={search_term_string}",
      "query-input": "required name=search_term_string",
    },
  };

  useEffect(() => {
    const landingLayout = document.getElementById("landing-layout");
    if (!landingLayout) return;
    if (inView) {
      landingLayout.classList.add("bg-limeOklch");
      landingLayout.classList.add("dark:bg-blueOklch");
      landingLayout.classList.remove("bg-warm");
      landingLayout.classList.remove("dark:bg-black");
    } else {
      landingLayout.classList.remove("bg-limeOklch");
      landingLayout.classList.remove("dark:bg-blueOklch");
      landingLayout.classList.add("bg-warm");
      landingLayout.classList.add("dark:bg-black");
    }
  }, [inView]);

  useEffect(() => {
    if (isSafari()) {
      return;
    }
    setDisableBlur(false);
    const handleMouseMove = (event: MouseEvent) => {
      const footer = footerRef.current;
      if (!footer) return;

      const rect = footer.getBoundingClientRect();
      mousePosition.current = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      };

      if (!requestRef.current) {
        requestRef.current = requestAnimationFrame(updateIconsRotation);
      }
    };

    const resetIconsRotation = () => {
      iconsRef.current.forEach((icon) => {
        icon.style.transform = "rotate(0deg)";
      });
    };

    const updateIconsRotation = () => {
      iconsRef.current.forEach((icon) => {
        const rect = icon.getBoundingClientRect();
        const iconCenter = {
          x:
            rect.left +
            rect.width / 2 -
            footerRef.current!.getBoundingClientRect().left,
          y:
            rect.top +
            rect.height / 2 -
            footerRef.current!.getBoundingClientRect().top,
        };
        const dx = mousePosition.current.x - iconCenter.x;
        const dy = mousePosition.current.y - iconCenter.y;
        const angle = Math.atan2(dy, dx) * (180 / Math.PI) - 135;

        icon.style.transform = `rotate(${angle}deg)`;
      });

      requestRef.current = undefined;
    };

    const footer = footerRef.current;
    footer?.addEventListener("mousemove", handleMouseMove);
    footer?.addEventListener("mouseleave", resetIconsRotation);
    return () => {
      footer?.removeEventListener("mousemove", handleMouseMove);
      footer?.removeEventListener("mouseleave", resetIconsRotation);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);

  return (
    <div className="pt-24 border-black/50 dark:border-white/50" ref={footerRef}>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <div className="z-10 mx-auto w-full max-w-landing bg-transparent">
        <div className="relative grid w-full grid-cols-5 px-4 sm:px-8 md:grid-cols-8 lg:grid-cols-12">
          <div className="col-span-3 flex h-20 items-end text-xl lg:text-2xl">
            <Link
              href="/signup"
              className="flex items-end gap-3 opacity-100 transition-opacity hover:opacity-50"
            >
              Get started <ArrowUpRight className="size-6" />
            </Link>
          </div>
          {Array.from({ length: 57 }).map((_, idx) => (
            <div
              key={idx}
              className={cn("flex flex-none h-20 items-end justify-start", {
                "blur-lg": !disableBlur && idx === 0,
                "blur-md": !disableBlur && idx === 1,
                "blur-[8px]": !disableBlur && idx === 2,
                "blur-sm": !disableBlur && idx === 3,
                "blur-[2px]": !disableBlur && idx === 4,
                "blur-[1px]": !disableBlur && idx === 5,
                "hidden md:flex": idx > 16 && idx < 37,
                "hidden lg:flex": idx >= 37,
              })}
            >
              <div
                ref={(el) => {
                  if (!el) return;
                  iconsRef.current[idx] = el;
                }}
              >
                <Icon size={24} />
              </div>
            </div>
          ))}
        </div>
        <footer className="z-20 flex px-4 py-24 sm:px-8" role="contentinfo">
          <div className="max-w-screen-landing flex flex-auto flex-col-reverse gap-16 text-sm lg:flex-row lg:gap-0">
            <section
              className="relative flex w-1/2 flex-col"
              aria-labelledby="brand-section"
            >
              <div className="bottom-0 left-0 mb-16 lg:absolute lg:mb-0">
                <Fade fraction={0} onVisibilityChange={setInView}>
                  <ThemeToggle />
                </Fade>
              </div>

              <Logo width={320} />
            </section>
            <nav
              className="flex flex-auto flex-col gap-16 lg:flex-row lg:gap-8"
              aria-label="Footer navigation"
            >
              {footerConfig.map((section, idx) => (
                <section
                  className="flex flex-1 flex-col gap-3 font-inter"
                  key={idx}
                  aria-labelledby={`footer-${section.title.toLowerCase()}`}
                >
                  <h3
                    id={`footer-${section.title.toLowerCase()}`}
                    className="font-medium"
                  >
                    {section.title}
                  </h3>
                  <ul className="flex flex-col gap-3">
                    {section.links.map((link, idx) => (
                      <li key={idx}>
                        <a
                          href={link.href}
                          className="transition-colors text-black/50 hover:text-black dark:text-white/50 dark:hover:text-white"
                          aria-describedby={
                            link.description ? `${link.href}-desc` : undefined
                          }
                          title={link.description}
                          {...(link.href.startsWith("http")
                            ? { target: "_blank", rel: "noopener noreferrer" }
                            : {})}
                        >
                          {link.title}
                        </a>
                        {link.description && (
                          <span id={`${link.href}-desc`} className="sr-only">
                            {link.description}
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                </section>
              ))}
            </nav>
          </div>
        </footer>
      </div>
    </div>
  );
};

const isSafari = () => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};
