"use client";

import { use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "jotai";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { cn } from "#/utils/classnames";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";
import {
  getPosition,
  SyncedPromptsProvider,
} from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { type SyncedPlaygroundBlock } from "#/ui/prompts/schema";
import { PlaygroundPromptBlocks } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playground-prompt-blocks";
import { PromptBlocksSynced } from "#/app/app/[org]/p/[project]/prompts/synced/prompt-blocks-synced";
import { LoggedOutPlaygroundHeader } from "./logged-out-playground-header";

import { Plus } from "lucide-react";
import { Button } from "#/ui/button";
import { BasicTooltip } from "#/ui/tooltip";
import { PlaygroundAddTask } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/add-task";
import { useAvailableModels } from "#/ui/prompts/models";
import { type JSONStructure } from "#/ui/prompts/hooks";
import {
  type LoggedOutPlaygroundData,
  LoggedOutPlaygroundTable,
} from "./logged-out-playground-table";
import Header from "#/ui/layout/header";
import { useOrg } from "#/utils/user";
import { Spinner } from "#/ui/icons/spinner";
import { useIsClient } from "#/utils/use-is-client";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { newId } from "braintrust";
import { useIndexedDBObject } from "#/utils/use-indexeddb-object";
import { DEFAULT_PLAYGROUND_SETTINGS } from "../app/[org]/p/[project]/playgrounds/settings";
import { UpsellProvider, useUpsellDialog } from "./upsell-dialog";
import { LoggedOutLoopProvider } from "./logged-out-loop-provider";
import { DockedChatSpacer } from "../app/[org]/p/[project]/playgrounds/[playground]/docked-chat-spacer";
import { motion } from "motion/react";

const SAVED_PROMPT_META = {};

const JSON_STRUCTURE: JSONStructure = {
  input: "VARCHAR",
  expected: "VARCHAR",
  metadata: "VARCHAR",
};

const PROMPT_EXTENSIONS_PARAMS = {
  jsonStructure: JSON_STRUCTURE,
  outputNames: [],
  expandInputVariables: true,
  disablePromptLinter: true,
};

export default function SimplePlayground() {
  const defaultPromptData = useDefaultPromptData();
  const [
    {
      playgroundBlocks,
      datasetRows,
      playgroundSettings,
      savedScorers,
      scorerFunctions,
    },
    setLoggedOutPlaygroundData,
  ] = useIndexedDBObject<LoggedOutPlaygroundData>({
    store: "loggedOutData",
    key: "playground",
    defaultValue: {
      playgroundBlocks: [
        {
          id: newId(),
          prompt_data: defaultPromptData,
          function_data: { type: "prompt" },
          _xact_id: "0",
        },
      ],
      datasetRows: [
        {
          id: `row-${Date.now()}`,
          input: null,
          expected: null,
          metadata: null,
        },
      ],
      playgroundSettings: DEFAULT_PLAYGROUND_SETTINGS,
      savedScorers: [],
      scorerFunctions: {},
    },
  });

  const { openUpsellDialog, upsellDialog } = useUpsellDialog();

  const playgroundBlocksSorted = useMemo(
    () =>
      playgroundBlocks.toSorted((a, b) =>
        (getPosition(a) ?? "").localeCompare(getPosition(b) ?? ""),
      ),
    [playgroundBlocks],
  );

  const saveSyncedPrompt = useCallback(
    async ({ id, val }: { id: string; val: SyncedPlaygroundBlock }) => {
      setLoggedOutPlaygroundData((data) => {
        const blocks = data.playgroundBlocks;
        const existingIndex = blocks.findIndex((block) => block.id === id);
        if (existingIndex >= 0) {
          const newBlocks = [...blocks];
          newBlocks[existingIndex] = val;
          return { ...data, playgroundBlocks: newBlocks };
        } else {
          return { ...data, playgroundBlocks: [...blocks, val] };
        }
      });
      return null; // No transaction ID for local edit
    },
    [setLoggedOutPlaygroundData],
  );

  const deletePrompt = useCallback(
    async (id: string) => {
      setLoggedOutPlaygroundData((data) => ({
        ...data,
        playgroundBlocks: data.playgroundBlocks.filter(
          (block) => block.id !== id,
        ),
      }));
      return null; // No transaction ID for local edit
    },
    [setLoggedOutPlaygroundData],
  );

  const addTaskButton = useMemo(
    () => (
      <PlaygroundAddTask
        numTasks={playgroundBlocks.length}
        projectName=""
        disabledOriginIds={[]}
      >
        <div>
          <BasicTooltip tooltipContent="Add task">
            <Button
              Icon={Plus}
              variant="default"
              size="xs"
              className="rounded-full"
            />
          </BasicTooltip>
        </div>
      </PlaygroundAddTask>
    ),
    [playgroundBlocks.length],
  );

  const org = useOrg();
  const { allAvailableModels, configuredModelsByProvider, modelsByProvider } =
    useAvailableModels({
      orgName: org.name,
    });
  const isClient = useIsClient();
  const mainPaneScrollContainer = useRef<HTMLDivElement>(null);
  const upsellContextValue = useMemo(
    () => ({ onUpsell: () => openUpsellDialog() }),
    [openUpsellDialog],
  );

  return (
    <div className="h-screen w-screen items-center justify-center">
      <Header
        redirectPaths={{
          signUp: "/app/setup?referrer=playground",
          signIn: "/app/create-playground",
        }}
        signupTrackingSource="logged_out_playground"
      />
      <JotaiProvider>
        <SyncedPromptsProvider
          externalValue={playgroundBlocks}
          save={saveSyncedPrompt}
          allAvailableModels={allAvailableModels}
        >
          <LoggedOutLoopProvider openUpsellDialog={openUpsellDialog}>
            <motion.div layout className="flex w-full ">
              <MainContentWrapper
                className={cn(
                  "flex flex-col overflow-hidden py-0 flex-1",
                  HEIGHT_WITH_TOP_OFFSET,
                )}
                hideFooter
              >
                <UpsellProvider value={upsellContextValue}>
                  <LoggedOutPlaygroundHeader
                    openUpsellDialog={openUpsellDialog}
                  />
                  <div
                    ref={mainPaneScrollContainer}
                    className="-mx-3 flex flex-1 flex-col overflow-auto px-3"
                  >
                    {isClient ? (
                      <>
                        <PlaygroundPromptBlocks>
                          <PromptBlocksSynced
                            datasetId={undefined}
                            isReadOnly={false}
                            projectId={undefined}
                            orgName={org.name}
                            // If the user is logged in, use their actual configured models. Otherwise, show every model we support.
                            modelOptionsByProvider={
                              org.id
                                ? configuredModelsByProvider
                                : modelsByProvider
                            }
                            deletePrompt={deletePrompt}
                            savedPromptMeta={SAVED_PROMPT_META}
                            runPrompts={openUpsellDialog}
                            diffEnabled={false}
                            addPromptButton={addTaskButton}
                            allAvailableModels={allAvailableModels}
                            datasetJsonStructure={JSON_STRUCTURE}
                            promptExtensionsParams={PROMPT_EXTENSIONS_PARAMS}
                          />
                          {playgroundBlocks.length > 0 && (
                            <div className="flex flex-none items-center pl-0.5">
                              {addTaskButton}
                            </div>
                          )}
                        </PlaygroundPromptBlocks>
                        <LoggedOutPlaygroundTable
                          promptBlocks={playgroundBlocksSorted}
                          scrollContainerRef={mainPaneScrollContainer}
                          allAvailableModels={allAvailableModels}
                          datasetRows={datasetRows}
                          playgroundSettings={playgroundSettings}
                          savedScorers={savedScorers}
                          scorerFunctions={scorerFunctions}
                          setLoggedOutPlaygroundData={
                            setLoggedOutPlaygroundData
                          }
                          openUpsellDialog={openUpsellDialog}
                        />
                      </>
                    ) : (
                      <div className="flex size-full items-center justify-center">
                        <Spinner />
                      </div>
                    )}
                  </div>
                </UpsellProvider>
                {upsellDialog}
              </MainContentWrapper>
              <DockedChatSpacer />
            </motion.div>
          </LoggedOutLoopProvider>
        </SyncedPromptsProvider>
      </JotaiProvider>
    </div>
  );
}
