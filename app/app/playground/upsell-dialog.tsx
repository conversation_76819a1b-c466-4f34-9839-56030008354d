import { useAnalytics } from "#/ui/analytics/segment-analytics";
import { buttonVariants } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { Icon } from "#/ui/landing/logo";
import { BlueLink } from "#/ui/link";
import { signInPath, signUpPath } from "#/utils/auth/redirects";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { createContext, useContext, useState } from "react";

const UpsellContext = createContext<{
  onUpsell?: () => void;
}>({});
export const UpsellProvider = UpsellContext.Provider;
export const useUpsellContext = () => useContext(UpsellContext);

export function useUpsellDialog() {
  const [open, setOpen] = useState(false);

  return {
    openUpsellDialog: () => setOpen(true),
    upsellDialog: <UpsellDialog open={open} onOpenChange={setOpen} />,
  };
}

function UpsellDialog({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const { track } = useAnalytics();
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent hideCloseButton className="w-full !max-w-sm p-5">
        <DialogHeader className="flex flex-col items-center gap-3">
          <Icon size={32} />
          <DialogTitle className="text-xl">
            Sign in to continue exploring
          </DialogTitle>
          <DialogDescription className="mt-4 text-center">
            To run playgrounds, manage{" "}
            <BlueLink href="/docs/guides/datasets">datasets</BlueLink>, save{" "}
            <BlueLink href="/docs/guides/functions/prompts">
              versioned prompts
            </BlueLink>{" "}
            and <BlueLink href="/docs/guides/functions/agents">agents</BlueLink>
            , optimize with <BlueLink href="/docs/guides/loop">Loop</BlueLink>,{" "}
            and more, sign in to Braintrust.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="pt-4">
          <Link
            className={cn(buttonVariants({ size: "sm" }), "flex-1")}
            href={signInPath({ redirectPath: "/app/create-playground" })}
          >
            Sign in
          </Link>
          <Link
            className={cn(
              buttonVariants({ size: "sm", variant: "inverted" }),
              "flex-1",
            )}
            onClick={() => {
              track("signup", {
                source: "logged_out_playground",
              });
            }}
            href={signUpPath({
              redirectPath: "/app/setup?referrer=playground",
            })}
          >
            Sign up for free
          </Link>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
