import { FileQuestion } from "lucide-react";
import Footer from "#/ui/landing/footer";

export default function NotFound() {
  return (
    <div className="relative flex min-h-screen w-full flex-col p-8">
      <div className="flex flex-1 flex-col items-center px-8 py-36">
        <FileQuestion className="z-10 mb-6 size-6 text-primary-800" />
        <div className="z-0">
          <div className="mb-4 text-center font-planar text-5xl font-medium text-primary-800">
            404 Page not found
          </div>
        </div>
        <div className="z-10">
          <div className="my-6 max-w-md text-pretty text-center text-primary-500">
            The page you are looking for does not exist. Please check the URL
            and try again.
          </div>
        </div>
      </div>
      <div className="grow" />
      <Footer inApp className="!pb-0" />
    </div>
  );
}
