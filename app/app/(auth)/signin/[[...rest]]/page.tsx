"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { Google } from "#/ui/icons/providers";
import { Input } from "#/ui/input";
import { API_KEY_AUTH } from "#/utils/auth/constants";
import { getRedirectParam } from "#/utils/auth/redirect";
import { SignIn, useSignIn } from "@clerk/nextjs";
import Link from "next/link";
import { redirect } from "next/navigation";
import { use } from "react";

export default function SignInPage(props: {
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  const searchParams = use(props.searchParams);
  const redirectPath = getRedirectParam(searchParams);
  const forceRedirectUrl = `/redirects/after-signin${
    redirectPath ? `?redirect_url=${encodeURIComponent(redirectPath)}` : ""
  }`;

  const { isLoaded } = useSignIn();

  if (API_KEY_AUTH) {
    return redirect("/signin-apikey");
  }

  // Show placeholder while Clerk is loading
  if (!isLoaded) {
    return (
      <div className="flex flex-col pb-4 pt-8 leading-[1.38462]">
        <h1 className="mb-8 text-balance text-left font-planar text-4xl font-medium leading-tight tracking-[-0.01em] text-primary-900 sm:text-5xl">
          Sign in to Braintrust
        </h1>
        <div className="mb-8 flex flex-col gap-2">
          <label>Email address</label>
          <Input
            type="email"
            disabled
            placeholder="Enter email address"
            className="h-11 text-base"
          />
        </div>
        <Button variant="primary" disabled className="mb-6 h-11 text-base">
          Continue
        </Button>
        <div className="mb-6 flex items-center gap-4">
          <span className="flex-1 border-b" />
          <span className="text-xs uppercase text-primary-400">Or</span>
          <span className="flex-1 border-b" />
        </div>
        <Button
          variant="default"
          disabled
          className="mb-10 h-11 gap-3 border text-sm font-medium"
        >
          <Google size={20} />
          Continue with Google
        </Button>
        <div className="text-sm text-primary-500">
          Don&apos;t have an account?{" "}
          <Link href="/signup" className="font-medium text-accent-700">
            Sign up
          </Link>
        </div>
      </div>
    );
  }

  return (
    <SignIn
      // NOTE: We could have different semantics for what we do when someone
      // signs in vs. signs up, but we just don't right now.
      forceRedirectUrl={forceRedirectUrl}
      signUpForceRedirectUrl={forceRedirectUrl}
      signUpUrl="/signup"
    />
  );
}
