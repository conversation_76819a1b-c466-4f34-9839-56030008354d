/* eslint-disable @typescript-eslint/consistent-type-assertions */

import { NextResponse } from "next/server";
import Orb from "orb-billing";

import {
  type SubscriptionPlanChangedPayload,
  type OrbWebhookPayload,
  type SubscriptionStartedPayload,
  type SubscriptionUsageExceededPayload,
} from "./types";

import { setResourceLimits } from "./handlers/resource-limits";
import { setPlanId } from "./handlers/plan-id";
import {
  captureSentryServerException,
  captureSentryServerMessage,
} from "#/utils/sentry/sentry";
import {
  isFreeSubscription,
  isProSubscription,
} from "#/app/app/[org]/settings/billing/plans";
import {
  sendUsageExceededAlertToSentry,
  sendUsageExceededEmail,
} from "./handlers/usage-exceeded";
import { sendClayWebhookMessage } from "#/utils/clay/clay";

let orb: Orb | null = null;
if (process.env.ORB_API_KEY && process.env.ORB_WEBHOOK_SECRET) {
  orb = new Orb({
    apiKey: process.env.ORB_API_KEY,
    webhookSecret: process.env.ORB_WEBHOOK_SECRET,
  });
}

async function sendNewSubscriptionAlerts(subscription: Orb.Subscription) {
  if (subscription) {
    const customerName = subscription?.customer?.name;
    const customerEmail = subscription?.customer?.email;
    const isProPlan = isProSubscription(subscription);
    const isFreePlan = isFreeSubscription(subscription);

    let message;
    let tag;
    if (isProPlan) {
      message = `Pro plan activated for ${customerName ?? "Unknown customer"} 🚀🚀🚀`;
      tag = "new-pro-subscription";
    } else if (isFreePlan) {
      message = `Free plan activated for ${customerName ?? "Unknown customer"} 🚀`;
      tag = "new-free-subscription";
    }

    if (message && tag) {
      captureSentryServerMessage({
        message,
        tags: {
          page: tag,
        },
      });

      await sendClayWebhookMessage({
        name: customerName,
        email: customerEmail,
        source: isProPlan ? "new-pro-org" : "new-free-org",
      });
    }
  }
}

export async function POST(request: Request) {
  if (!orb) {
    return NextResponse.json(
      {
        error:
          "Orb not initialized. Set ORB_API_KEY and ORB_WEBHOOK_SECRET environment variables.",
      },
      { status: 500 },
    );
  }

  try {
    const body = await request.text();

    const payload = orb.webhooks.unwrap(
      body,
      request.headers,
      process.env.ORB_WEBHOOK_SECRET,
    ) as OrbWebhookPayload;

    console.log(JSON.stringify(payload, null, 2));

    switch (payload.type) {
      case "subscription.started": {
        const event = payload as SubscriptionStartedPayload;
        await Promise.all([
          setResourceLimits({ event, allowLowerLimits: false }),
          setPlanId(event),
        ]);

        const isBackfill = event?.subscription?.metadata?.isBackfill === "true";
        if (!isBackfill) {
          await sendNewSubscriptionAlerts(event.subscription);
        }

        break;
      }
      case "subscription.plan_changed": {
        const event = payload as SubscriptionPlanChangedPayload;
        const isSamePlan =
          event.properties?.previous_plan_id === event?.subscription?.plan?.id;

        await Promise.all([
          setResourceLimits({
            event,
            allowLowerLimits:
              isFreeSubscription(event.subscription) && !isSamePlan,
          }),
          setPlanId(event),
        ]);

        if (!isSamePlan) {
          await sendNewSubscriptionAlerts(event.subscription);
        }

        break;
      }
      case "subscription.usage_exceeded": {
        const event = payload as SubscriptionUsageExceededPayload;
        sendUsageExceededAlertToSentry(event);
        await sendUsageExceededEmail(event);
        break;
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Webhook error:", error);

    captureSentryServerException({
      error: error instanceof Error ? error : String(error),
      tags: {
        page: "orb-webhook-error",
      },
    });
    return NextResponse.json(
      { error: `Webhook handler failed: ${(error as Error).message}` },
      { status: 500 },
    );
  }
}
