import * as React from "react";

import { z } from "zod";
import { resend } from "#/lib/resend";

import {
  isFreePlan,
  isMonthlyLogsIngestedGBId,
  isProPlan,
  isScoresAndCustomMetricsId,
} from "#/app/app/[org]/settings/billing/plans";
import { captureSentryServerMessage } from "#/utils/sentry/sentry";

import { type SubscriptionUsageExceededPayload } from "../types";
import UsageNotification from "#/ui/email-templates/usage-notification";
import { getResourceLimits } from "#/app/app/[org]/settings/billing/actions";

function getPercentUsed({
  metric,
  quantityThreshold,
}: {
  metric: "scores_and_custom_metrics" | "log_bytes";
  quantityThreshold: number;
}): 80 | 90 | 100 | undefined {
  const thresholds: Record<
    "scores_and_custom_metrics" | "log_bytes",
    Record<number, 80 | 90 | 100>
  > = {
    scores_and_custom_metrics: {
      8000: 80,
      9000: 90,
      10000: 100,
    },
    log_bytes: {
      0.8: 80,
      0.9: 90,
      1: 100,
    },
  };

  const value = thresholds[metric]?.[quantityThreshold];
  if (value === 80 || value === 90 || value === 100) {
    return value;
  }
  return undefined;
}

function getEmailSubject({
  metric,
  percentUsed,
}: {
  metric: "scores_and_custom_metrics" | "log_bytes";
  percentUsed: 80 | 90 | 100;
}): string {
  const metricLabel =
    metric === "scores_and_custom_metrics"
      ? "scores and custom metrics"
      : "processed data";

  if (percentUsed === 100) {
    return `Action needed: ${metricLabel} monthly usage limit reached`;
  }

  return `Heads up: You've used ${percentUsed}% of your monthly ${metricLabel} limit`;
}

const usageExceededEventSchema = z.object({
  subscription: z.object({
    customer: z.object({
      name: z.string(),
      email: z.string(),
      external_customer_id: z.string(),
    }),
    plan: z.object({
      id: z.string(),
    }),
  }),
  properties: z.object({
    billable_metric_id: z.string(),
    quantity_threshold: z.number(),
    alert_configuration: z.object({
      id: z.string(),
    }),
  }),
});

function getMetricType(
  metricId: string,
): "scores_and_custom_metrics" | "log_bytes" | undefined {
  if (isScoresAndCustomMetricsId(metricId)) return "scores_and_custom_metrics";
  if (isMonthlyLogsIngestedGBId(metricId)) return "log_bytes";
  return undefined;
}

function sendAlert({
  customerName,
  planId,
  quantityThreshold,
  metric,
}: {
  customerName: string;
  planId: string;
  metric: "scores_and_custom_metrics" | "log_bytes";
  quantityThreshold: number;
}) {
  const isPro = isProPlan(planId);
  const isFree = isFreePlan(planId);

  const metricLabel =
    metric === "scores_and_custom_metrics"
      ? "Scores and Custom Metrics"
      : "Log Bytes";

  let planLabel;
  if (isPro) {
    planLabel = "Pro plan";
  } else if (isFree) {
    planLabel = "Free plan";
  } else {
    planLabel = "Unknown plan";
  }

  const message = `Usage exceeded on ${metricLabel} for ${planLabel}. Threshold: ${quantityThreshold}. Customer: ${customerName}`;
  captureSentryServerMessage({
    message,
    tags: {
      page: "orb-usage-exceeded",
    },
  });
}

/**
 * Sends a Sentry alert if a usage threshold is exceeded.
 * Currently only triggers for the Free plan when 'Scores and Custom Metrics' is exceeded.
 */
export function sendUsageExceededAlertToSentry(
  event: SubscriptionUsageExceededPayload,
) {
  const parsedEvent = usageExceededEventSchema.safeParse(event);
  if (!parsedEvent.success) {
    console.error(
      "Invalid event payload for usage exceeded:",
      parsedEvent.error,
    );
    return;
  }

  const { subscription, properties } = parsedEvent.data;

  // Only send alerts for free plans for now
  if (!isFreePlan(subscription.plan.id)) {
    return;
  }

  const metricId = properties.billable_metric_id;
  const metric = getMetricType(metricId);
  if (!metric) return;

  sendAlert({
    customerName: subscription.customer.name,
    planId: subscription.plan.id,
    metric,
    quantityThreshold: properties.quantity_threshold,
  });
}

export async function sendUsageExceededEmail(
  event: SubscriptionUsageExceededPayload,
) {
  if (!resend) {
    console.error(
      "Resend is not initialized, cannot send usage exceeded email",
    );
    return;
  }

  const parsedEvent = usageExceededEventSchema.safeParse(event);
  if (!parsedEvent.success) {
    console.error(
      "Invalid event payload for usage exceeded email:",
      parsedEvent.error,
    );
    return;
  }

  const { subscription, properties } = parsedEvent.data;

  // Only send usage alert emails to free plans for now
  if (!isFreePlan(subscription.plan.id)) {
    return;
  }

  const resourceLimits = await getResourceLimits({
    orgId: subscription.customer.external_customer_id,
  });

  // If this is a Free plan with unlimited log bytes, we likely have a clerical error, and we should not send an email
  const limits = resourceLimits?.num_log_bytes_calendar_months ?? null;
  if (limits === null) {
    console.error(
      "Resource limits are null for organization: ",
      subscription.customer.external_customer_id,
      "Skipping email",
    );
    return;
  }

  const orgName = subscription.customer.name;

  const metricId = properties.billable_metric_id;
  const metric = getMetricType(metricId);
  if (!metric)
    throw new Error(
      `Missing or invalid metricId/billable_metric_id for usage exceeded email. Customer: ${orgName}`,
    );

  const quantityThreshold = properties.quantity_threshold;
  if (quantityThreshold === undefined || quantityThreshold === null) {
    throw new Error(
      `Missing or invalid quantity_threshold for usage exceeded email. Customer: ${orgName}`,
    );
  }

  const percentUsed = getPercentUsed({ metric, quantityThreshold });
  if (!percentUsed) {
    throw new Error(
      `Missing or invalid percentUsed for usage exceeded email.  Skipping email for organization: ${orgName}`,
    );
  }

  const emailAddress = subscription.customer.email;
  if (!emailAddress) {
    throw new Error(
      `Missing or invalid customer.email for usage exceeded email. Skipping email for organization: ${orgName}`,
    );
  }

  const { error } = await resend.emails.send(
    {
      from: "Braintrust <<EMAIL>>",
      to: emailAddress,
      subject: getEmailSubject({ metric, percentUsed }),
      react: React.createElement(
        UsageNotification,
        {
          usageType: metric,
          percentUsed,
          organizationName: orgName,
        },
        null,
      ),
    },
    {
      // Prevents duplicate emails from being sent. Resend will not send the email if the idempotencyKey has been used within the last 24 hours.
      idempotencyKey: `${properties.alert_configuration.id}-${orgName}-${percentUsed}`,
    },
  );

  if (error) {
    throw new Error(error.message);
  }
}
