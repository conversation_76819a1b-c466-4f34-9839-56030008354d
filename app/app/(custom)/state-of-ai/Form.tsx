"use client";

import { Input } from "#/ui/input";
import { But<PERSON> } from "#/ui/button";
import { useRouter } from "next/navigation";
import { RadioGroup, RadioGroupItem } from "#/ui/radio";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormMessage,
  FormLabel,
  FormItem,
  FormField,
} from "#/ui/form";
import TextArea from "#/ui/text-area";

const registrationFormSchema = z.object({
  name: z.string().min(1),
  company: z.string().min(1),
  entree: z.string().min(1),
  comment: z.string().optional(),
});

export const StateOfAIForm = ({
  isInvited,
  email,
}: {
  isInvited: boolean;
  email?: string;
}) => {
  const router = useRouter();

  const form = useForm<z.infer<typeof registrationFormSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(registrationFormSchema),
    defaultValues: {
      name: "",
      company: "",
      entree: "chicken",
      comment: "",
    },
  });

  if (email && !isInvited) {
    return (
      <div className="text-2xl">
        {email} is not on the list of invited guests. If you believe this is an
        error, please contact us at{" "}
        <a href="mailto:<EMAIL>"><EMAIL></a>.
      </div>
    );
  }

  if (email && isInvited) {
    return (
      <>
        <div className="mb-4 text-2xl">
          Please register to confirm your attendance.
        </div>
        <Form {...form}>
          <form
            className="flex w-full max-w-md flex-col items-start gap-4"
            onSubmit={form.handleSubmit(async (data) => {
              try {
                const res = await fetch("/state-of-ai/submit", {
                  method: "POST",
                  body: JSON.stringify({
                    name: data.name,
                    email,
                    comment: `May 1 event registration

Name: ${data.name}
Email: ${email}
Company: ${data.company}
Entree: ${data.entree}
Comment: ${data.comment}
`,
                  }),
                  headers: {
                    "Content-Type": "application/json",
                  },
                });
                const json = await res.json();

                if (!json.ok) {
                  toast.error(
                    "Something went wrong. Please email <NAME_EMAIL>.",
                  );
                  return;
                }

                toast.success(
                  "Thank you for registering. We'll be in touch soon.",
                );
                router.replace("/state-of-ai", { scroll: false });
              } catch (error) {
                toast.error(
                  "Something went wrong. Please email <NAME_EMAIL>.",
                );
              }
            })}
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xl">Full name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      id="name"
                      placeholder="Enter full name"
                      className="h-auto flex-1 text-xl border-black/20"
                      style={{ background: "none" }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xl">Company</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      id="company"
                      placeholder="Enter company"
                      className="h-auto flex-1 text-xl border-black/20"
                      style={{ background: "none" }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="entree"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xl">Dinner entree</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <label className="flex items-center gap-2">
                        <RadioGroupItem
                          value="chicken"
                          // className="bg-blue-500"
                        />
                        <div>
                          Artichoke, ricotta, and lemon stuffed chicken (nut
                          free)
                        </div>
                      </label>
                      <label className="flex items-center gap-2">
                        <RadioGroupItem
                          value="beef"
                          // className="bg-blue-500"
                        />
                        <div>
                          Pan seared filet mignon (gluten free, nut free)
                        </div>
                      </label>
                      <label className="flex items-center gap-2">
                        <RadioGroupItem
                          value="vegetarian"
                          // className="bg-blue-500"
                        />
                        <div>
                          Stuffed bell pepper with rice, zucchini, sun-dried
                          tomatoes, and olives (gluten free, vegan, nut free)
                        </div>
                      </label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem className="mb-4 w-full">
                  <FormLabel className="text-xl">Comment</FormLabel>
                  <FormControl>
                    <TextArea
                      className="w-full text-base bg-transparent border-black/20 dark:bg-transparent"
                      placeholder="Enter comment (optional)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="flex-none border-0 text-xl bg-black text-warm disabled:bg-black/50 dark:bg-black"
              isLoading={form.formState.isSubmitting}
              disabled={!form.formState.isValid}
            >
              Register
            </Button>
          </form>
        </Form>
      </>
    );
  }

  return (
    <>
      <div className="text-2xl">
        The in-person event is open to invited guests only. Please enter your
        email to gain access to the event.
      </div>
      <form
        className="flex w-full max-w-md gap-4"
        onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.currentTarget);
          const email = formData.get("email");
          if (email) {
            router.push(`/state-of-ai?email=${email}`, { scroll: false });
          }
        }}
      >
        <Input
          type="email"
          id="name"
          name="email"
          placeholder="Enter email address"
          className="h-auto flex-1 text-xl border-black/20"
          style={{ background: "none" }}
        />
        <Button
          type="submit"
          className="flex-none border-0 text-xl bg-black text-warm"
        >
          Get access
        </Button>
      </form>
    </>
  );
};
