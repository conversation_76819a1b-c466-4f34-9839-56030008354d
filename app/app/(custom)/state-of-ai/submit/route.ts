import { resend } from "#/lib/resend";
import * as ics from "ics";

export async function POST(req: Request) {
  if (!resend) {
    return Response.json(
      { error: "Resend is not initialized" },
      { status: 500 },
    );
  }

  const body = await req.json();

  try {
    const { error: icsError, value: icsContent } = ics.createEvent({
      start: [2025, 5, 2, 0, 0],
      startInputType: "utc",
      duration: { hours: 4 },
      title: "The State of AI",
      description: "A dinner and discussion about the future of AI",
      location: "Pearl SF, 601 19th Street, San Francisco CA 94607",
      attendees: [
        {
          name: body.name,
          email: body.email,
        },
      ],
      organizer: {
        name: "Braintrust",
        email: "<EMAIL>",
      },
    });

    if (icsError) {
      return Response.json(
        { error: icsError, type: "icsError" },
        { status: 500 },
      );
    }

    const { error: emailToBraintrustError } = await resend.emails.send({
      from: "Braintrust <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `Contact form submission from ${body.name} (${body.email})`,
      text: `
        ${body.name} (${body.email}) submitted a contact form with:
        ${body.comment}
      `,
    });

    if (emailToBraintrustError) {
      return Response.json(
        { error: emailToBraintrustError, type: "emailToBraintrustError" },
        { status: 500 },
      );
    }

    const { error: emailToAttendeeError } = await resend.emails.send({
      from: "Braintrust <<EMAIL>>",
      to: [body.email],
      subject: `State of AI registration confirmed`,
      text: `You are confirmed for Braintrust's State of AI on May 1, 2025 at 5pm.`,
      attachments: [
        {
          filename: "state-of-ai.ics",
          content: icsContent,
        },
      ],
    });

    if (emailToAttendeeError) {
      return Response.json(
        { error: emailToAttendeeError, type: "emailToAttendeeError" },
        { status: 500 },
      );
    }

    return Response.json({ ok: true });
  } catch (error) {
    console.error(error);
    return Response.json({ error, type: "unknownError" }, { status: 500 });
  }
}
