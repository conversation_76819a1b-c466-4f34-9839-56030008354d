import { GoogleAnalytics } from "#/ui/analytics-provder";
import type { Metadata, Viewport } from "next";
import { ThemeProvider } from "#/ui/theme-provider";
import { Toaster } from "sonner";
import { TooltipProvider } from "#/ui/tooltip";
import { Icon, Logo } from "#/ui/landing/logo";
import Image from "next/image";
import soai from "./soai-riso.png";
import localFont from "next/font/local";
import soaiLogo from "./soai-logo.svg";
import { StateOfAIForm } from "./Form";
import { resend } from "#/lib/resend";
import alex from "./alex.png";
import malte from "./malte.png";
import simon from "./simon.png";
import sarah from "./sarah.png";
import sualeh from "./sualeh.png";
import rita from "./rita.png";
import sydney from "./sydney.png";
import gautam from "./gautam.png";
import Link from "next/link";
import { getNonce } from "#/security/csp";

const btSans = localFont({
  src: [
    {
      path: "./btsans-regular.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "./btsans-semibold.otf",
      weight: "550",
      style: "bold",
    },
  ],
  display: "swap",
  variable: "--font-bt-sans",
});

export default async function StateOfAI({
  searchParams,
}: {
  searchParams: Promise<{
    email?: string;
  }>;
}) {
  const { email } = await searchParams;

  // Get list of contacts in audience
  const list =
    resend &&
    email &&
    (await resend.contacts.list({
      audienceId: "43f601a2-52d6-4ae7-9762-c2d2542767e4",
    }));

  // Check if email is in list
  const isInvited =
    list && list?.data?.data.some((contact) => contact.email === email);

  const nonce = await getNonce();

  return (
    <ThemeProvider forcedTheme="light" nonce={nonce}>
      <div
        id="landing-layout"
        className={`min-h-screen overflow-x-hidden font-btSans antialiased transition-colors duration-500 bg-warm text-black dark:bg-warm dark:text-black ${btSans.variable}`}
      >
        <div className="flex min-h-full w-full flex-col">
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            nonce={nonce}
          >
            <TooltipProvider>
              <main>
                <div className="relative mx-auto w-full max-w-4xl">
                  <div className="flex items-center justify-between px-5 py-8">
                    <Link href="/" className="z-50 block cursor-pointer">
                      <Logo width={150} />
                    </Link>
                    <Icon />
                  </div>
                  <Image
                    src={soaiLogo}
                    alt="State of AI"
                    className="z-10 w-4/5 max-w-xl pt-[100px]"
                    width={1000}
                    height={1000}
                  />
                  <Image
                    src={soai}
                    alt="State of AI"
                    className="absolute -top-36 -mr-4 ml-8 mix-blend-darken [clip-path:inset(0_5px)]"
                    width={1000}
                    height={1000}
                  />
                  <div className="flex flex-col items-end gap-3 px-5 pt-36 text-4xl font-[550]">
                    <div>San Francisco</div>
                    <div>May 1, 2025</div>
                    <div>5 - 9 PM</div>
                  </div>
                  <div className="max-w-2xl text-balance px-5 pt-24 text-4xl">
                    Conversations with some of the leading builders in AI.
                  </div>
                </div>
                <div className="z-40 mx-auto flex w-full max-w-7xl flex-wrap justify-center gap-6 px-5 py-24 text-4xl">
                  <Speaker name="Simon Last" title="Co-founder, Notion">
                    <Image src={simon} alt="Simon Last" />
                  </Speaker>
                  <Speaker name="Malte Ubl" title="CTO, Vercel">
                    <Image src={malte} alt="Malte Ubl" />
                  </Speaker>
                  <Speaker name="Alex Konrad" title="Founder, Upstarts Media">
                    <Image src={alex} alt="Alex Konrad" />
                  </Speaker>
                  <Speaker
                    name="Sydney Sarachek"
                    title="Senior Director, AI, NBA"
                  >
                    <Image src={sydney} alt="Sydney Sarachek" />
                  </Speaker>
                  <Speaker
                    name="Sualeh Asif"
                    title="Co-founder and CPO, Anysphere"
                  >
                    <Image src={sualeh} alt="Sualeh Asif" />
                  </Speaker>
                  <Speaker
                    name="Rita Kozlov"
                    title="VP, Developers & AI, Cloudflare"
                  >
                    <Image src={rita} alt="Rita Kozlov" />
                  </Speaker>
                  <Speaker name="Sarah Sachs" title="AI Lead, Notion">
                    <Image src={sarah} alt="Sarah Sachs" />
                  </Speaker>
                  <Speaker name="Gautam Kedia" title="ML Leader, Stripe">
                    <Image src={gautam} alt="Gautam Kedia" />
                  </Speaker>
                </div>
                <div className="mx-auto w-full max-w-4xl">
                  <div className="max-w-2xl text-pretty px-5 py-24 text-4xl">
                    We&apos;re bringing together a select group of innovators
                    shaping the future of AI. Join us for dinner and
                    thought-provoking panels from industry leaders.
                  </div>
                  <div className="mb-24 flex max-w-xl flex-col items-start gap-4 px-5">
                    <StateOfAIForm isInvited={!!isInvited} email={email} />
                  </div>
                </div>
              </main>
            </TooltipProvider>
          </ThemeProvider>
          <GoogleAnalytics />
          <Toaster />
        </div>
      </div>
    </ThemeProvider>
  );
}

const Speaker = ({
  name,
  title,
  children,
}: {
  name: string;
  title: string;
  children: React.ReactNode;
}) => {
  return (
    <div className="z-40 flex w-48 flex-col items-start bg-warm md:w-56">
      {children}
      <div className="pt-4 text-2xl">{name}</div>
      <div className="text-base opacity-50">{title}</div>
    </div>
  );
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#000000" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};

export const metadata: Metadata = {
  title: "State of AI - May 1, 2025 - Braintrust",
  description: "A dinner and discussion about the future of AI",
  openGraph: {
    title: "State of AI - May 1, 2025 - Braintrust",
    description: "A dinner and discussion about the future of AI",
    images: [{ url: "/soai/og.png" }],
  },
};
