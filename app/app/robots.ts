import type { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: "*",
      allow: "/",
      disallow: [
        "/app/*",
        "/api/*",
        "/_next/*",
        "/_vercel/*",
        "/admin/*",
        "/private/*",
        "/temp/*",
        "/tmp/*",
        "*.json$",
        "*.xml$",
        "*.csv$",
        "*.sql$",
        "*.log$",
      ],
    },
    sitemap: "https://www.braintrust.dev/sitemap.xml",
  };
}
