---
title: Architecture
---

import Image from 'next/image';

# Architecture

Braintrust allows you to log raw data while you run your AI applications, including inputs, outputs, and prompts.
Because of the sensitivity of this data, we support running the logging stack in your AWS account, ensuring that
data never leaves your account, and never flows through Braintrust's infrastructure. This component is referred to
as the _data plane_.

At its core, the data plane deployment works by installing an API layer along with a database in your environment.
On [AWS](/docs/self-hosting/aws), this is packaged as a few [AWS Lambda](https://aws.amazon.com/lambda/) function, a [Postgres database](https://www.postgresql.org/),
and a few other services in a [VPC](https://docs.aws.amazon.com/vpc/latest/userguide/what-is-amazon-vpc.html), packaged up
as a [CloudFormation template](https://aws.amazon.com/cloudformation/). You can also deploy it via [docker](/docs/self-hosting/docker)
just about anywhere.

When you log from Braintrust's TypeScript or Python library, it sends the events directly to the data plane, never touching Braintrust's
servers. And when you visit the UI (on https://www.braintrustdata.com/app), your browser runs requests against the data plane directly.

![Architecture diagram](/docs/architecture.png)
