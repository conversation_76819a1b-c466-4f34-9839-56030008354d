---
title: Model Context Protocol (MCP)
---

# Model Context Protocol (MCP)

Use this guide to enable your IDE to interact with the Braintrust API using Model Context Protocol.

## What is MCP?
The [Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) is a standardized framework that enables AI models to interact with your development environment. It allows for real-time exchange of experiment results, code context, and debugging information between your IDE and AI systems like Braintrust.

MCP is supported in many AI coding tools, including:
- [Cursor](https://www.cursor.com/)
- [Windsurf](https://docs.codeium.com/windsurf)
- VS Code via [Cline extension](https://github.com/cline/cline)
- [Claude for Desktop](https://claude.ai/download)

## Installation
Braintrust has a native MCP server which can read experiment results to help you automatically
debug and improve your app. To install it, add the following to your `mcp.json` file (for example, `.cursor/mcp.json`):

```json
{
  "mcpServers": {
    "server-name": {
      "command": "npx",
      "args": ["-y", "@braintrust/mcp-server@latest", "--api-key", "YOUR_API_KEY"]
    }
  }
}
```

## Usage

Once you've set up the MCP server, you can interact with your Braintrust projects directly in your IDE through natural language commands.

Try asking about Braintrust experiment results, code context, and debugging information!
