---
title: "Cookbook"
---

import { CookbookCards, CookbookCard } from "#/ui/docs/cookbook-cards";
import recipes from "./recipes.json";

# Cookbook

This cookbook, inspired by [OpenAI's cookbook](https://cookbook.openai.com/), is a collection of recipes for common
use cases of [Braintrust](/). Each recipe is an open source self-contained example, hosted on
[GitHub](https://github.com/braintrustdata/braintrust-cookbook). We welcome community contributions
and aspire for the cookbook to be a collaborative, living, breathing collection of best practices for
building high quality AI products.

<CookbookCards>
  {recipes
    .sort((a, b) => new Date(b.date) - new Date(a.date))
    .map((recipe, idx) => {
      const slug = encodeURIComponent(recipe.urlPath);
      return (
        <CookbookCard
          key={idx}
          route={`/docs/cookbook/recipes/${slug}`}
          title={recipe.title}
          authors={recipe.authors}
          date={recipe.date}
          language={recipe.language}
          tags={recipe.tags}
          logoIconUrl={recipe.logo}
        />
      );
    })}
</CookbookCards>
