[{"title": "Text-to-SQL", "path": "examples/Text2SQL/Text2SQL.ipynb", "tags": ["evals", "sql"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2023-08-12", "urlPath": "Text2SQL", "language": "python"}, {"title": "Classifying news articles", "path": "examples/ClassifyingNewsArticles/ClassifyingNewsArticles.ipynb", "tags": ["evals", "classification"], "authors": [{"name": "<PERSON>", "website": "https://twitter.com/davidtsong", "avatar": "/blog/img/author/david-song.jpg"}], "date": "2023-09-01", "urlPath": "ClassifyingNewsArticles", "language": "python"}, {"title": "Improving Github issue titles using their contents", "path": "examples/Github-Issues/Github-Issues.ipynb", "tags": ["evals", "summarization"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2023-10-29", "urlPath": "Github-Issues", "language": "typescript"}, {"title": "Coda's Help Desk with and without RAG", "path": "examples/CodaHelpDesk/CodaHelpDesk.ipynb", "tags": ["evals", "rag"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/austinmxx/", "avatar": "/blog/img/author/austin-moehle.jpg"}, {"name": "<PERSON>", "website": "https://twitter.com/siuheihk", "avatar": "/blog/img/author/kenny-wong.png"}], "date": "2023-12-21", "urlPath": "CodaHelpDesk", "language": "python"}, {"title": "Generating beautiful HTML components", "path": "examples/HTMLGenerator/HTMLGenerator.ipynb", "tags": ["logging", "datasets", "evals"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-01-29", "urlPath": "HTMLGenerator", "language": "typescript"}, {"title": "Generating release notes and hill-climbing to improve them", "path": "examples/ReleaseNotes/ReleaseNotes.ipynb", "tags": ["evals", "hill-climbing"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-02-02", "urlPath": "ReleaseNotes", "language": "typescript"}, {"title": "How Zapier uses assertions to evaluate tool usage in chatbots", "path": "examples/Assertions/Assertions.ipynb", "tags": ["evals", "assertions", "tools"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/vitorbal", "avatar": "/blog/img/author/vitor-balocco.jpg"}], "date": "2024-02-13", "logo": "https://cdn.zapier.com/zapier/images/favicon.ico", "banner": "/docs/cookbook-banners/Zapier.png", "urlPath": "Assertions", "language": "typescript"}, {"title": "AI Search Bar", "path": "examples/AISearch/ai_search_evals.ipynb", "tags": ["evals", "sql"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/austinmxx/", "avatar": "/blog/img/author/austin-moehle.jpg"}], "date": "2024-03-04", "urlPath": "AISearch", "language": "python"}, {"title": "Detecting Prompt Injections", "path": "examples/PromptInjectionDetector/PromptInjectionGPT4o.ipynb", "tags": ["evals", "classification"], "authors": [{"name": "<PERSON>", "website": "https://twitter.com/nelsonauner", "avatar": "/blog/img/author/nelson-auner.jpg"}], "date": "2024-05-20", "urlPath": "PromptInjectionDetector", "language": "python"}, {"title": "Comparing evals across multiple AI models", "path": "examples/ModelComparison/ModelComparison.ipynb", "tags": ["evals", "charts"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/j13huang/", "avatar": "/blog/img/author/john-huang.jpg"}], "date": "2024-05-22", "urlPath": "ModelComparison", "language": "typescript"}, {"title": "Optimizing Ragas to evaluate a RAG pipeline", "path": "examples/SimpleRagas/SimpleRagas.ipynb", "tags": ["evals", "rag"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}, {"name": "<PERSON>", "website": "https://twitter.com/nelsonauner", "avatar": "/blog/img/author/nelson-auner.jpg"}], "date": "2024-05-27", "urlPath": "SimpleRagas", "language": "python"}, {"title": "LLM Eval For Text2SQL", "path": "examples/Text2SQL-Data/Text2SQL-Data.ipynb", "tags": ["evals", "datasets", "text2sql"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-05-29", "urlPath": "Text2SQL-Data", "language": "python"}, {"title": "Evaluating a chat assistant", "path": "examples/EvaluatingChatAssistant/EvaluatingChatAssistant.ipynb", "tags": ["evals", "chat"], "authors": [{"name": "Tara Nagar", "website": "https://www.linkedin.com/in/taranagar/", "avatar": "/blog/img/author/tara-nagar.jpg"}], "date": "2024-07-16", "urlPath": "EvaluatingChatAssistant", "language": "typescript"}, {"title": "Tool calls in LLaMa 3.1", "path": "examples/LLaMa-3_1-Tools/LLaMa-3_1-Tools.ipynb", "tags": ["evals", "llama-3.1", "tools"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-07-26", "urlPath": "LLaMa-3_1-<PERSON><PERSON>", "language": "typescript"}, {"title": "Benchmarking inference providers", "path": "examples/ProviderBenchmark/ProviderBenchmark.ipynb", "tags": ["evals", "llama-3.1", "providers"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-07-29", "urlPath": "ProviderBenchmark", "language": "typescript"}, {"title": "An agent that runs OpenAPI commands", "path": "examples/APIAgent-Py/APIAgent.ipynb", "tags": ["agent", "rag", "evals"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-08-12", "urlPath": "APIAgent-Py", "language": "python"}, {"title": "Unreleased AI: A full stack Next.js app for generating changelogs", "path": "examples/UnreleasedAI/UnreleasedAI.mdx", "tags": ["evals", "logging", "next.js"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2024-08-28", "banner": "/docs/cookbook-banners/AI-app.png", "urlPath": "UnreleasedAI", "language": "typescript"}, {"title": "Evaluating multimodal receipt extraction", "path": "examples/ReceiptExtraction/ReceiptExtraction.ipynb", "tags": ["evals", "multimodal", "receipts"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-09-30", "urlPath": "ReceiptExtraction", "language": "python"}, {"title": "Using functions to build a RAG agent", "path": "examples/ToolRAG/ToolRAG.mdx", "tags": ["functions", "rag", "tools"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}, {"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}], "date": "2024-10-08", "urlPath": "ToolRAG", "language": "typescript"}, {"title": "Using OpenTelemetry for LLM observability", "path": "examples/OTEL-logging/OTEL-logging.mdx", "tags": ["evals", "tools"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2024-10-31", "urlPath": "OTEL-logging", "language": "typescript"}, {"title": "Using Python functions to extract text from images", "path": "examples/ToolOCR/ToolOCR.mdx", "tags": ["python", "tools", "ocr", "functions"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2024-11-22", "urlPath": "ToolOCR", "language": "typescript"}, {"title": "Evaluating SimpleQA", "path": "examples/SimpleQA/SimpleQA.ipynb", "tags": ["datasets", "evals"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ankrgyl", "avatar": "/blog/img/author/ankur-goyal.jpg"}, {"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2024-12-06", "urlPath": "SimpleQA", "language": "python"}, {"title": "Evaluating audio with the OpenAI Realtime API", "path": "examples/Realtime/Realtime.mdx", "tags": ["evals", "tools", "audio"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2024-12-14", "urlPath": "Realtime", "language": "typescript"}, {"title": "Evaluating the precision and recall of an emotion classifier", "path": "examples/PrecisionRecall/PrecisionRecall.ipynb", "tags": ["recall", "precision", "evals", "classifier", "python"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-01-17", "urlPath": "PrecisionRecall", "language": "python"}, {"title": "Evaluating a prompt chaining agent", "path": "examples/PromptChaining/prompt-chaining.ipynb", "tags": ["agent", "evals", "python"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-01-30", "urlPath": "Prompt<PERSON><PERSON>ning", "language": "python"}, {"title": "Classifying spam using structured outputs", "path": "examples/SpamClassifier/SpamClassifier.mdx", "tags": ["classifier", "structured outputs", "playground"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2025-02-08", "urlPath": "SpamClassifier", "language": "typescript"}, {"title": "Evaluating a voice agent", "path": "examples/VoiceAgent/voiceagent.ipynb", "tags": ["agent", "evals", "voice"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-02-13", "urlPath": "VoiceAgent", "language": "python"}, {"title": "Evaluating video QA", "path": "examples/VideoQA/VideoQA.ipynb", "tags": ["evals", "video", "datasets"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-02-18", "urlPath": "VideoQA", "language": "python"}, {"title": "Prompt versioning and deployment", "path": "examples/PromptVersioning/PromptVersioning.ipynb", "tags": ["evals", "prompting", "functions"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-02-24", "urlPath": "PromptVersioning", "language": "python"}, {"title": "Evaluating a web agent", "path": "examples/WebAgent/WebAgent.ipynb", "tags": ["eval", "agent", "multimodal"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}, {"name": "<PERSON>", "website": "https://www.linkedin.com/in/adrianbarbir/", "avatar": "/blog/img/author/adrian-barbir.jpg"}], "date": "2025-03-08", "urlPath": "WebAgent", "language": "python"}, {"title": "Tracing Vercel AI SDK applications", "path": "examples/VercelAISDKTracing/vercel-ai-sdk-tracing.mdx", "tags": ["logging", "Next.js"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/philliphetzel/", "avatar": "/blog/img/author/phil-hetzel.jpg"}], "date": "2025-05-15", "urlPath": "VercelAISDKTracing", "language": "typescript"}, {"title": "Evaluating video QA with Twelve Labs", "path": "examples/VideoQATwelveLabs/VideoQATwelveLabs.ipynb", "tags": ["eval", "video", "multimodal"], "authors": [{"name": "<PERSON>", "website": "https://x.com/le_james94", "avatar": "/blog/img/author/james-le.jpg"}, {"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2025-05-14", "urlPath": "VideoQATwelveLabs", "language": "python"}, {"title": "Using PDF attachments in playgrounds", "path": "examples/PDFPlayground/PDFPlayground.mdx", "tags": ["logging", "multimodal", "playground", "typescript"], "authors": [{"name": "<PERSON>", "website": "https://www.linkedin.com/in/cmesteban/", "avatar": "/blog/img/author/carlos-esteban.jpg"}, {"name": "<PERSON><PERSON><PERSON>", "website": "https://twitter.com/ornelladotcom", "avatar": "/blog/img/author/ornella-al<PERSON><PERSON>.jpg"}], "date": "2025-05-22", "urlPath": "PDFPlayground", "language": "typescript"}]