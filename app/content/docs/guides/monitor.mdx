---
title: "Monitor"
metaTitle: "Monitor logs and experiments"
---

# Monitor page

The **Monitor** page shows aggregate metrics data for both the logs and experiments in a given project. The included charts show values related to the selected time period for latency, token count, time to first token, cost, request count, and scores.

![Monitor page](/docs/guides/monitor/monitor-basic.png)

## Group by metadata

Select the **Group** dropdown menu to group the data by specific metadata fields, including custom fields.

![Monitor page with group by](/docs/guides/monitor/monitor-group-by.png)

## Filter series

Select the filter dropdown menu on any individual chart to apply filters.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/monitor/filtersposter.png">
  <source src="/docs/guides/monitor/seriesfilter.mp4" type="video/mp4" />
</video>


## Select a timeframe

Select a timeframe from the given options to see the data associated with that time period.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/monitor/timerangeposter.png">
  <source src="/docs/guides/monitor/timerange.mp4" type="video/mp4" />
</video>

## Select to view traces

Select a datapoint node in any of the charts to view the corresponding traces for that time period.

![Monitor page click to view traces](/docs/guides/monitor/monitor-click.png)
