---
title: "Human review"
---

import { Maximize2 } from "lucide-react";
import { Callout } from "fumadocs-ui/components/callout";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";

# Human review

Although Braintrust helps you automatically evaluate AI software, human
review is a critical part of the process. Braintrust seamlessly integrates human
feedback from end users, subject matter experts, and product teams in one place. You can
use human review to evaluate/compare experiments, assess the efficacy of your automated scoring
methods, and curate log events to use in your evals. As you add human review scores, your logs will update in real time.

![Human review label](./human-review/label.png)

## Configuring human review

To set up human review, define the scores you want to collect in your
project's **Configuration** tab.

![Human Review Configuration](./human-review/config-page.png)

Select **Add human review score** to configure a new score. A score can be one of
- Continuous number value between `0%` and `100%`, with a slider input control.
- Categorical value where you can define the possible options and their scores. Categorical value options
  are also assigned a unique percentage value between `0%` and `100%` (stored as 0 to 1).
- Free-form text where you can write a string value to the `metadata` field at a specified path.

![Create modal](./human-review/create-modal.png)

Created human review scores will appear in the **Human review** section in every experiment and log trace in the project. Categorical scores configured to "write to expected" and free-form scores will also appear on dataset rows.


### Writing to expected fields

You may choose to write categorical scores to the `expected` field of a span instead of a score.
To enable this, check the **Write to expected field instead of score** option. There is also
an option to **Allow multiple choice** when writing to the expected field.

<Callout type="info">
  A numeric score will not be assigned to the categorical options when writing to the expected
  field. If there is an existing object in the expected field, the categorical value will be
  appended to the object.
</Callout>

![Write to expected](./human-review/expected-fields.png)

In addition to categorical scores, you can always directly edit the structured output for the `expected` field of any span through the UI.

## Reviewing logs and experiments

To manually review results from your logs or experiment, select a row to open trace view. There, you can edit the human review scores you previously configured.

<video className="border rounded-md" loop autoPlay muted poster="/docs/in-experiment-poster.mp4">
  <source src="/docs/in-experiment.mp4" type="video/mp4" />
</video>

As you set scores, they will be automatically saved and reflected in the summary metrics. The process is the same whether you're reviewing logs or experiments.

### Leaving comments

In addition to setting scores, you can also add comments to spans and update their `expected` values. These updates
are tracked alongside score updates to form an audit trail of edits to a span.

If you leave a comment that you want to share with a teammate, you can copy a link that will deeplink to the comment.

<video className="border rounded-md" loop autoPlay muted poster="/docs/comment-poster.mp4">
  <source src="/docs/comment.mp4" type="video/mp4" />
</video>

## Focused review mode

If you or a subject matter expert is reviewing a large number of logs or experiments, you can use **Review** mode to enter
a UI that's optimized specifically for review. To enter review mode, hit the "r" key or the expand (<Maximize2 className="size-3 inline" />)
icon next to the **Human review** header in a span.

<video className="border rounded-md" loop autoPlay muted poster="/docs/review-mode-poster.mp4">
  <source src="/docs/review-mode.mp4" type="video/mp4" />
</video>

In review mode, you can set scores, leave comments, and edit expected values. Review mode is optimized for keyboard
navigation, so you can quickly move between scores and rows with keyboard shortcuts. You can also share a link to the
review mode view with other team members, and they'll drop directly into review mode.

### Reviewing data that matches a specific criteria

To easily review a subset of your logs or experiments that match a given criteria, you can filter using English or [BTQL](/docs/reference/btql#btql-query-syntax), then enter review mode.

In addition to filters, you can use [tags](/docs/guides/logging#tags-and-queues) to mark items for `Triage`, and then review them all at once.

You can also save any filters, sorts, or column configurations as views. Views give you a standardized place to see any current or future logs that match a given criteria, for example, logs with a Factuality score less than 50%. Once you create your view, you can enter review mode right from there.

<video className="border rounded-md" loop autoPlay muted poster="/docs/filter-view-review-poster.mp4">
  <source src="/docs/filter-view-review.mp4" type="video/mp4" />
</video>

Reviewing is a common task, and therefore you can enter review mode from any experiment or log view. You can also re-enter review mode from any view to audit
past reviews or update scores.

### Benefits over an annotation queue

* Designed for optimal productivity: The combination of views and human review mode simplifies the review process with intuitive filters, reusable configurations, and keyboard navigation, enabling faster, more efficient log evaluation and feedback.

* Dynamic and flexible views: Views dynamically update with new logs matching saved criteria, eliminating the need to set up and maintain complex automation rules.

* Easy collaboration: Sharing review mode links allows for team collaboration without requiring intricate permissions or setup overhead.

## Filtering using feedback

In the UI, you can filter on log events with specific scores by adding a filter using the filter button, like "Preference is greater than 75%",
and then add the matching rows to a dataset for further investigation.

You can also programmatically filter log events using the API using a query and the project ID:

<CodeTabs>
<TSTab>
```typescript #skip-compile
await braintrust.projects.logs.fetch(projectId, { query });
```
</TSTab>
<PYTab>
```python
braintrust.projects.logs.fetch("<project_id>", "scores.Preference > 0.75")
```
</PYTab>
</CodeTabs>

This is a powerful way to utilize human feedback
to improve your evals.

## Capturing end-user feedback

The same set of updates — scores, comments, and expected values — can be captured from end-users as well. See the
[Logging guide](/docs/guides/logs/write#user-feedback) for more details.
