---
title: "Views"
---

import { <PERSON>Tabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";

# Views

You'll often want to create a view that shows data organized and visualized a certain way on the same underlying data. Views are saved table configurations that preserve filters, sorts, column order and column visibility. All table-based layouts, including logs, experiments, datasets and projects support configured views.

![Views](/docs/guides/views.png)

## Default locked views

Some table layouts include default views for convenience. These views are locked and cannot be modified or deleted.

* **All rows** corresponds to all of the records in a given table. This is the default, unfiltered view.

On experiment and logs pages:
* **Non-errors** corresponds to all of the records in a given table that do not contain errors.
* **Errors** corresponds to all of the records in a given table that contain errors.

On experiment pages:
* **Unreviewed** hides items that have already been human-reviewed.

## Creating and managing custom views

### In the UI

To create a custom view, start by applying the filters, sorts, and columns that you would like to have visible in your view. Then, navigate to the **Views** dropdown and select **Create view**.

<video className="border rounded-md" loop autoPlay muted poster="/docs/guides/views-poster.png">
  <source src="/docs/guides/views.mp4" type="video/mp4" />
</video>

After entering a view, any changes you make to the filters, sorts, and columns will be auto-saved.

To rename, duplicate, delete, or set as default, use the **...** menu next to the view name.

![Views menu](/docs/guides/views-menu.png)

### In code

Views can also be created and managed programmatically [via the API](/docs/reference/api/Views).

## Access

Views are accessible and configurable by any member of the organization.

## Best practices

Use views when:
* You frequently reapply the same filters.
* You want to standardize what your team sees.
* You want to review only a subset of records.

Make sure to use clear, descriptive names so your team can quickly understand the purpose of each view. Some example views might be:
* "Logs with Factuality < 50%"
* "Unreviewed high-priority traces"
* "Failing test cases"
* "Tagged with 'Customer Support'"
* "Lisa's test cases"

### Using views with custom columns

If you regularly filter by complex or nested JSON queries or metadata, consider creating [custom columns](/docs/guides/evals/interpret#create-custom-columns). Custom columns let you surface frequently-used or computed values directly as columns, simplifying repetitive filtering tasks. Custom columns are also rendered in trace spans, with their own span field view type (for example, JSON, Markdown, or HTML).

For example, you can analyze data across multiple models within a single experiment view:

* First, define a custom column extracting the model name from your metadata.
* Then, apply the custom column, sort, and any additional standard filters, then save this configuration as a view.
* Lastly, use the filter dropdown to quickly toggle between models.
