---
title: "Deploy with <PERSON><PERSON>"
---

import { <PERSON>Tabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Callout } from "fumadocs-ui/components/callout";
import Link from "fumadocs-core/link";

# Deploying Braintrust with Docker

This guide walks through the process of deploying Braintrust through Docker. Braintrust's
self-hosted deployment splits data into a control plane and a data plane. You can self-host
the data plane without ever exposing any data to Braintrust's servers or team. The control plane
includes the UI and some metadata, and your browser and SDK code communicate directly with the
data plane. As a result, you can still visit Braintrust through [https://braintrust.dev](https://braintrust.dev),
but all data will be stored and processed in your own infrastructure.

<Callout type="warn">
  We recognize that in certain scenarios, you may want the utmost isolation while testing our product
  and offer a "full" deployment mode that includes the control plane and data plane. **This is only available
  for testing with the intent of using the hybrid configuration in production**. Please reach out to
  [support](mailto:<EMAIL>) for more information.
</Callout>

The files necessary for launching the data plane deployment are hosted on
[GitHub](https://github.com/braintrustdata/braintrust-deployment). The
repository contains a docker compose configuration file which you can deploy or use as a reference.
If needed, you may customize the environment variables in the compose file to suit
your deployment needs. Although the deployment uses Docker compose, in practice, you
only need to deploy 1-2 containers and so you can deploy it however you'd like (on an
instance, using Kubernetes, etc.).

To get started, just pull down the deployment repo and launch Braintrust!

## Data plane configuration

To launch the data plane:

```bash
git clone https://github.com/braintrustdata/braintrust-deployment.git
cd braintrust-deployment/docker
docker compose -f docker-compose.api.yml pull
docker compose -f docker-compose.api.yml up -d --remove-orphans
```
<Callout type="info">
  **Important for AWS Users**: If you plan to run these containers on EC2 with [IMDSv2](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/configuring-instance-metadata-service.html),
  you need to increase the **[instance metadata hop limit](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/configuring-instance-metadata-options.html)** from the default value of **1** to **2**. This is because containers require at least one extra network hop to access instance metadata. Without increasing the hop limit, requests to the metadata service will fail which means that the containers will not be able to auto discover the AWS region or credentials.
</Callout>

Once it runs, you can verify the API is running with

```bash
curl http://localhost:8000/
```

The services can be shutdown with `docker compose` as well:

```bash
docker compose -f docker-compose.api.yml down
```

Once you have started the docker containers, navigate to the <Link href="/app/settings?subroute=api-url" target="_blank">API URL section</Link> of the settings page. Enter the URL of your server in the "API URL" section. The docker server defaults to `http://localhost:8000`, which will work as long as the browser is running on the same machine as the API server. The external port can be configured by adjusting the first value of the [port mapping](https://github.com/braintrustdata/braintrust-deployment/blob/baa6a964a0a8e8b76b36472001ef0d0c0b1697eb/docker/docker-compose.api.yml#L52) in the compose file.

![API URL settings page](/docs/settings_api_url.png)

If the browser is successfully able to connect to your server, you should see
the message "API ping successful". At this point, you should be able to use
Braintrust as usual!

If this is your first time using Braintrust, you may want to go through the
[quickstart](/docs) guide next.

### Setting up HTTPS

If you wish to deploy the API server to a non-localhost URL, it will
need to be exposed on an HTTPS endpoint. A common way to accomplish this is to
obtain an SSL certificate for your domain, and forward traffic to your API
server using a reverse proxy, such as nginx.

### Deploying other services

In addition to the API server, you may also deploy the realtime
service through docker. The realtime server transfers end-to-end encrypted user
data using a key that's only available to the data plane. Self-hosting may be
desirable to have complete control over these services and ensure that sensitive
data in any form goes through only your own servers.

The realtime service is bundled in the full configuration, and you can include
it in the API configuration with minor modifications. The modified
`docker-compose.api.yml` might look as follows:

```bash
services:
  braintrust-redis:
    # Same as before.
  braintrust-postgres:
    # Same as before.
  braintrust-standalone-api:
    # Same as before, except we add environment variables pointing the API
    # server to our deployed realtime service.
    environment:
      # We use `host.docker.internal` here because the docker container can only
      # access other services on the same host network through this domain.
      REALTIME_URL: http://host.docker.internal:8788
      # Make sure to leave the other environment variables in.
  braintrust-standalone-realtime:
    # Uncomment the `braintrust-standalone-realtime` block.
```

If you are deploying the realtime service on a different machine, make sure to
adjust the URL set on the `braintrust-standalone-api` service accordingly.
Finally, point the webapp to your realtime URL in the <Link href="/app/settings?subroute=api-url" target="_blank"> API URLs page </Link>.

You will want to use the publicly-accessible form of the URL, rather than the
docker-accessible form, so make sure to substitute `host.docker.internal` with
`localhost` if that applies.

<Callout type="warn">
  The proxy is now bundled into the API, so you do not need to deploy it as a separate service, as
  long as your API is at least version 0.0.51.
</Callout>

![All URL settings page](/docs/settings_all_urls.png)

If you wish to deploy more services yourself, such as the webapp, you will
likely want to switch to the full configuration.

### Detailed reference

To see a full list of the containers and environment variables you can configure, see the
[docker-compose.api.yml](https://github.com/braintrustdata/braintrust-deployment/blob/main/docker/docker-compose.api.yml) file
in the [braintrust-deployment](https://github.com/braintrustdata/braintrust-deployment) repository.

## Recommendations for Production

When deploying Braintrust in production, consider these minimum and recommended hardware requirements for reliable performance and uptime. These requirements assume typical production usage patterns. For high-utilization deployments, you may need to scale these resources up significantly. Monitor your resource utilization and adjust accordingly.

### API Service
- **CPU**: At least 2 vCPUs per instance
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Instance count**: Minimum 2, recommended 4+
- **Environment Variables**:
  - `NODE_MEMORY_PERCENT`: recommended `80` to `90` if the API is running on a dedicated instance or container orchestrator with cgroup memory limits (e.g. Kubernetes, ECS).

### Database (PostgreSQL)
- **CPU**: Minimum 4 vCPUs, recommended 8+ vCPUs
- **Memory**: Minimum 32GB RAM, recommended 64GB+
- **Storage Size**: 1000GB, monitor for growth.
- **Storage IOPS**: Minimum 10,000 IOPS, recommended 15,000+

### Redis Cache
- **CPU**: 2 vCPUs
- **Memory**: Minimum 2GB RAM, recommended 4GB+ for production workloads

<Callout type="warn">
**Important for AWS Users**: Avoid using burstable Redis instances (t-family instances like `cache.t4g.micro`) in production. These instances use CPU credits that can be exhausted during high-load periods, leading to performance throttling. Instead, use non-burstable instances like `cache.r7g.large` or `cache.r6g.medium` for predictable performance.
</Callout>

### S3

Certain features require S3 buckets, namely: attachments, bundled code, overflowing responses, and Brainstore.

The response overflow bucket is named `RESPONSE_BUCKET_NAME` and must have a CORS configuration that enables
`GET` and `HEAD` requests from the browser, equivalent to:

```yaml
CorsRules:
  - AllowedHeaders:
      - "*"
    AllowedMethods:
      - GET
      - HEAD
    AllowedOrigins:
      - "*"
    MaxAge: 3600
```

The bundled code and attachments features both share the `CODE_BUNDLE_BUCKET` variable. This bucket must have
a CORS rule which enables `PUT` requests from the browser, equivalent to:

```yaml
CorsRules:
  - AllowedHeaders:
      - "*"
    AllowedMethods:
      - PUT
    AllowedOrigins:
      - "*"
    MaxAge: 3600
```

To store attachments in a separate bucket from bundled code, set the `ATTACHMENT_BUCKET` variable. This bucket must
have a CORS rule as described above.

### Brainstore (if enabled)
- **CPU**: Minimum 8 vCPUs, recommended 16+ vCPUs, ARM architecture recommended
- **Memory**: Minimum 16GB RAM, recommended 32GB+
- **Storage Size**: Minimum 256GB, recommended 1024GB+
- **Storage IOPS**: Use NVME (storage is ephemeral)


## Troubleshooting

The state of the Braintrust deployment is fully managed on docker. Therefore, if
something is not running as expected, you should be able to inspect the
container, either by dumping its logs or opening up a shell inside the container
to poke around. For instance, after launching the API configuration, you should
see three containers:

```bash
% docker ps
CONTAINER ID   IMAGE                                             COMMAND                  CREATED          STATUS                    PORTS                    NAMES
c67b49727823   public.ecr.aws/braintrust/standalone-api:latest   "python entrypoint_a…"   16 minutes ago   Up 16 minutes             0.0.0.0:8000->8000/tcp   bt-docker-braintrust-standalone-api-1
6ed70334c6cf   public.ecr.aws/braintrust/postgres:latest         "docker-entrypoint.s…"   16 minutes ago   Up 16 minutes (healthy)   0.0.0.0:5532->5432/tcp   bt-docker-braintrust-postgres-1
37840f55bfd5   public.ecr.aws/braintrust/redis:latest            "docker-entrypoint.s…"   16 minutes ago   Up 16 minutes (healthy)   0.0.0.0:6479->6379/tcp   bt-docker-braintrust-redis-1
```

You can dump the logs of the API container using `docker logs [CONTAINER ID]`,
or spawn a shell inside the container using `docker exec -it [CONTAINER ID] bash`.
For further questions, feel free to reach out at
[<EMAIL>](mailto:<EMAIL>).
