---
title: Weekly update 11/13/23
description: "Function calling and tool support, new blog posts, and project UI improvements."
author: "<PERSON>"
date: "13 November 2023"
image: "/blog/meta/nov-13.png"
twimage: "/blog/meta/nov-13.png"
---

import SignUpLink from "#/ui/docs/signup";
import C<PERSON><PERSON><PERSON><PERSON> from "#/app/(landing)/cta-button";
import { Blog<PERSON>uth<PERSON> } from "#/ui/blog/blog-author";

# Braintrust Weekly Update

<BlogAuthor author="<PERSON>" date="13 November 2023" />

It’s been another busy week at Braintrust. We’ve shipped some user experience improvements and fixes this week to make it easier for you to build reliable AI apps:

#### We added function calling to our playground

<figure>
  ![Added OpenAI function calling in the prompt
  playground](/docs/release-notes/ReleaseNotes-2023-11-functions.gif)
</figure>

Many developers have been asking us to support OpenAI’s function calling in our prompt playground. We added it this week. Just click “Add tools” in your prompt to get started experimenting with OpenAI functions.

#### [Release notes](https://www.braintrustdata.com/docs/release-notes):

- Made experiment column resized widths persistent
- Fixed our libraries including Autoevals to work with OpenAI’s new libraries
- Added support for function calling and tools in our prompt playground
- Added tabs on a project page for Datasets, Experiments, etc.

#### Fun links and mentions:

- [State of AI Report from Retool. How are companies adopting AI?](https://www.braintrustdata.com/blog/state-of-ai)
- [The AI app development journey: how do evaluations save you time?](https://www.braintrustdata.com/blog/journey)

Braintrust is the enterprise-grade stack for building AI products. From evaluations, to prompt playground, to data management, we take uncertainty and tedium out of incorporating AI into your business.

<SignUpLink>Sign up</SignUpLink> now, or check out our [pricing page](/pricing) for
more details.

<div className="mt-12">
  <CtaButton className="w-full sm:w-auto sm:h-12" cta="Get started for free" />
</div>
