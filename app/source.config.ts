import {
  defineCollections,
  defineConfig,
  defineDocs,
  frontmatterSchema,
} from "fumadocs-mdx/config";
import { z } from "zod";
import { externalLinks } from "./lib/external-links";

export const blogCollection = defineCollections({
  type: "doc",
  dir: "./content/blog",
  schema: frontmatterSchema.extend({
    date: z.string(),
    image: z.string().optional(),
    twimage: z.string().optional(),
    draft: z.boolean().optional(),
    authors: z.array(z.string()).optional(),
    tags: z.array(z.string()).optional(),
    lastModified: z.string().optional(),
  }),
});

export const legalCollection = defineCollections({
  type: "doc",
  dir: "./content/legal",
  schema: frontmatterSchema.extend({
    date: z.string().optional(),
  }),
});

export const { docs, meta } = defineDocs({
  docs: {
    schema: frontmatterSchema.extend({
      metaTitle: z.string().optional(),
    }),
  },
});

export default defineConfig({
  mdxOptions: {
    rehypePlugins: [externalLinks],
  },
});
