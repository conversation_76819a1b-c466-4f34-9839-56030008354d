import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: "https://<EMAIL>/4507221754380288",

  enabled: process.env.NODE_ENV === "production",

  // Auto session tracking results in a lot of request to Sentry in service of a feature (Release Health) that we don't use.
  // https://forum.sentry.io/t/understanding-the-events-sent-by-the-js-sdk-type-session/13606/4
  autoSessionTracking: false,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  replaysOnErrorSampleRate: 1.0,

  // We only want to record replay sessions for errors
  replaysSessionSampleRate: 0,

  tracesSampleRate: 0.05,

  integrations: [
    Sentry.replayIntegration({
      maskAllText: true,
      blockAllMedia: true,
      beforeErrorSampling: (event) => {
        const pageTag = event?.tags?.page;

        // We only want to record replays for page crashes, not for network blips, etc.
        if (
          pageTag === "org-page-error" ||
          pageTag === "global-error" ||
          pageTag === "boundary-error" ||
          pageTag === "error-banner"
        ) {
          return true;
        }

        return false;
      },
    }),
    Sentry.browserTracingIntegration(),
  ],

  ignoreErrors: ["ClerkJS: Token refresh failed"],

  beforeSend: (event) => {
    // Is this user on-prem?
    const isOnPrem = event.contexts?.org?.isOnPrem;

    // Is this user on-prem, but opted to share the error trace?
    const isOnPremShare = event.extra?.isOnPremShare;

    const shouldSendToSentry = isOnPrem === false || isOnPremShare === true;

    if (shouldSendToSentry) {
      return event;
    }

    return null;
  },
});
