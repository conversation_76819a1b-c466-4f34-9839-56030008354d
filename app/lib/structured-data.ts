import {
  type WithContext,
  type Organization,
  type WebSite,
  type SoftwareApplication,
  type Article,
  type BreadcrumbList,
} from "schema-dts";

export const generateOrganizationSchema = (): WithContext<Organization> => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Braintrust",
  url: "https://www.braintrust.dev",
  logo: "https://www.braintrust.dev/icon512.png",
  description:
    "The end-to-end platform for building world-class AI products. Evaluate, log, and monitor your LLM applications with comprehensive evals, observability, and debugging tools.",
  sameAs: [
    "https://twitter.com/braintrustdata",
    "https://github.com/braintrustdata",
    "https://www.linkedin.com/company/braintrust-data",
  ],
  foundingDate: "2022",
  contactPoint: {
    "@type": "ContactPoint",
    contactType: "customer service",
    email: "<EMAIL>",
    url: "https://www.braintrust.dev/contact",
  },
  address: {
    "@type": "PostalAddress",
    addressCountry: "US",
    addressLocality: "San Francisco",
    addressRegion: "CA",
  },
});

export const generateWebSiteSchema = (): WithContext<WebSite> => ({
  "@context": "https://schema.org",
  "@type": "WebSite",
  name: "Braintrust",
  url: "https://www.braintrust.dev",
  description: "The end-to-end platform for building world-class AI products",
  potentialAction: {
    "@type": "SearchAction",
    target: {
      "@type": "EntryPoint",
      urlTemplate: "https://www.braintrust.dev/docs?q={search_term_string}",
    },
    // @ts-ignore - query-input is valid Schema.org property not in schema-dts types
    "query-input": "required name=search_term_string",
  },
});

export const generateSoftwareApplicationSchema =
  (): WithContext<SoftwareApplication> => ({
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "Braintrust",
    url: "https://www.braintrust.dev",
    description:
      "The end-to-end platform for building world-class AI products. Evaluate, log, and monitor your LLM applications with comprehensive evals, observability, and debugging tools.",
    applicationCategory: "DeveloperApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      availability: "https://schema.org/InStock",
      description: "Free tier available",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      ratingCount: "100",
      bestRating: "5",
      worstRating: "1",
    },
    featureList: [
      "AI Model Evaluation",
      "LLM Observability",
      "Performance Monitoring",
      "Debugging Tools",
      "Experiment Tracking",
      "Dataset Management",
      "Prompt Engineering",
      "A/B Testing",
    ],
  });

export const generateArticleSchema = ({
  title,
  description,
  url,
  publishedTime,
  modifiedTime,
  authors,
  tags,
}: {
  title: string;
  description: string;
  url: string;
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
}): WithContext<Article> => ({
  "@context": "https://schema.org",
  "@type": "Article",
  headline: title,
  description,
  url,
  datePublished: publishedTime,
  dateModified: modifiedTime || publishedTime,
  author: authors?.map((author) => ({
    "@type": "Person",
    name: author,
  })),
  publisher: {
    "@type": "Organization",
    name: "Braintrust",
    logo: {
      "@type": "ImageObject",
      url: "https://www.braintrust.dev/icon512.png",
    },
  },
  keywords: tags?.join(", "),
  mainEntityOfPage: {
    "@type": "WebPage",
    "@id": url,
  },
});

export const generateBreadcrumbSchema = (
  items: Array<{ name: string; url: string }>,
): WithContext<BreadcrumbList> => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  itemListElement: items.map((item, index) => ({
    "@type": "ListItem",
    position: index + 1,
    name: item.name,
    item: item.url,
  })),
});

export const generateFAQSchema = (
  faqs: Array<{ question: string; answer: string }>,
) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  mainEntity: faqs.map((faq) => ({
    "@type": "Question",
    name: faq.question,
    acceptedAnswer: {
      "@type": "Answer",
      text: faq.answer,
    },
  })),
});
