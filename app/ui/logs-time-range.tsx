import { Button } from "./button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { TIME_RANGE_OPTIONS } from "#/app/app/[org]/monitor/time-controls/time-range";
import { DropdownMenuItem } from "./dropdown-menu";
import { Calendar } from "./calendar";
import { cn } from "#/utils/classnames";
import { endOfDay, format, startOfDay } from "date-fns";
import { PLAN_SLUGS, useOrgPlan } from "#/app/app/[org]/settings/billing/plans";
import { type TimeRangeFilter } from "#/utils/view/use-view";

const TIME_RANGE_OPTIONS_ALL_TIME = [
  ...TIME_RANGE_OPTIONS,
  { value: "all", label: "All time", isLive: false },
] as const;

const DEFAULT_RANGE = TIME_RANGE_OPTIONS_ALL_TIME.find((r) => r.value === "3d");

export const parseDateString = (dateString: string) => {
  return new Date(
    parseInt(dateString.slice(0, 4)), // year
    parseInt(dateString.slice(5, 7)) - 1, // month (0-indexed)
    parseInt(dateString.slice(8, 10)), // day
  );
};

export const LogsTimeRange = ({
  value,
  setValue,
}: {
  value: TimeRangeFilter;
  setValue: (value: TimeRangeFilter) => void;
}) => {
  const orgPlan = useOrgPlan();
  const isFreeOrg = orgPlan === PLAN_SLUGS.FREE;
  const isCustom = typeof value === "object";
  const selectedDefaultRange = !isCustom
    ? TIME_RANGE_OPTIONS_ALL_TIME.find((r) => r.value === value)
    : undefined;

  const isLive = selectedDefaultRange?.isLive;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown>
          {isCustom ? (
            value.from === value.to ? (
              `${format(parseDateString(value.from), "MMM d, yyyy")}`
            ) : (
              <>
                {format(parseDateString(value.from), "MMM d, yyyy")} -{" "}
                {format(parseDateString(value.to), "MMM d, yyyy")}
              </>
            )
          ) : selectedDefaultRange ? (
            <>
              <DefaultRangePill isLive={isLive}>
                {selectedDefaultRange.value === "all"
                  ? "All"
                  : selectedDefaultRange.value}
              </DefaultRangePill>
              {selectedDefaultRange.label === "All time" ? "" : "Past "}
              {selectedDefaultRange.label}
            </>
          ) : (
            <>
              <DefaultRangePill isLive={isLive}>
                {DEFAULT_RANGE!.value}
              </DefaultRangePill>
              Past {DEFAULT_RANGE!.label}
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="!max-w-xs">
        {TIME_RANGE_OPTIONS_ALL_TIME.map((r) => (
          <DropdownMenuItem
            key={r.value}
            onClick={() => setValue(r.value)}
            className="group"
            disabled={isFreeOrg && (r.value === "30d" || r.value === "all")}
          >
            <DefaultRangePill isLive={r.isLive}>
              {r.value === "all" ? "All" : r.value}
            </DefaultRangePill>
            {r.label === "All time" ? "" : "Past "}
            {r.label}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>Custom</DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <Calendar
              mode="range"
              numberOfMonths={2}
              selected={
                isCustom
                  ? {
                      from: startOfDay(parseDateString(value.from)),
                      to: endOfDay(parseDateString(value.to)),
                    }
                  : undefined
              }
              onSelect={(dateRange) => {
                const range = {
                  from: dateRange?.from && startOfDay(dateRange?.from),
                  to: dateRange?.to
                    ? endOfDay(dateRange?.to)
                    : dateRange?.from
                      ? // if there is only a "from" date selected, use the end of the day time for the "to" date
                        endOfDay(dateRange?.from)
                      : undefined,
                };
                if (!range.from || !range.to) {
                  return;
                }
                setValue({
                  from: format(range.from, "yyyy-MM-dd"),
                  to: format(range.to, "yyyy-MM-dd"),
                });
              }}
              disabled={{
                before: isFreeOrg
                  ? startOfDay(new Date(Date.now() - 14 * 24 * 60 * 60 * 1000))
                  : undefined,
                after: endOfDay(new Date()),
              }}
            />
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        {isFreeOrg && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>
              Data retention is limited to 14 days for organizations on the Free
              plan
            </DropdownMenuLabel>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const DefaultRangePill = ({
  children,
  isLive,
}: {
  children: React.ReactNode;
  isLive?: boolean;
}) => {
  return (
    <span
      className={cn(
        "text-normal mr-0.5 w-9 rounded-[3px] py-px text-center text-[11px] transition-colors bg-primary-200 group-hover:bg-primary-300 text-primary-600",
        {
          "bg-accent-100 text-accent-700 group-hover:bg-accent-200": isLive,
        },
      )}
    >
      {isLive ? "LIVE" : children}
    </span>
  );
};
