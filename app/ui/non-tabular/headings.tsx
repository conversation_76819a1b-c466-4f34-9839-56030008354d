import { cn } from "#/utils/classnames";
import { type HTMLAttributes } from "react";
import styles from "./heading-styles.module.css";

interface TextProps extends HTMLAttributes<HTMLElement> {
  className?: string;
}

export const H1 = ({ className, children, ...props }: TextProps) => (
  <h1 className={cn(styles.h1, "dark:text-white", className)} {...props}>
    {children}
  </h1>
);

export const H2 = ({ className, children, ...props }: TextProps) => (
  <h2 className={cn(styles.h2, "dark:text-white", className)} {...props}>
    {children}
  </h2>
);

export const BodyRegular = ({ className, children, ...props }: TextProps) => (
  <p className={cn(styles.bodyRegular, className)} {...props}>
    {children}
  </p>
);

export const BodyLight = ({ className, children, ...props }: TextProps) => (
  <p className={cn(styles.bodyLight, className)} {...props}>
    {children}
  </p>
);
