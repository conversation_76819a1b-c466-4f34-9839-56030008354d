import { cn } from "#/utils/classnames";
import { type PropsWithChildren } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";

export const CustomBottomSheet = ({
  isOpen,
  className,
  children,
  onClose,
}: PropsWithChildren<{
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}>) => {
  const { enabledScopes } = useHotkeysContext();

  useHotkeys("Escape", onClose, {
    enabled: isOpen && !enabledScopes.includes("global-keyboard-shortcuts"),
    preventDefault: true,
  });

  return (
    <div className={cn("fixed inset-0 z-50 pointer-events-none")}>
      <div
        className={cn(
          "absolute inset-0 z-10 pointer-events-none transition-opacity bg-black opacity-0",
          {
            "pointer-events-auto opacity-40": isOpen,
          },
        )}
        onClick={onClose}
      />
      <div
        className={cn(
          "absolute flex flex-col justify-end inset-0 z-50 translate-y-full pointer-events-none transition-transform ease-in-out duration-100",
          className,
          {
            "translate-y-0": isOpen,
          },
        )}
      >
        {children}
      </div>
    </div>
  );
};
