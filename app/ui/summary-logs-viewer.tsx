"use client";

// This file is very similar to, but not exactly the same as, logs-viewer.tsx. This is the new approach going forward,
// so if you change it, you may need to change logs-viewer.tsx as well.
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import { type Clause } from "#/utils/search/search";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from "react";
import { ExternalLink } from "#/ui/link";
import { type SavingState } from "#/ui/saving";
import {
  EmptyStateCodeSnippets,
  ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS,
  TableEmptyState,
} from "#/ui/table/TableEmptyState";
import { Activity } from "lucide-react";
import { singleQuote } from "#/utils/sql-utils";
import { useEntityStorage } from "#/lib/clientDataStorage";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { LogsProgressInsight } from "#/app/app/[org]/p/[project]/(ExperimentsChart)/logs-progress-insight";
import { Bubble } from "#/ui/table/bubble";
import { formatFullDateTime } from "#/utils/date";
import { ObjectPermissionsDialog } from "#/app/app/[org]/p/[project]/permissions/object-permissions-dialog";
import { Skeleton } from "#/ui/skeleton";
import { type ViewType } from "@braintrust/core/typespecs";
import { type ViewParams } from "#/utils/view/use-view";
import { useCustomColumns } from "#/utils/custom-columns/use-custom-columns";
import { useGetRowsForExport } from "#/utils/data-object";
import columnReorderer from "#/app/app/[org]/p/[project]/experiments/[experiment]/table-column-reorderer";
import { deriveScoreFields, useLogScoreSummary } from "#/ui/use-log-summary";
import { usePanelSize } from "#/ui/use-panel-size";
import { type TraceViewParams } from "#/ui/trace/trace";
import { TableRowHeightToggle } from "#/ui/table-row-height-toggle";
import { useTraceFullscreen } from "#/ui/trace/use-trace-fullscreen";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import Footer from "#/ui/landing/footer";
import { useSummaryPaginatedObjectViewerDataComponents } from "#/ui/summary-paginated-object-viewer";
import { type RowId } from "#/utils/diffs/diff-objects";
import {
  INITIALLY_VISIBLE_COLUMNS,
  usePaginatedObjectViewerVizComponents,
} from "#/ui/paginated-object-viewer";
import { type LogsPanelLayout } from "#/ui/logs-viewer";
import { DuckDBTypeHints } from "#/utils/schema";
import { useIsSidenavDocked } from "#/app/app/[org]/sidenav-state";
import { cn } from "#/utils/classnames";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { LogsHeader } from "#/app/app/[org]/p/[project]/logs/logs-header";
import { LogsSparklines } from "#/app/app/[org]/p/[project]/logs-sparklines";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { fetchBtql } from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { useQueryClient } from "@tanstack/react-query";
import { LogsTimeRange } from "./logs-time-range";

export default function SummaryLogsViewer({
  logType,
  logId,
  setSavingState,
  defaultPanelLayout,
}: {
  logType: "project_logs";
  logId: string | null;
  extraLeftControls?: React.ReactNode;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  defaultPanelLayout: LogsPanelLayout;
}) {
  useAnalytics({
    page: {
      category: "logs",
      props: {
        project_id: logId,
      },
    },
  });

  const {
    orgName,
    projectId,
    projectName,
    config: projectConfig,
  } = useContext(ProjectContext);

  const [logBucket, setLogBucket] = useEntityStorage({
    entityType: "project",
    entityIdentifier: projectId ?? "",
    key: "logBucket",
  });

  const objectType = "project_logs";
  const viewType: ViewType | null = "logs";
  const viewParams: ViewParams | undefined = useMemo(
    () =>
      projectId && viewType
        ? {
            objectType: "project",
            objectId: projectId,
            viewType,
          }
        : undefined,
    [projectId],
  );

  const customColumnState = useCustomColumns({
    objectType: "project",
    objectId: projectId,
    subtype: "project_log",
  });

  const [permissionsOpen, setPermissionsOpen] = useState(false);
  const [selectedBucket, setSelectedBucket] = useState<
    Clause<"filter"> | undefined
  >(undefined);

  const minSidePanelWidth = usePanelSize(640);
  const minMainPanelWidth = usePanelSize(500);

  const mainPanelScrollContainer = useRef<HTMLDivElement>(null);

  const traceViewParamArgs = useMemo(
    (): Partial<TraceViewParams> => ({
      title: "log",
    }),
    [],
  );

  const paginatedObjectViewerDataComponents =
    useSummaryPaginatedObjectViewerDataComponents({
      objectType: logType,
      objectId: logId,
      objectName: projectName,
      pageSize: 50,
      setSavingState,
      selectedBucket,
      traceViewParamArgs,
      viewParams,
      disableGrouping: true,
    });

  const { projectedPaths, filters, viewProps, initialQueryLoading } =
    paginatedObjectViewerDataComponents;

  const projectSummaryMetrics = useIsFeatureEnabled("projectSummaryMetrics");

  const {
    logSummaryQuery,
    logSummarySignals,
    logSummarySchema,
    logSummaryBtqlSchema,
    tooExpensive,
  } = useLogScoreSummary(
    logBucket,
    projectSummaryMetrics ? (projectId ?? null) : null,
    viewProps,
    customColumnState.customColumnDefinitions,
  );

  const scoreFields = useMemo(() => {
    if (!logSummarySchema || !logSummaryBtqlSchema) {
      return [];
    }
    const { scoreFields } = deriveScoreFields({
      summarySchema: logSummarySchema,
      btqlSchema: logSummaryBtqlSchema,
      scoreConfig: projectConfig.scores,
    });
    return scoreFields;
  }, [logSummarySchema, logSummaryBtqlSchema, projectConfig.scores]);

  const extraLeftControls = useMemo(
    () => (
      <>
        <TableRowHeightToggle
          tableRowHeight={viewProps.rowHeight ?? "compact"}
          onSetRowHeight={viewProps.setRowHeight}
        />
      </>
    ),
    [viewProps.rowHeight, viewProps.setRowHeight],
  );

  const [rowIds, setRowIds] = useState<RowId[]>([]);

  const getRowsForExport = useGetRowsForExport({
    objectType,
    objectId: projectId,
    filters,
    exportedColumns: projectedPaths,
    // Only export the currently loaded data, since the logs page can be
    // infinitely long, but refetch to export non-truncated data.
    shouldRefetchObject: true,
    layout: viewProps.layout,
    dataObjectSearchParams: useMemo(
      () => ({
        shape: "summary",
        limit: paginatedObjectViewerDataComponents.tableQuery.data?.length,
      }),
      [paginatedObjectViewerDataComponents.tableQuery.data],
    ),
  });

  const builder = useBtqlQueryBuilder({});
  const btqlFlags = useBtqlFlags();
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const queryClient = useQueryClient();
  const refetchTooltipContentData = useCallback(
    async (fieldName: string, rowId: string, previewLength?: number) => {
      if (!logId) return undefined;

      const queryKey = ["tooltip-content", logId, rowId, previewLength];
      const rowData = await queryClient.fetchQuery({
        queryKey,
        queryFn: async () => {
          const response = await fetchBtql({
            args: {
              query: {
                filter: builder.and({
                  btql: `id = '${rowId}'`,
                }),
                from: builder.from(logType, [logId], "summary"),
                select: [{ op: "star" }],
                preview_length: previewLength ?? 1500,
              },
              brainstoreRealtime: true,
              disableLimit: false,
            },
            btqlFlags,
            apiUrl,
            getOrRefreshToken,
          });
          return response.data[0];
        },
        staleTime: 60 * 1000,
        gcTime: 5 * 60 * 1000,
      });

      return rowData?.[fieldName];
    },
    [
      apiUrl,
      builder,
      btqlFlags,
      logId,
      logType,
      getOrRefreshToken,
      queryClient,
    ],
  );

  const vizQueryPropsOverride = useMemo(
    () => ({
      isHumanReviewModeEnabled: true,
      scrollContainerRef: mainPanelScrollContainer,
      multilineRow: viewProps.rowHeight === "tall" ? { numRows: 5 } : undefined,
      showRowNumber: true,
      columnReorderer,
      isSortable: false,
      typeHints: {
        ...DuckDBTypeHints[objectType],
        ...Object.fromEntries(
          customColumnState?.customColumnDefinitions?.map((c) => [
            c.name,
            "JSON",
          ]) ?? [],
        ),
      },
      isLoading: initialQueryLoading,
      disabledFilterColumns: ["comments"],
      refetchTooltipContentData,
      noRowsFoundLabel: "No logs found in the specified time range",
    }),
    [
      viewProps.rowHeight,
      customColumnState.customColumnDefinitions,
      objectType,
      initialQueryLoading,
      refetchTooltipContentData,
    ],
  );

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: LogsPanelLayout = {
        ...defaultPanelLayout,
        main: sizes[0],
        trace: sizes[1],
      };
      document.cookie = `react-resizable-panels:logs2-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const queryError =
    paginatedObjectViewerDataComponents.tableQuery.queryErrors?.find(Boolean);
  const paginatedObjectViewerObjectVizComponents =
    usePaginatedObjectViewerVizComponents(
      {
        ...paginatedObjectViewerDataComponents,
      },
      {
        rowIds,
        setRowIds,
        aiSearchType: "logs",
        includeExperimentSelectionSection: true,
        extraLeftControls,
        extraRightControls: (
          <LogsTimeRange
            value={viewProps.timeRangeFilter}
            setValue={viewProps.setTimeRangeFilter}
          />
        ),

        vizQueryPropsOverride,
        customError: queryError ? <>{queryError.message}</> : undefined,
      },
    );

  const isSidenavDocked = useIsSidenavDocked();

  const { isTraceFullscreen } = useTraceFullscreen();

  const columnVisibility = useMemo(() => {
    return {
      ...INITIALLY_VISIBLE_COLUMNS,
      ...viewProps.columnVisibility,
    };
  }, [viewProps.columnVisibility]);

  const header = (
    <LogsHeader
      projectId={projectId ?? ""}
      projectName={projectName}
      orgName={orgName}
      getRowsForExport={getRowsForExport}
      columnVisibility={columnVisibility}
    />
  );

  if (paginatedObjectViewerObjectVizComponents.status === "loading_initial") {
    return (
      <>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          hideReviewButton
          columnVisibility={columnVisibility}
        />
        <BodyWrapper outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}>
          <MainContentWrapper>
            <Skeleton className="mb-2 h-[200px]" />
            <TableSkeleton />
          </MainContentWrapper>
        </BodyWrapper>
      </>
    );
  } else if (
    paginatedObjectViewerObjectVizComponents.status === "loaded_empty"
  ) {
    return (
      <>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          hideReviewButton
          columnVisibility={columnVisibility}
        />
        <BodyWrapper outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}>
          <MainContentWrapper>
            <TableEmptyState
              Icon={Activity}
              label={
                <>
                  There are no logs in this project yet. Events will
                  automatically show up here as you log them. To get started,
                  try this code snippet or{" "}
                  <ExternalLink href={"/docs/guides/logging"}>
                    learn&nbsp;more
                  </ExternalLink>
                  .
                </>
              }
            >
              {ORGS_WITH_DISABLED_EMPTY_STATE_CODE_SNIPPETS.includes(
                orgName,
              ) ? null : (
                <EmptyStateCodeSnippets
                  ts={`import { initLogger, traced } from "braintrust";

const logger = initLogger({
  projectName: "${projectName}",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

async function someLLMFunction(input: string) {
  return traced(async (span) => {
    const output = invokeLLM(input);
    span.log({ input, output });
  });
}`}
                  py={`from braintrust import init_logger, traced

logger = init_logger(project="${projectName}")

@traced
def some_llm_function(input):
    return invoke_llm(input)

def my_route_handler(req):
    return some_llm_function(req.body)`}
                />
              )}
            </TableEmptyState>
          </MainContentWrapper>
        </BodyWrapper>
      </>
    );
  }

  const { tableViewComponents, tracePanelComponents } =
    paginatedObjectViewerObjectVizComponents;

  if (isTraceFullscreen && tracePanelComponents) {
    return (
      <>
        {header}
        <BodyWrapper
          outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}
          innerClassName="flex overflow-hidden flex-1"
        >
          {tracePanelComponents}
        </BodyWrapper>
      </>
    );
  }

  return (
    <>
      {header}
      <BodyWrapper
        innerClassName="flex"
        outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}
      >
        <ResizablePanelGroup
          direction="horizontal"
          autoSaveId="logsPanelLayout"
          className="flex !flex-row-reverse overflow-hidden"
          onLayout={onPanelLayout}
        >
          {tracePanelComponents && (
            <>
              <ResizablePanel
                order={1}
                defaultSize={Math.max(
                  defaultPanelLayout.trace,
                  minSidePanelWidth,
                )}
                minSize={minSidePanelWidth}
                id="trace"
                className="flex flex-col overflow-hidden"
              >
                {tracePanelComponents}
              </ResizablePanel>
              <ResizableHandle />
            </>
          )}
          <ResizablePanel
            order={0}
            minSize={minMainPanelWidth}
            defaultSize={defaultPanelLayout.main}
            id="main"
            className="relative"
          >
            <div
              className={cn(
                "flex flex-col overflow-auto px-3 pb-3 rounded-tl-md",
                HEIGHT_WITH_DOUBLE_TOP_OFFSET,
                {
                  "rounded-tl-none": !isSidenavDocked,
                },
              )}
              ref={mainPanelScrollContainer}
            >
              <ObjectPermissionsDialog
                objectType="project_log"
                objectId={projectId ?? ""}
                objectName={projectName}
                orgName={orgName}
                projectName={projectName}
                open={permissionsOpen}
                onOpenChange={setPermissionsOpen}
              />
              {projectSummaryMetrics ? (
                <div className="sticky left-0 w-full pb-6 pt-4">
                  <div className="mb-2 text-sm text-primary-600">
                    In the past 7 days
                  </div>
                  <LogsSparklines context="logs-page" />
                </div>
              ) : null}

              {tooExpensive ? (
                <TableEmptyState
                  className="sticky left-0 mb-2 w-full pt-2"
                  label="Scores chart unavailable when self-hosting with Postgres-only"
                />
              ) : projectSummaryMetrics ? (
                <LogsProgressInsight
                  className="sticky left-0 mb-2 w-full pt-2"
                  experimentsQuery={logSummaryQuery}
                  experimentsSignals={logSummarySignals}
                  onExperimentClick={({ id }) => {
                    // Add an hour to the date
                    const bucket = new Date(id);
                    let nextBucket: Date;
                    switch (logBucket) {
                      case "hour":
                        nextBucket = new Date(
                          bucket.getTime() + 60 * 60 * 1000,
                        );
                        break;
                      case "day":
                        nextBucket = new Date(
                          bucket.getTime() + 24 * 60 * 60 * 1000,
                        );
                        break;
                      case "week":
                        nextBucket = new Date(
                          bucket.getTime() + 7 * 24 * 60 * 60 * 1000,
                        );
                        break;
                      case "month":
                        nextBucket = new Date(bucket);
                        nextBucket.setMonth(bucket.getMonth() + 1);
                        break;
                      default:
                        nextBucket = new Date();
                        console.assert(false, "Unknown log bucket");
                    }
                    const filter = `created >= ${singleQuote(
                      bucket.toISOString(),
                    )} AND created <= ${singleQuote(nextBucket.toISOString())}`;
                    setSelectedBucket({
                      type: "filter",
                      text: filter,
                      bubble: new Bubble({
                        type: "filter",
                        label: `${formatFullDateTime(bucket)}-${formatFullDateTime(
                          nextBucket,
                        )}`,
                        isReadonly: true,
                        clear: () => {
                          setSelectedBucket(undefined);
                        },
                      }),
                      tree: [
                        {
                          type: "sql_filter",
                          expr: filter,
                        },
                      ],
                    });
                  }}
                  title="Scores over time"
                  bucket={logBucket}
                  setBucket={setLogBucket}
                  insightIdentifier={"project-log-score-progress" + projectId}
                  scoreNames={scoreFields}
                />
              ) : null}
              {tableViewComponents}
              <div className="grow" />
              <Footer
                className="sticky left-0 w-full pb-2 sm:pb-2 lg:pb-2"
                inApp
                orgName={orgName}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </BodyWrapper>
    </>
  );
}
