import { MessagesSquare } from "lucide-react";
import { useEffect } from "react";

import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
} from "#/ui/dropdown-menu";
import { useDiffModeState, useTraceViewTypeState } from "#/ui/query-parameters";
import { ListTree, SquareChartGantt } from "lucide-react";
import { useTraceSearch } from "./trace-search-context";

export const TraceLayoutDropdown = ({
  label = "Trace",
  appendedLabel,
  className,
}: {
  label?: string;
  appendedLabel?: string;
  className?: string;
}) => {
  const [viewType, setViewType] = useTraceViewTypeState();
  const { setSearchOpen } = useTraceSearch();
  const [diffModeState] = useDiffModeState();

  const isDiffMode = diffModeState.enabled;

  // If we somehow end up in diff mode while viewing the thread, switch to trace
  useEffect(() => {
    if (isDiffMode && viewType === "thread") {
      setViewType("trace");
    }
  }, [isDiffMode, viewType, setViewType]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown className={className}>
          {viewType === "timeline"
            ? "Timeline"
            : viewType === "thread"
              ? "Thread"
              : label}
          {appendedLabel}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuCheckboxItem
          checked={viewType === "trace"}
          className="gap-2"
          onSelect={() => {
            setViewType("trace");
          }}
        >
          <ListTree className="size-3" />
          {label}
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={viewType === "timeline"}
          className="gap-2"
          onSelect={() => {
            setViewType("timeline");
            setSearchOpen(false);
          }}
        >
          <SquareChartGantt className="size-3" />
          Timeline
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          disabled={isDiffMode}
          checked={viewType === "thread"}
          className="gap-2"
          onSelect={() => {
            setViewType("thread");
            setSearchOpen(false);
          }}
        >
          <MessagesSquare className="size-3" />
          Thread
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
