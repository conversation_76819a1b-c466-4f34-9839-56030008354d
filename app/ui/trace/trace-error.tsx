import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { deserializeJSONStringAsString } from "#/utils/object";
import { useMemo } from "react";
import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";

export function ErrorDisplay({ error }: { error: unknown }) {
  const errorString = useMemo(() => {
    let e = error;
    if (isDiffObject(error)) {
      e = error[DiffRightField];
    }
    if (typeof e !== "string") {
      // NOTE: This should never happen, but it's probably worth being a bit defensive.
      return JSON.stringify(e, null, 2);
    }
    return deserializeJSONStringAsString(e);
  }, [error]);
  return (
    <div className="group relative mb-4 whitespace-pre-wrap break-words rounded-md border p-2 font-mono text-xs bg-bad-50 border-bad-100 text-primary-800">
      <CopyToClipboardButton
        textToCopy={errorString}
        size="xs"
        variant="ghost"
        className="absolute right-1 top-1 border opacity-0 transition-opacity border-primary-200 text-primary-400 group-hover:opacity-100 group-hover:bg-primary-100"
      />
      {errorString}
    </div>
  );
}
