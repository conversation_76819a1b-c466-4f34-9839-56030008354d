"use client";

import React from "react";
import { DiffNumbers } from "#/ui/diff";
import {
  spanMetricNames,
  type SpanMetrics,
  systemMetricNames,
  type SystemMetrics,
} from "./graph";
import {
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { Tag, tagSortFn } from "#/ui/tag";

import { DateWithTooltip } from "#/ui/date";
import { isEmpty } from "#/utils/object";
import { useTraceSearchGetters } from "./trace-search-context";
import { getSearchResultFieldClassName } from "./search-utils";
import { cn } from "#/utils/classnames";
import { transformMetricName } from "#/ui/type-formatters/metrics";
import { BasicTooltip } from "#/ui/tooltip";
import { HelpCircle } from "lucide-react";
import { formatPriceValue } from "#/ui/type-formatters/metrics";

export function MetricSection({
  systemMetrics,
  spanMetrics,
  tags,
  shouldOnlyRenderTags,
  spanId,
  comparisonClassName,
}: {
  systemMetrics: SystemMetrics | DiffObjectType<SystemMetrics> | undefined;
  spanMetrics:
    | (SpanMetrics & Record<string, number>)
    | DiffObjectType<SpanMetrics & Record<string, number>>
    | undefined;
  tags?: {
    name: string;
    color?: string | null;
    description?: string | null;
    position?: string | null;
  }[];
  shouldOnlyRenderTags?: boolean;
  spanId: string;
  comparisonClassName: string;
}) {
  const { searchResultFields, resultIndex } = useTraceSearchGetters();

  const filteredSpanMetrics: [string, number | React.ReactNode][] | undefined =
    spanMetrics &&
    Object.entries(
      isDiffObject(spanMetrics)
        ? formatSpanMetricsDiff({
            left: spanMetrics[DiffLeftField],
            right: spanMetrics[DiffRightField],
            comparisonClassName,
          })
        : spanMetrics,
    ).filter(
      ([k, v]) =>
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        !(systemMetricNames as readonly string[]).includes(k) &&
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        !(spanMetricNames as readonly string[]).includes(k) &&
        !["caller_lineno", "caller_filename", "caller_functionname"].includes(
          k,
        ) &&
        v !== null &&
        v !== undefined &&
        // Hide token metrics with value 0
        !(k.endsWith("_tokens") && v === 0),
    );

  const formattedSystemMetrics =
    systemMetrics &&
    !shouldOnlyRenderTags &&
    (isDiffObject(systemMetrics)
      ? formatSystemMetricsDiff({
          left: systemMetrics[DiffLeftField]!,
          right: systemMetrics[DiffRightField]!,
          comparisonClassName,
        })
      : formatSystemMetrics(systemMetrics));

  const filteredSystemMetrics =
    formattedSystemMetrics &&
    Object.entries(formattedSystemMetrics).filter(([_, v]) => !isEmpty(v));

  if (
    (!filteredSystemMetrics || filteredSystemMetrics.length === 0) &&
    (!filteredSpanMetrics || filteredSpanMetrics.length === 0)
  )
    return null;

  if (shouldOnlyRenderTags && (!tags || tags.length === 0)) return null;

  return (
    <div className="col-span-2 mb-3 flex flex-wrap gap-x-2 gap-y-3 font-inter @container">
      {systemMetrics ? (
        <>
          {filteredSystemMetrics &&
            filteredSystemMetrics.map(([k, v]) => {
              const tip = metricTips[k];

              const content = (
                <div
                  key={k}
                  className="flex w-full gap-2 @sm:w-44 @sm:flex-col @sm:gap-1"
                >
                  <MetricsLabel>
                    {transformMetricName(k)}
                    {tip ? (
                      <HelpCircle className="ml-1 inline-block size-2.5 text-primary-400" />
                    ) : undefined}
                  </MetricsLabel>
                  <MetricsValue>
                    {typeof v === "number" ? v.toLocaleString() : v}
                  </MetricsValue>
                </div>
              );

              if (tip) {
                return (
                  <BasicTooltip tooltipContent={tip} key={k} side="left">
                    {content}
                  </BasicTooltip>
                );
              }

              return content;
            })}
          {tags && tags.length > 0 && (
            <div
              id="tags-section"
              className={getSearchResultFieldClassName({
                resultIndex,
                searchResultFields,
                spanId,
                field: "tags",
                className: "flex w-full gap-2 @sm:w-44 @sm:flex-col @sm:gap-1",
              })}
            >
              <MetricsLabel>Tags</MetricsLabel>
              <MetricsValue>
                <div className="flex flex-wrap gap-1 text-xs font-normal">
                  {tags.sort(tagSortFn).map((tag, idx) => (
                    <Tag
                      key={idx}
                      color={tag.color}
                      label={tag.name}
                      description={tag.description ?? undefined}
                    />
                  ))}
                </div>
              </MetricsValue>
            </div>
          )}
        </>
      ) : null}
      {/* TODO: Generalize this to distinguish system metrics (token counts) from arbitrary metrics */}
      {filteredSpanMetrics &&
        filteredSpanMetrics.map(([k, v]) => (
          <div
            key={k}
            className="flex w-full gap-2 @sm:w-44 @sm:flex-col @sm:gap-1"
          >
            <MetricsLabel>{transformMetricName(k)}</MetricsLabel>
            <MetricsValue>
              {typeof v === "number" ? v?.toLocaleString() : v}
            </MetricsValue>
          </div>
        ))}
    </div>
  );
}

const metricTips: Record<string, string> = {
  offset: "Time since the first span's start time",
};

function formatSystemMetrics(
  metrics: SystemMetrics,
): Record<keyof SystemMetrics, string | React.ReactNode | undefined> {
  return {
    start: metrics.start ? (
      <TimeMetric
        timeMs={
          metrics.start !== undefined ? metrics.start * 1000 : metrics.start
        }
        fallback="not started"
      />
    ) : undefined,
    duration:
      metrics.duration !== undefined
        ? `${metrics.duration.toLocaleString(undefined, {
            maximumFractionDigits: 4,
          })}s`
        : undefined,
    offset:
      metrics.offset !== undefined && metrics.offset !== 0
        ? `${metrics.offset.toLocaleString(undefined, {
            maximumFractionDigits: 4,
          })}s`
        : undefined,
    time_to_first_token:
      metrics.time_to_first_token !== undefined
        ? `${metrics.time_to_first_token.toLocaleString(undefined, {
            maximumFractionDigits: 2,
          })}s`
        : undefined,
    tokens: metrics.tokens ? `${metrics.tokens.toLocaleString()}` : undefined,
    prompt_tokens: metrics.prompt_tokens
      ? `${metrics.prompt_tokens.toLocaleString()}`
      : undefined,
    prompt_cached_tokens: metrics.prompt_cached_tokens
      ? `${metrics.prompt_cached_tokens.toLocaleString()}`
      : undefined,
    prompt_cache_creation_tokens: metrics.prompt_cache_creation_tokens
      ? `${metrics.prompt_cache_creation_tokens.toLocaleString()}`
      : undefined,
    completion_tokens: metrics.completion_tokens
      ? `${metrics.completion_tokens.toLocaleString()}`
      : undefined,
    estimated_cost: metrics.estimated_cost
      ? `${formatPriceValue(metrics.estimated_cost)}`
      : undefined,
    cached:
      metrics.cached === undefined
        ? undefined
        : !metrics.cached
          ? "false"
          : "true",
    retries:
      !isEmpty(metrics.retries) && metrics.retries > 0
        ? `${metrics.retries.toLocaleString()}`
        : undefined,
  };
}

const TimeMetric = ({
  className,
  timeMs,
  fallback,
}: {
  className?: string;
  timeMs?: number;
  fallback: string;
}) => {
  return timeMs ? (
    <DateWithTooltip
      className={className}
      dateMs={timeMs}
      withSeconds
      relativeTimeOptions={{ round: true, includeSeconds: true }}
    />
  ) : (
    fallback
  );
};

function formatSpanMetricsDiff({
  left,
  right,
  comparisonClassName,
}: {
  left?: Record<string, number> | null;
  right?: Record<string, number> | null;
  comparisonClassName: string;
}): Record<string, number | React.JSX.Element> {
  if (!left || !right) {
    return right ?? left ?? {};
  }

  const keys = Array.from(
    new Set([...Object.keys(left), ...Object.keys(right)]),
  );
  return Object.fromEntries(
    keys.map((key, i) => [
      key,
      <DiffNumbers
        key={i}
        oldNumber={left[key]}
        newNumber={right[key]}
        formatNumber={(n) => n.toLocaleString()}
        percentDiff={(start, end) =>
          (Number(end) - Number(start)) / Number(start)
        }
        oldScoreClassName={comparisonClassName}
      />,
    ]),
  );
}

function formatSystemMetricsDiff({
  left,
  right,
  comparisonClassName,
}: {
  left?: SystemMetrics;
  right?: SystemMetrics;
  comparisonClassName: string;
}): Record<keyof SystemMetrics | string, string | React.ReactNode | undefined> {
  if (!left || !right) {
    return right
      ? formatSystemMetrics(right)
      : left
        ? formatSystemMetrics(left)
        : Object.fromEntries(
            systemMetricNames.map((name) => [name, undefined]),
          );
  }

  return {
    start: (
      <DiffDates
        leftTime={left.start}
        rightTime={right.start}
        fallback="not started"
        comparisonClassName={comparisonClassName}
      />
    ),
    offset: (
      <DiffTimers
        leftTime={left.offset}
        rightTime={right.offset}
        comparisonClassName={comparisonClassName}
      />
    ),
    duration: (
      <DiffTimers
        leftTime={left.duration}
        rightTime={right.duration}
        comparisonClassName={comparisonClassName}
      />
    ),
    time_to_first_token:
      !left.time_to_first_token && !right.time_to_first_token ? null : (
        <DiffTimers
          leftTime={left.time_to_first_token}
          rightTime={right.time_to_first_token}
          comparisonClassName={comparisonClassName}
        />
      ),
    tokens:
      !right.tokens && !left.tokens ? null : (
        <DiffTokens
          leftTokens={left.tokens}
          rightTokens={right.tokens}
          comparisonClassName={comparisonClassName}
        />
      ),
    prompt_tokens:
      !left.prompt_tokens && !right.prompt_tokens ? null : (
        <DiffTokens
          leftTokens={left.prompt_tokens}
          rightTokens={right.prompt_tokens}
          comparisonClassName={comparisonClassName}
        />
      ),
    completion_tokens:
      !left.completion_tokens && !right.completion_tokens ? null : (
        <DiffTokens
          leftTokens={left.completion_tokens}
          rightTokens={right.completion_tokens}
          comparisonClassName={comparisonClassName}
        />
      ),
    cached:
      left.cached !== right.cached ? (
        <DiffNumbers
          oldNumber={left.cached || 0}
          newNumber={right.cached || 0}
          oldScoreClassName={comparisonClassName}
          formatNumber={(n) => (!n || n === 0 ? "false" : "true")}
          percentDiff={(start, end) => (end - start) / start}
          upIsGood={true}
        />
      ) : !left.cached ? (
        "false"
      ) : (
        "true"
      ),
    estimated_cost:
      left.estimated_cost !== undefined &&
      right.estimated_cost !== undefined ? (
        <DiffNumbers
          oldNumber={left.estimated_cost}
          newNumber={right.estimated_cost}
          oldScoreClassName={comparisonClassName}
          formatNumber={(n) => formatPriceValue(n)}
          percentDiff={(start, end) =>
            (Number(end) - Number(start)) / Number(start)
          }
          formatDiff={(start, end) => formatPriceValue(Math.abs(end - start))}
          upIsGood={false}
        />
      ) : right.estimated_cost ? (
        formatPriceValue(right.estimated_cost)
      ) : left.estimated_cost ? (
        formatPriceValue(left.estimated_cost)
      ) : undefined,
    retries:
      !right.retries && !left.retries ? null : (
        <DiffNumbers
          oldNumber={left.retries ?? 0}
          newNumber={right.retries ?? 0}
          formatNumber={(n) => n.toLocaleString()}
          percentDiff={(start, end) => (end - start) / start}
          upIsGood={false}
        />
      ),
  };
}

function roundDecimals(num: number, decimals: number) {
  return Math.round(num * 10 ** decimals) / 10 ** decimals;
}

function DiffDates({
  leftTime,
  rightTime,
  fallback,
  comparisonClassName,
}: {
  leftTime?: number;
  rightTime?: number;
  fallback: string;
  comparisonClassName: string;
}) {
  return (
    <>
      <TimeMetric
        className={cn(comparisonClassName, "bg-transparent text-xs")}
        timeMs={leftTime ? leftTime * 1000 : undefined}
        fallback={fallback}
      />
      <span className="px-1 font-inter text-primary-500">{" -> "}</span>
      <TimeMetric
        className="text-primary-800"
        timeMs={rightTime ? rightTime * 1000 : undefined}
        fallback={fallback}
      />
    </>
  );
}

function DiffTimers({
  leftTime,
  rightTime,
  comparisonClassName,
}: {
  leftTime?: number;
  rightTime?: number;
  comparisonClassName: string;
}) {
  return leftTime !== undefined && rightTime !== undefined ? (
    <DiffNumbers
      oldNumber={roundDecimals(leftTime, 2)}
      oldScoreClassName={comparisonClassName}
      newNumber={roundDecimals(rightTime, 2)}
      formatNumber={(n) =>
        `${n.toLocaleString(undefined, { maximumFractionDigits: 2 })}s`
      }
      formatDiff={(start, end) =>
        `${Math.abs(end - start).toLocaleString(undefined, {
          maximumFractionDigits: 2,
        })}s`
      }
      percentDiff={(start, end) => (end - start) / start}
      upIsGood={false}
    />
  ) : (
    <>{rightTime || leftTime || null}</>
  );
}

function DiffTokens({
  leftTokens,
  rightTokens,
  comparisonClassName,
}: {
  leftTokens?: number;
  rightTokens?: number;
  comparisonClassName: string;
}) {
  return leftTokens !== undefined && rightTokens !== undefined ? (
    <DiffNumbers
      oldNumber={leftTokens}
      newNumber={rightTokens}
      formatNumber={(n) => `${n}`}
      percentDiff={(start, end) =>
        (Number(end) - Number(start)) / Number(start)
      }
      upIsGood={false}
    />
  ) : (
    <>{rightTokens || leftTokens || null}</>
  );
}

const MetricsLabel = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="w-2/5 break-words text-xs text-primary-500 @sm:w-auto @sm:flex-none">
      {children}
    </div>
  );
};
const MetricsValue = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="w-3/5 text-xs @sm:w-auto @sm:text-sm @sm:font-medium">
      {children}
    </div>
  );
};
