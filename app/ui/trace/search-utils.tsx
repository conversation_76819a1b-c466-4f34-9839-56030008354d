import { cn } from "#/utils/classnames";
import { type SearchResultField } from "./trace-search-context";

export const getSearchResultFieldClassName = ({
  resultIndex,
  searchResultFields,
  spanId,
  field,
  className = "",
}: {
  resultIndex?: number | null;
  searchResultFields?: SearchResultField[];
  spanId: string;
  field: string;
  className?: string;
}) => {
  const isHighlighted = searchResultFields?.some(
    (result) =>
      (result.spanRowId === spanId || result.comparisonSpanRowId === spanId) &&
      result.field === field,
  );
  const isActive =
    resultIndex != null &&
    (searchResultFields?.[resultIndex]?.spanRowId === spanId ||
      searchResultFields?.[resultIndex]?.comparisonSpanRowId === spanId) &&
    searchResultFields?.[resultIndex]?.field === field;

  return cn(
    className,
    isHighlighted &&
      "ring-2 ring-offset-4 ring-offset-background ring-yellow-100 dark:ring-yellow-900/60 rounded-md",
    isActive && "ring-yellow-300 dark:ring-yellow-700",
  );
};

export const unknownToString = (value: unknown) => {
  if (value === null) return "null";
  if (typeof value === "string") return value;
  if (typeof value === "number") return value.toString();
  if (typeof value === "boolean") return value.toString();
  if (typeof value === "object") return JSON.stringify(value);
  return "";
};
