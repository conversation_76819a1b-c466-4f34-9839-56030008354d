import { type Span, parsePromptSpanData } from "@braintrust/local";
import { useMemo, useState } from "react";
import { TransactionIdField } from "#/utils/duckdb";
import { FunctionDialog } from "#/ui/prompts/function-editor/function-dialog";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { extractExtraPromptMessages } from "#/ui/prompts/hooks";
import { usePrompt } from "#/ui/prompts/use-prompt";
import { newPrompt } from "#/ui/prompts/schema";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { newId } from "braintrust";
import { toast } from "sonner";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useRouter } from "next/navigation";
import { useSpanPromptMeta } from "./use-span-prompt-meta";
import { resolveModelFromName, useAvailableModels } from "#/ui/prompts/models";
import { produce } from "immer";

export { parsePromptFromSpan } from "@braintrust/local";

export function PromptFromTrace({
  projectId,
  orgName,
  projectName,
  span,
  promptDialogOpened,
  setPromptDialogOpened,
  copilotContext,
  type,
}: {
  projectId: string;
  orgName: string;
  projectName: string;
  span: Span;
  promptDialogOpened: boolean;
  setPromptDialogOpened: (o: boolean) => void;
  copilotContext: CopilotContextBuilder;
  type: FunctionObjectType;
}) {
  const { allAvailableModels } = useAvailableModels({ orgName });
  const promptMeta = useSpanPromptMeta({
    span,
    projectId,
    isLLMSpan: true,
  });

  const [selectedPromptVersion, setSelectedPromptVersion] = useState<
    string | null
  >(null);

  const {
    prompt: loadedPrompt,
    status,
    auditLogScan,
    auditLogSignals,
    createPrompt,
  } = usePrompt({
    promptInfoFromSpan: promptMeta,
    originVersionOverride: selectedPromptVersion,
  });

  const parsedPromptData = useMemo(() => {
    const parsed = parsePromptSpanData(span.data);
    if (!parsed.success) {
      return null;
    }
    const model = parsed.data.options?.model;
    if (model) {
      return produce(parsed.data, (draft) => {
        const { model: modelName } = resolveModelFromName(
          model,
          allAvailableModels,
        );
        if (draft.options) {
          draft.options.model = modelName;
        } else {
          draft.options = { model: modelName };
        }
      });
    }
    return parsed.data;
  }, [span.data, allAvailableModels]);

  const extraMessages = useMemo(() => {
    return parsedPromptData && loadedPrompt
      ? extractExtraPromptMessages(parsedPromptData, loadedPrompt)
      : undefined;
  }, [loadedPrompt, parsedPromptData]);

  const renderedPrompt = useMemo(() => {
    // If the prompt is inline, we need to render a new prompt object with the parsed prompt data
    if (!promptMeta?.id) {
      return {
        ...newPrompt(projectId),
        prompt_data: parsedPromptData,
      };
    }
    if (extraMessages && loadedPrompt?.prompt_data?.prompt?.type === "chat") {
      return produce(loadedPrompt, (draft) => {
        if (draft?.prompt_data?.prompt?.type !== "chat") {
          return;
        }
        draft.prompt_data.prompt.messages = [
          ...(draft.prompt_data.prompt.messages ?? []),
          ...extraMessages,
        ];
      });
    }
    return loadedPrompt ?? null;
  }, [
    loadedPrompt,
    parsedPromptData,
    promptMeta?.id,
    projectId,
    extraMessages,
  ]);

  const isSavedPrompt = !!renderedPrompt?.name;
  const router = useRouter();

  return (
    promptMeta && (
      <FunctionDialog
        type={type}
        context="try_prompt"
        title={`Try ${type}`}
        // This key should be unique for each span, so that one span's data doesn't overwrite the variables
        // for another's "Try prompt". It includes promptVersion so that the content of the dialog is updated when the version changes.
        identifier={`prompt-${renderedPrompt?.id}-${span.id}-${renderedPrompt?.[TransactionIdField]}`}
        initialFunction={renderedPrompt}
        status={status}
        opened={promptDialogOpened}
        setOpened={(newIsOpened) => {
          setPromptDialogOpened(newIsOpened);
          if (!newIsOpened) {
            setSelectedPromptVersion(null);
          }
        }}
        mode={{
          type: isSavedPrompt ? "update" : "create",
          upsert: async (promptToSave, updateSlug) => {
            // Overwrite any prompt_session related id fields if we're creating a new prompt
            if (!isSavedPrompt) {
              promptToSave.id = newId();
              promptToSave[TransactionIdField] = "0";
            }
            const _xact_id = await createPrompt(promptToSave, updateSlug);
            if (!isSavedPrompt) {
              setPromptDialogOpened(false);
              toast.success(`Created ${type}`, {
                action: {
                  label: "Edit in library",
                  onClick: () => {
                    router.push(
                      getSavedPromptLink({
                        orgName,
                        projectSlug: projectName,
                        promptId: promptToSave.id,
                        promptVersion: _xact_id ?? undefined,
                        type,
                      }),
                    );
                  },
                },
              });
            } else {
              setSelectedPromptVersion(_xact_id);
            }
            return _xact_id;
          },
        }}
        activityProps={
          !!renderedPrompt?.id && isSavedPrompt
            ? {
                auditLogScan,
                auditLogReady: auditLogSignals,
                rowId: renderedPrompt.id,
                setPromptVersion: setSelectedPromptVersion,
                selectedVersion: renderedPrompt?.[TransactionIdField] ?? null,
              }
            : undefined
        }
        variableData={promptMeta.variables}
        objectType={isSavedPrompt ? "project_prompts" : "prompt_session"}
        orgName={orgName}
        projectName={projectName}
        projectId={projectId}
        copilotContext={copilotContext}
      />
    )
  );
}
