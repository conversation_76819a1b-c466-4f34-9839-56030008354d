import { proxyV1Url } from "#/utils/url";
import {
  type <PERSON><PERSON><PERSON><PERSON><PERSON>,
  type ClauseSpec,
  type ClauseType,
  type Search,
  addClause,
  makeBubble,
  removeClause,
} from "#/utils/search/search";
import { useOrg } from "#/utils/user";
import { type AISearchResult } from "@braintrust/local";
import { type ClientOptions } from "openai";

import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
  useState,
} from "react";
import { toast } from "sonner";
import { useSessionToken } from "#/utils/auth/session-token";
import useEvent from "react-use-event-hook";

export type ApplySearch = (
  query: string | null,
  clauseToRemove?: ClauseSpec<ClauseType>,
  opts?: {
    originType?: "form" | "btql";
    label?: string;
  },
) => Promise<void>;

const useFilterSortBarSearch = ({
  runAISearch,
  clauseChecker,
  setSearch,
}: {
  runAISearch?: (
    openAIOpts: ClientOptions,
    query: string,
  ) => Promise<AISearchResult>;
  clauseChecker: ClauseChecker | null;
  setSearch?: Dispatch<SetStateAction<Search>>;
}) => {
  const [loading, setLoading] = useState(false);

  const org = useOrg();

  const { getOrRefreshToken } = useSessionToken();

  const openAIBaseURL = useMemo(
    () => proxyV1Url(org.proxy_url),
    [org.proxy_url],
  );

  const registerClause = useMemo(
    () =>
      clauseChecker
        ? async (
            newClause: ClauseSpec<ClauseType>,
            clauseToRemove?: ClauseSpec<ClauseType>,
          ) => {
            const checkResult = await clauseChecker(newClause);
            if (checkResult.type === "checked") {
              setSearch?.((s) => {
                let newSearch = { ...s };
                if (clauseToRemove) {
                  newSearch = removeClause(newSearch, clauseToRemove);
                }
                const updated = addClause(newSearch, {
                  ...newClause,
                  ...checkResult.extraFields,
                  bubble: makeBubble({ clause: newClause, setSearch }),
                });
                return updated;
              });
            }
            return checkResult;
          }
        : null,
    [clauseChecker, setSearch],
  );

  const isValidBTQL = useCallback(
    async (query: string | null) => {
      if (!query || !clauseChecker) {
        return { valid: false };
      }

      const filterResult = await clauseChecker({
        type: "filter",
        text: query,
      });
      if (filterResult.type === "checked") {
        return { valid: true, type: "filter" };
      }

      const sortResult = await clauseChecker({
        type: "sort",
        text: query,
      });
      if (sortResult.type === "checked") {
        return { valid: true, type: "sort" };
      }

      const tagResult = await clauseChecker({
        type: "tag",
        text: query,
      });
      if (tagResult.type === "checked") {
        return { valid: true, type: "tag" };
      }

      return { valid: false };
    },
    [clauseChecker],
  );

  const applySearch = useEvent(
    async (
      query: string | null,
      clauseToRemove?: ClauseSpec<ClauseType>,
      opts?: {
        originType?: "form" | "btql";
        label?: string;
        comparison?: {
          experimentId: string;
        };
      },
    ) => {
      if (!query || !registerClause) {
        return;
      }

      const checkFilterResult = await registerClause(
        {
          type: "filter",
          text: query,
          label: opts?.label,
          originType: opts?.originType,
          comparison: opts?.comparison,
        },
        clauseToRemove,
      );
      if (checkFilterResult.type !== "error") {
        // If the original query is already a valid SQL filter,
        // then we don't need to run AI search.
        return;
      }

      const checkTagResult = await registerClause(
        {
          type: "tag",
          text: query,
          label: opts?.label,
          originType: opts?.originType,
          comparison: opts?.comparison,
        },
        clauseToRemove,
      );
      if (checkTagResult.type !== "error") {
        // If the original query is already a valid SQL tag,
        // then we don't need to run AI search.
        return;
      }

      if (!runAISearch) {
        toast.error(checkFilterResult.error);
        return;
      }

      setLoading(true);
      try {
        // NOTE: Like autoeval based evaluators, this won't work for users with
        // custom bearer token requirements, nor should we continue doing it
        // this way (it should be a function call).
        const sessionToken = await getOrRefreshToken();
        const { match, filter, sort, tags } = await runAISearch(
          {
            baseURL: openAIBaseURL,
            apiKey:
              sessionToken !== "unauthenticated"
                ? sessionToken.btAuthToken
                : undefined,
          },
          query,
        );
        if (!match) {
          const filterRegistered =
            !!filter &&
            (await (async () => {
              const result = await registerClause(
                {
                  type: "filter",
                  text: filter,
                  label: opts?.label,
                  originType: opts?.originType,
                  comparison: opts?.comparison,
                },
                clauseToRemove,
              );
              if (result.type === "error") {
                console.error("Filter error: ", result.error);
              }
              return result.type !== "error";
            })());
          const sortRegistered =
            !!sort &&
            (await (async () => {
              const result = await registerClause(
                {
                  type: "sort",
                  text: sort,
                  label: opts?.label,
                  originType: opts?.originType,
                  comparison: opts?.comparison,
                },
                clauseToRemove,
              );
              if (result.type === "error") {
                console.error("Sort error: ", result.error);
              }
              return result.type !== "error";
            })());
          const tagRegistered =
            !!tags &&
            (
              await Promise.all(
                tags.map(async (t) => {
                  const result = await registerClause(
                    {
                      type: "tag",
                      text: t,
                      label: opts?.label,
                      originType: opts?.originType,
                      comparison: opts?.comparison,
                    },
                    clauseToRemove,
                  );
                  if (result.type === "error") {
                    console.error("Tag error: ", result.error);
                  }
                  return result.type !== "error";
                }),
              )
            ).some((v) => v);
          if (filterRegistered || sortRegistered || tagRegistered) {
            return;
          }
          // TODO: If there is a filter or sort error, attempt to fix by running
          // AI search again with the full chat history and include the SQL error
          // message in the followup prompt. For now, just log the errors and fall
          // back to free text match.
        }
      } catch (e) {
        toast.error("Failed to run AI search", { description: `${e}` });
      } finally {
        setLoading(false);
      }
      // If we were unable to generate a valid SQL filter or sort,
      // register a free text match instead.
      registerClause({ type: "match", text: query }, clauseToRemove);
    },
  );

  return {
    loading,
    applySearch,
    isValidBTQL,
  };
};

export default useFilterSortBarSearch;
