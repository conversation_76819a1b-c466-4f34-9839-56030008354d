import { useState, useCallback, useEffect, useRef, memo } from "react";
import { Button, type ButtonProps } from "#/ui/button";
import { Input } from "#/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Plus, X, Trash, Ellipsis, Asterisk } from "lucide-react";
import type { LucideIcon } from "lucide-react";
import {
  generateId,
  generateUniquePropertyKey,
  getDefaultValue,
  type FieldValue,
  nodesToValue,
  NodeStructureType,
  type SchemaNode,
  TYPE_OPTIONS,
  valueToNodes,
  validateNodeKey,
  NodeValueType,
} from "./schema-node";
import { BasicTooltip } from "#/ui/tooltip";
import {
  detectUnsupportedPatterns,
  UnsupportedSchemaBlock,
  MAX_NESTING_LIMIT,
} from "./schema-unsupported";
import TextArea from "./text-area";
import { cn } from "#/utils/classnames";

const RemoveButton = ({
  id,
  onRemove,
  readOnly,
  show,
  onHoverStart,
  onHoverEnd,
  className,
}: {
  id: string;
  onRemove: (id: string) => void;
  readOnly: boolean;
  show: boolean;
  onHoverStart?: () => void;
  onHoverEnd?: () => void;
  className?: string;
}) => {
  if (!show) return null;
  return (
    <Button
      disabled={readOnly}
      size="xs"
      variant="ghost"
      className={cn(
        "opacity-0 transition-opacity text-primary-400 group-hover:opacity-100",
        className,
      )}
      onClick={() => onRemove(id)}
      onMouseEnter={onHoverStart}
      onMouseLeave={onHoverEnd}
      Icon={Trash}
    />
  );
};

const KeyInput = ({
  node,
  onChange,
  readOnly,
  keyError,
}: {
  node: SchemaNode;
  onChange: (key: string) => void;
  readOnly: boolean;
  keyError?: string;
}) => {
  if (!node.key) return null;

  return (
    <Input
      disabled={readOnly}
      value={node.key}
      onChange={(e) => onChange(e.target.value)}
      className={cn("h-7 flex-1 text-xs font-mono", {
        "border-bad-500 focus-visible:ring-offset-0 focus-visible:border-bad-500 focus-visible:ring-bad-500":
          keyError,
      })}
      placeholder="Enter key"
    />
  );
};

const SchemaIcon = ({ Icon }: { Icon?: LucideIcon }) =>
  Icon ? (
    <div className="size-3 text-primary-500">
      <Icon className="size-3" />
    </div>
  ) : null;

const DescriptionInput = ({
  node,
  onUpdate,
  readOnly,
}: {
  node: SchemaNode;
  onUpdate: (id: string, updates: Partial<SchemaNode>) => void;
  readOnly: boolean;
}) => (
  <TextArea
    disabled={readOnly}
    value={node.description || ""}
    onChange={(e) => onUpdate(node.id, { description: e.target.value })}
    placeholder="Enter description"
    className="resize-none rounded border-0 px-2 py-1.5 text-xs"
  />
);

const ErrorDisplay = ({ error }: { error?: string }) =>
  error ? (
    <div className="px-2 pt-1.5 text-xs text-bad-500">{error}</div>
  ) : null;

interface SchemaNodeEditorProps {
  node: SchemaNode;
  onRemoveNode: (id: string) => void;
  onUpdateNode: (id: string, updates: Partial<SchemaNode>) => void;
  onAddChild: (
    parentId: string,
    type: NodeStructureType | NodeValueType,
  ) => void;
  readOnly?: boolean;
  level?: number;
  parentNode?: SchemaNode;
  hoveredNodeId?: string;
  onHoverNode?: (nodeIds: string[] | null) => void;
  rootNode?: SchemaNode;
  trashHoveredNodeId?: string;
  onTrashHover?: (nodeId: string | undefined) => void;
  currentNestingLevel?: number;
}

const SchemaNodeEditor = memo(
  ({
    node,
    onRemoveNode,
    onUpdateNode,
    onAddChild,
    readOnly = false,
    level = 0,
    parentNode,
    hoveredNodeId,
    onHoverNode,
    rootNode,
    trashHoveredNodeId,
    onTrashHover,
    currentNestingLevel,
  }: SchemaNodeEditorProps) => {
    const typeOption = TYPE_OPTIONS.find((opt) => opt.nodeType === node.type);
    const [keyError, setKeyError] = useState<string | undefined>();
    const [isShaking, setIsShaking] = useState(false);

    const handleKeyChange = (newKey: string) => {
      setKeyError(undefined);

      const validation = validateNodeKey(newKey, parentNode, node.id);
      if (!validation.success) {
        setKeyError(validation.error);
        setIsShaking(true);
        setTimeout(() => setIsShaking(false), 500);
      } else {
        onUpdateNode(node.id, { key: newKey });
      }
    };

    const isValueType = (type: string): type is NodeValueType => {
      return (
        type === NodeValueType.STRING ||
        type === NodeValueType.NUMBER ||
        type === NodeValueType.BOOLEAN ||
        type === NodeValueType.NULL
      );
    };

    const isValueTypeNode = isValueType(node.type);

    const hasEnum = node.enum !== undefined;
    const showEnumCheckbox =
      isValueType(node.type) &&
      (node.type === NodeValueType.STRING ||
        node.type === NodeValueType.NUMBER);

    const showRequiredCheckbox =
      (isValueTypeNode && !!node.key) ||
      !!node.key ||
      (node.type === NodeStructureType.OBJECT &&
        parentNode &&
        parentNode.type !== NodeStructureType.ARRAY);

    const showFlexibleCheckbox = node.type === NodeStructureType.OBJECT;

    return (
      <div
        className={cn(
          "relative flex flex-col items-start gap-2 border-l pl-3 transition-colors focus-within:border-primary-400 hover:border-primary-300",
          {
            "!border-bad-600": trashHoveredNodeId === node.id,
          },
        )}
      >
        <div className="group flex w-full flex-col gap-2">
          <div className="flex items-center gap-2">
            <div
              className={cn(
                "flex items-center gap-1 flex-1 transition-transform ease-in-out",
                { "animate-[shake_0.5s_ease-in-out]": isShaking },
              )}
            >
              <div className="flex">
                <SchemaDropdown
                  buttonClassName="border-r-0 rounded-r-none"
                  onAddProperty={(type) => {
                    if (isValueTypeNode) {
                      onUpdateNode(node.id, {
                        type: type,
                      });
                      return;
                    }
                    const updatedNode: Partial<SchemaNode> = {
                      type,
                      children:
                        type === NodeStructureType.ARRAY ? [] : undefined,
                    };
                    onUpdateNode(node.id, updatedNode);
                  }}
                  readOnly={readOnly}
                  menuButtonText={typeOption?.label}
                  icon={typeOption?.icon}
                />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="xs"
                      Icon={Ellipsis}
                      className="rounded-l-none text-primary-400"
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="!max-w-xs" align="start">
                    <DropdownMenuLabel>Options</DropdownMenuLabel>
                    {showRequiredCheckbox && (
                      <DropdownMenuCheckboxItem
                        checked={node.required}
                        onCheckedChange={(checked) => {
                          onUpdateNode(node.id, { required: checked });
                        }}
                        onSelect={(e) => {
                          e.preventDefault();
                        }}
                      >
                        Required
                      </DropdownMenuCheckboxItem>
                    )}
                    {showEnumCheckbox && (
                      <DropdownMenuCheckboxItem
                        checked={hasEnum}
                        onCheckedChange={(checked) => {
                          onUpdateNode(node.id, {
                            enum: checked ? [] : undefined,
                          });
                        }}
                        onSelect={(e) => {
                          e.preventDefault();
                        }}
                      >
                        Limit values to specific values
                      </DropdownMenuCheckboxItem>
                    )}
                    {showFlexibleCheckbox && (
                      <DropdownMenuCheckboxItem
                        checked={node.additionalProperties}
                        onCheckedChange={(checked) => {
                          onUpdateNode(node.id, {
                            additionalProperties: checked,
                          });
                        }}
                        onSelect={(e) => {
                          e.preventDefault();
                        }}
                      >
                        Allow additional properties
                      </DropdownMenuCheckboxItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <KeyInput
                node={node}
                onChange={handleKeyChange}
                readOnly={readOnly}
                keyError={keyError}
              />
            </div>
            {node.required && (
              <BasicTooltip tooltipContent="This field is required">
                <div className="flex h-7 items-center justify-center text-bad-500">
                  <Asterisk className="size-3" />
                </div>
              </BasicTooltip>
            )}
            <RemoveButton
              id={node.id}
              onRemove={onRemoveNode}
              readOnly={readOnly}
              show={Boolean(node.key || level > 0)}
              onHoverStart={() => onTrashHover?.(node.id)}
              onHoverEnd={() => onTrashHover?.(undefined)}
            />
          </div>
          {node.type === NodeValueType.STRING && hasEnum && (
            <TagInput
              values={node.enum || []}
              onChange={(values) => onUpdateNode(node.id, { enum: values })}
              readOnly={readOnly}
              placeholder="Enter comma-separated string values"
              type="string"
            />
          )}
          {node.type === NodeValueType.NUMBER && hasEnum && (
            <TagInput
              values={node.enum || []}
              onChange={(values) => onUpdateNode(node.id, { enum: values })}
              readOnly={readOnly}
              placeholder="Enter comma-separated number values"
              type="number"
            />
          )}

          <ErrorDisplay error={keyError} />
          <div className="flex flex-col">
            <DescriptionInput
              node={node}
              onUpdate={onUpdateNode}
              readOnly={readOnly}
            />
            {node.additionalProperties && (
              <BasicTooltip tooltipContent="This object allows properties that are not defined in the schema">
                <span className="px-2 text-[11px] text-primary-400">
                  Allow additional properties
                </span>
              </BasicTooltip>
            )}
          </div>
        </div>

        {node.children && node.children.length > 0 && (
          <div className="flex w-full flex-col gap-3 pb-2 pt-3">
            {node.children.map((child) => (
              <SchemaNodeEditor
                key={child.id}
                node={child}
                onRemoveNode={onRemoveNode}
                onUpdateNode={onUpdateNode}
                onAddChild={onAddChild}
                readOnly={readOnly}
                level={level + 1}
                parentNode={node}
                hoveredNodeId={hoveredNodeId || undefined}
                onHoverNode={onHoverNode}
                rootNode={rootNode}
                trashHoveredNodeId={trashHoveredNodeId}
                onTrashHover={(nodeId) => onTrashHover?.(nodeId)}
                currentNestingLevel={(currentNestingLevel || 0) + 1}
              />
            ))}
          </div>
        )}

        {((node.type === NodeStructureType.ARRAY &&
          (!node.children || node.children.length === 0)) ||
          node.type === NodeStructureType.OBJECT) && (
          <BasicTooltip
            tooltipContent={
              currentNestingLevel && currentNestingLevel >= MAX_NESTING_LIMIT
                ? "Maximum nesting level reached"
                : undefined
            }
          >
            <SchemaDropdown
              onAddProperty={(type) => {
                onAddChild(node.id, type);
              }}
              disabled={Boolean(
                currentNestingLevel && currentNestingLevel >= MAX_NESTING_LIMIT,
              )}
              readOnly={readOnly}
              isObject={
                node.type === NodeStructureType.OBJECT ||
                node.type === NodeStructureType.ARRAY
              }
              icon={Plus}
              menuButtonText={
                node.type === NodeStructureType.ARRAY
                  ? "Array item schema"
                  : undefined
              }
              variant="ghost"
              buttonClassName="hover:bg-primary-200 text-primary-500"
            />
          </BasicTooltip>
        )}
      </div>
    );
  },
);
SchemaNodeEditor.displayName = "SchemaNodeEditor";

interface SchemaDropdownProps {
  onAddProperty: (type: NodeStructureType | NodeValueType) => void;
  readOnly: boolean;
  menuButtonText?: string;
  allowedNodeTypes?: (NodeStructureType | NodeValueType)[];
  icon?: LucideIcon;
  disabled?: boolean;
  variant?: ButtonProps["variant"];
  buttonClassName?: string;
  isObject?: boolean;
}

const SchemaDropdown = ({
  onAddProperty,
  readOnly,
  menuButtonText = "Property",
  isObject,
  allowedNodeTypes = [
    NodeValueType.STRING,
    NodeValueType.NUMBER,
    NodeValueType.BOOLEAN,
    NodeValueType.NULL,
    NodeStructureType.ARRAY,
    NodeStructureType.OBJECT,
  ],
  icon,
  variant,
  disabled = false,
  buttonClassName,
}: SchemaDropdownProps) => {
  const renderDropdownItem = (option: (typeof TYPE_OPTIONS)[0]) => (
    <DropdownMenuItem
      key={option.label}
      onSelect={() => onAddProperty(option.nodeType)}
      className="flex items-center gap-2"
    >
      <SchemaIcon Icon={option.icon} />
      {option.label}
    </DropdownMenuItem>
  );

  const valueOptions = TYPE_OPTIONS.filter((opt) => isValueType(opt.nodeType));
  const structureOptions = TYPE_OPTIONS.filter(
    (opt) =>
      !isValueType(opt.nodeType) && allowedNodeTypes.includes(opt.nodeType),
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="xs"
          Icon={icon}
          className={cn(
            "justify-start",
            {
              "w-28": !isObject,
            },
            buttonClassName,
          )}
          isDropdown={!isObject}
          disabled={readOnly || disabled}
          variant={variant}
          iconClassName="text-primary-500"
        >
          <span className="flex-1 text-left">{menuButtonText}</span>
        </Button>
      </DropdownMenuTrigger>
      {!disabled && (
        <DropdownMenuContent align="start">
          {allowedNodeTypes.some((type) => isValueType(type)) && (
            <>
              <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
                Values
              </DropdownMenuLabel>
              {valueOptions.map(renderDropdownItem)}
            </>
          )}
          {(allowedNodeTypes.includes(NodeStructureType.ARRAY) ||
            allowedNodeTypes.includes(NodeStructureType.OBJECT)) && (
            <>
              {allowedNodeTypes.some((type) => isValueType(type)) && (
                <DropdownMenuSeparator />
              )}
              <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
                Structures
              </DropdownMenuLabel>
              {structureOptions.map(renderDropdownItem)}
            </>
          )}
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  );
};

const isValueType = (type: string): type is NodeValueType => {
  return (
    type === NodeValueType.STRING ||
    type === NodeValueType.NUMBER ||
    type === NodeValueType.BOOLEAN ||
    type === NodeValueType.NULL
  );
};

const TagInput = ({
  values,
  onChange,
  readOnly,
  placeholder,
  type,
}: {
  values: string[] | number[];
  onChange: (values: string[] | number[]) => void;
  readOnly: boolean;
  placeholder: string;
  type: "string" | "number";
}) => {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const isStringArray = (arr: unknown[]): arr is string[] =>
    arr.every((v) => typeof v === "string");
  const isNumberArray = (arr: unknown[]): arr is number[] =>
    arr.every((v) => typeof v === "number");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (type === "number") {
      const numberRegex = /^[0-9.,-]*$/;
      if (!numberRegex.test(value)) return;
      const decimalCount = (value.match(/\./g) || []).length;
      if (decimalCount > 1) return;
      const minusCount = (value.match(/-/g) || []).length;
      if (minusCount > 1) return;
      if (value.includes("-") && value.indexOf("-") !== 0) return;
    }
    setInputValue(value);
    if (value.includes(",")) {
      const parts = value
        .split(",")
        .map((part) => part.trim())
        .filter((part) => part !== "");
      if (type === "number" && isNumberArray(values)) {
        const newNumbers = parts
          .map((part) => {
            const cleanPart = part.endsWith(".") ? part.slice(0, -1) : part;
            return parseFloat(cleanPart);
          })
          .filter((num) => !isNaN(num))
          .filter((num) => !values.includes(num));
        if (newNumbers.length > 0) {
          onChange([...values, ...newNumbers]);
        }
      } else if (type === "string" && isStringArray(values)) {
        const newStrings = parts.filter((str) => !values.includes(str));
        if (newStrings.length > 0) {
          onChange([...values, ...newStrings]);
        }
      }
      setInputValue("");
    }
  };

  const handleRemoveValue = (valueToRemove: string | number) => {
    if (type === "number" && isNumberArray(values)) {
      onChange(values.filter((v) => v !== valueToRemove));
    } else if (type === "string" && isStringArray(values)) {
      onChange(values.filter((v) => v !== valueToRemove));
    }
    setTimeout(() => inputRef.current?.focus(), 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && inputValue === "" && values.length > 0) {
      e.preventDefault();
      const lastValue = values[values.length - 1];
      handleRemoveValue(lastValue);
    }
    if (e.key === "Enter" && inputValue.trim()) {
      e.preventDefault();
      if (type === "number" && isNumberArray(values)) {
        const cleanInput = inputValue.trim().endsWith(".")
          ? inputValue.trim().slice(0, -1)
          : inputValue.trim();
        const numValue = parseFloat(cleanInput);
        if (!isNaN(numValue) && !values.includes(numValue)) {
          onChange([...values, numValue]);
        }
      } else if (type === "string" && isStringArray(values)) {
        const strValue = inputValue.trim();
        if (strValue && !values.includes(strValue)) {
          onChange([...values, strValue]);
        }
      }
      setInputValue("");
    }
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  return (
    <div
      className="border-input flex min-h-[28px] cursor-text flex-wrap items-center gap-1 rounded-md border p-1 text-sm"
      onClick={handleContainerClick}
    >
      {values.map((value, index) => (
        <div
          key={index}
          className="flex items-center gap-1 rounded-md border px-1.5 py-0.5 pr-0.5 text-xs bg-primary-100 text-primary-900"
        >
          <span>{String(value)}</span>
          {!readOnly && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveValue(value);
              }}
              className="ml-0 text-muted-foreground hover:text-foreground"
            >
              <X className="size-2.5" />
            </button>
          )}
        </div>
      ))}
      <Input
        ref={inputRef}
        disabled={readOnly}
        value={inputValue}
        className="-ml-2 h-auto min-w-30 flex-1 border-none py-1 text-xs outline-none !bg-transparent placeholder:opacity-75 placeholder:text-muted-foreground focus:border-none focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50"
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={values.length === 0 ? placeholder : ""}
      />
    </div>
  );
};

interface SchemaBuilderProps {
  value: FieldValue;
  onChange: (value: FieldValue) => void;
  readOnly?: boolean;
  forceRefresh?: boolean;
  onUnsupportedIssuesChange?: (hasIssues: boolean) => void;
}

export const SchemaBuilder = ({
  value,
  onChange,
  readOnly = false,
  forceRefresh = false,
  onUnsupportedIssuesChange,
}: SchemaBuilderProps) => {
  const [rootNode, setRootNode] = useState<SchemaNode>(() =>
    valueToNodes(value),
  );
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [trashHoveredNodeId, setTrashHoveredNodeId] = useState<
    string | undefined
  >(undefined);
  const isInternalUpdate = useRef(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const unsupportedIssues = detectUnsupportedPatterns(value);

  useEffect(() => {
    onUnsupportedIssuesChange?.(unsupportedIssues.length > 0);
  }, [unsupportedIssues.length, onUnsupportedIssuesChange]);

  // Debounced onChange to prevent excessive re-renders when we're updating the schema (e.g. typing in the description)
  const debouncedOnChange = useCallback(
    (newValue: FieldValue) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        onChange(newValue);
      }, 300);
    },
    [onChange],
  );

  useEffect(() => {
    if (!isInternalUpdate.current) {
      // We force a complete refresh for example when inserting an example
      if (forceRefresh) {
        setRootNode(valueToNodes(value));
      } else {
        // Preserve existing node structure on normal value changes
        setRootNode((prevNode) => valueToNodes(value, undefined, prevNode));
      }
    }
    isInternalUpdate.current = false;
  }, [value, forceRefresh]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleRemoveNode = useCallback(
    (id: string) => {
      const removeNode = (node: SchemaNode): SchemaNode | null => {
        if (node.id === id) return null;

        if (node.children) {
          const filteredChildren = node.children
            .map((child) => removeNode(child))
            .filter((child): child is SchemaNode => child !== null);

          return { ...node, children: filteredChildren };
        }

        return node;
      };

      const updatedRoot = removeNode(rootNode);
      if (updatedRoot) {
        isInternalUpdate.current = true;
        setRootNode(updatedRoot);
        const newValue = nodesToValue(updatedRoot);
        debouncedOnChange(newValue);
      } else {
        const emptyNode = valueToNodes({});
        isInternalUpdate.current = true;
        setRootNode(emptyNode);
        debouncedOnChange(nodesToValue(emptyNode));
      }
    },
    [rootNode, debouncedOnChange],
  );

  const handleUpdateNode = useCallback(
    (id: string, updates: Partial<SchemaNode>) => {
      const updateNode = (node: SchemaNode): SchemaNode => {
        if (node.id === id) {
          return { ...node, ...updates };
        }

        if (node.children) {
          return {
            ...node,
            children: node.children.map((child) => updateNode(child)),
          };
        }

        return node;
      };

      const updatedRoot = updateNode(rootNode);
      isInternalUpdate.current = true;
      setRootNode(updatedRoot);
      const newValue = nodesToValue(updatedRoot);
      debouncedOnChange(newValue);
    },
    [rootNode, debouncedOnChange],
  );

  const handleAddChild = useCallback(
    (parentId: string, type: NodeStructureType | NodeValueType) => {
      const addChildToNode = (node: SchemaNode): SchemaNode => {
        if (node.id === parentId) {
          // For arrays, adding an item replaces the current items schema
          if (node.type === NodeStructureType.ARRAY) {
            const newItemNode: SchemaNode = {
              id: generateId(),
              type,
              value: getDefaultValue(type),
            };

            return {
              ...node,
              children: [newItemNode],
              isExpanded: true,
            };
          }

          const uniqueKey = generateUniquePropertyKey(node);
          const defaultValue = getDefaultValue(type);
          const newChild: SchemaNode = {
            id: generateId(),
            key: uniqueKey,
            type,
            value: defaultValue,
            ...(type === NodeStructureType.OBJECT ||
            type === NodeStructureType.ARRAY
              ? { children: [] }
              : {}),
          };

          return {
            ...node,
            children: [...(node.children || []), newChild],
            isExpanded: true,
          };
        }

        if (node.children) {
          return {
            ...node,
            children: node.children.map((child) => addChildToNode(child)),
          };
        }

        return node;
      };

      const updatedRoot = addChildToNode(rootNode);
      isInternalUpdate.current = true;
      setRootNode(updatedRoot);
      const newValue = nodesToValue(updatedRoot);
      debouncedOnChange(newValue);
    },
    [rootNode, debouncedOnChange],
  );

  const handleHoverNode = useCallback((nodeIds: string[] | null) => {
    setHoveredNodeId(nodeIds && nodeIds.length > 0 ? nodeIds[0] : null);
  }, []);

  if (unsupportedIssues.length > 0) {
    return <UnsupportedSchemaBlock issues={unsupportedIssues} />;
  }

  return (
    <SchemaNodeEditor
      node={rootNode}
      onRemoveNode={handleRemoveNode}
      onUpdateNode={handleUpdateNode}
      onAddChild={handleAddChild}
      readOnly={readOnly}
      hoveredNodeId={hoveredNodeId || undefined}
      onHoverNode={handleHoverNode}
      rootNode={rootNode}
      trashHoveredNodeId={trashHoveredNodeId}
      onTrashHover={(nodeId) => setTrashHoveredNodeId(nodeId)}
      currentNestingLevel={0}
    />
  );
};
