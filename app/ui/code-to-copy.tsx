import { cn } from "#/utils/classnames";
import { SyntaxHighlight } from "./syntax-highlighter";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";
import { type BundledLanguage } from "./highlight";

interface CodeToCopyProps {
  data: string;
  language?: BundledLanguage;
  className?: string;
  textToCopy?: string;
  disableCopyToClipboardTooltip?: boolean;
  highlighterClassName?: string;
}

export default function CodeToCopy({
  data: currentText,
  textToCopy,
  language = "bash",
  className,
  highlighterClassName,
  disableCopyToClipboardTooltip,
}: CodeToCopyProps) {
  if (typeof currentText !== "string") {
    throw new Error(
      `Unexpected type for data (${currentText}): ${typeof currentText}`,
    );
  }

  const copyContents = textToCopy || currentText;
  return (
    <>
      <div className={cn("relative", className)}>
        <div className="group flex">
          <SyntaxHighlight
            language={language}
            className={cn(
              "flex-1 rounded-md border p-4 text-sm",
              highlighterClassName,
            )}
            content={currentText}
          />
          <CopyToClipboardButton
            textToCopy={copyContents}
            disableTooltip={disableCopyToClipboardTooltip}
            className="absolute right-2 top-2 size-8 p-0 opacity-0 transition-opacity bg-background group-hover:opacity-100"
          />
        </div>
      </div>
    </>
  );
}
