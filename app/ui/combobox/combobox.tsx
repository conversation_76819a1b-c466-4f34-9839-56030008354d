import * as React from "react";

import { cn } from "#/utils/classnames";
import { ChevronDownIcon, XIcon } from "lucide-react";
import { Button, type ButtonProps } from "#/ui/button";
import { ComboboxCommand, type ComboboxCommandProps } from "./combobox-command";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { type PopoverContentProps } from "@radix-ui/react-popover";
import { BasicTooltip } from "#/ui/tooltip";
export type ComboboxProps<T extends { value: string; label: string }> =
  ComboboxCommandProps<T> & {
    selectedValue?: string;
    contentClassName?: string;
    buttonClassName?: string;
    buttonSize?: ButtonProps["size"];
    buttonVariant?: ButtonProps["variant"];
    placeholderLabel?: string | React.ReactNode;
    placeholderClassName?: string;
    stayOpenOnChange?: boolean;
    variant: "inline" | "button";
    disabled?: boolean;
    contentWidth?: number;
    clearable?: boolean;
    triggerClassName?: string;
    onChange: (value: string | undefined, option: T) => void;
    renderComboboxDisplayLabel?: (option: T) => React.ReactNode;
    iconSize?: number;
    iconClassName?: string;
    renderOptionTooltip?: (option: T) => React.ReactNode;
    modal?: boolean;
    side?: PopoverContentProps["side"];
    align?: PopoverContentProps["align"];
    tooltipContent?: string;
    isLoading?: boolean;
  };

const defaultLabelRenderer = (option: { label: string }) => option.label;

export function Combobox<
  T extends {
    value: string;
    label: string;
    disabled?: boolean;
  },
>({
  selectedValue,
  contentClassName,
  buttonClassName,
  buttonSize,
  buttonVariant,
  iconSize = 12,
  iconClassName,
  children, // children can be used instead of placeholder label
  placeholderLabel,
  placeholderClassName,
  stayOpenOnChange,
  variant,
  align,
  side,
  disabled = false,
  clearable,
  renderComboboxDisplayLabel = defaultLabelRenderer,
  triggerClassName,
  onChange,
  modal,
  tooltipContent,
  isLoading,
  ...commandProps
}: React.PropsWithChildren<ComboboxProps<T>>) {
  const [open, setOpen] = React.useState(false);

  const selectedOption = commandProps.options
    .flatMap((o) => ("options" in o ? o.options : [o]))
    .find((option) => option.value === selectedValue);

  const isInline = variant === "inline";

  const button = children ?? (
    <Button
      disabled={disabled}
      isLoading={isLoading}
      role="combobox"
      aria-controls="combobox-options"
      aria-expanded={open}
      variant={isInline ? "ghost" : buttonVariant}
      transparent={isInline}
      size={buttonSize ? buttonSize : isInline ? "inline" : undefined}
      className={cn("group/button", buttonClassName)}
    >
      <span className={placeholderClassName}>
        {selectedOption
          ? renderComboboxDisplayLabel(selectedOption)
          : placeholderLabel}
      </span>
      {clearable &&
      !isInline &&
      selectedOption &&
      selectedOption.value !== "" ? (
        <XIcon
          size={iconSize}
          className="ml-0.5 flex-none cursor-pointer text-primary-400 hover:text-primary-800"
          onClick={(e) => {
            e.stopPropagation();
            onChange(undefined, selectedOption);
          }}
        />
      ) : (
        <ChevronDownIcon
          size={iconSize}
          className={cn("flex-none text-primary-500", iconClassName)}
        />
      )}
    </Button>
  );

  return (
    <Popover open={open} onOpenChange={setOpen} modal={modal}>
      <PopoverTrigger asChild className={triggerClassName}>
        {tooltipContent ? (
          <div
            className={cn({
              "pointer-events-none": disabled,
            })}
          >
            <BasicTooltip tooltipContent={tooltipContent}>
              {button}
            </BasicTooltip>
          </div>
        ) : (
          button
        )}
      </PopoverTrigger>
      <PopoverContent
        align={align}
        side={side}
        collisionPadding={5}
        hideWhenDetached
        className={cn("p-0 overflow-hidden flex flex-col", contentClassName)}
        style={{ width: commandProps.contentWidth }}
      >
        <ComboboxCommand<T>
          {...commandProps}
          selectedValue={selectedValue}
          clearable={clearable}
          onChange={(value, option) => {
            onChange(value, option);
            if (!stayOpenOnChange) {
              setOpen(false);
            }
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
