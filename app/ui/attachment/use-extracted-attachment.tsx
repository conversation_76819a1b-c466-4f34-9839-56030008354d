"use client";

import { isEmpty } from "@braintrust/core";
import { getContentType, getImageString } from "#/ui/image-utils";
import {
  braintrustAttachmentReferenceSchema,
  externalAttachmentReferenceSchema,
  type AttachmentReference,
  type AttachmentStatus,
} from "@braintrust/core/typespecs";
import { attachmentStatusSchema } from "@braintrust/core/typespecs";
import { useQuery } from "@tanstack/react-query";
import { apiFetchGet } from "#/utils/btapi/fetch";
import { useOrg } from "#/utils/user";
import {
  isAuthenticatedSession,
  useSessionToken,
} from "#/utils/auth/session-token";
import { toast } from "sonner";
import { z } from "zod";
import { isBase64File } from "@braintrust/local/functions";
import mime from "mime-types";

const ENABLE_THUMBNAIL_MAX_SIZE_BYTES = 2_000_000; // 2 MB.
const ENABLE_VIEWER_MAX_SIZE_BYTES = 20_000_000; // 20 MB.

const inlineAttachmentReferenceSchema = z.object({
  type: z.literal("inline_attachment"),
  src: z.string().min(1),
  filename: z.string().optional(),
  content_type: z.string().optional(),
});

type InlineAttachmentReference = z.infer<
  typeof inlineAttachmentReferenceSchema
>;
export type ExtractedAttachment =
  | AttachmentReference
  | InlineAttachmentReference;

export type AttachmentType = ExtractedAttachment["type"];

export const isAttachmentReference = (value: unknown): boolean => {
  if (!value || typeof value !== "object" || !("type" in value)) {
    return false;
  }

  switch (value.type) {
    case "inline_attachment":
      return inlineAttachmentReferenceSchema.safeParse(value).success;
    case "external_attachment":
      return externalAttachmentReferenceSchema.safeParse(value).success;
    case "braintrust_attachment":
      return braintrustAttachmentReferenceSchema.safeParse(value).success;
    default:
      return false;
  }
};

export const getAttachmentSource = (extracted: ExtractedAttachment) => {
  switch (extracted.type) {
    case "inline_attachment":
      return extracted.src;
    case "external_attachment":
      return extracted.url;
    case "braintrust_attachment":
      return extracted.key;
    default:
      const _exhaustiveCheck: never = extracted;
      throw new Error(`Unknown attachment type: ${_exhaustiveCheck}`);
  }
};

export const useExtractedAttachment = ({
  extracted,
}: {
  extracted?: ExtractedAttachment;
}) => {
  const attachmentUrlResponse = useAttachmentLoader(extracted);

  if (!extracted) return;
  const isFromUrl = extracted.type === "inline_attachment";

  const contentType = extracted.content_type ?? "";
  const filename = extracted.filename ?? "(embedded)";

  const status: AttachmentStatus = isFromUrl
    ? { upload_status: "done" }
    : (attachmentUrlResponse?.status ?? {
        upload_status: "done",
      });
  const downloadUrl = isFromUrl
    ? extracted.src
    : attachmentUrlResponse?.downloadUrl;
  const embedUrl = isFromUrl ? extracted.src : attachmentUrlResponse?.embedUrl;
  const contentLength = attachmentUrlResponse?.contentLength;

  const isLoaded =
    !isEmpty(status) && status.upload_status === "done" && !!downloadUrl;
  const isAudio = contentType.startsWith("audio/");
  const isImage = contentType.startsWith("image/");
  const isVideo = contentType.startsWith("video/");
  const isPdf = contentType === "application/pdf";
  const isVisual = isImage || isPdf || isVideo;
  // Avoid loading large images. Could be replaced with a thumbnail endpoint.
  const enableThumbnail =
    isEmpty(contentLength) || contentLength < ENABLE_THUMBNAIL_MAX_SIZE_BYTES;
  const isStreaming = isAudio || isVideo;
  const enableViewer =
    isStreaming ||
    isFromUrl ||
    (!isEmpty(contentLength) && contentLength < ENABLE_VIEWER_MAX_SIZE_BYTES);

  return {
    type: extracted.type,
    filename,
    status,
    contentType,
    downloadUrl,
    embedUrl,
    isLoaded,
    isAudio,
    isImage,
    isVideo,
    isPdf,
    isVisual,
    enableThumbnail,
    enableViewer,
  };
};

const maybeURL = (str: string): URL | undefined => {
  // short circuit to avoid parsing base64 data URLs and other non-embeddable protocols
  if (!str.startsWith("http://") && !str.startsWith("https://")) {
    return undefined;
  }

  try {
    const url = new URL(str);
    if (url.protocol === "http:" || url.protocol === "https:") {
      return url;
    }
  } catch {
    // yeet the error
  }
  return undefined;
};

// infer mime type from file extension and check if it's something we support rendering as an attachment
const guessContentType = (url: URL): string | undefined => {
  try {
    const ext = url.pathname.split(".").pop()?.toLowerCase();
    if (ext) {
      const contentType = mime.lookup(ext) || undefined;
      if (
        contentType &&
        (contentType.startsWith("audio/") ||
          contentType.startsWith("image/") ||
          contentType.startsWith("video/") ||
          contentType === "application/pdf")
      ) {
        return contentType;
      }
    }
  } catch {
    // yeet the error
  }
  return undefined;
};

export function extractAttachmentReferences(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Log data can have any shape.
  data: any,
): [ExtractedAttachment[], string[]] {
  const attachments: ExtractedAttachment[] = [];
  const keyPaths: string[] = [];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Log data can have any shape.
  const extractAttachmentsHelper = (data: any, keyPath: string[]) => {
    try {
      const src = getImageString(data);
      const url = maybeURL(src);
      const content_type = url ? guessContentType(url) : undefined;
      if (url && content_type) {
        attachments.push({
          type: "inline_attachment",
          src,
          filename: url.pathname.split("/").pop() || undefined,
          content_type,
        });
        keyPaths.push(keyPath.join("|"));
        return;
      } else if (
        isBase64File(
          src,
          (mimeType) =>
            mimeType.startsWith("image/") || mimeType === "application/pdf",
        )
      ) {
        const content_type = getContentType(src);
        attachments.push({ type: "inline_attachment", src, content_type });
        keyPaths.push(keyPath.join("|"));
        return;
      }
    } catch {
      // Do nothing.
    }

    if (isEmpty(data) || !(data instanceof Object)) {
      return;
    }
    if (data.type && data.type === "braintrust_attachment") {
      const attachment = braintrustAttachmentReferenceSchema.safeParse(data);
      if (attachment.success) {
        attachments.push(attachment.data);
        keyPaths.push(keyPath.join("|"));
      }
      return; // Attachment cannot be nested inside another attachment.
    } else if (data.type && data.type === "external_attachment") {
      const attachment = externalAttachmentReferenceSchema.safeParse(data);
      if (attachment.success) {
        attachments.push(attachment.data);
        keyPaths.push(keyPath.join("|"));
      }
      return; // Attachment cannot be nested inside another attachment.
    } else if (data.type && data.type === "inline_attachment") {
      const attachment = inlineAttachmentReferenceSchema.safeParse(data);
      if (attachment.success) {
        const ref = attachment.data;
        if (!ref.filename || !ref.content_type) {
          const url = maybeURL(ref.src);
          if (url && !ref.filename) {
            ref.filename = url.pathname.split("/").pop() || undefined;
          }
          if (url && !ref.content_type) {
            ref.content_type = guessContentType(url);
          }
        }
        attachments.push(ref);
        keyPaths.push(keyPath.join("|"));
      }
      return; // Attachment cannot be nested inside another attachment.
    }

    for (const key in data) {
      keyPath.push(key);
      extractAttachmentsHelper(data[key], keyPath);
      keyPath.pop();
    }
  };

  extractAttachmentsHelper(data, ["root"]);

  return [attachments, keyPaths];
}

export const attachmentUrlResponseSchema = z.object({
  downloadUrl: z.string().url().optional(),
  embedUrl: z.string().url().optional(),
  contentLength: z.number().optional(),
  status: attachmentStatusSchema.optional(),
});

export type AttachmentURLResponse = z.infer<typeof attachmentUrlResponseSchema>;

export function useAttachmentLoader(
  reference?: ExtractedAttachment,
): AttachmentURLResponse {
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const queryId = reference ? getAttachmentSource(reference) : undefined;

  const { data: response = {} } = useQuery({
    enabled: !!reference,
    queryKey: [org.id, org.api_url, "attachment", queryId],
    queryFn: async () => {
      if (
        !reference ||
        !org.id ||
        !isAuthenticatedSession(await getOrRefreshToken()) ||
        reference.type === "inline_attachment"
      ) {
        return {};
      }

      const requestParams = new URLSearchParams({
        ...(reference.type === "external_attachment"
          ? { url: reference.url }
          : { key: reference.key }),
        filename: reference.filename,
        content_type: reference.content_type,
        org_id: org.id,
      }).toString();

      const resp = await apiFetchGet(
        `${org.api_url}/attachment?${requestParams}`,
        await getOrRefreshToken(),
      );

      if (!resp.ok) {
        toast.error(
          `Couldn't load attachment "${reference.filename}": ${resp.statusText}`,
        );
        console.error(resp);
        throw new Error(`Failed to load attachment: ${resp.statusText}`);
      }

      const parsedResp = attachmentUrlResponseSchema.safeParse(
        await resp.json(),
      );
      if (!parsedResp.success) {
        toast.error(
          `API returned invalid response while loading attachment "${reference.filename}"`,
        );
        console.error(parsedResp.error);
        throw parsedResp.error;
      }

      return parsedResp.data;
    },
  });

  return response;
}
