"use client";

import type { AttachmentStatus } from "@braintrust/core/typespecs";
import type { CSSProperties, Dispatch, SetStateAction } from "react";
import { Loading } from "#/ui/loading";
import { cn } from "#/utils/classnames";

export function AttachmentViewer({
  status,
  src,
  filename,
  contentType,
  setAudioPlaying,
  id,
  className,
  style,
}: {
  status: AttachmentStatus;
  src?: string;
  filename: string;
  contentType: string;
  setAudioPlaying?: Dispatch<SetStateAction<boolean>>;
  id?: string;
  className?: string;
  style?: CSSProperties;
}) {
  const commonProps = { id, className, style, src };

  if (status.upload_status !== "done" || !src) {
    return <Loading className={className} />;
  }

  if (contentType.startsWith("image/")) {
    return (
      <img
        {...commonProps}
        className={cn("object-contain mx-auto", className)}
        alt={filename}
      />
    );
  } else if (contentType.startsWith("audio/")) {
    return (
      <audio
        {...commonProps}
        controls
        preload="none"
        className={cn("w-full", className)}
        onPlay={() => setAudioPlaying?.(true)}
        onPause={() => setAudioPlaying?.(false)}
      />
    );
  } else if (contentType.startsWith("video/")) {
    return <video {...commonProps} controls />;
  } else if (contentType === "application/pdf") {
    return <iframe {...commonProps} className={cn(className, "rounded-md")} />;
  } else {
    return (
      <p className="text-center text-sm text-primary-500">
        Preview not available for this file type
      </p>
    );
  }
}
