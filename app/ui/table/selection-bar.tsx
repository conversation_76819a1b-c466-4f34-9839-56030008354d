import { cn } from "#/utils/classnames";
import React, { forwardRef } from "react";
import { Button, type ButtonProps } from "#/ui/button";
import { XIcon } from "lucide-react";

export const SelectionBar = ({
  className,
  children,
  ...otherProps
}: React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
>) => {
  return (
    <div
      className={cn(
        "pointer-events-none absolute inset-x-0 bottom-0 flex justify-center px-6 pb-12 z-20",
        className,
      )}
    >
      <div
        className={cn(
          "flex items-center p-2 pr-3 flex-wrap bg-background border-2 border-accent-500 rounded-md gap-2 pointer-events-auto shadow-lg",
        )}
        {...otherProps}
      >
        {children}
      </div>
    </div>
  );
};

export const CancelSelectionButton = ({
  onCancelSelection,
  className,
  selectedRowsNumber,
  selectedLabel = "selected",
  ...otherProps
}: {
  selectedRowsNumber: number;
  onCancelSelection: () => void;
  selectedLabel?: string;
} & ButtonProps) => {
  return (
    <Button
      variant="primary"
      size="xs"
      onClick={onCancelSelection}
      title="Reset selection"
      Icon={XIcon}
      className={className}
      {...otherProps}
    >
      {selectedRowsNumber} {selectedLabel}
    </Button>
  );
};

export const SelectionBarButton = forwardRef<HTMLButtonElement, ButtonProps>(
  function SelectionBarButton({ className, children, ...otherProps }, ref) {
    return (
      <Button size="xs" className={className} {...otherProps} ref={ref}>
        {children}
      </Button>
    );
  },
);
