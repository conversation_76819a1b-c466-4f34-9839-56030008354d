import { type CustomColumn, type SpanIFrame } from "@braintrust/core/typespecs";
import { type ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";
import { type StreamingContentProps } from "./cells/streaming";
import { type ViewProps } from "#/utils/view/use-view";
import {
  gridBaseColumn,
  gridComparisonColumns,
  gridExpectedColumn,
  gridInputColumn,
  gridMetadataColumn,
  gridTagsColumn,
  gridSpanIframeFields,
  type SetColumnSortFn,
} from "./formatters/grid-layout-columns";
import { type RowComparisonFormatterProps } from "./formatters/row-comparison-formatter";
import { type RowData } from "#/ui/arrow-table";

export default function useGridColumnDefs<TsData extends RowData, TsValue>({
  isPlayground,
  isLoopEnabled,
  gridLayout,
  experimentName,
  comparisonExperiments,
  customColumns,
  columnVisibility,
  neverVisibleColumns,
  span_iframes,
  streamingContentProps,
  rowHeight,
  rowComparisonProps,
  setColumnSort,
  removeLimiter,
  isExpectedOutput,
}: {
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  gridLayout: boolean;
  experimentName?: string;
  columns: ColumnDef<TsData, TsValue>[];
  comparisonExperiments?: { name: string; id: string }[];
  customColumns?: CustomColumn[];
  columnVisibility: Record<string, boolean>;
  neverVisibleColumns?: Set<string>;
  span_iframes: SpanIFrame[];
  streamingContentProps?: StreamingContentProps;
  rowHeight: ViewProps["rowHeight"];
  rowComparisonProps?: RowComparisonFormatterProps;
  setColumnSort: SetColumnSortFn<TsData>;
  removeLimiter?: boolean;
  isExpectedOutput: boolean;
}) {
  const spanIframeColumnDefs = useMemo(
    () => gridSpanIframeFields<TsData, TsValue>({ spanIframes: span_iframes }),
    [span_iframes],
  );
  const inputColumnDef = useMemo(
    () => gridInputColumn<TsData, TsValue>({ isPlayground, setColumnSort }),
    [isPlayground, setColumnSort],
  );
  const expectedColumnDef = useMemo(
    () => gridExpectedColumn<TsData, TsValue>({ setColumnSort }),
    [setColumnSort],
  );
  const customColNames = useMemo(
    () => customColumns?.map(({ name }) => name),
    [customColumns],
  );
  const filteredColumnVisibility = useMemo(
    () => ({
      ...columnVisibility,
      ...Object.fromEntries(
        Array.from(neverVisibleColumns ?? []).map((k) => [k, false]),
      ),
    }),
    [columnVisibility, neverVisibleColumns],
  );
  const baseColumnDef = useMemo(
    () =>
      experimentName
        ? gridBaseColumn<TsData, TsValue>({
            experimentName,
            columnVisibility: filteredColumnVisibility,
            customColumns: customColNames,
            streamingContentProps,
            tableRowHeight: rowHeight ?? "compact",
            isPlayground,
            isLoopEnabled,
            setColumnSort,
            removeLimiter,
            index: 0,
          })
        : null,
    [
      experimentName,
      filteredColumnVisibility,
      customColNames,
      streamingContentProps,
      rowHeight,
      isPlayground,
      isLoopEnabled,
      setColumnSort,
      removeLimiter,
    ],
  );
  const comparisonColumnDefs = useMemo(
    () =>
      gridComparisonColumns<TsData, TsValue>({
        comparisonExperiments,
        columnVisibility: filteredColumnVisibility,
        rowComparisonProps,
        customColumns: customColNames,
        streamingContentProps,
        tableRowHeight: rowHeight ?? "compact",
        isPlayground,
        isLoopEnabled,
        removeLimiter,
        setColumnSort,
      }),
    [
      comparisonExperiments,
      filteredColumnVisibility,
      rowComparisonProps,
      customColNames,
      streamingContentProps,
      rowHeight,
      isPlayground,
      isLoopEnabled,
      removeLimiter,
      setColumnSort,
    ],
  );
  const metadataColumnDef = useMemo(
    () => gridMetadataColumn<TsData, TsValue>({ setColumnSort }),
    [setColumnSort],
  );
  const tagsColumnDef = useMemo(
    () => gridTagsColumn<TsData, TsValue>({ setColumnSort }),
    [setColumnSort],
  );

  const gridLayoutColumns = useMemo(() => {
    if (!gridLayout || !baseColumnDef) {
      return;
    }
    const cols: ColumnDef<TsData, TsValue>[] = [];
    cols.push(...spanIframeColumnDefs);
    cols.push(inputColumnDef);
    if (isPlayground && !isExpectedOutput) {
      cols.push(expectedColumnDef);
    }

    cols.push(baseColumnDef);
    cols.push(...comparisonColumnDefs);
    if (isPlayground) {
      cols.push(tagsColumnDef);
      cols.push(metadataColumnDef);
    }

    return cols;
  }, [
    gridLayout,
    spanIframeColumnDefs,
    isPlayground,
    baseColumnDef,
    comparisonColumnDefs,
    inputColumnDef,
    expectedColumnDef,
    metadataColumnDef,
    tagsColumnDef,
    isExpectedOutput,
  ]);

  return {
    gridLayoutColumns,
    // return undefined explicitly for iframe columns if gridLayoutColumns is also undefined
    spanIframeColumns: gridLayoutColumns ? spanIframeColumnDefs : undefined,
  };
}
