import { type RowId } from "#/utils/diffs/diff-objects";
import { useEffect } from "react";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { useActiveRowAndSpan } from "#/ui/query-parameters";

type Props = {
  isHumanReviewModeEnabled?: boolean;
  openSidePanel: (
    rowId: RowId,
    opts?: { humanReviewMode?: boolean; openInNewTab?: boolean },
  ) => void;
  rowIds: RowId[];
};

/**
 * Handle "r" hotkey when not in sidebar or human review mode.
 * This is mounted as a component returning null to limit the hotkey-context re-renders
 */
export default function TableHotkeyManager({
  isHumanReviewModeEnabled,
  openSidePanel,
  rowIds,
}: Props) {
  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const firstRowId = rowIds[0];
  const { disableScope, enableScope, enabledScopes } = useHotkeysContext();

  useEffect(() => {
    enableScope("table");
    return () => {
      disableScope("table");
    };
  }, [enableScope, disableScope]);

  useHotkeys(
    "r",
    () => {
      if (!firstRowId) return;
      openSidePanel(activeRowId ?? firstRowId, { humanReviewMode: true });
    },
    {
      enabled:
        isHumanReviewModeEnabled &&
        !enabledScopes.includes("human-review") &&
        !!firstRowId,
      description: "Open human review mode",
      preventDefault: true,
    },
  );

  return null;
}
