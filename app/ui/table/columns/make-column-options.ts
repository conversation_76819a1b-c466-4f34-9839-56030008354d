import { type Column } from "@tanstack/react-table";
import {
  GRID_EXPECTED_COLUMN_ID,
  GRID_INPUT_COLUMN_ID,
  GRID_METADATA_COLUMN_ID,
} from "#/ui/table/formatters/grid-layout-columns";
import { RowStarColumnId } from "#/ui/table/display-columns";

const CONTROLLABLE_GRID_COLUMNS: Record<
  string,
  {
    baseId: string;
    sortValue: number;
  }
> = {
  [GRID_INPUT_COLUMN_ID]: { baseId: "input", sortValue: -2 },
  [GRID_EXPECTED_COLUMN_ID]: { baseId: "expected", sortValue: -1 },
  [GRID_METADATA_COLUMN_ID]: { baseId: "metadata", sortValue: 1 },
};

const GRID_BASE_COLUMN_IDS = new Set(
  Object.values(CONTROLLABLE_GRID_COLUMNS).map((v) => v.baseId),
);

export function makeColumnOptions<TsData, TsValue>({
  columns,
  isGridLayout,
  isPlayground,
  neverVisibleColumns,
  disabledFilterColumns,
  inferEnabled,
}: {
  columns: Column<TsData, TsValue>[];
  isGridLayout: boolean;
  isPlayground?: boolean;
  neverVisibleColumns?: Set<string>;
  disabledFilterColumns?: string[];
  inferEnabled: boolean;
}) {
  const baseColumns = columns.filter((c) => {
    if (c.columnDef.meta?.internalType != null) {
      return false;
    }
    if (neverVisibleColumns?.has(c.id)) {
      return false;
    }

    if (
      isGridLayout &&
      (c.id === "output_vs_expected" ||
        c.id === "id" ||
        c.id === "scores" ||
        c.id === "metrics")
    ) {
      return false;
    }

    return true;
  });

  const visibilityColumns = !isGridLayout
    ? baseColumns
    : baseColumns
        .filter((c) => {
          if (
            c.id === "output" ||
            c.id === "span_type_info" ||
            c.id === "created" ||
            c.id === "id" ||
            c.id === "input" ||
            c.id.startsWith("metrics.")
          ) {
            return false;
          }

          if (isPlayground && GRID_BASE_COLUMN_IDS.has(c.id)) {
            return false;
          }

          if (c?.columnDef.meta?.isGridLayout) {
            return isPlayground
              ? CONTROLLABLE_GRID_COLUMNS[c.id] != null
              : c.id === GRID_INPUT_COLUMN_ID;
          }
          return true;
        })
        .toSorted((a, b) => {
          const sortValueA = CONTROLLABLE_GRID_COLUMNS[a.id]?.sortValue;
          const sortValueB = CONTROLLABLE_GRID_COLUMNS[b.id]?.sortValue;
          if (sortValueA != null && sortValueB != null) {
            return sortValueA - sortValueB;
          }
          return sortValueA ?? sortValueB ?? 0;
        });

  const starColumn = columns.find((c) => c.id === RowStarColumnId);
  const filterableColumns = baseColumns.concat(starColumn ?? []).filter((c) => {
    if (disabledFilterColumns?.includes(c.id)) {
      return false;
    }
    if (isGridLayout && isPlayground && c.id === "span_type_info") {
      return false;
    }
    if (c.id === "metadata" && !inferEnabled) return false;
    if (c.columnDef.meta?.isGridLayout) return false;
    if (c.id.startsWith("spaniframes.")) {
      return false;
    }
    return true;
  });
  return { visibilityColumns, filterableColumns };
}
