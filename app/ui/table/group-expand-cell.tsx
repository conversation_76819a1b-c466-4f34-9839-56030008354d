import { type Cell } from "@tanstack/react-table";
import { But<PERSON> } from "#/ui/button";
import { ChevronRight } from "lucide-react";
import { cn } from "#/utils/classnames";

interface GroupExpandCellProps<TsData> {
  cell: Cell<TsData, unknown>;
}

export function SpacerGroupCell<TsData>({
  cell,
}: GroupExpandCellProps<TsData>) {
  return (
    <div
      style={{ width: cell.column.getSize() }}
      className="h-full cursor-pointer pt-3 border-primary-200"
      onClick={(e) => {
        e.stopPropagation();
        cell.row.toggleExpanded();
      }}
    />
  );
}

export function GroupExpandCell<TsData>({
  cell,
}: GroupExpandCellProps<TsData>) {
  return (
    <div
      style={{ width: cell.column.getSize() }}
      className="flex h-full cursor-pointer justify-center pt-3 text-primary-300"
      onClick={(e) => {
        e.stopPropagation();
        cell.row.toggleExpanded();
      }}
    >
      <Button
        className={cn("size-7 p-0 text-primary-400 transition-transform", {
          "rotate-90": cell.row.getIsExpanded(),
        })}
        transparent
      >
        <ChevronRight className="size-4" />
      </Button>
    </div>
  );
}
