import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "#/ui/dropdown-menu";
import { type DiffObjectType, getDiffRight } from "#/utils/diffs/diff-objects";
import Link from "next/link";
import { ExternalLink, ListFilter, ListPlus } from "lucide-react";
import { getExperimentLink } from "#/app/app/[org]/p/[project]/experiments/[experiment]/getExperimentLink";
import { ExperimentColorSwatch } from "#/ui/charts/colors";
import { regressionFilterSerialize } from "#/ui/query-parameters";
import { type RegressionFilter } from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import { type CellContext } from "@tanstack/react-table";
import { pluralize, pluralizeWithCount } from "#/utils/plurals";
import { cn } from "#/utils/classnames";
import { type CustomColumn } from "#/utils/custom-columns/use-custom-columns";
import { type Vector } from "apache-arrow";
import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { useComparisonRowData } from "#/ui/row-comparison/useComparisonRowData";

export type RowComparisonFormatterProps = {
  orgName: string;
  projectName: string;
  experimentId: string;
  comparisonExperimentData: ComparisonExperimentSpanSummary[];
  addRegressionFilter: (f: RegressionFilter) => void;
  gridLayout?: boolean | null;
  comparisonIndex?: number;
  modelSpecScan?: string | null;
  customColumns?: CustomColumn[];
};

export function RowComparisonFormatterFactory(
  rowComparisonProps: RowComparisonFormatterProps,
) {
  const { experimentId, addRegressionFilter, comparisonIndex } =
    rowComparisonProps;
  return function RowComparisonFormatter<
    TsData extends {
      id?: string | DiffObjectType<string>;
      __bt_internal?: Record<
        string,
        { rowCount?: bigint; compareIds?: Vector }
      >;
      comparison_key?: string;
    } & object,
    TsValue,
  >(props: CellContext<TsData, TsValue>) {
    const comparisonKey = `${getDiffRight(props.row.original.comparison_key) ?? ""}`;
    const bt_internal = props.row?.original?.__bt_internal ?? {};

    const displayExperimentAlias =
      comparisonIndex != null ? `e${comparisonIndex + 2}` : "e1";
    const displayCount = Number(
      bt_internal[displayExperimentAlias]?.compareIds?.length ??
        bt_internal[displayExperimentAlias]?.rowCount ??
        0,
    );

    const trigger = (
      <Button
        size="xs"
        className={cn("h-auto py-0 text-primary-500", {
          // grid layout empty state should not have negative margin
          "-ml-4": comparisonIndex == null,
        })}
        Icon={ListPlus}
        onClick={(e) => e.stopPropagation()}
      >
        {displayCount}
      </Button>
    );

    // grid mode
    if (comparisonIndex != null) {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            onClick={(e) => e.stopPropagation()}
          >
            {displayCount > 0 ? (
              <LinkItem
                {...rowComparisonProps}
                count={displayCount}
                index={comparisonIndex}
                comparisonKey={comparisonKey}
              />
            ) : (
              <DropdownMenuItem>No rows in this experiment</DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    const count = Number(bt_internal.e1?.rowCount ?? 0);
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
        <DropdownMenuContent align="start" onClick={(e) => e.stopPropagation()}>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
            }}
            onSelect={(e) => {
              addRegressionFilter({
                comparisonType: "exists" as const,
                field: {
                  type: "none",
                  value: "",
                },
                anyField: true,
                group: {
                  type: "input",
                  value: comparisonKey,
                },
                experimentId,
              });
            }}
          >
            <ListFilter className="size-3" />
            Filter {count} trial {pluralize(count, "row")} in this experiment
          </DropdownMenuItem>
          <CompareMenuItems
            rowOriginal={props.row.original}
            comparisonKey={comparisonKey}
            rowComparisonProps={rowComparisonProps}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };
}

export function CompareMenuItems<
  TsData extends {
    __bt_internal?: Record<string, { rowCount?: bigint; compareIds?: Vector }>;
  },
>({
  rowOriginal,
  comparisonKey,
  rowComparisonProps,
}: {
  rowOriginal: TsData | undefined;
  comparisonKey: string;
  rowComparisonProps: RowComparisonFormatterProps;
}) {
  return rowComparisonProps.comparisonExperimentData.map((e, i) => (
    <CompareMenuItem
      key={i}
      index={i}
      rowOriginal={rowOriginal}
      comparisonKey={comparisonKey}
      rowComparisonProps={rowComparisonProps}
      experiment={e}
    />
  ));
}

export function CompareMenuItem<
  TsData extends {
    __bt_internal?: Record<string, { rowCount?: bigint; compareIds?: Vector }>;
  },
>({
  index,
  rowOriginal,
  comparisonKey,
  rowComparisonProps,
  experiment,
}: {
  index: number;
  rowOriginal: TsData | undefined;
  comparisonKey: string;
  rowComparisonProps: RowComparisonFormatterProps;
  experiment: ComparisonExperimentSpanSummary;
}) {
  const {
    comparisonRowId,
    comparisonRowIds,
    setSelectedComparisonParams,
    setComparisonExperimentId,
  } = useComparisonRowData({
    sourceData: rowOriginal
      ? {
          type: "rowOriginal",
          row: rowOriginal,
        }
      : undefined,
    comparisonKey,
    comparisonExperimentData: rowComparisonProps.comparisonExperimentData,
    experimentIndexOverride: index,
  });

  const count = comparisonRowIds.length;
  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger disabled={count === 0}>
        <ExperimentColorSwatch
          index={index + 1}
          className="flex-1"
          swatchClassName="flex-none"
        >
          <span className="flex flex-1 items-center justify-between gap-2">
            <span className="flex-1 truncate">{experiment.name}</span>
            <span className="text-xs text-primary-500">{count}</span>
          </span>
        </ExperimentColorSwatch>
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent>
        <DropdownMenuLabel>
          This row matches {pluralizeWithCount(count, "row", "rows")} in{" "}
          {experiment.name}
        </DropdownMenuLabel>
        {count > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Compare with</DropdownMenuLabel>
            {comparisonRowIds.map((id, j) => {
              const isSelectedRowId = comparisonRowId === id;
              return (
                <DropdownMenuCheckboxItem
                  key={j}
                  onClick={(e) => e.stopPropagation()}
                  onSelect={() => {
                    setComparisonExperimentId(experiment.id);
                    setSelectedComparisonParams((prev) => ({
                      ...prev,
                      [comparisonKey]: {
                        [experiment.id]: id,
                      },
                    }));
                  }}
                  checked={isSelectedRowId}
                >
                  Row {j + 1}
                </DropdownMenuCheckboxItem>
              );
            })}
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild onClick={(e) => e.stopPropagation()}>
              <LinkItem
                {...rowComparisonProps}
                count={count}
                index={index}
                comparisonKey={comparisonKey}
              />
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
}

function LinkItem(
  props: RowComparisonFormatterProps & {
    count: number;
    index: number;
    comparisonKey: string;
  },
) {
  const {
    orgName,
    projectName,
    comparisonExperimentData,
    gridLayout,
    count,
    index,
    comparisonKey,
  } = props;
  return (
    <DropdownMenuItem asChild onClick={(e) => e.stopPropagation()}>
      <Link
        className="flex grow items-center gap-2"
        href={
          getExperimentLink({
            orgName,
            projectName,
            experimentName: comparisonExperimentData[index]?.name,
          }) +
          regressionFilterSerialize(
            new URLSearchParams({
              diff: "between_experiments",
              g: "__bt_input",
              c: "",
              gl: String(gridLayout),
            }),
            {
              rf: [
                {
                  comparisonType: "exists" as const,
                  field: {
                    type: "none",
                    value: "",
                  },
                  anyField: true,
                  group: {
                    type: "input",
                    value: `${comparisonKey}`,
                  },
                  experimentId: comparisonExperimentData[index]?.id,
                },
              ],
            },
          )
        }
        target="_blank"
      >
        <ExternalLink className="size-3" />
        <ExperimentColorSwatch index={index + 1} />
        Open {count} trial {pluralize(count, "row")} in comparison experiment
      </Link>
    </DropdownMenuItem>
  );
}
