import type { FormatterMap } from "#/ui/field-to-column";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@braintrust/local/api-schema";
import {
  ArrowUpRight,
  ArrowDownRight,
  Equal,
  PercentIcon,
  Tag,
  CurlyBraces,
  Hash,
  User2Icon,
  TextIcon,
  Clock,
  DollarSign,
  GitBranch,
  Calendar,
  Fingerprint,
  CircleAlert,
  Beaker,
  Database,
  ScrollText,
  Blocks,
  Timer,
  FunctionSquare,
  MessageSquareText,
  Cuboid,
  Shapes,
} from "lucide-react";

export const HeaderIcons: Record<string, FormatterMap["string"]["headerIcon"]> =
  {
    description: TextIcon,
    prompt_data: Cuboid,
    function_data: FunctionSquare,
    num_examples: Hash,
    num_errors: CircleAlert,
    error_rate: CircleAler<PERSON>,
    creator: User2Icon,
    source: GitBranch,
    scores: PercentIcon,
    tags: Tag,
    name: TextIcon,
    span_type_info: TextIcon,
    id: Fingerprint,
    input: ArrowDownRight,
    output: ArrowUpRight,
    expected: Equal,
    metadata: CurlyBraces,
    created: Calendar,
    last_updated: Calendar,
    error: <PERSON><PERSON>ler<PERSON>,
    experiments: Beaker,
    playgrounds: Shapes,
    datasets: Database,
    dataset: Database,
    Logs: ScrollText,
    duration: Clock,
    llm_duration: Clock,
    total_tokens: Blocks,
    prompt_tokens: Blocks,
    completion_tokens: Blocks,
    prompt_cached_tokens: Blocks,
    prompt_cache_creation_tokens: Blocks,
    estimated_cost: DollarSign,
    "Logs (7d)": ScrollText,
    "Tokens (7d)": Blocks,
    "TTF token (avg 7d)": Timer,
    "Duration (avg 7d)": Timer,
    "Duration (p95 7d)": Timer,
    "Errors (7d)": CircleAlert,
    comments: MessageSquareText,
    start: Calendar,
    end: Calendar,
  };

export const MetricHeaderIcons: Record<
  string,
  FormatterMap["string"]["headerIcon"]
> = {};

export const HeaderFormatters: FormatterMap = Object.fromEntries(
  Array.from(
    new Set(Object.keys(HeaderAliases).concat(Object.keys(HeaderIcons))),
  ).map((key) => [
    key,
    { headerLabel: HeaderAliases[key], headerIcon: HeaderIcons[key] },
  ]),
);

export function makeFormatterMap<TsTable = {}, TsValue = "">(
  ...formatters: FormatterMap<TsTable, TsValue>[]
): FormatterMap<TsTable, TsValue> {
  return mergeFormatterMaps(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    ...[HeaderFormatters as unknown as FormatterMap<TsTable, TsValue>].concat(
      formatters,
    ),
  );
}

function mergeFormatterMaps<TsTable = {}, TsValue = "">(
  ...formatters: FormatterMap<TsTable, TsValue>[]
): FormatterMap<TsTable, TsValue> {
  return formatters.reduce((acc, formatter) => {
    for (const key in formatter) {
      acc[key] = { ...acc[key], ...formatter[key] };
    }
    return acc;
  }, {});
}
