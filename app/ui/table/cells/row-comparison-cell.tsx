import { cn } from "#/utils/classnames";
import { type Cell, flexRender } from "@tanstack/react-table";

interface RowComparisonCellProps<TsData> {
  cell: Cell<TsData, unknown>;
  isGroupRow?: boolean;
}

export function RowComparisonCell<TsData>({
  cell,
  isGroupRow,
}: RowComparisonCellProps<TsData>) {
  return (
    <div
      style={{ width: cell.column.getSize() }}
      className={cn(
        "relative flex w-full justify-center items-start py-2 flex-none",
        {
          "cursor-pointer border-primary-200": isGroupRow,
        },
      )}
      onClick={(e) => {
        if (isGroupRow) {
          e.stopPropagation();
          cell.row.toggleExpanded();
        }
      }}
    >
      {isGroupRow
        ? null
        : flexRender(cell.column.columnDef.cell, {
            inTable: true,
            meta: cell.column.columnDef.meta,
            value: cell.getValue(),
            setValue: (_: unknown) => {},
            ...cell.getContext(),
          })}
    </div>
  );
}
