import React, { type RefObject } from "react";
import { type Row, type Table } from "@tanstack/react-table";
import { Checkbox } from "#/ui/checkbox";
import { cn } from "#/utils/classnames";

export const TableCheckbox = function <TData>({
  lastSelectedRowId,
  table,
  row,
  showRowNumber,
}: {
  lastSelectedRowId: RefObject<string | undefined | null>;
  table: Table<TData>;
  row: Row<TData>;
  showRowNumber?: boolean;
}) {
  const isChecked = row.getIsSelected();

  return (
    <div
      className="flex size-full cursor-pointer justify-end pr-2"
      onClick={(e) => {
        e.stopPropagation();
        if (e.shiftKey && lastSelectedRowId.current) {
          const allRows = table.getRowModel().rows;
          const lastSelectedRowIndex = allRows.findIndex(
            (r) => r.id === lastSelectedRowId.current,
          );
          const currRowIndex = allRows.findIndex((r) => r.id === row.id);
          const [start, end] =
            lastSelectedRowIndex < currRowIndex
              ? [lastSelectedRowIndex, currRowIndex]
              : [currRowIndex, lastSelectedRowIndex];
          const rowsToSelect = allRows.slice(start, end + 1);
          const currentSelection = table.getState().rowSelection;
          const sameIsAlreadySelected =
            rowsToSelect.every((r) => r.getIsSelected()) &&
            rowsToSelect.length ==
              Object.keys(currentSelection).filter(
                (key) => currentSelection[key] === true,
              ).length;
          if (sameIsAlreadySelected) {
            table.setRowSelection({});
          } else {
            const newSelection = rowsToSelect.reduce(
              (acc: Record<string, boolean>, r) => ({
                ...acc,
                [r.id]: true,
              }),
              {},
            );
            table.setRowSelection(newSelection);
          }
        } else {
          // eslint-disable-next-line react-compiler/react-compiler
          lastSelectedRowId.current = row.id;
          row.getToggleSelectedHandler()(e);
        }
      }}
    >
      {showRowNumber && (
        <span
          className={cn(
            "flex-1 pl-2 pr-1 pt-2.5 text-right text-[10px] tabular-nums text-primary-500 group-hover/tablerow:hidden",
            {
              hidden: isChecked,
            },
          )}
        >
          {row.index + 1}
        </span>
      )}
      <Checkbox
        className={cn("flex-none items-start pt-2.5", {
          "hidden group-hover/tablerow:flex": !isChecked && showRowNumber,
        })}
        checked={isChecked}
        disabled={!row.getCanSelect()}
        name={`__row_selection-${row.id}`}
        // if shift is pressed take last selected row and select all rows between
      />
    </div>
  );
};
