import type { BTQLResponse, InferSchema } from "#/utils/btql/btql";
import { type Type } from "@braintrust/btql/binder";

export interface InferTreeNode {
  name: string;
  type?: Type; // no value if no type
  children: InferTreeNode[];
}

export interface InferField {
  namePath: string[];
  topValues: { count: number; value?: unknown }[];
  type: Type;
}

export const inferDataToInferFields = (
  inferData: BTQLResponse<InferSchema> | null | undefined,
): InferField[] => {
  return (
    inferData?.data.map((r) => {
      return {
        namePath: r.name.slice(0),
        topValues: r.top_values.slice(0),
        type: r.type,
      };
    }) || []
  ).toSorted((a, b) =>
    a.namePath.join("|").localeCompare(b.namePath.join("|")),
  );
};

export const getInferField = (fields: InferField[], path: string[]) => {
  return fields.find((f) => f.namePath.join("|") === path.join("|"));
};
