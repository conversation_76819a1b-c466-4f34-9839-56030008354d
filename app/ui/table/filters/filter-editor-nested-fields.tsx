import { useEffect, useMemo, useState } from "react";
import type { InferField } from "./filter-editor-infer-data";
import { Combobox } from "#/ui/combobox/combobox";
import { Spinner } from "#/ui/icons/spinner";
import { Input } from "#/ui/input";

const ROOT_NAME = "Field root";

export const FilterEditorNestedFields = (props: {
  inferFields: InferField[];
  inferPath: string[];
  setInferPath: (path: string[]) => void;
  hasNestedFields: boolean;
  isLoading?: boolean;
}) => {
  const { inferFields, inferPath, setInferPath, isLoading, hasNestedFields } =
    props;

  const [customField, setCustomField] = useState<boolean>(false);
  const [customFieldValue, setCustomFieldValue] = useState<string>("");

  const options = useMemo(() => {
    return inferFields.map((f) => {
      const flatName = f.namePath.slice(1).join(".") || ROOT_NAME;
      return {
        label: flatName,
        value: f.namePath.join("|"),
        meta: {
          namePath: f.namePath,
          type: String(f.type),
        },
      };
    });
  }, [inferFields]);

  const selectedOption = useMemo(() => {
    if (!hasNestedFields || customField) {
      return;
    }
    const existing = options.find(
      (opt) => opt.meta.namePath.join("|") === inferPath.join("|"),
    );
    if (existing) {
      return existing;
    }

    return options.at(0);
  }, [hasNestedFields, inferPath, options, customField]);

  // for defaulting initial selection
  useEffect(() => {
    if (customField) {
      return;
    }
    const hashA = selectedOption?.meta.namePath.join("|");
    const hashB = inferPath.join("|");
    if (selectedOption && hashA && hashB && hashA !== hashB) {
      setInferPath(selectedOption.meta.namePath);
    }
  }, [customField, inferPath, setInferPath, selectedOption, options]);

  if (isLoading) {
    return (
      <div className="flex items-center text-primary-600">
        <Spinner className="size-3" />
      </div>
    );
  }

  if (!hasNestedFields) {
    return <></>;
  }

  return (
    <>
      <Combobox
        options={options}
        noSearch={options.length < 5}
        variant="button"
        buttonSize="xs"
        noResultsLabel="No path found"
        itemLabelClassName="line-clamp-3"
        searchPlaceholder={`Search paths in ${inferPath[0]}`}
        selectedValue={selectedOption?.value}
        placeholderLabel="Custom path"
        align="start"
        renderComboboxDisplayLabel={(opt) => {
          return <div>{opt.meta.namePath.slice(1).join(".") || ROOT_NAME}</div>;
        }}
        onChange={(_, opt) => {
          setInferPath(opt.meta.namePath);
          setCustomField(false);
        }}
        bottomActions={[
          {
            label: "Use a custom path",
            onSelect: () => {
              setCustomField(true);
              setCustomFieldValue("");
            },
            selected: Boolean(customField),
          },
        ]}
      />
      {customField && (
        <div className="flex">
          <Input
            type="text"
            placeholder="Enter path"
            name="path"
            value={customFieldValue}
            onChange={(e) => {
              const { value } = e.target;
              setCustomFieldValue(value);
              setInferPath([inferPath[0], ...value.split(".")]);
            }}
            className="h-7 px-2 text-xs"
          />
        </div>
      )}
    </>
  );
};
