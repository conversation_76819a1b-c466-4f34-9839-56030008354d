import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
} from "#/ui/dropdown-menu";
import { type Column } from "@tanstack/react-table";
import { Percent, StarIcon } from "lucide-react";
import { type FormatterMap } from "#/ui/field-to-column";
import { RowStarColumnId } from "#/ui/table/display-columns";

export const FilterEditorColumnsSelect = <TData, TValue>({
  selectableColumns,
  column,
  formatters,
  setColumn,
  enableStarColumn,
}: {
  selectableColumns: Column<TData>[];
  column?: Column<TData>;
  formatters?: FormatterMap<TData, TValue>;
  setColumn: (column: Column<TData>) => void;
  enableStarColumn?: boolean;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="xs" isDropdown isLoading={selectableColumns.length === 0}>
          <ColumnOptionDisplay col={column} formatters={formatters} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuGroup>
          {selectableColumns.map((col) => {
            const isStarColumn = col.id === RowStarColumnId;
            if (isStarColumn && !enableStarColumn) {
              return null;
            }
            return (
              <DropdownMenuItem
                key={col.id}
                onSelect={() => {
                  setColumn(col);
                }}
              >
                <ColumnOptionDisplay col={col} formatters={formatters} />
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const ColumnOptionDisplay = <TData, TValue>({
  col,
  formatters,
}: {
  col?: Column<TData>;
  formatters?: FormatterMap<TData, TValue>;
}) => {
  const name = col?.columnDef.meta?.name ?? "";
  const formatter = formatters?.[name];
  const isScore = isScoreColumn(col);
  const isStarCol = col?.id === RowStarColumnId;

  if (!col) return "Select column";

  return (
    <>
      {formatter?.headerIcon ? (
        <formatter.headerIcon className="size-3 flex-none text-primary-500" />
      ) : isStarCol ? (
        <StarIcon className="size-3 flex-none text-primary-500" />
      ) : isScore ? (
        <Percent className="size-3 flex-none text-primary-500" />
      ) : null}
      <span className="max-w-48 truncate">
        {isStarCol ? "Starred" : formatter?.headerLabel || name || col.id}
      </span>
    </>
  );
};

export const isScoreColumn = <TData, TValue>(
  column?: Column<TData, TValue>,
  scoreNames?: string[],
) => {
  return (
    column?.columnDef.meta?.path?.[0] === "scores" ||
    scoreNames?.includes(column?.columnDef.id ?? "")
  );
};
