import React from "react";
import {
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenu,
} from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import { Gauge, Grid2X2, Rows3 } from "lucide-react";
import { type ViewProps } from "#/utils/view/use-view";
import { BasicTooltip } from "#/ui/tooltip";

export const tableLayoutTypes = ["list", "grid", "summary"] as const;
export type TableLayoutType = (typeof tableLayoutTypes)[number];

export const LayoutTypeControl = ({
  viewProps,
  defaultLayout,
  disabledLayouts,
}: {
  viewProps: ViewProps;
  disabledLayouts?: TableLayoutType[];
  defaultLayout: TableLayoutType;
}) => {
  const layoutType = viewProps.layout ?? defaultLayout;
  const listLayout = layoutType === "list";
  const gridLayout = layoutType === "grid";
  const summaryLayout = layoutType === "summary";
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>
          <BasicTooltip tooltipContent="View layout">
            <Button
              size="xs"
              variant="ghost"
              Icon={gridLayout ? Grid2X2 : summaryLayout ? Gauge : Rows3}
            >
              <span className="hidden @lg/controls:block">
                {gridLayout ? "Grid" : summaryLayout ? "Summary" : "List"}
              </span>
            </Button>
          </BasicTooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuCheckboxItem
          checked={listLayout || !layoutType}
          disabled={disabledLayouts?.includes("list")}
          onSelect={() => viewProps.setLayout("list")}
        >
          <Rows3 className="mr-2 size-3" /> List layout
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={gridLayout}
          onSelect={() => viewProps.setLayout("grid")}
          disabled={disabledLayouts?.includes("grid")}
        >
          <Grid2X2 className="mr-2 size-3" /> Grid layout
        </DropdownMenuCheckboxItem>
        <DropdownMenuCheckboxItem
          checked={summaryLayout}
          onSelect={() => viewProps.setLayout("summary")}
          disabled={disabledLayouts?.includes("summary")}
        >
          <Gauge className="mr-2 size-3" /> Summary layout
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
