import { <PERSON><PERSON><PERSON>D<PERSON>, ListFilter, Tag, X } from "lucide-react";
import { cn } from "#/utils/classnames";
import { type ClientOptions } from "openai";
import { type AISearchResult } from "@braintrust/local";
import React, { type Dispatch, type SetStateAction, useState } from "react";
import {
  type ClauseChe<PERSON>,
  type ClauseType,
  type Search,
} from "#/utils/search/search";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipPortal,
} from "#/ui/tooltip";
import { FilterEditor, ComparisonTag } from "./filter-editor";

interface BubbleProps {
  label?: string | React.ReactNode;
  text?: string;
  comparisonId?: string;
  onClose: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  runAISearch?: (
    openAIOpts: ClientOptions,
    query: string,
  ) => Promise<AISearchResult>;
  setSearch?: Dispatch<SetStateAction<Search>>;
  clauseChecker?: ClauseChecker | null;
  type?: ClauseType;
  disabled?: boolean;
  originType?: "btql" | "form";
  className?: string;
  hidden?: boolean;
  baseExperiment?: {
    id: string;
    name: string;
  };
  comparisonExperiments?: {
    id: string;
    name: string;
  }[];
  isPlayground?: boolean;
  scoreNames?: string[];
}

const BubbleTypeIcon = ({
  type,
  className,
}: {
  type: ClauseType;
  className?: string;
}) => {
  if (type === "filter") return <ListFilter className={className} />;
  if (type === "sort") return <ArrowUpDown className={className} />;
  if (type === "tag") return <Tag className={className} />;
  if (type === "match") return null; // do not show icon for match
  return null;
};

export function BubbleChip({
  type,
  label,
  text,
  comparisonId,
  onClose,
  runAISearch,
  setSearch,
  clauseChecker,
  disabled,
  originType,
  className,
  hidden,
  baseExperiment,
  comparisonExperiments,
  isPlayground,
  scoreNames,
}: BubbleProps) {
  const [isOpen, setOpen] = useState(false);

  if (hidden) return null;

  if (
    type &&
    !!originType &&
    typeof label === "string" &&
    !!setSearch &&
    !disabled
  ) {
    return (
      <BubbleShell
        tooltipText={isOpen ? undefined : label}
        onClose={onClose}
        isEditable
        className={className}
      >
        <FilterEditor
          runAISearch={runAISearch}
          clauseChecker={clauseChecker ?? null}
          isOpen={isOpen}
          setOpen={setOpen}
          setSearch={setSearch}
          selectableColumns={[]}
          initialExperimentId={comparisonId}
          baseExperiment={baseExperiment}
          comparisonExperiments={comparisonExperiments}
          forceTab="btql"
          sideOffset={8}
          disableTooltip
          alignOffset={-8}
          clauseToReplace={{
            type,
            text: text ?? "",
            label,
          }}
          isPlayground={isPlayground}
          scoreNames={scoreNames}
        >
          <div className="flex cursor-pointer items-center gap-1">
            <span
              className={cn(
                "max-w-full flex-1 block cursor-pointer truncate text-left align-middle",
                {
                  "font-mono font-semibold":
                    !label ||
                    originType === "btql" ||
                    type === "match" ||
                    type === "sort",
                },
              )}
            >
              <BubbleTypeIcon
                type={type}
                className="mb-[2px] mr-1 inline-block size-3"
              />
              {type === "match" ? `"${label ?? text}"` : (label ?? text)}
            </span>
            <ComparisonTag
              className="max-w-36 flex-none truncate"
              comparisonExperiments={comparisonExperiments}
              comparisonId={comparisonId}
            />
          </div>
        </FilterEditor>
      </BubbleShell>
    );
  }
  return (
    <BubbleShell
      onClose={onClose}
      className={cn(
        {
          "border-0 opacity-60 bg-primary-50 text-primary-300": disabled,
        },
        className,
      )}
      tooltipText={label}
    >
      <span className="w-full truncate text-left">{label ?? text}</span>
      <ComparisonTag
        className="max-w-36 flex-none truncate"
        comparisonExperiments={comparisonExperiments}
        comparisonId={comparisonId}
      />
    </BubbleShell>
  );
}

const BubbleShell = ({
  children,
  isEditable,
  tooltipText,
  onClose,
  className,
}: React.PropsWithChildren<{
  onClose: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  isEditable?: boolean;
  tooltipText?: React.ReactNode;
  className?: string;
}>) => (
  <Tooltip disableHoverableContent delayDuration={100}>
    <TooltipTrigger asChild>
      <div
        className={cn(
          "h-7 flex items-center bg-background border border-accent-700 text-accent-700 rounded-md max-w-sm font-medium text-xs pl-1.5 pr-0 gap-1 truncate",
          {
            "hover:bg-accent-50": isEditable,
          },
          className,
        )}
      >
        {children}
        <CloseButton onClose={onClose} />
      </div>
    </TooltipTrigger>
    <TooltipPortal>
      {tooltipText && (
        <TooltipContent
          className="max-w-sm whitespace-pre-line text-xs"
          align="start"
        >
          {tooltipText}
        </TooltipContent>
      )}
    </TooltipPortal>
  </Tooltip>
);

export interface BubbleType {
  id: number;
  type: ClauseType;
  label: React.ReactNode;
  text?: string;
  comparisonId?: string;
  isReadonly?: boolean;
  clear: () => void;
  originType?: "btql" | "form";
  hidden?: boolean;
}

function CloseButton({
  onClose,
}: {
  onClose: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}) {
  return (
    <button
      className="flex-none pr-1 opacity-50 hover:opacity-100"
      onClick={(e) => {
        onClose(e);
      }}
      title="Remove filter"
    >
      <X className="size-3" />
    </button>
  );
}
