import { cn } from "#/utils/classnames";
import Link from "next/link";
import type { ComponentProps, CSSProperties, ReactNode } from "react";

export function Card({
  children,
  title,
  description,
  href,
  ...props
}: {
  children: ReactNode;
  title: string;
  description?: string;
  href: string;
}) {
  return (
    <Link
      href={href}
      className="mb-4 flex flex-col rounded-lg border p-4 transition-colors bg-primary-50 border-primary-200 hover:bg-primary-100"
      {...props}
    >
      <h3 className="text-xl font-medium leading-snug">{title}</h3>
      {description && (
        <p className="mt-2 text-sm text-primary-600">{description}</p>
      )}
    </Link>
  );
}

function _Cards({
  children,
  className,
  style,
  ...props
}: ComponentProps<"div">) {
  return (
    <div
      className={cn(className, "columns-2 mt-4 mb-4")}
      {...props}
      style={
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        {
          ...style,
        } as CSSProperties
      }
    >
      {children}
    </div>
  );
}

export const Cards = Object.assign(_Cards, { displayName: "Cards", Card });
