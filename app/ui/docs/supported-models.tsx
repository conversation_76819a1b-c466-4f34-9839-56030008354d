import {
  AvailableModels,
  getModelEndpointTypes,
} from "@braintrust/proxy/schema";

export default function SupportedModels() {
  return (
    <ul className="mt-4 list-disc">
      {Object.entries(AvailableModels)
        .filter(([_, { format }]) => format !== "js")
        .map(([name]) => {
          const provider = getModelEndpointTypes(name);
          return (
            <li key={name} className="my-2">
              {name} ({provider.join(", ")}){" "}
            </li>
          );
        })}
    </ul>
  );
}
