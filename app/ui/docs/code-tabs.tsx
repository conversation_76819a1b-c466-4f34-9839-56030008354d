import { Tabs, Tab } from "fumadocs-ui/components/tabs";

export const CodeTabs = ({
  children,
  items = ["TypeScript", "Python"],
}: React.PropsWithChildren<{ items?: string[] }>) => {
  return (
    <div className="code-tabs">
      <Tabs items={items} persist groupId="code-tabs">
        {children}
      </Tabs>
    </div>
  );
};

const CodeTab = ({
  children,
  languageName,
}: React.PropsWithChildren<{ languageName: string }>) => (
  <Tab value={languageName} className="max-h-none!">
    {children}
  </Tab>
);

export const TSTab = ({ children }: React.PropsWithChildren) => (
  <CodeTab languageName="TypeScript">{children}</CodeTab>
);

export const PYTab = ({ children }: React.PropsWithChildren) => (
  <CodeTab languageName="Python">{children}</CodeTab>
);

export const CurlTab = ({ children }: React.PropsWithChildren) => (
  <CodeTab languageName="cURL">{children}</CodeTab>
);

export const GoTab = ({ children }: React.PropsWithChildren) => (
  <CodeTab languageName="Go">{children}</CodeTab>
);
