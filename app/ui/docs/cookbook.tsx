import { cn } from "#/utils/classnames";
import Link from "next/link";
import React from "react";
import { AvatarGroup } from "#/ui/avatar";
import { buttonVariants } from "#/ui/button";
import { GithubIcon } from "#/ui/github-button";

export interface Author {
  name: string;
  avatar: string;
  website: string;
}

export function Subheader({
  authors,
  githubUrl,
  date,
  className,
}: {
  authors: Author[];
  githubUrl: string;
  date?: string;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex flex-row justify-between",
        "nx-border-b nx-pb-4 nx-border-neutral-200/70 contrast-more:nx-border-neutral-400 dark:nx-border-primary-100/10 contrast-more:dark:nx-border-neutral-400",
        className,
      )}
    >
      <div className={cn("flex flex-row")}>
        <AvatarGroup
          avatars={authors.map((a) => ({
            link: a.website,
            imgUrl: a.avatar,
            size: "md",
          }))}
        />
        <div className="ml-2 text-xs">
          <div className="text-sm font-medium">
            {authors.map((author, idx) => (
              <React.Fragment key={idx}>
                <Link
                  href={author.website}
                  target="_blank"
                  rel="noreferrer"
                  className="!no-underline"
                >
                  {author.name}
                </Link>
                {idx < authors.length - 1 && ", "}
              </React.Fragment>
            ))}
          </div>
          {date && (
            <div className="text-primary-500">
              {new Date(date).toLocaleDateString("default", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </div>
          )}
        </div>
      </div>{" "}
      <Link
        href={githubUrl}
        target="_blank"
        rel="noreferrer"
        className={cn("plain !no-underline", buttonVariants({ size: "sm" }))}
      >
        <GithubIcon />
        <span>Open in Github</span>
      </Link>
    </div>
  );
}
