import { cn } from "#/utils/classnames";
import Link from "next/link";
import React from "react";
import { AvatarGroup } from "#/ui/avatar";
import { type Author } from "./cookbook";

export function CookbookCards({ children }: { children: React.ReactNode }) {
  return (
    <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {children}
    </div>
  );
}

export function CookbookCard({
  title,
  route,
  authors,
  date,
  language,
  tags,
  logoIconUrl,
}: {
  title: string;
  route: string;
  authors: Author[];
  date: string;
  className?: string;
  language: string;
  tags: string[];
  logoIconUrl?: string;
}) {
  return (
    <Link
      href={route}
      className="plain flex h-52 flex-col rounded-lg border p-4 !no-underline transition-colors bg-primary-50 border-primary-100 hover:bg-primary-100"
    >
      <div className="mb-1 flex h-5 flex-none justify-between text-xs font-medium uppercase text-primary-500">
        {language}
      </div>
      <h3 className="m-0 flex-1 overflow-hidden text-xl font-medium leading-snug">
        <div className="line-clamp-3">{title}</div>
      </h3>
      <Authors authors={authors} />

      <div className="mt-2 flex items-center gap-2 text-xs font-normal text-primary-600">
        <span className="flex-none text-primary-400">
          {new Date(date).toLocaleDateString("default", {
            month: "short",
            day: "numeric",
            year: "numeric",
          })}
        </span>
        <span className="flex-1 truncate text-right font-medium uppercase">
          {tags.map((tag, idx) => (
            <span className="pl-2" key={idx}>
              {tag}
            </span>
          ))}
        </span>
      </div>
    </Link>
  );
}

function Authors({ authors }: { authors: Author[] }) {
  return (
    <div className={cn("flex items-center font-normal")}>
      <AvatarGroup avatars={authors.map((a) => ({ imgUrl: a.avatar }))} />
      <div className="ml-1 text-sm text-primary-800">
        {authors.map((author, idx) => (
          <React.Fragment key={idx}>
            <span>{author.name}</span>
            {idx < authors.length - 1 && ", "}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
