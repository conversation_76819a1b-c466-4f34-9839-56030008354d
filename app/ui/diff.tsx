import React, { useMemo, useState } from "react";
import * as Diff from "diff";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { isEmpty, normalizeArrowForJSON } from "#/utils/object";
import { cn } from "#/utils/classnames";
import { FileWarning } from "lucide-react";
import { NULL_DASH, NullFormatter } from "./table/formatters/null-formatter";

export const diffBgClassName = {
  good: "bg-good-100 dark:bg-good-50",
  bad: "bg-bad-100 dark:bg-bad-50",
};

export interface DiffTextProps {
  oldText: string;
  newText: string;
  prefix?: number;
  code?: boolean;
  addedFieldName?: string;
  removedFieldName?: string;
}

export interface DiffScoreProps {
  oldScoreClassName?: string;
  newScoreClassName?: string;
  oldScoreSwatchClassName?: string;
  oldScore: number | null;
  newScore: number | null;
  changedOnly?: boolean;
}

export type DiffNumberProps = {
  oldScoreClassName?: string;
  newScoreClassName?: string;
  oldScoreSwatchClassName?: string;
  oldNumber: number | null;
  newNumber: number | null;
  formatNumber: (number: number) => string;
  formatDiff?: (start: number, end: number) => string;
  percentDiff: (start: number, end: number) => number;
  upIsGood?: boolean;
  changedOnly?: boolean;
  hideChanged?: boolean;
};

export function DiffParts({
  diff,
  code = false,
  addedFieldName,
  removedFieldName,
}: {
  diff: Diff.Change[];
  code: boolean;
  addedFieldName?: string;
  removedFieldName?: string;
}) {
  const Inline = code ? "code" : "span";

  return (
    <span className={cn("break-words", code && "whitespace-pre-wrap text-xs")}>
      {diff.map((part, index) => {
        return (
          <NullableTooltipWrapper
            key={index}
            addedFieldName={addedFieldName}
            removedFieldName={removedFieldName}
            added={part.added}
            removed={part.removed}
          >
            <Inline
              key={index}
              className={cn({
                [diffBgClassName.good]: part.added,
                [diffBgClassName.bad]: part.removed,
              })}
            >
              {part.value}
            </Inline>
          </NullableTooltipWrapper>
        );
      })}
    </span>
  );
}

const NullableTooltipWrapper = (props: {
  added?: boolean | null;
  removed?: boolean | null;
  addedFieldName?: string;
  removedFieldName?: string;
  children: React.ReactNode;
}) => {
  const { added, removed, addedFieldName, removedFieldName } = props;
  if (
    (!added && !removed) ||
    (addedFieldName === undefined && removedFieldName === undefined)
  ) {
    return <>{props.children}</>;
  }
  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>{props.children}</TooltipTrigger>
      <TooltipContent side="top" className="p-1 text-xs">
        {added ? addedFieldName : removedFieldName}
      </TooltipContent>
    </Tooltip>
  );
};

export function DiffText({
  oldText: oldTextProp,
  newText: newTextProp,
  prefix,
  code = false,
  addedFieldName,
  removedFieldName,
}: DiffTextProps) {
  const oldText =
    prefix !== undefined
      ? oldTextProp.slice(0, prefix)
      : oldTextProp.slice(0, 4096);
  const newText =
    prefix !== undefined
      ? newTextProp.slice(0, prefix)
      : newTextProp.slice(0, 4096);

  const truncated =
    !prefix &&
    (oldTextProp.length > oldText.length ||
      newTextProp.length > newText.length);

  const diff = useMemo(() => {
    return Diff.diffWordsWithSpace(
      oldText.slice(0, 4096),
      newText.slice(0, 4096),
    );
  }, [oldText, newText]);

  return (
    <>
      <DiffParts
        diff={diff}
        code={code}
        addedFieldName={addedFieldName}
        removedFieldName={removedFieldName}
      />
      {truncated && (
        <div className="py-4 italic">
          ...
          <br />
          <br />
          This diff output has been truncated for performance reasons (to 4096
          characters). If this is an object, then you may want to log the object
          directly, and Braintrust will do a more efficient diff within the
          object.
        </div>
      )}
    </>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
(BigInt.prototype as any).toJSON = function () {
  const strValue = this.toString();
  const numValue = parseInt(strValue);
  if (BigInt(numValue) === this) {
    return numValue;
  } else {
    return strValue;
  }
};

export interface DiffObjectProps {
  oldObject: object | string;
  newObject: object | string | undefined;
  prefix?: number;
}

const GIANT_DIFF_THRESHOLD = 16384;
export function useDiffState({
  oldObject,
  newObject,
  prefix,
  allowGiantDiff,
}: DiffObjectProps & { allowGiantDiff: boolean }) {
  // We stringify the objects for two reasons:
  // 1. It allows us to memoize _when_ we compute the diffs (when the strings change, not the object pointers)
  // 2. It allows us to convert TypedArrays to regular arrays (by way of the replacer function). There is probably
  //    a more efficient way to do this (e.g. change jsdiff to accept a custom replacer as an option), but this is
  //    good enough for now. We should revisit if we encounter performance issues.
  const oldObjectStr = useMemo(
    () =>
      isEmpty(oldObject)
        ? ""
        : JSON.stringify(oldObject, normalizeArrowForJSON),
    [oldObject],
  );
  const newObjectStr = useMemo(
    () =>
      isEmpty(newObject)
        ? ""
        : JSON.stringify(newObject, normalizeArrowForJSON),
    [newObject],
  );
  const oldObjectType = typeof oldObject;
  const newObjectType = typeof newObject;

  const diff = useMemo(() => {
    if (
      !allowGiantDiff &&
      (oldObjectStr.length > GIANT_DIFF_THRESHOLD ||
        newObjectStr.length > GIANT_DIFF_THRESHOLD)
    ) {
      return null;
    }

    if (prefix !== undefined) {
      return Diff.diffWordsWithSpace(
        oldObjectStr ? oldObjectStr.slice(0, prefix) : "",
        newObjectStr ? newObjectStr.slice(0, prefix) : "",
      );
    }

    if (oldObjectType === "string" && newObjectType === "string") {
      return Diff.diffWordsWithSpace(oldObjectStr, newObjectStr);
    }

    return Diff.diffJson(
      oldObjectStr ? JSON.parse(oldObjectStr) : "",
      newObjectStr ? JSON.parse(newObjectStr) : "",
    );
  }, [
    newObjectStr,
    oldObjectStr,
    oldObjectType,
    newObjectType,
    allowGiantDiff,
    prefix,
  ]);

  return { diff };
}

export function DiffObject({
  oldObject,
  newObject,
  prefix,
  code = false,
  addedFieldName,
  removedFieldName,
}: DiffObjectProps & {
  code?: boolean;
  addedFieldName?: string;
  removedFieldName?: string;
}) {
  const [allowGiantDiff, _setAllowGiantDiff] = useState(false);
  const { diff } = useDiffState({
    oldObject,
    newObject,
    prefix,
    allowGiantDiff,
  });

  if (diff === null) {
    return (
      <span className="inline-flex items-center gap-2 text-primary-500">
        <FileWarning className="size-3" /> Diff is too large to display
      </span>
    );
  } else {
    return (
      <DiffParts
        diff={diff}
        code={code}
        addedFieldName={addedFieldName}
        removedFieldName={removedFieldName}
      />
    );
  }
}

export const roundedScoreInPercents = (score: number | null) => {
  return score == null
    ? NULL_DASH
    : `${(Math.round(Number(score) * 100 * 10) / 10).toFixed(1)}%`;
};

export function DiffScore({
  oldScoreClassName,
  newScoreClassName,
  oldScoreSwatchClassName,
  oldScore,
  newScore,
  changedOnly = false,
}: DiffScoreProps) {
  return (
    <DiffNumbers
      oldScoreClassName={oldScoreClassName}
      newScoreClassName={newScoreClassName}
      oldScoreSwatchClassName={oldScoreSwatchClassName}
      oldNumber={oldScore}
      newNumber={newScore}
      formatNumber={roundedScoreInPercents}
      percentDiff={(start, end) => Number(end) - Number(start)}
      upIsGood
      changedOnly={changedOnly}
    />
  );
}

export function DiffNumbers({
  oldScoreClassName,
  newScoreClassName,
  oldScoreSwatchClassName,
  oldNumber,
  newNumber,
  formatNumber,
  formatDiff,
  percentDiff,
  upIsGood,
  changedOnly = false,
  hideChanged = false,
}: DiffNumberProps) {
  const bothNull = newNumber === null && oldNumber === null;

  if (bothNull) {
    return <NullFormatter />;
  }

  const newNumC = newNumber ?? 0;
  const oldNumC = oldNumber ?? 0;

  const isChanged =
    newNumber !== oldNumber && newNumber != null && oldNumber != null;
  const isImproved = upIsGood ? newNumC > oldNumC : newNumC < oldNumC;

  const roundedScoreInPercents = (score: number) => {
    const ret = (Math.round(Number(score) * 100 * 10) / 10).toFixed(1);
    // if infinity or -infinity, return 100
    if (ret === "Infinity" || ret === "-Infinity") {
      return "Inf";
    } else {
      return `${ret}%`;
    }
  };

  const diff = formatDiff
    ? formatDiff(oldNumC, newNumC)
    : roundedScoreInPercents(Math.abs(percentDiff(oldNumC, newNumC)));

  const changed = isChanged && !hideChanged && (
    <span
      className={cn("text-xs ml-1 text-primary-600", {
        [diffBgClassName.good]: upIsGood !== undefined ? isImproved : undefined,
        [diffBgClassName.bad]: upIsGood !== undefined ? !isImproved : undefined,
      })}
    >
      {newNumC > oldNumC ? "+" : "-"}
      {diff}
    </span>
  );

  if (changedOnly) {
    return changed;
  }

  return (
    <>
      <span
        className={cn(
          "text-comparison-600",
          oldScoreClassName,
          "bg-transparent",
          {
            "font-medium": !!oldNumber,
          },
        )}
      >
        {oldScoreSwatchClassName && (
          <span
            className={cn(
              oldScoreSwatchClassName,
              "size-1.5 mr-1 inline-block rounded-full mb-[1px]",
            )}
          />
        )}
        {!isEmpty(oldNumber) ? formatNumber(oldNumber) : <NullFormatter />}
      </span>
      <span className="px-1 font-inter text-primary-500">{" -> "}</span>
      <span
        className={cn("text-primary-800", newScoreClassName, "bg-transparent", {
          "font-medium": !!newNumber,
        })}
      >
        {!isEmpty(newNumber) ? formatNumber(newNumber) : <NullFormatter />}
      </span>
      {changed}
    </>
  );
}
