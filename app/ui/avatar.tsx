import { cn } from "#/utils/classnames";
import { UserRound } from "lucide-react";
import Link from "next/link";

export type AvatarSize = "xs" | "sm" | "md" | "lg";

type AvatarProps = {
  imgUrl?: string | null;
  size?: AvatarSize;
  link?: string | null;
};

export const Avatar = ({
  className,
  imgUrl,
  size = "sm",
  link,
}: {
  className?: string;
} & AvatarProps) => {
  const measuredSizeClass =
    size == "xs"
      ? "size-4"
      : size === "sm"
        ? "size-6"
        : size === "md"
          ? "size-8"
          : "size-12";
  const avatar = imgUrl ? (
    <img
      src={imgUrl}
      className={cn(
        "bg-primary-200 rounded-full text-[0px] border flex-none",
        measuredSizeClass,
        className,
      )}
      alt="Avatar"
    />
  ) : (
    <div
      className={cn(
        "border border-primary-400 rounded-full flex-none flex items-center justify-center bg-primary-300 text-primary-600",
        measuredSizeClass,
      )}
    >
      <UserRound
        size={size === "xs" ? 10 : size === "sm" ? 12 : size === "md" ? 16 : 18}
      />
    </div>
  );
  if (!link) {
    return avatar;
  }
  return (
    <Link
      href={link}
      target="_blank"
      rel="noreferrer"
      className="flex items-center space-x-1"
    >
      {avatar}
    </Link>
  );
};

export const AvatarGroup = ({
  className,
  avatars,
}: {
  className?: string;
  avatars: AvatarProps[];
}) => {
  return (
    <div className={cn("flex self-center", className)}>
      {avatars.map((avatarProps, idx) => (
        <div
          className={cn(
            "flex-none",
            idx > 0 && marginForSize(avatarProps.size),
          )}
          key={idx}
          style={{ zIndex: avatars.length - idx }}
        >
          <Avatar {...avatarProps} />
        </div>
      ))}
    </div>
  );
};

function marginForSize(size: AvatarSize = "sm"): string {
  switch (size) {
    case "xs":
      return "-ml-2";
    case "lg":
      return "-ml-5";
    case "md":
    case "sm":
    default:
      return "-ml-3";
  }
}
