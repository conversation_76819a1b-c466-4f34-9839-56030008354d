import { useCallback, useEffect } from "react";
import { useMemo, useState } from "react";
import {
  type ExperimentsChartingValues,
  type View,
  type ViewParams,
  type ViewProps,
  makeRequiredPatchParams,
  useViewQ<PERSON>y,
  useViews<PERSON><PERSON>y<PERSON><PERSON>,
  selectionType<PERSON>arser,
} from "#/utils/view/use-view";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useOrg } from "#/utils/user";
import { MinVersion, useFeatureFlags } from "#/lib/feature-flags";
import { apiDelete, apiPatch, apiPostCors } from "#/utils/btapi/fetch";
import { BT_FOUND_EXISTING_HEADER } from "@braintrust/core";
import { viewOptionsSchema } from "@braintrust/core/typespecs";
import { useSessionToken } from "#/utils/auth/session-token";
import { toastAndLogError } from "#/utils/view/view-utils";
import { ViewDropdown } from "./views/view-dropdown";
import { CreateViewDialog } from "./views/create-view-dialog";
import { RenameViewDialog } from "./views/rename-view-dialog";
import { DeleteViewDialog } from "./views/delete-view-dialog";
import { useInitializeView } from "./views/use-initialize-view";

function makeDuplicateViewError(name: string) {
  return `A view with the name ${name} already exists`;
}

export function Views<ExperimentsCharting extends boolean = false>({
  pageIdentifier,
  viewParams,
  defaultViewName = "All rows",
  viewProps,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  defaultViewName?: string;
  viewProps: ViewProps<ExperimentsCharting>;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const { api_url: apiUrl } = useOrg();

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  useEffect(() => {
    if (!areViewsEnabled) {
      console.warn(
        `Views API endpoint not available. Please upgrade your API server to a version >= ${MinVersion.views} to access table views.`,
      );
    }
  }, [areViewsEnabled]);

  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });
  const { data, isPending } = useViewQuery({
    viewParams,
    pageIdentifier,
  });

  const defaultView = useMemo(
    () => ({ id: null, builtin: true, name: defaultViewName }),
    [defaultViewName],
  );

  const views: View[] | undefined = useMemo(() => {
    return [defaultView, ...(data ?? [])];
  }, [data, defaultView]);

  const {
    search: _,
    resetState,
    columnOrder,
    columnSizing,
    columnVisibility,
  } = viewProps;

  const onViewInitialized = useCallback(
    (view: View | null) => {
      resetState(view, true);
    },
    [resetState],
  );
  const [viewName, setViewName] = useInitializeView({
    pageIdentifier,
    viewParams,
    onViewInitialized,
  });
  const selectedView: View | undefined =
    data?.find((v) => v.name === viewName) ?? defaultView;

  const [isCreateViewDialogOpen, setIsCreateViewDialogOpen] = useState(false);
  const [isRenameViewDialogOpen, setIsRenameViewDialogOpen] = useState(false);
  const [isDeleteViewDialogOpen, setIsDeleteViewDialogOpen] = useState(false);
  const deleteViewMutationFn = async (id: string | null) => {
    if (!id) {
      throw new Error("Cannot delete default view");
    }
    const sessionToken = await getOrRefreshToken();
    const resp = await apiDelete({
      url: `${apiUrl}/v1/view/${id}`,
      sessionToken,
      payload: makeRequiredPatchParams(viewParams),
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    return await resp.json();
  };

  const loadView = (view: View | null) => {
    setViewName(view?.name ?? null);
    resetState(view);
  };

  const queryClient = useQueryClient();
  const { mutate: deleteView } = useMutation({
    mutationFn: deleteViewMutationFn,
    onMutate: async (id: string | null) => {
      await queryClient.cancelQueries({ queryKey });
      const previousViews = queryClient.getQueryData<View[]>(queryKey);
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== id),
      );
      loadView(null);
      return previousViews;
    },
    onError: (error, _, previousViews) => {
      toastAndLogError("delete", error);
      queryClient.setQueryData<View[]>(queryKey, previousViews);
    },
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== view.id),
      );
    },
  });

  const hasExperimentChartProps = "excludedMeasures" in viewProps;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- A little hacky but get the typing to work
  const chartViewProps = viewProps as ViewProps<true>;
  const experimentsChartProps = useMemo(():
    | ExperimentsChartingValues
    | Record<string, never> => {
    if (hasExperimentChartProps) {
      return {
        excludedMeasures: chartViewProps.excludedMeasures,
        yMetric: chartViewProps.yMetric,
        xAxis: chartViewProps.xAxis,
        symbolGrouping: chartViewProps.symbolGrouping,
        xAxisAggregation: chartViewProps.xAxisAggregation,
        chartAnnotations: chartViewProps.chartAnnotations,
      };
    }
    return {};
  }, [
    hasExperimentChartProps,
    chartViewProps.excludedMeasures,
    chartViewProps.yMetric,
    chartViewProps.xAxis,
    chartViewProps.symbolGrouping,
    chartViewProps.xAxisAggregation,
    chartViewProps.chartAnnotations,
  ]);

  const viewOptionsToSave = useMemo(() => {
    try {
      return viewOptionsSchema.parse({
        columnOrder,
        columnSizing,
        columnVisibility,
        grouping:
          typeof viewProps.grouping === "string"
            ? viewProps.grouping
            : selectionTypeParser.serialize(viewProps.grouping),
        rowHeight: viewProps.rowHeight,
        tallGroupRows: viewProps.tallGroupRows,
        layout: viewProps.layout,
        chartHeight: viewProps.chartHeight,
        ...experimentsChartProps,
      });
    } catch (e) {
      toastAndLogError("parse", e, true);
    }

    return undefined;
  }, [
    columnOrder,
    columnSizing,
    columnVisibility,
    viewProps.grouping,
    viewProps.rowHeight,
    viewProps.tallGroupRows,
    viewProps.layout,
    viewProps.chartHeight,
    experimentsChartProps,
  ]);

  const createViewMutationFn = async (name: string) => {
    if (!viewParams) {
      throw new Error("viewParams not set");
    }
    const sessionToken = await getOrRefreshToken();
    if (views?.some((v) => v.name === name)) {
      throw new Error(makeDuplicateViewError(name));
    }
    const payload: Omit<View, "id"> = {
      object_type: viewParams.objectType,
      object_id: viewParams.objectId,
      view_type: viewParams.viewType,
      name,
      view_data: { search: viewProps.search },
      options: viewOptionsToSave,
    };
    const resp = await apiPostCors({
      url: `${apiUrl}/v1/view`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(makeDuplicateViewError(name));
    }
    return await resp.json();
  };

  const {
    mutate: createView,
    variables: optimisticCreatedName,
    isPending: isCreatePending,
  } = useMutation({
    mutationFn: createViewMutationFn,
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("create", error),
    onSuccess: async (result: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) => [
        ...(prev ?? []),
        result,
      ]);
      setViewName(result.name ?? null);
      // reset to clear url params
      resetState(result);
    },
  });

  const renameViewMutationFn = async ({
    viewId,
    name,
  }: {
    viewId: string | null;
    name: string;
  }) => {
    const payload = { ...makeRequiredPatchParams(viewParams), name };
    if (!viewId) {
      throw new Error("Cannot rename default view");
    }
    const sessionToken = await getOrRefreshToken();
    if (views?.some((v) => v.name === name)) {
      throw new Error(makeDuplicateViewError(name));
    }
    const resp = await apiPatch({
      url: `${apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(makeDuplicateViewError(name));
    }
    return await resp.json();
  };

  const {
    mutate: renameView,
    variables: renameViewVariables,
    isPending: isRenamePending,
  } = useMutation({
    mutationFn: renameViewMutationFn,
    onMutate: async ({
      viewId,
      name,
    }: {
      viewId: string | null;
      name: string;
    }) => {
      await queryClient.cancelQueries({ queryKey });
      const previousViews = queryClient.getQueryData<View[]>(queryKey);
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === viewId ? { ...v, name } : v)),
      );
      return previousViews;
    },
    onError: (error, _, previousViews) => {
      toastAndLogError("rename", error);
      queryClient.setQueryData<View[]>(queryKey, previousViews);
    },
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === view.id ? view : v)),
      );
      setViewName(view.name ?? null);
    },
  });

  if (!areViewsEnabled) {
    return null;
  }

  return (
    <>
      <ViewDropdown
        views={views}
        selectedView={selectedView}
        viewNameOverride={
          isCreatePending
            ? optimisticCreatedName
            : isRenamePending
              ? renameViewVariables?.name
              : undefined
        }
        loadView={loadView}
        isLoadingViews={isPending}
        setCreateViewDialogOpen={setIsCreateViewDialogOpen}
        setRenameViewDialogOpen={setIsRenameViewDialogOpen}
        setDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
        pageIdentifier={pageIdentifier}
      />
      <CreateViewDialog
        isCreateViewDialogOpen={isCreateViewDialogOpen}
        setIsCreateDatasetDialogOpen={setIsCreateViewDialogOpen}
        createView={createView}
      />
      {selectedView && (
        <>
          <RenameViewDialog
            isRenameViewDialogOpen={isRenameViewDialogOpen}
            setIsRenameViewDialogOpen={setIsRenameViewDialogOpen}
            view={selectedView}
            renameView={renameView}
          />
          <DeleteViewDialog
            isDeleteViewDialogOpen={isDeleteViewDialogOpen}
            setIsDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
            view={selectedView}
            deleteView={() => deleteView(selectedView.id)}
          />
        </>
      )}
    </>
  );
}
