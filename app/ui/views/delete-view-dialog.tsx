import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { type View } from "#/utils/view/use-view";

export function DeleteViewDialog({
  isDeleteViewDialogOpen,
  setIsDeleteViewDialogOpen,
  view,
  deleteView,
}: {
  isDeleteViewDialogOpen: boolean;
  setIsDeleteViewDialogOpen: (open: boolean) => void;
  view: View;
  deleteView: () => void;
}) {
  return (
    <ConfirmationDialog
      open={isDeleteViewDialogOpen}
      onOpenChange={setIsDeleteViewDialogOpen}
      title={"Delete view"}
      description={`Are you sure you want to delete view "${view.name}"?`}
      confirmText="Delete"
      onConfirm={() => {
        setIsDeleteViewDialogOpen(false);
        deleteView();
      }}
    />
  );
}
