import { OneLineTextPrompt } from "#/ui/dialogs/one-line-text-prompt";

export function CreateViewDialog({
  isCreateViewDialogOpen,
  setIsCreateDatasetDialogOpen,
  createView,
}: {
  isCreateViewDialogOpen: boolean;
  setIsCreateDatasetDialogOpen: (open: boolean) => void;
  createView: (name: string) => void;
}) {
  return (
    <OneLineTextPrompt
      title="Create view"
      description="Save the current view configurations (filters, sorts, and columns) for quick access"
      fieldName="Name"
      onSubmit={(name) => {
        createView(name);
        setIsCreateDatasetDialogOpen(false);
      }}
      onOpenChange={() => setIsCreateDatasetDialogOpen(false)}
      open={isCreateViewDialogOpen}
      submitLabel="Create"
      placeholder="Enter view name"
    >
      <div className="rounded-md p-3 text-sm bg-accent-50 text-accent-700">
        After this view is created, any changes to filters, sorts, and columns
        will be auto-saved to this view.
      </div>
    </OneLineTextPrompt>
  );
}
