"use client";

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
} from "#/ui/navigation-menu";
import Link from "next/link";

import { Button, buttonVariants } from "#/ui/button";
import { navigationLinks } from "#/app/(landing)/constants";
import { Logo } from "#/ui/landing/logo";
import { cn } from "#/utils/classnames";
import { She<PERSON>, <PERSON>etContent, SheetTitle, SheetTrigger } from "#/ui/sheet";
import { type BtSession } from "#/utils/auth/server-session";
import { signInPath, signUpPath } from "#/utils/auth/redirects";
import { Menu } from "lucide-react";
import { CtaLink } from "#/app/(landing)/cta-button";
import { useState } from "react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface Props {
  className?: string;
  session: BtSession | null;
}

const Header = ({ className, session }: Props) => {
  const [mobileSheetOpen, setMobileSheetOpen] = useState(false);
  const closeSheet = () => setMobileSheetOpen(false);

  return (
    <header
      className={cn(
        "flex gap-4 items-center justify-between p-4 sm:p-8 z-30 relative",
        className,
      )}
    >
      <Link href={session?.loggedIn ? "/home" : "/"} aria-label="Home">
        <Logo width={170} />
      </Link>
      <Sheet open={mobileSheetOpen} onOpenChange={setMobileSheetOpen}>
        <SheetTrigger asChild className={cn("lg:hidden")}>
          <Button variant="ghost" size="icon">
            <Menu className="size-6 md:size-8" />
          </Button>
        </SheetTrigger>
        <SheetContent side="top" className="shadow-md bg-background lg:hidden">
          <VisuallyHidden>
            <SheetTitle>Navigation</SheetTitle>
          </VisuallyHidden>
          <div className="flex flex-col gap-3">
            {navigationLinks.map((link) => (
              <Link
                key={link.title}
                href={link.href}
                className="text-center"
                onMouseUp={closeSheet}
              >
                {link.title}
              </Link>
            ))}
            <Link
              href={signInPath()}
              className="text-center"
              onMouseUp={closeSheet}
            >
              Sign in
            </Link>
            {!session?.loggedIn && (
              <Link
                href={signUpPath()}
                className="text-center"
                onMouseUp={closeSheet}
              >
                Sign up for free
              </Link>
            )}
          </div>
        </SheetContent>
      </Sheet>
      <NavigationMenu className={cn("hidden", "lg:block")}>
        <NavigationMenuList className="gap-2 text-sm font-medium">
          {navigationLinks.map((link) => (
            <NavigationMenuItem key={link.title} style={{ marginLeft: 0 }}>
              <Link
                href={link.href}
                className={cn(
                  buttonVariants({
                    variant: "ghost",
                    size: "sm",
                  }),
                  "pointer-events-auto text-base font-normal",
                )}
              >
                {link.title}
              </Link>
            </NavigationMenuItem>
          ))}
          {session?.loggedIn ? (
            <>
              <NavigationMenuItem>
                <Link
                  href="/app"
                  className={cn(
                    buttonVariants({ size: "sm" }),
                    "text-base font-normal border-primary-800",
                  )}
                >
                  Dashboard
                </Link>
              </NavigationMenuItem>
            </>
          ) : (
            <>
              <>
                <NavigationMenuItem>
                  <Link
                    href={signInPath()}
                    className={cn(
                      buttonVariants({
                        size: "sm",
                        variant: "ghost",
                      }),
                      "text-base font-normal pointer-events-auto",
                    )}
                  >
                    Sign in
                  </Link>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <CtaLink
                    variant="border"
                    className="text-base font-normal border-primary-800"
                    size="sm"
                    cta="Sign up for free"
                  />
                </NavigationMenuItem>
              </>
            </>
          )}
        </NavigationMenuList>
      </NavigationMenu>
    </header>
  );
};

export default Header;
