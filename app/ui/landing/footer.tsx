import { footerLinks } from "#/app/(landing)/constants";
import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { Logo } from "./logo";

interface Props {
  className?: string;
  maxWidth?: string;
  inApp?: boolean;
  inDocs?: boolean;
  orgName?: string;
}

export const Footer = ({
  className,
  maxWidth,
  inApp,
  inDocs,
  orgName,
}: Props) => {
  const braintrustLinkHref = inApp && orgName ? getOrgLink({ orgName }) : "/";

  return (
    <footer
      className={cn(
        "pb-12 relative z-10 border-primary-200 flex-none text-sm pt-12 @container",
        {
          "lg:flex lg:items-start lg:gap-10 lg:pb-30": !inDocs,
          "sm:pb-[2rem] lg:pb-[3rem] text-primary-500 text-xs": inApp,
          "justify-center": !inApp && !inDocs,
          "flex items-start text-xs": inDocs,
        },
        className,
      )}
      style={{ maxWidth }}
    >
      <nav
        className={cn(
          "flex items-center justify-center md:justify-start max-w-none footer",
          {
            "justify-start items-start": inApp || inDocs,
            "md:justify-center": !inApp,
          },
        )}
      >
        <ul
          className={cn(
            "flex flex-col flex-wrap @lg:flex-row items-start @lg:items-center font-medium gap-2 @lg:gap-x-5 @lg:gap-y-2",
            {
              "font-normal": inDocs,
              "items-center": !inApp && !inDocs,
              "justify-start flex-row @lg:flex-row": inApp,
            },
          )}
        >
          <li
            className={cn("mb-3 @md:mb-0", {
              "w-full md:mb-3 pt-2": inDocs,
              "w-full @lg:w-auto @md:mb-1 @lg:mb-0": inApp,
            })}
          >
            <Link href={braintrustLinkHref} className="hover:text-primary-700">
              <Logo width={inApp ? 110 : 120} className="overflow-visible" />
            </Link>
          </li>
          {(inApp || inDocs
            ? [{ title: "Home", href: "/home" }, ...footerLinks]
            : footerLinks
          ).map((link) => (
            <li key={link.title} style={{ marginLeft: 0 }}>
              <Link
                href={link.href}
                className="font-normal hover:text-primary-700"
                target={inApp ? "_blank" : undefined}
              >
                {link.title}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </footer>
  );
};

export default Footer;
