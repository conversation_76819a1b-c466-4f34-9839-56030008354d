import { TooltipPortal } from "@radix-ui/react-tooltip";
import { Button, type ButtonProps } from "./button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip";
import { useCallback, useState } from "react";
import { Check, Clipboard } from "lucide-react";
import { Spinner } from "./icons/spinner";

export const CopyToClipboardButton = ({
  textToCopy,
  getTextToCopy,
  copyMessage = "Copy to clipboard",
  copiedMessage = "Copied!",
  children,
  disableTooltip,
  prependIcon,
  ...buttonProps
}: {
  textToCopy: string;
  getTextToCopy?: () => Promise<string>;
  copyMessage?: string;
  copiedMessage?: string;
  disableTooltip?: boolean;
  prependIcon?: boolean;
} & ButtonProps) => {
  const [didCopy, setDidCopy] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const getText = useCallback(async () => {
    // if no async function to get text to copy provided, use textToCopy
    if (!getTextToCopy) {
      return textToCopy;
    }

    // set isLoading while running async function to get text to copy
    setIsLoading(true);
    const asyncText = await getTextToCopy();
    setIsLoading(false);
    return asyncText;
  }, [textToCopy, getTextToCopy]);

  const copyToClipboard = async (e: React.MouseEvent) => {
    e.preventDefault();
    const text = await getText();
    navigator.clipboard.writeText(text);
    setDidCopy(true);
    setTimeout(() => {
      setDidCopy(false);
    }, 2000);
  };

  const button = (
    <Button
      {...buttonProps}
      disabled={buttonProps.disabled ?? isLoading}
      onClick={copyToClipboard}
      Icon={
        isLoading
          ? Spinner
          : children && !prependIcon
            ? undefined
            : didCopy
              ? Check
              : Clipboard
      }
    >
      {children}
    </Button>
  );

  if (disableTooltip) return button;

  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>{button}</TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          className="text-xs"
          onPointerDownOutside={(e) => {
            e.preventDefault();
          }}
        >
          {didCopy ? copiedMessage : copyMessage}
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};
