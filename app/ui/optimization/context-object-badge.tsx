import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import { MessageCircle, Database } from "lucide-react";
import { X } from "lucide-react";

export const ContextObjectBadge = ({
  contextObjectItem,
  onDelete,
}: {
  contextObjectItem: ContextObject;
  onDelete?: (contextObjectItem: ContextObject) => void;
}) => {
  return (
    <div
      className={cn(
        "flex max-w-40 items-center gap-1 rounded-md p-0.5 border pl-2 bg-primary-100 border-primary-300/50 text-primary-700",
        {
          "px-2 py-1": !onDelete,
        },
      )}
    >
      {contextObjectItem.resource === "task" ? (
        <MessageCircle className="mr-1 size-3 text-cyan-600" />
      ) : (
        <Database className="mr-1 size-3 text-fuchsia-600" />
      )}
      <span className="flex-1 truncate">{contextObjectItem.name}</span>
      {onDelete && (
        <Button
          variant="ghost"
          size="icon"
          className={cn("ml-1 size-5 bg-transparent hover:bg-primary-200")}
          Icon={X}
          onClick={() => {
            onDelete(contextObjectItem);
          }}
        />
      )}
    </div>
  );
};
