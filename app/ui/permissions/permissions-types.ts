import { z } from "zod";

import {
  type Permission,
  aclObjectTypeEnum,
  permissionEnum,
} from "@braintrust/core/typespecs";

export const userObjectTypeSchema = z.enum(["user", "group"]);
export type UserObjectType = z.infer<typeof userObjectTypeSchema>;

export const permissionsCheckboxSchema = z.strictObject({
  // Whether or not the user/group has the full set of required permissions.
  is_checked: z.boolean(),
  // Whether or not the checkbox is mutable. True if `is_checked` and none of the
  // "qualifier" conditions below are active.
  is_mutable: z.boolean(),
  // Whether or not the user/group has a subset of the required permissions.
  has_partial_permissions: z.boolean(),
  // A set of roles that the permission was obtained from. If empty, the
  // implication is that the permission was obtained from an un-bundled
  // permission setting.
  roles: z.string().array(),
  // A set of groups that the user/group belongs to that were granted the
  // permission.
  inherited_groups: z.string().array(),
  // A set of parent objects that the permission was inherited from.
  inherited_parent_objects: aclObjectTypeEnum.array(),
  // For permissions which are meant to be applied in a restricted form, this
  // boolean is true if the permission came from an un-restricted grant.
  unrestricted_grant_for_restricted_permission: z.boolean(),
});

export type PermissionsCheckbox = z.infer<typeof permissionsCheckboxSchema>;

// Display item for a user/group who shows up as already having permissions on
// an object. They are categorized along much the same lines as a
// PermissionsCheckbox.
export const userGroupWithObjectPermissionsSchema = z.strictObject({
  user_object_type: userObjectTypeSchema,
  user_group_id: z.string(),
  // Whether or not the user/group has any directly-granted permissions (a
  // direct grant must be on the object, on this user/group, as a
  // bare-permission (not a role), and with object type restricted).
  has_direct_permission: z.boolean(),
  roles: permissionsCheckboxSchema.shape.roles,
  inherited_groups: permissionsCheckboxSchema.shape.inherited_groups,
  inherited_parent_objects:
    permissionsCheckboxSchema.shape.inherited_parent_objects,
  has_unrestricted_permissions: z.boolean(),
});

export type UserGroupWithObjectPermissions = z.infer<
  typeof userGroupWithObjectPermissionsSchema
>;

export const permissionsRequirementSchema = z.strictObject({
  objectType: aclObjectTypeEnum,
  permissions: z
    .object({
      permission: permissionEnum,
      restrictObjectType: aclObjectTypeEnum.nullish(),
    })
    .array(),
});

export type PermissionsRequirement = z.infer<
  typeof permissionsRequirementSchema
>;

export type PermissionsRequirementPermission =
  PermissionsRequirement["permissions"][0];

export const crudPermissions: Permission[] = [
  "create",
  "read",
  "update",
  "delete",
];

export const manageAccessPermissions: [Permission, ...Permission[]] = [
  "create_acls",
  "read_acls",
  "update_acls",
  "delete_acls",
];

export const permissionsCheckboxGroupsSchema = z.record(
  z.record(permissionsCheckboxSchema),
);
export type PermissionsCheckboxGroups = z.infer<
  typeof permissionsCheckboxGroupsSchema
>;

export const permissionsRequirementKeySchema = z.strictObject({
  outerKey: z.string(),
  innerKey: z.string(),
});

export type PermissionsRequirementKey = z.infer<
  typeof permissionsRequirementKeySchema
>;

export function permissionsRequirementKeyStr(
  outerKey: string,
  innerKey: string,
) {
  const x: PermissionsRequirementKey = { outerKey, innerKey };
  return JSON.stringify(x);
}
