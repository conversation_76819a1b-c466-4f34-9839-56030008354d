import { orgPermissionsRequirements } from "#/app/app/[org]/settings/team/org-permissions-types";
import { type AclObjectType, permissionEnum } from "@braintrust/core/typespecs";

import {
  type UpdatePermissionsRequirementItem,
  updatePermissionsRequirements,
} from "./permissions-client-actions";
import {
  type PermissionsCheckboxGroups,
  type PermissionsRequirement,
  type UserObjectType,
  permissionsRequirementKeyStr,
} from "./permissions-types";
import { projectPermissionsRequirements } from "#/app/app/[org]/p/project-permissions-types";
import { makeObjectPermissionsRequirements } from "#/app/app/[org]/p/object-permissions-types";
import { type LoadedBtSessionToken } from "#/utils/auth/session-token";
import { aclSpecs } from "@braintrust/local/app-schema";

const leafObjectTypes: Set<AclObjectType> = new Set([
  "experiment",
  "dataset",
  "project_log",
  "prompt_session",
  "prompt",
  "group",
]);

export async function updatePermissionsForUserOrGroup({
  values,
  apiUrl,
  sessionToken,
  objectId,
  objectType,
  userObjectType,
  userGroupId,
}: {
  values: PermissionsCheckboxGroups;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  objectId: string;
  objectType: AclObjectType;
  userGroupId: string;
  userObjectType: UserObjectType;
}) {
  const aclsToSave: UpdatePermissionsRequirementItem[] = [];

  Object.keys(values).forEach((groupKey) => {
    const items = values[groupKey];

    Object.keys(items).forEach((checkboxKey) => {
      const item = items[checkboxKey];

      // Be very paranoid and only save the item if the server told us it's mutable
      if (item.is_mutable) {
        let permissionsRequirementKey;

        // If the object type is a 'leaf' type, the UI is not nested, so we don't need to include the groupKey
        if (leafObjectTypes.has(objectType)) {
          permissionsRequirementKey = checkboxKey;
        } else {
          permissionsRequirementKey = permissionsRequirementKeyStr(
            groupKey,
            checkboxKey,
          );
        }

        let requirement: PermissionsRequirement | undefined;
        if (objectType === "organization") {
          requirement = orgPermissionsRequirements[permissionsRequirementKey];
        } else if (objectType === "project") {
          requirement =
            projectPermissionsRequirements[permissionsRequirementKey];
        } else {
          const requirements = makeObjectPermissionsRequirements(objectType);
          requirement = requirements[permissionsRequirementKey];
        }

        if (requirement) {
          aclsToSave.push({
            requirement,
            objectId,
            userObjectType,
            userGroupId,
            action: item.is_checked ? "add" : "remove",
          });
        }
      }
    });
  });

  await updatePermissionsRequirements({
    apiUrl,
    sessionToken,
    requirements: aclsToSave,
  });
}

export function getChangedValues({
  initialValues,
  submittedValues,
}: {
  initialValues: PermissionsCheckboxGroups;
  submittedValues: PermissionsCheckboxGroups;
}): PermissionsCheckboxGroups {
  const changedValues: PermissionsCheckboxGroups = {};

  Object.keys(initialValues).forEach((groupKey) => {
    const items = initialValues[groupKey];

    Object.keys(items).forEach((checkboxKey) => {
      const item = items[checkboxKey];
      const submittedItem = submittedValues[groupKey][checkboxKey];

      if (item.is_checked !== submittedItem.is_checked) {
        if (!changedValues[groupKey]) {
          changedValues[groupKey] = {};
        }
        changedValues[groupKey][checkboxKey] = submittedItem;
      }
    });
  });

  return changedValues;
}

const allPermissions = permissionEnum.options;

const projectLevelObjectTypeKeys = Object.entries(aclSpecs)
  .filter((entry): entry is [AclObjectType, (typeof aclSpecs)[AclObjectType]] =>
    entry[1].parentAclObjectTypes.includes("org_project"),
  )
  .map(([key]) => key);

function getPermissionsToRemove(objectTypes: AclObjectType[]) {
  return objectTypes.flatMap((objectType) =>
    allPermissions.map((permission) => ({
      permission,
      restrictObjectType: objectType,
    })),
  );
}

function getProjectPermissionsToRemove() {
  return getPermissionsToRemove(projectLevelObjectTypeKeys);
}

function getObjectPermissionsToRemove(objectType: AclObjectType) {
  return getPermissionsToRemove([objectType]);
}

export async function removeDirectAccess({
  objectId,
  objectType,
  userOrGroupId,
  userObjectType,
  apiUrl,
  sessionToken,
}: {
  objectId: string;
  objectType: AclObjectType;
  userOrGroupId: string;
  userObjectType: UserObjectType;
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
}) {
  const permissionsToRemove =
    objectType === "project"
      ? getProjectPermissionsToRemove()
      : getObjectPermissionsToRemove(objectType);

  await updatePermissionsRequirements({
    apiUrl,
    sessionToken,
    requirements: [
      {
        requirement: {
          objectType,
          permissions: permissionsToRemove,
        },
        objectId,
        userObjectType,
        userGroupId: userOrGroupId,
        action: "remove",
      },
    ],
  });
}
