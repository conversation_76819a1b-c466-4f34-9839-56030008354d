"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import PermissionsDialog from "#/ui/permissions/permissions-dialog";
import PermissionsSet<PERSON>orm<PERSON>ield from "#/ui/permissions/permissions-form-field";
import { getObjectCheckboxItems, getProjectCheckboxItems } from "./items";
import { useOrg } from "#/utils/user";
import {
  getChangedValues,
  updatePermissionsForUserOrGroup,
} from "./form-helpers";
import { getInheritedGroups, getInheritedParentObjects } from "./utils";
import { type UserObjectType } from "./permissions-types";
import { type getProjectPermissions } from "#/app/app/[org]/p/project-permissions-actions";
import {
  type ProjectPermissionsCheckboxes,
  projectPermissionsCheckboxesSchema,
} from "#/app/app/[org]/p/project-permissions-types";
import { useQueryFunc } from "#/utils/react-query";
import { useSessionToken } from "#/utils/auth/session-token";

function Dialog({
  projectId,
  projectName,
  userObjectType,
  userOrGroupId,
  userOrGroupLabel,
  permissionsData,
  onSubmit,
}: {
  projectId: string;
  projectName: string;
  userObjectType: UserObjectType;
  userOrGroupId: string;
  userOrGroupLabel: string;
  permissionsData: ProjectPermissionsCheckboxes;
  onSubmit: (values: ProjectPermissionsCheckboxes) => void;
}) {
  const org = useOrg();

  const { getOrRefreshToken } = useSessionToken();

  const defaultValues = {
    project: permissionsData.project,
    experiment: permissionsData.experiment,
    dataset: permissionsData.dataset,
    project_log: permissionsData.project_log,
    prompt: permissionsData.prompt,
    prompt_session: permissionsData.prompt_session,
  };

  const form = useForm<ProjectPermissionsCheckboxes>({
    resolver: zodResolver(projectPermissionsCheckboxesSchema),
    defaultValues,
  });

  async function onFormSubmit(values: ProjectPermissionsCheckboxes) {
    const sessionToken = await getOrRefreshToken();

    const changedValues = getChangedValues({
      initialValues: defaultValues,
      submittedValues: values,
    });

    await updatePermissionsForUserOrGroup({
      values: changedValues,
      apiUrl: org.api_url,
      sessionToken,
      objectId: projectId,
      objectType: "project",
      userGroupId: userOrGroupId,
      userObjectType,
    });

    onSubmit(values);
  }

  return (
    <PermissionsDialog
      title={`${projectName} project permissions for ${userOrGroupLabel}`}
      form={form}
      inheritedGroups={getInheritedGroups(permissionsData)}
      inheritedParentObjects={getInheritedParentObjects(permissionsData)}
      onSubmit={onFormSubmit}
      className="sm:max-w-2xl"
    >
      <div className="flex flex-col gap-8">
        <div className="flex flex-col gap-8 md:flex-row">
          <PermissionsSetFormField
            form={form}
            items={getObjectCheckboxItems()}
            name="project"
            label="Project"
          />
          <PermissionsSetFormField
            form={form}
            items={getProjectCheckboxItems()}
            name="experiment"
            label="Experiments"
          />
          <PermissionsSetFormField
            form={form}
            items={getProjectCheckboxItems()}
            name="dataset"
            label="Datasets"
          />
        </div>
        <div className="flex flex-col gap-8 md:flex-row">
          <PermissionsSetFormField
            form={form}
            items={getProjectCheckboxItems()}
            name="project_log"
            label="Logs"
          />
          <PermissionsSetFormField
            form={form}
            items={getProjectCheckboxItems()}
            name="prompt"
            label="Prompts"
          />
          <PermissionsSetFormField
            form={form}
            items={getProjectCheckboxItems()}
            name="prompt_session"
            label="Playgrounds"
          />
        </div>
      </div>
    </PermissionsDialog>
  );
}

function ProjectLevelPermissionsDialog({
  projectId,
  projectName,
  userOrGroupLabel,
  userOrGroupId,
  userObjectType,
  onSubmit,
}: {
  projectName: string;
  projectId: string;
  userOrGroupLabel: string;
  userOrGroupId: string;
  userObjectType: UserObjectType;
  onSubmit: (values: ProjectPermissionsCheckboxes) => void;
}) {
  const {
    data: permissionsData,
    isLoading,
    invalidate,
  } = useQueryFunc<typeof getProjectPermissions>({
    fName: "getProjectPermissions",
    args: {
      projectId,
      userObjectType,
      userGroupId: userOrGroupId,
    },
  });
  if (isLoading || !permissionsData) {
    return null;
  }

  return (
    <Dialog
      projectId={projectId}
      projectName={projectName}
      userOrGroupLabel={userOrGroupLabel}
      permissionsData={permissionsData}
      userOrGroupId={userOrGroupId}
      userObjectType={userObjectType}
      onSubmit={(values) => {
        onSubmit(values);
        invalidate();
      }}
    />
  );
}

export default ProjectLevelPermissionsDialog;
