"use client";

import { z } from "zod";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import PermissionsDialog from "#/ui/permissions/permissions-dialog";
import PermissionsSet<PERSON><PERSON><PERSON>ield from "#/ui/permissions/permissions-form-field";
import { getObjectCheckboxItems } from "./items";
import { useOrg } from "#/utils/user";
import {
  getChangedValues,
  updatePermissionsForUserOrGroup,
} from "./form-helpers";
import { getInheritedGroups, getInheritedParentObjects } from "./utils";
import { type UserObjectType } from "./permissions-types";
import { type getObjectPermissions } from "#/app/app/[org]/p/object-permissions-actions";
import { type AclObjectType } from "@braintrust/core/typespecs";
import {
  type ObjectPermissionsCheckboxes,
  objectPermissionsCheckboxesSchema,
} from "#/app/app/[org]/p/object-permissions-types";
import { useQueryFunc } from "#/utils/react-query";
import { useSessionToken } from "#/utils/auth/session-token";

function Dialog({
  objectId,
  objectName,
  objectType,
  userObjectType,
  userOrGroupId,
  userOrGroupLabel,
  permissionsData,
  onSubmit,
}: {
  objectId: string;
  objectName: string;
  objectType: AclObjectType;
  userObjectType: UserObjectType;
  userOrGroupId: string;
  userOrGroupLabel: string;
  permissionsData: { permissions: ObjectPermissionsCheckboxes };
  onSubmit: (values: { permissions: ObjectPermissionsCheckboxes }) => void;
}) {
  const org = useOrg();

  const { getOrRefreshToken } = useSessionToken();

  const defaultValues = permissionsData;

  const form = useForm<{ permissions: ObjectPermissionsCheckboxes }>({
    resolver: zodResolver(
      z.strictObject({ permissions: objectPermissionsCheckboxesSchema }),
    ),
    defaultValues,
  });

  async function onFormSubmit(values: {
    permissions: ObjectPermissionsCheckboxes;
  }) {
    const sessionToken = await getOrRefreshToken();

    const changedValues = getChangedValues({
      initialValues: defaultValues,
      submittedValues: values,
    });

    await updatePermissionsForUserOrGroup({
      values: changedValues,
      apiUrl: org.api_url,
      sessionToken,
      objectId,
      objectType,
      userGroupId: userOrGroupId,
      userObjectType,
    });

    onSubmit(values);
  }

  return (
    <PermissionsDialog
      title={`${objectName} object permissions for ${userOrGroupLabel}`}
      form={form}
      inheritedGroups={getInheritedGroups(permissionsData)}
      inheritedParentObjects={getInheritedParentObjects(permissionsData)}
      onSubmit={onFormSubmit}
      className="sm:max-w-xl"
    >
      <PermissionsSetFormField
        form={form}
        items={getObjectCheckboxItems()}
        name="permissions"
        label="Permissions"
      />
    </PermissionsDialog>
  );
}

function ObjectLevelPermissionsDialog({
  objectId,
  objectType,
  objectName,
  userOrGroupLabel,
  userOrGroupId,
  userObjectType,
  onSubmit,
}: {
  objectId: string;
  objectType: AclObjectType;
  objectName: string;
  userOrGroupLabel: string;
  userOrGroupId: string;
  userObjectType: UserObjectType;
  onSubmit: (values: { permissions: ObjectPermissionsCheckboxes }) => void;
}) {
  const {
    data: permissionsData,
    isLoading,
    invalidate,
  } = useQueryFunc<typeof getObjectPermissions>({
    fName: "getObjectPermissions",
    args: {
      objectId,
      objectType,
      userObjectType,
      userGroupId: userOrGroupId,
    },
  });

  if (isLoading || !permissionsData) {
    return null;
  }

  return (
    <Dialog
      objectId={objectId}
      objectName={objectName}
      objectType={objectType}
      userOrGroupLabel={userOrGroupLabel}
      permissionsData={{ permissions: permissionsData }}
      userOrGroupId={userOrGroupId}
      userObjectType={userObjectType}
      onSubmit={(values) => {
        onSubmit(values);
        invalidate();
      }}
    />
  );
}

export default ObjectLevelPermissionsDialog;
