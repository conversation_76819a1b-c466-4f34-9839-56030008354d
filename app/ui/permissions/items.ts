import { type AclObjectType } from "@braintrust/core/typespecs";

const LABELS: Record<string, string> = {
  create: "Create",
  read: "Read",
  update: "Update",
  delete: "Delete",
  manage_access: "Manage Access",
} as const;

export const OBJECT_TYPE_LABELS: Record<AclObjectType, string> = {
  role: "Role",
  group: "Group",
  organization: "Organization",
  project: "Project",
  experiment: "Experiment",
  dataset: "Dataset",
  prompt: "Prompt",
  prompt_session: "Playground",
  org_member: "Organization Member",
  project_log: "Project Log",
  org_project: "Organization Project",
} as const;

export type PermissionsCheckboxItem = {
  id: string;
  label: string;
};

export function getOrgPermissionCheckboxItems() {
  return [
    { id: "manageSettings", label: "Manage Settings" },
    { id: "inviteMembers", label: "Invite Members" },
    { id: "removeMembers", label: "Remove Members" },
    { id: "manageAccess", label: LABELS.manage_access },
  ];
}

export function getProjectCheckboxItems() {
  return [
    { id: "create", label: LABELS.create },
    { id: "read", label: LABELS.read },
    { id: "update", label: LABELS.update },
    { id: "delete", label: LABELS.delete },
    { id: "manageAccess", label: LABELS.manage_access },
  ];
}

export function getObjectCheckboxItems() {
  return [
    { id: "read", label: LABELS.read },
    { id: "update", label: LABELS.update },
    { id: "delete", label: LABELS.delete },
    { id: "manageAccess", label: LABELS.manage_access },
  ];
}
