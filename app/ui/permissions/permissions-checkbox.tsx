import React from "react";
import { Checkbox } from "#/ui/shadcn-checkbox";
import { FormControl, FormItem, FormLabel } from "#/ui/form";
import {
  type ControllerRenderProps,
  type FieldValues,
  type Path,
} from "react-hook-form";
import { MinusIcon } from "lucide-react";
import { type PermissionsCheckboxItem } from "./items";

function EditableCheckbox<T extends FieldValues>({
  field,
  item,
}: {
  field: ControllerRenderProps<T, Path<T>>;
  item: { id: string; label: string };
}) {
  return (
    <div className="flex items-start">
      <FormControl>
        <Checkbox
          checked={field.value[item.id].is_checked}
          onCheckedChange={(checked) => {
            field.onChange({
              ...field.value,
              [item.id]: {
                ...field.value[item.id],
                is_checked: checked,
              },
            });
          }}
        />
      </FormControl>
      <FormLabel className="pl-2 text-sm">{item.label}</FormLabel>
    </div>
  );
}

function RoleAppliedCheckbox({ label }: { label: string }) {
  return (
    <div className="flex items-start">
      <Checkbox checked={true} disabled />
      <div className="pl-2 text-sm">{label}</div>
    </div>
  );
}

function GroupAppliedCheckbox({
  label,
  groupAppliedName,
}: {
  label: string;
  groupAppliedName?: string;
}) {
  return (
    <div className="flex items-start">
      <Checkbox checked={true} disabled />
      <div className="pl-2 text-sm">
        {label}
        <div className="text-xs text-primary-500">
          Inherited from {groupAppliedName || "a group"}
        </div>
      </div>
    </div>
  );
}

function PartialAppliedCheckbox({ label }: { label: string }) {
  return (
    <div className="flex items-start">
      <div className="flex size-4 items-center justify-center">
        <MinusIcon className="size-4" />
      </div>
      <div className="pl-2 text-sm">
        {label}
        <div className="text-xs text-primary-500">
          Permission is partially filled
        </div>
      </div>
    </div>
  );
}

function NonApplicableCheckbox({ label }: { label: string }) {
  return (
    <div className="flex items-baseline">
      <div className="flex size-4 items-center justify-center">
        <MinusIcon className="size-4" />
      </div>
      <div className="pl-2 text-sm">
        {label}
        <div className="text-xs text-primary-500">
          Permission cannot be updated through the UI
        </div>
      </div>
    </div>
  );
}

function PermissionsCheckbox<T extends FieldValues>({
  field,
  item,
}: {
  field: ControllerRenderProps<T, Path<T>>;
  item: PermissionsCheckboxItem;
}) {
  let groupAppliedName = (field.value[item.id].inherited_groups || []).join(
    ",",
  );
  if (
    !groupAppliedName &&
    field.value[item.id].unrestricted_grant_for_restricted_permission
  ) {
    groupAppliedName = "a more general permission";
  }
  if (
    !groupAppliedName &&
    field.value[item.id].inherited_parent_objects.length
  ) {
    groupAppliedName = "a parent object";
  }

  let mode;
  if (groupAppliedName) {
    mode = "group-applied";
  } else if (field.value[item.id].roles?.length) {
    mode = "role-applied";
  } else if (field.value[item.id].has_partial_permissions) {
    mode = "partial-permissions";
  } else if (!field.value[item.id].is_mutable) {
    mode = "non-applicable";
  } else {
    mode = "checkbox";
  }

  let content;
  switch (mode) {
    case "checkbox":
      content = <EditableCheckbox field={field} item={item} />;
      break;

    case "role-applied":
      content = <RoleAppliedCheckbox label={item.label} />;
      break;

    case "group-applied":
      content = (
        <GroupAppliedCheckbox
          label={item.label}
          groupAppliedName={groupAppliedName}
        />
      );
      break;

    case "partial-permissions":
      content = <PartialAppliedCheckbox label={item.label} />;
      break;

    case "non-applicable":
      content = <NonApplicableCheckbox label={item.label} />;
      break;

    default:
      break;
  }

  return (
    <FormItem
      key={item.id}
      className="flex flex-row items-start space-x-3 space-y-0"
    >
      {content}
    </FormItem>
  );
}

export default PermissionsCheckbox;
