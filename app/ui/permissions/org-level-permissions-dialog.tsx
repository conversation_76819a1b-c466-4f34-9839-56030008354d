"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import PermissionsDialog from "#/ui/permissions/permissions-dialog";
import PermissionsSetFormField from "#/ui/permissions/permissions-form-field";
import {
  getOrgPermissionCheckboxItems,
  getProjectCheckboxItems,
} from "./items";
import {
  type OrgPermissionsCheckboxes,
  orgPermissionsCheckboxesSchema,
} from "#/app/app/[org]/settings/team/org-permissions-types";
import { type getOrgPermissions } from "#/app/app/[org]/settings/team/org-permissions-actions";
import { useOrg } from "#/utils/user";
import {
  getChangedValues,
  updatePermissionsForUserOrGroup,
} from "./form-helpers";
import { getInheritedGroups, getInheritedParentObjects } from "./utils";
import { type UserObjectType } from "./permissions-types";
import { useQueryFunc } from "#/utils/react-query";
import { useSessionToken } from "#/utils/auth/session-token";

function Dialog({
  userObjectType,
  userOrGroupId,
  userOrGroupLabel,
  permissionsData,
  onSubmit,
}: {
  userObjectType: UserObjectType;
  userOrGroupId: string;
  userOrGroupLabel: string;
  permissionsData: OrgPermissionsCheckboxes;
  onSubmit: (values: OrgPermissionsCheckboxes) => void;
}) {
  const org = useOrg();
  const orgId = org.id;
  const orgName = org.name;

  const { getOrRefreshToken } = useSessionToken();

  const defaultValues = {
    project: permissionsData.project,
    organization: permissionsData.organization,
  };

  const form = useForm<OrgPermissionsCheckboxes>({
    resolver: zodResolver(orgPermissionsCheckboxesSchema),
    defaultValues,
  });

  async function onFormSubmit(values: OrgPermissionsCheckboxes) {
    const sessionToken = await getOrRefreshToken();

    const changedValues = getChangedValues({
      initialValues: defaultValues,
      submittedValues: values,
    });

    await updatePermissionsForUserOrGroup({
      values: changedValues,
      apiUrl: org.api_url,
      sessionToken,
      objectType: "organization",
      objectId: orgId ?? "",
      userGroupId: userOrGroupId,
      userObjectType,
    });

    onSubmit(values);
  }

  return (
    <PermissionsDialog
      title={`${userOrGroupLabel} permissions for ${orgName}`}
      form={form}
      inheritedGroups={getInheritedGroups(permissionsData)}
      inheritedParentObjects={getInheritedParentObjects(permissionsData)}
      onSubmit={onFormSubmit}
      className="sm:max-w-xl"
    >
      <div className="flex gap-12">
        <PermissionsSetFormField
          form={form}
          items={getOrgPermissionCheckboxItems()}
          name="organization"
          label="Organization"
        />
        <PermissionsSetFormField
          form={form}
          items={getProjectCheckboxItems()}
          name="project"
          label="Projects"
        />
      </div>
    </PermissionsDialog>
  );
}

function OrgLevelPermissionsDialog({
  orgId,
  userOrGroupLabel,
  userOrGroupId,
  userObjectType,
  onSubmit,
}: {
  orgId: string;
  userOrGroupLabel: string;
  userOrGroupId: string;
  userObjectType: UserObjectType;
  onSubmit: (values: OrgPermissionsCheckboxes) => void;
}) {
  const {
    data: permissionsData,
    isLoading,
    invalidate,
  } = useQueryFunc<typeof getOrgPermissions>({
    fName: "getOrgPermissions",
    args: { orgId, userObjectType, userGroupId: userOrGroupId },
  });
  if (isLoading || !permissionsData) {
    return null;
  }

  return (
    <Dialog
      userOrGroupLabel={userOrGroupLabel}
      permissionsData={permissionsData}
      userOrGroupId={userOrGroupId}
      userObjectType={userObjectType}
      onSubmit={(values) => {
        onSubmit(values);
        invalidate();
      }}
    />
  );
}

export default OrgLevelPermissionsDialog;
