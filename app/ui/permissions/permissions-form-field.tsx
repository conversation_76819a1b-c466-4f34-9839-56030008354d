import React from "react";
import { Form<PERSON>ield, FormItem, FormLabel, FormMessage } from "#/ui/form";
import PermissionsCheckbox from "#/ui/permissions/permissions-checkbox";
import {
  type FieldValues,
  type Path,
  type UseFormReturn,
} from "react-hook-form";
import { type PermissionsCheckboxItem } from "./items";
import { cn } from "#/utils/classnames";

type SelectAllButtonState = "hidden" | "select-all" | "deselect-all";

function PermissionsSetFormField<T extends FieldValues>({
  form,
  items,
  name,
  label,
}: {
  form: UseFormReturn<T>;
  items: PermissionsCheckboxItem[];
  name: Path<T>;
  label: string;
}) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const isAllImmutable = Object.keys(field.value).every(
          (key) => !field.value[key]?.is_mutable,
        );

        const isAllChecked = Object.keys(field.value).every(
          (key) => field.value[key]?.is_checked,
        );

        let selectAllButtonState: SelectAllButtonState;
        if (isAllImmutable) {
          selectAllButtonState = "hidden";
        } else if (isAllChecked) {
          selectAllButtonState = "deselect-all";
        } else {
          selectAllButtonState = "select-all";
        }

        const updateAllCheckboxValues = (setChecked: boolean) => {
          const values = field.value;
          if (!values) return;

          const newValues = { ...values };
          Object.keys(values).forEach((key) => {
            if (values[key]?.is_mutable) {
              newValues[key] = {
                ...newValues[key],
                is_checked: setChecked,
              };
            }
          });

          form.setValue(name, newValues, {
            shouldValidate: true,
            shouldDirty: true,
          });
        };

        const handleSelectAll = () => updateAllCheckboxValues(true);
        const handleDeselectAll = () => updateAllCheckboxValues(false);

        return (
          <FormItem>
            <div className="mb-2 flex items-baseline">
              <FormLabel className="font-semibold">{label}</FormLabel>
              <div className="flex items-center pl-2">
                <button
                  type="button"
                  className={cn("text-xs hover:underline", {
                    invisible: selectAllButtonState === "hidden",
                  })}
                  onClick={
                    selectAllButtonState === "deselect-all"
                      ? handleDeselectAll
                      : handleSelectAll
                  }
                >
                  {selectAllButtonState === "deselect-all" ? "None" : "All"}
                </button>
              </div>
            </div>
            {items.map((item) => (
              <FormField
                key={item.id}
                control={form.control}
                name={name}
                render={({ field }) => {
                  return (
                    <FormItem
                      key={item.id}
                      className="flex flex-row items-start space-x-3 space-y-0"
                    >
                      <PermissionsCheckbox field={field} item={item} />
                    </FormItem>
                  );
                }}
              />
            ))}
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

export default PermissionsSetFormField;
