import React from "react";

import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "#/ui/dialog";

import { Form } from "#/ui/form";

import { type FieldValues, type UseFormReturn } from "react-hook-form";
import { Spinner } from "#/ui/icons/spinner";
import { getInheritanceLabel } from "#/app/app/[org]/p/[project]/configuration/permissions/utils";
import { toast } from "sonner";

function PermissionsDialog<T extends FieldValues>({
  title,
  children,
  form,
  onSubmit,
  inheritedGroups,
  inheritedParentObjects,
  className,
}: {
  title: string;
  children: React.ReactNode;
  form: UseFormReturn<T>;
  onSubmit: (values: T) => Promise<void>;
  inheritedGroups?: string[];
  inheritedParentObjects?: string[];
  className?: string;
}) {
  const inheritedGroupsLabel = getInheritanceLabel(inheritedGroups);
  const inheritedParentObjectsLabel = getInheritanceLabel(
    inheritedParentObjects,
  );
  const showInherited = inheritedGroupsLabel || inheritedParentObjectsLabel;

  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const onFormSubmit = async () => {
    setIsSubmitting(true);

    try {
      await onSubmit(form.getValues());

      toast.success("Permissions saved");
    } catch (error) {
      toast.error(`Failed to save permissions`, {
        description: `${error}`,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DialogContent
      className={className || "sm:max-w-[615px]"}
      // Prevent autofocs on the first input, it causes tooltips to appear
      onOpenAutoFocus={(e) => e.preventDefault()}
    >
      <DialogHeader>
        <DialogTitle className="leading-tight">{title}</DialogTitle>
      </DialogHeader>
      {showInherited && (
        <div className="rounded-md border p-3 text-xs bg-primary-50 border-primary-100">
          Some permissions cannot be changed because they are inherited from
          <ul className="pl-8">
            {inheritedGroupsLabel && (
              <li className="list-disc">
                Permission groups:{" "}
                <span className="font-medium">{inheritedGroupsLabel}</span>
              </li>
            )}
            {inheritedParentObjectsLabel && (
              <li className="list-disc">
                Parent resources:{" "}
                <span className="font-medium">
                  {inheritedParentObjectsLabel}
                </span>
              </li>
            )}
          </ul>
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)} className="space-y-8">
          {children}
          <DialogFooter className="gap-2 sm:gap-0">
            <DialogClose asChild>
              <Button type="button" size="sm" variant="ghost">
                Cancel
              </Button>
            </DialogClose>
            <Button
              size="sm"
              type="submit"
              disabled={isSubmitting}
              variant="primary"
            >
              {isSubmitting ? <Spinner className="mr-1" /> : null}
              Save
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </DialogContent>
  );
}

export default PermissionsDialog;
