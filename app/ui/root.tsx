"use client";

import { useUser } from "#/utils/user";
import { useEffect } from "react";
import { IdentifyUser } from "./use-analytics";
import { signInPath } from "#/utils/auth/redirects";
import { useRouter } from "next/navigation";
import { useRedirectPath } from "#/utils/use-redirect-path";

export default function SessionRoot({
  children,
  loginRequired,
}: {
  children: React.ReactNode;
  loginRequired: boolean;
}) {
  return (
    <>
      <LoginRequired loginRequired={loginRequired}>
        <SessionizedRoot>{children}</SessionizedRoot>
      </LoginRequired>
    </>
  );
}

export function LoginRequired({
  children,
  loginRequired,
}: {
  children: React.ReactNode;
  loginRequired: boolean;
}) {
  const { status, session } = useUser();
  const router = useRouter();
  const redirectPath = useRedirectPath();
  useEffect(() => {
    if (!loginRequired) {
      return;
    } else if (loginRequired && status === "unauthenticated") {
      router.push(signInPath({ redirectPath }));
    }
  }, [loginRequired, session, router, status, redirectPath]);

  return status === "authenticated" ||
    (status !== "loading" && !loginRequired) ? (
    <>{children}</>
  ) : null;
}

export function SessionizedRoot({ children }: { children: React.ReactNode }) {
  const { status } = useUser();

  if (status === "loading") {
    return null;
  } else {
    return (
      <>
        <IdentifyUser>{children}</IdentifyUser>
      </>
    );
  }
}
