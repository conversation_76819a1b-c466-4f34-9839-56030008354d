import { type ReactNode, forwardRef, useEffect, useRef } from "react";
import { cn } from "#/utils/classnames";

export const Checkbox = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: ReactNode;
    inputClassName?: string;
    textClassName?: string;
  }
>(function Checkbox(props, ref) {
  const { className, label, inputClassName, textClassName, ...otherProps } =
    props;

  // Avoid a warning from React when providing a 'checked' prop, but
  // onChange is undefined and neither readOnly nor defaultChecked are provided
  let readOnly = otherProps.readOnly;
  if (
    otherProps.checked !== undefined &&
    otherProps.onChange === undefined &&
    otherProps.defaultChecked === undefined &&
    readOnly === undefined
  ) {
    readOnly = true;
  }

  return (
    <label className={cn("text-sm font-medium flex items-center", className)}>
      <input
        className={cn(
          "w-4 h-4 text-accent-500 bg-primary-100 border-primary-300 hover:border-primary-400 rounded transition-colors",
          "focus:ring-accent-400 cursor-pointer focus:ring-2",
          otherProps.disabled && "cursor-not-allowed opacity-50",
          inputClassName,
        )}
        type="checkbox"
        {...otherProps}
        readOnly={readOnly}
        ref={ref}
      />
      {label ? (
        <span className={cn("pl-2", textClassName)}>{label}</span>
      ) : null}
    </label>
  );
});

export const IndeterminateCheckbox = forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: string;
    state?: "checked" | "unchecked" | "indeterminate";
  }
>(function IndeterminateCheckbox(props, ref) {
  const { label, state, checked, ...otherProps } = props;
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!inputRef?.current) return;
    switch (state) {
      case "checked":
        inputRef.current.indeterminate = false;
        inputRef.current.checked = true;
        break;
      case "unchecked":
        inputRef.current.indeterminate = false;
        inputRef.current.checked = false;
        break;
      case "indeterminate":
        inputRef.current.indeterminate = true;
        break;
      case undefined:
        inputRef.current.indeterminate = false;
        inputRef.current.checked = checked || false;
        break;
      default:
        break;
    }
  }, [state, checked, inputRef]);

  return <Checkbox label={label} ref={inputRef} {...otherProps} />;
});
