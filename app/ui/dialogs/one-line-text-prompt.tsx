import { But<PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { useRef } from "react";

export function OneLineTextPrompt({
  onSubmit,
  title,
  description,
  submitLabel,
  fieldName,
  defaultValue,
  open,
  onOpenChange,
  placeholder,
  children,
}: {
  onSubmit: (newValue: string) => void;
  submitLabel?: string;
  title: string;
  description?: string;
  fieldName: React.ReactNode;
  defaultValue?: string;
  open: boolean;
  onOpenChange: (open: boolean, reason?: "submit" | "close") => void;
  placeholder?: string;
  children?: React.ReactNode;
}) {
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <Dialog open={open} onOpenChange={(open) => onOpenChange(open, "close")}>
      <DialogContent className="sm:max-w-[600px]">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const inputValue = inputRef.current?.value;
            if (inputValue) {
              onSubmit(inputValue);
            }
            onOpenChange(false, "submit");
          }}
        >
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {description && (
              <DialogDescription>{description}</DialogDescription>
            )}
            {children}
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="prompt_name" className="text-right">
                {fieldName}
              </label>
              <PlainInput
                id="prompt_name"
                defaultValue={defaultValue}
                className="col-span-3"
                ref={inputRef}
                autoComplete="off"
                placeholder={placeholder}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">{submitLabel || "Save"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
