import { isEmpty } from "#/utils/object";
import { type DuckDBJSONType, getDuckDBTypeChild } from "#/utils/schema";
import {
  type AccessorFnColumnDef,
  type ColumnDefTemplate,
  type GroupColumnDef,
} from "@tanstack/react-table";
import { DataType, type Field, type TypeMap } from "apache-arrow";
import { isNumericType } from "./trace/diff-score-object";
import type { FormatterProps } from "./arrow-table";
import { type PathTree } from "#/utils/display-paths";
import {
  DiffLeftField,
  DiffRightField,
  isEmptyDiffObject,
} from "#/utils/diffs/diff-objects";
import { DefaultFormatter } from "./table/formatters/default-formatter";
import { type CustomColumn } from "@braintrust/core/typespecs";
import { type LucideIcon } from "lucide-react";
import {
  type FieldMap,
  type SizeConstraintsMap,
} from "./table/use-column-defs";
import { getObjValueByPath } from "@braintrust/core";

export type FormatComponent<TsData, TsValue> = (
  props: FormatterProps<TsData, TsValue>,
) => React.ReactNode;

export type FormatterMeta<TsData = {}, TsValue = ""> = {
  headerIcon?: LucideIcon;
  headerLabel?: string;
  cell?: FormatComponent<TsData, TsValue>;
  parseValue?: (
    value: unknown,
    metaType: DataType,
    typeHint?: DuckDBJSONType,
  ) => string;
  mergeInto?: string;
  ignoreMultilineRendering?: boolean;
  pinnedColumnIndex?: number;
  // will greedily span across null columns
  colSpan?: boolean;
  colSize?: {
    minSize: number;
    size: number;
  };
  groupColumnName?: string;
};

export type FormatterMap<TsData = {}, TsValue = ""> = {
  [key: string]: FormatterMeta<TsData, TsValue>;
};

export type MergedValue<TsData, TsValue> = {
  value: unknown;
  formatter: FormatComponent<TsData, TsValue>;
};

function isScalarOrChildrenOf(
  type: DataType,
  fn: (t: DataType) => boolean,
): boolean {
  if (
    (DataType.isList(type) || DataType.isStruct(type)) &&
    type.children.length > 0
  ) {
    return fn(type.children[0].type);
  } else {
    return fn(type);
  }
}

function findFormatterMeta<TsData, TsValue>(
  formatters: FormatterMap<TsData, TsValue>,
  path: string[],
): FormatterMeta<TsData, TsValue> | undefined {
  return formatters[path.join(".")];
}

function findFormatter<TsData, TsValue>(
  formatters: FormatterMap<TsData, TsValue>,
  path: string[],
): FormatterMeta<TsData, TsValue> {
  // Try searching for the whole path first.
  let currPath = [...path];
  while (currPath.length > 0) {
    const formatterElement = findFormatterMeta(formatters, currPath);
    if (formatterElement) return formatterElement;
    currPath = currPath.slice(0, -1);
  }

  if (path.length > 1) {
    // Try just searching for the last element.
    return findFormatter(formatters, path.slice(-1));
  }

  return {};
}

export const COLUMN_ICON_CLASS =
  "mr-1 inline-block -mt-[1px] size-3 text-primary-400";

export function fieldToColumn<TFieldData extends TypeMap, TSData, TSValue>({
  field,
  path,
  id_prefix,
  displayPaths,
  formatters,
  typeHints,
  fieldMap,
  sizeConstraintsMap = {},
  moreText,
  hasSummary,
  customColumns,
  comparisonFields,
  refetchTooltipContentData,
}: {
  field: Field<TFieldData[keyof TFieldData]>;
  path: string[];
  id_prefix: string;
  displayPaths: PathTree | undefined;
  typeHints?: DuckDBJSONType;
  formatters: FormatterMap<TSData, TSValue>;
  fieldMap: FieldMap;
  sizeConstraintsMap?: SizeConstraintsMap;
  moreText: boolean;
  hasSummary?: boolean;
  customColumns?: CustomColumn[];
  comparisonFields?: (Field<TFieldData[keyof TFieldData]>[] | undefined)[];
  refetchTooltipContentData?: (
    fieldName: string,
    rowId: string,
    previewLength?: number,
  ) => Promise<unknown>;
}): (GroupColumnDef<TSData, TSValue> | AccessorFnColumnDef<TSData, TSValue>)[] {
  const fullPath = [...path, field.name];
  const id = fullPath.join(".");

  fieldMap[id] = {
    path: fullPath,
    field,
    indexColumnId: id_prefix,
  };

  const typeHint = typeHints && getDuckDBTypeChild(typeHints, field.name);

  // This is a bit of a hack, that gets around the fact that diff fields
  // are presented as lists. We should probably generalize the "formatter"
  // struct (perhaps with a higher order piece of metadata than the formatter
  // function itself), and check that to dig into the list and pick the
  // appropriate formatting function.
  const isNumeric = isScalarOrChildrenOf(field.type, (type) =>
    isNumericType(type),
  );
  const isTimestamp = isScalarOrChildrenOf(field.type, (type) =>
    DataType.isTimestamp(type),
  );

  const {
    structField: comparisonStruct,
    comparisonFields: nextComparisonFields,
  } = findComparisonStruct(field.name, comparisonFields);
  const structField = comparisonStruct ?? field;
  if (
    DataType.isStruct(structField.type) &&
    !isDiffStruct(structField.type) &&
    (displayPaths === undefined ||
      (displayPaths.type === "node" &&
        displayPaths.children[field.name]?.type == "node"))
  ) {
    return structField.type.children.flatMap((f, i) =>
      fieldToColumn({
        field: f,
        path: fullPath,
        id_prefix: `${id_prefix}_${i}`,
        displayPaths: displayPaths?.children[field.name],
        typeHints: typeHint,
        formatters,
        fieldMap,
        moreText,
        customColumns,
        comparisonFields: nextComparisonFields,
        refetchTooltipContentData,
      }),
    );
  } else if (findFormatterMeta(formatters, fullPath)?.mergeInto) {
    return [];
  } else {
    const {
      headerIcon: Icon,
      headerLabel,
      cell,
      ignoreMultilineRendering,
      pinnedColumnIndex,
      parseValue,
      colSpan,
      colSize,
      groupColumnName,
    } = findFormatter(formatters, fullPath);

    let minSize, size;
    if (colSize) {
      minSize = colSize.minSize;
      size = colSize.size;
    } else if (sizeConstraintsMap[field.name]) {
      minSize = sizeConstraintsMap[field.name].minSize;
      size = sizeConstraintsMap[field.name].size;
    } else if (
      (DataType.isFloat(field.type) || DataType.isDecimal(field.type)) &&
      hasSummary
    ) {
      // Unless a size is specified, make sure that the score
      // columns have a default size
      minSize = 200;
      size = 200;
    } else {
      minSize = 70;
      size = 120;
    }

    // NOTE: This assumes that the merged paths are of length 1. But at this point, all of the
    // stuff in the table is single-level anyway.
    const mergedInto = Object.entries(formatters).filter(
      ([_, f]) => f.mergeInto === fullPath[0],
    );

    const HeaderComponent = () => (
      <span>
        {Icon ? <Icon className={COLUMN_ICON_CLASS} /> : null}
        {headerLabel || field.name}
      </span>
    );

    // hide score column in list view when there's no scores
    if (
      fullPath.length === 1 &&
      fullPath[0] === "scores" &&
      displayPaths?.type === "node" &&
      displayPaths?.children?.scores?.type === "leaf"
    ) {
      return [];
    }
    const customColumn = customColumns?.find((c) => c.name === id);
    const shouldRefetch =
      refetchTooltipContentData &&
      (!!customColumn ||
        ["input", "output", "expected", "metadata"].includes(field.name));
    return [
      {
        id,
        header: HeaderComponent,
        meta: {
          name: field.name,
          header: HeaderComponent,
          type: field.type,
          typeHint,
          isNumeric,
          isTimestamp,
          path: fullPath,
          moreText,
          ignoreMultilineRendering,
          parseValue,
          pinnedColumnIndex,
          colSpan,
          customColumnId: customColumn?.id,
          allowHoverableTooltip: field.name === "tags",
          groupColumnName,
          fetchFullContent: shouldRefetch
            ? async (rowId, value, previewLength) => {
                if (
                  typeof value === "string" &&
                  (value.endsWith('..."') || value.endsWith("..."))
                ) {
                  return refetchTooltipContentData(
                    field.name,
                    rowId,
                    previewLength,
                  );
                }
                return Promise.resolve(value);
              }
            : undefined,
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
        accessorFn: (row: any): TSValue => {
          const originalValue = getObjValueByPath(row, fullPath);
          const mergedValues: Record<
            string,
            { value: unknown; formatter: FormatComponent<TSData, TSValue> }
          > = {};
          if (mergedInto.length > 0) {
            for (const [key, f] of mergedInto) {
              const value = getObjValueByPath(row, [key]);
              if (isEmpty(value) || isEmptyDiffObject(value) || !f.cell) {
                continue;
              }

              mergedValues[key] = {
                value,
                formatter: f.cell ?? DefaultFormatter,
              };
            }

            if (Object.keys(mergedValues).length > 0) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              return {
                value: originalValue,
                mergedValues,
              } as TSValue;
            }
          }

          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          return originalValue as TSValue;
        },
        enableColumnFilter: true,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        cell: (cell ?? DefaultFormatter) as ColumnDefTemplate<{}>,
        size: size ? size : isNumeric ? 70 : 150,
        minSize,
      },
    ];
  }
}

export function isDiffStruct(type: DataType) {
  if (!DataType.isStruct(type)) {
    return false;
  }

  if (type.children.length !== 2) {
    return false;
  }

  const [left, right] = type.children;
  return left.name === DiffLeftField && right.name === DiffRightField;
}

function findComparisonStruct<T extends TypeMap>(
  fieldName: string,
  fields?: (Field<T[keyof T]>[] | undefined)[],
) {
  let found = null;
  for (const comparisonFields of fields ?? []) {
    if (found) {
      break;
    }
    if (!comparisonFields) {
      continue;
    }
    for (const field of comparisonFields) {
      if (DataType.isStruct(field.type) && field.name === fieldName) {
        found = field;
        break;
      }
    }
  }

  if (!found) {
    return {
      structField: null,
      comparisonFields: undefined,
    };
  }

  return {
    structField: found,
    comparisonFields: fields?.map((f) => {
      const subField = f?.find((f) => f.name === fieldName);
      if (!subField || !DataType.isStruct(subField.type)) {
        return undefined;
      }

      return subField.type.children;
    }),
  };
}
