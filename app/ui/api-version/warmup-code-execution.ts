import { useEntityStorage } from "#/lib/clientDataStorage";
import { useSessionToken } from "#/utils/auth/session-token";
import { apiPostCors } from "#/utils/btapi/fetch";
import { useOrg } from "#/utils/user";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { _urljoin } from "@braintrust/core";
import { useEffect } from "react";

export function useWarmupCodeExecution() {
  const org = useOrg();
  const [codeExecutionWarmed, setCodeExecutionWarmed] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.id || "",
    key: "codeExecutionWarmed",
  });

  const { getOrRefreshToken } = useSessionToken();

  useEffect(() => {
    if (codeExecutionWarmed || org.api_url) {
      return;
    }
    (async () => {
      try {
        const resp = await apiPostCors({
          url: _urljoin(
            normalizeProxyUrlBase(org.proxy_url),
            "function",
            "warmup-code-execution",
          ),
          sessionToken: await getOrRefreshToken(),
          payload: {},
          alreadySerialized: false,
          headers: {
            "x-bt-org-name": org.name,
          },
        });
        if (resp.ok) {
          setCodeExecutionWarmed(true);
        } else {
          console.warn(
            "Failed to warm up code execution. You may need to upgrade your stack to enable custom code.",
            await resp.text(),
          );
        }
      } catch (e) {
        console.warn(
          "Failed to warm up code execution. You may need to upgrade your stack to enable custom code.",
          e,
        );
      }
    })();
  }, [codeExecutionWarmed, org, getOrRefreshToken, setCodeExecutionWarmed]);
}
