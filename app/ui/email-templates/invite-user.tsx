import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Img,
  Link,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface InviteUserProps {
  email: string;
  invitedByEmail?: string | null;
  organizationName?: string;
  baseUrl?: string;
}

const DEFAULT_BASE_URL = process.env.NEXT_PUBLIC_SITE_URL ?? "";

const InviteUser = ({
  email,
  invitedByEmail,
  organizationName,
  baseUrl = DEFAULT_BASE_URL,
}: InviteUserProps) => {
  const params = new URLSearchParams({
    email_address: email,
  });

  if (organizationName) {
    params.set("redirect_url", `${baseUrl}/app/${organizationName}`);
  }

  const signUpUrl = `${baseUrl}/signup?${params.toString()}`;

  return (
    <Html>
      <Head />
      <Preview>{`Join ${organizationName} on Braintrust`}</Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans bg-zinc-50">
          <Container className="mx-auto mt-[40px] max-w-[520px] rounded-md border border-solid px-[32px] pt-[32px] bg-white border-zinc-200/80">
            <Section>
              <Img
                src={`${baseUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Heading className="mt-[32px] text-xl font-normal text-black">
              You&apos;ve been invited to join{" "}
              <span className="font-bold">{organizationName}</span> on
              Braintrust
            </Heading>
            <Text className="text-base text-black">
              <Link
                href={`mailto:${invitedByEmail}`}
                className="no-underline text-blue-600"
              >
                {invitedByEmail}
              </Link>{" "}
              has invited you to use Braintrust to experiment, refine, ship and
              monitor LLM products that work.
            </Text>
            <Section className="my-[24px]">
              <Button
                className="rounded-md px-4 py-2.5 text-center text-[14px] font-medium no-underline bg-[#3b82f6] text-white"
                href={signUpUrl}
              >
                Join the team
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-zinc-500">
              Or, copy and paste this URL into your browser:{" "}
              <Link href={signUpUrl} className="no-underline text-blue-600">
                {signUpUrl}
              </Link>
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: InviteUserProps = {
  email: "<EMAIL>",
  invitedByEmail: "<EMAIL>",
  organizationName: "Example Org",
  baseUrl: "https://braintrust.dev",
};

InviteUser.PreviewProps = previewProps;

export default InviteUser;
