import { cn } from "#/utils/classnames";
import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "./button";
import { useState } from "react";
import { MarkdownViewer } from "./markdown";
import TextArea from "./text-area";

export const TextareaWithMdPreview = ({
  value,
  setValue,
  placeholder = "Description (optional)",
  className,
}: {
  value: string;
  setValue: (v: string) => void;
  placeholder?: string;
  className?: string;
}) => {
  const [tab, setTab] = useState<"write" | "preview">("write");
  return (
    <div
      className={cn(
        "rounded-md border border-primary-200 bg-primary-50 dark:bg-primary-100 focus-within:ring-ring focus-within:ring-2 ring-offset-background focus-within:outline-none flex flex-col",
        className,
      )}
    >
      <div className="flex flex-none items-baseline gap-4 border-b px-2 pt-2">
        <Button
          size="xs"
          transparent
          className={cn(
            "border-0 px-0 pb-2 rounded-none border-b border-transparent text-primary-600 hover:bg-transparent -mb-[1px]",
            {
              "text-primary-800 border-primary-500": tab === "write",
            },
          )}
          onClick={(e) => {
            e.preventDefault();
            setTab("write");
          }}
        >
          Write
        </Button>
        <Button
          size="xs"
          className={cn(
            "border-0 px-0 pb-2 rounded-none border-b border-transparent text-primary-600 hover:bg-transparent -mb-[1px]",
            {
              "text-primary-800 border-primary-500": tab === "preview",
              "!bg-transparent": !value,
            },
          )}
          disabled={!value}
          onClick={(e) => {
            e.preventDefault();
            setTab("preview");
          }}
        >
          Preview
        </Button>
        <div className="flex flex-1 items-center justify-end gap-1 text-xs text-primary-500">
          <Highlighter className="size-3" />
          Markdown supported
        </div>
      </div>
      {tab === "preview" ? (
        <div className="max-h-[524px] min-h-16 overflow-auto px-3 py-2">
          <MarkdownViewer className="p-0" value={value} />
        </div>
      ) : (
        <TextArea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          className={cn(
            "w-full border-none px-3 py-2 bg-transparent min-h-16 focus:ring-0 text-sm",
          )}
          maxRows={10}
          placeholder={placeholder}
        />
      )}
    </div>
  );
};
