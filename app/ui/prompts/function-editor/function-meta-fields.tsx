import { type TransactionId } from "#/utils/duckdb";
import { Input, inputClassName } from "#/ui/input";
import { cn } from "#/utils/classnames";
import { DataTextEditor, type TextEditorValue } from "#/ui/data-text-editor";

import { CollapsibleSection } from "#/ui/collapsible-section";
import { slugify } from "#/utils/slug";
import {
  isAllDataRenderOption,
  type RenderOption,
  renderOptions,
} from "#/utils/parse";
import { Skeleton } from "#/ui/skeleton";

export const FunctionInputSkeleton = ({ label }: { label: string }) => (
  <div className="flex-1">
    <div className="mb-2 text-xs font-medium">{label}</div>
    <Skeleton className="mb-2 h-8 max-w-sm" />
  </div>
);

export const FunctionNameField = ({
  name,
  isUpdate,
  onChange,
  className,
  isReadOnly,
}: {
  name: string;
  isUpdate: boolean;
  onChange: (fields: { name: string; slug?: string }) => void;
  className?: string;
  isReadOnly?: boolean;
}) => (
  <label className={cn("block", className)}>
    <div className="mb-2 text-xs font-medium">Name</div>
    <Input
      placeholder="Enter name"
      autoComplete="off"
      className="mb-2 h-8 max-w-sm px-2 disabled:cursor-default disabled:opacity-100"
      autoFocus
      disabled={isReadOnly}
      value={name}
      onChange={(e) => {
        const newName = e.target.value;
        onChange({
          name: newName,
          slug: isUpdate ? undefined : slugify(newName),
        });
      }}
    />
  </label>
);

export const FunctionSlugField = ({
  slug,
  onChange,
  className,
  isReadOnly,
}: {
  slug: string;
  onChange: (slug: string) => void;
  className?: string;
  isReadOnly?: boolean;
}) => (
  <label className={cn("block", className)}>
    <div className="mb-2 text-xs font-medium">Slug</div>
    <Input
      placeholder="Enter slug"
      autoComplete="off"
      className="mb-2 h-8 max-w-sm px-2 font-mono text-xs placeholder:font-inter placeholder:text-sm disabled:cursor-default disabled:opacity-100"
      value={slug}
      onChange={(e) => {
        onChange(e.target.value);
      }}
      disabled={isReadOnly}
    />
  </label>
);

export const FunctionDescriptionField = ({
  description,
  onChange,
  isReadOnly,
}: {
  description: string;
  onChange: (description: string) => void;
  isReadOnly?: boolean;
}) => {
  return (
    <div>
      <CollapsibleSection title="Description" defaultCollapsed>
        <textarea
          className={cn(
            inputClassName,
            "mb-3 min-h-[3lh] rounded-md border px-2 py-1.5 text-sm",
          )}
          placeholder="Enter prompt description"
          value={description}
          onChange={(e) => onChange(e.target.value)}
          disabled={isReadOnly}
        />
      </CollapsibleSection>
    </div>
  );
};

export const FunctionMetadataField = ({
  metadata,
  onChange,
  functionId,
  isReadOnly,
}: {
  metadata?: Record<string, unknown> | null;
  onChange: (value: TextEditorValue) => Promise<TransactionId | null>;
  functionId: string;
  isReadOnly?: boolean;
}) => {
  return (
    <div>
      <CollapsibleSection
        title="Metadata"
        localStorageKey={`${functionId}-savedPromptMetadata`}
        defaultCollapsed
      >
        <DataTextEditor
          value={metadata}
          onChange={onChange}
          formatOnBlur
          readOnly={isReadOnly}
          allowedRenderOptions={Object.entries(renderOptions)
            .filter(
              ([renderType, option]) =>
                isAllDataRenderOption(option) &&
                renderType !== "schema-builder",
            )
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            .map(([renderType]) => renderType as RenderOption)}
        />
      </CollapsibleSection>
    </div>
  );
};
