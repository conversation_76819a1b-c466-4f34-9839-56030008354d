import { useEffect, useRef, useState } from "react";
import { type UIFunction } from "#/ui/prompts/schema";
import { useAvailableModels } from "#/ui/prompts/models";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { type TextEditorHandle } from "#/ui/text-editor";
import { Skeleton } from "#/ui/skeleton";

import { FunctionInputSkeleton } from "./function-meta-fields";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { type Mode, type FunctionEditorProps, type MetaFields } from "./types";
import { SyncedPromptsProvider } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { FunctionDialogHeader } from "./function-dialog-header";
import { FunctionDialogFooter } from "./function-dialog-footer";
import { useFunctionEditorPrompt } from "./use-function-editor-prompt";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeysContext } from "react-hotkeys-hook";
import { Dialog } from "@radix-ui/react-dialog";
import { DialogContent, DialogDescription } from "#/ui/dialog";
import { ErrorBoundary } from "#/utils/error-boundary";
import { ErrorBanner } from "#/ui/error-banner";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { FunctionDialogBody } from "./function-dialog-body";
import { useOutputPrompt } from "./use-output-prompt";
import { getFunctionEditorTab } from "./use-function-editor-tabs";
import {
  DEFAULT_PROMPT_DATA_EDITOR_VALUE,
  DEFAULT_SCORER_DATA_EDITOR_VALUE,
} from "./function-run-section";

export function FunctionDialog({
  status,
  type,
  initialFunction,
  title,
  mode,
  objectType,
  opened,
  setOpened,
  ...rest
}: Omit<FunctionEditorProps, "isDirtyRef"> & {
  opened: boolean;
  setOpened?: (opened: boolean) => void;
}) {
  const isDirtyRef = useRef(false);
  const [confirmDiscard, setConfirmDiscard] = useState(false);

  // Although we don't have any specific hotkeys enabled for the prompt modal, we activate
  // this scope so that we can disable the global (Escape) key from closing the trace.
  const { enableScope, disableScope } = useHotkeysContext();
  useEffect(() => {
    if (opened) {
      enableScope(HotkeyScope.PromptModal);
    }
  }, [enableScope, opened]);

  const canEdit = mode.type === "create" || mode.type === "update";

  // Return a separate loading state rather than having each component in FunctionEditorInner handle its own loading state in place.
  // This means that when FunctionEditor inner is mounted, the initial prompt is loaded, so state can be initialized without
  // any effect syncing.
  const content =
    status === "loading" ? (
      <LoadingState
        type={type}
        initialFunction={initialFunction}
        mode={mode}
        title={title}
      />
    ) : (
      <FunctionDialogInner
        key={rest.identifier}
        type={type}
        objectType={objectType}
        initialFunction={initialFunction}
        mode={mode}
        title={title}
        isDirtyRef={isDirtyRef}
        {...rest}
      />
    );

  return (
    <>
      <Dialog
        modal={false}
        open={opened}
        onOpenChange={(o) => {
          if (!o) {
            if (isDirtyRef.current && canEdit) {
              setConfirmDiscard(true);
              return;
            }
            isDirtyRef.current = false;
            disableScope(HotkeyScope.PromptModal);
          }
          setOpened?.(o);
        }}
      >
        <DialogDescription className="hidden" />
        <DialogContent
          hideCloseButton
          className="flex h-[calc(100vh-100px)] flex-col gap-0 overflow-hidden p-0 sm:max-w-3xl"
          // We autofocus the first input in the editor - this prevents focusing the dialog itself during loading
          onOpenAutoFocus={(e) => e.preventDefault()}
          // Prevent closing the dialog when interacting with the code editor's autocomplete
          onInteractOutside={(e) => {
            if (
              e.target instanceof HTMLElement &&
              (e.target.id.includes("cm") ||
                Array.from(e.target.classList).some((c) => c.includes("cm")))
            ) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
          {opened && (
            <ErrorBoundary
              fallback={
                <div className="mx-2">
                  <ErrorBanner>
                    Something went wrong. Please try again and contact support
                    if the problem persists.
                  </ErrorBanner>
                </div>
              }
            >
              {content}
            </ErrorBoundary>
          )}
        </DialogContent>
      </Dialog>
      {canEdit && (
        <ConfirmationDialog
          open={confirmDiscard}
          onOpenChange={setConfirmDiscard}
          title="Discard changes"
          description="Are you sure you want to discard changes?"
          confirmText="Discard"
          onConfirm={() => {
            setConfirmDiscard(false);
            isDirtyRef.current = false;
            disableScope(HotkeyScope.PromptModal);
            setOpened?.(false);
          }}
        />
      )}
    </>
  );
}

function LoadingState({
  type,
  initialFunction,
  mode,
  title,
}: {
  type: FunctionObjectType;
  initialFunction: UIFunction | null;
  mode: Mode;
  title?: string;
}) {
  return (
    <div className="flex flex-auto flex-col overflow-auto p-4">
      <FunctionDialogHeader
        type={type}
        sourcePrompt={initialFunction}
        mode={mode}
        title={title}
      />
      <div className="flex flex-col gap-4">
        {mode.type !== "view_unsaved" && (
          <div className="flex w-full gap-4">
            <FunctionInputSkeleton label="Name" />
            <FunctionInputSkeleton label="Slug" />
          </div>
        )}
        <div className="flex h-[400px] flex-col">
          <Skeleton className="h-10" />
          <Skeleton className="mt-2 h-20" />
          <Skeleton className="mt-2 h-20" />
        </div>
      </div>
    </div>
  );
}

function FunctionDialogInner({
  identifier,
  orgName,
  projectId,
  projectName,
  objectType,
  mode,
  activityProps,
  initialFunction,
  variableData,
  title,
  type,
  context,
  jsonStructure,
  outputNames,
  copilotContext,
  isDirtyRef,
}: Omit<FunctionEditorProps, "status">) {
  const [error, setError] = useState<string | null>(null);
  const [metaFields, setMetaFields] = useState<MetaFields>({
    name: initialFunction?.name,
    slug: initialFunction?.slug,
    description: initialFunction?.description,
    metadata: initialFunction?.metadata,
  });

  const { allAvailableModels } = useAvailableModels({ orgName });

  const jsEditorRef = useRef<TextEditorHandle<string>>(null);
  const pyEditorRef = useRef<TextEditorHandle<string>>(null);

  const { promptState, coercedFunction } = useFunctionEditorPrompt({
    initialFunction,
    type,
    projectId,
    modeType: mode.type,
  });
  const tab = getFunctionEditorTab(promptState[0].function_data);

  const getOutputPrompt = useOutputPrompt({
    coercedFunction,
    metaFields,
  });

  // TODO: use state and an onLint callback to track lint errors
  /* eslint-disable react-compiler/react-compiler */
  const hasLintErrors =
    (tab === "ts" &&
      jsEditorRef.current &&
      jsEditorRef.current.getLintErrors().length > 0) ||
    (tab === "py" &&
      pyEditorRef.current &&
      pyEditorRef.current.getLintErrors().length > 0) ||
    false;
  /* eslint-enable react-compiler/react-compiler */

  /** Whether the user has interacted with the slug field. We don't auto-set the slug when the name changes after a user has touched the slug field. */
  const isSlugTouchedRef = useRef(false);

  const [dataEditorValue, setDataEditorValue] = useEntityStorage({
    entityType: "functions",
    entityIdentifier: identifier,
    key: "runEditorValue",
    defaultValue:
      variableData ??
      (type === "scorer"
        ? DEFAULT_SCORER_DATA_EDITOR_VALUE
        : DEFAULT_PROMPT_DATA_EDITOR_VALUE),
  });

  return (
    <SyncedPromptsProvider
      allAvailableModels={allAvailableModels}
      externalValue={promptState}
    >
      <div className="flex flex-auto flex-col overflow-auto p-4">
        <FunctionDialogHeader
          type={type}
          sourcePrompt={initialFunction}
          mode={mode}
          title={title}
        />
        <div className="flex flex-col gap-4">
          <FunctionDialogBody
            mode={mode}
            initialFunction={initialFunction}
            getOutputPrompt={getOutputPrompt}
            pyEditorRef={pyEditorRef}
            jsEditorRef={jsEditorRef}
            activityProps={activityProps}
            setError={setError}
            metaFields={metaFields}
            setMetaFields={setMetaFields}
            isSlugTouchedRef={isSlugTouchedRef}
            jsonStructure={jsonStructure}
            outputNames={outputNames}
            copilotContext={copilotContext}
            variableData={variableData}
            type={type}
            context={context}
            orgName={orgName}
            projectId={projectId}
            projectName={projectName}
            objectType={objectType}
            dataEditorValue={dataEditorValue}
            setDataEditorValue={setDataEditorValue}
          />
        </div>
      </div>
      <FunctionDialogFooter
        mode={mode}
        type={type}
        getOutputPrompt={getOutputPrompt}
        isSlugTouchedRef={isSlugTouchedRef}
        error={error}
        setError={setError}
        // eslint-disable-next-line react-compiler/react-compiler
        hasLintErrors={hasLintErrors}
        initialDirtyFunctionComparisonBase={coercedFunction}
        isDirtyRef={isDirtyRef}
        orgName={orgName}
        projectId={projectId}
        projectName={projectName}
        dataEditorValue={dataEditorValue}
        promptName={metaFields.name ?? undefined}
        context={context}
      />
    </SyncedPromptsProvider>
  );
}
