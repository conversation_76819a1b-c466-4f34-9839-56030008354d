import { useOrg } from "#/utils/user";
import { LexoRank } from "lexorank";
import { useAvailableModels } from "./models";
import { convertMessages, translatePromptModelParams } from "./prompt-utils";

export const useDefaultPromptData = ({
  orgName: orgNameProp,
}: { orgName?: string } = {}) => {
  const org = useOrg();
  const { allAvailableModels } = useAvailableModels({
    orgName: orgNameProp ?? org.name,
  });

  const firstAvailableModel = Object.keys(allAvailableModels)[0];
  const params = translatePromptModelParams({
    newModel: firstAvailableModel,
    allAvailableModels,
  });
  const prompt = convertMessages({
    newModel: firstAvailableModel,
    allAvailableModels,
  });

  return {
    prompt,
    options: {
      position: LexoRank.middle().toString(),
      model: firstAvailableModel,
      params,
    },
  };
};
