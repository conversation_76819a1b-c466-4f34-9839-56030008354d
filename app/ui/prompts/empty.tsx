import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import { Plus, Sparkle } from "lucide-react";
import { BlueLink } from "#/ui/link";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { Anthropic, Gemini, Mistral, OpenAI } from "#/ui/icons/providers";
import { cn } from "#/utils/classnames";
import Link from "next/link";

export function NoAISecrets({ orgName }: { orgName: string }) {
  return (
    <>
      No AI provider API secrets have been configured yet
      <BlueLink
        className="block font-medium"
        href={`${getOrgLink({ orgName })}/settings/secrets`}
      >
        Configure AI providers
      </BlueLink>
    </>
  );
}

export function AIProviderLogoStack({
  iconClassName,
  iconSize = 24,
  providerCount = 4,
}: {
  iconClassName?: string;
  iconSize?: number;
  providerCount?: number;
}) {
  const providers = [OpenAI, Anthropic, Gemini, Mistral].slice(
    0,
    providerCount,
  );

  const className = cn(
    "rounded-full border bg-background size-8 flex items-center justify-center -mr-2",
    iconClassName,
  );
  return (
    <div className="flex">
      {providers.map((Provider, i) => (
        <div className={className} key={i}>
          <Provider size={iconSize} />
        </div>
      ))}
      <div className={cn(className, "bg-primary-50 mr-0 text-primary-500")}>
        <Plus size={iconSize * 0.75} />
      </div>
    </div>
  );
}

export function NoAISecretsEmptyState({ orgName }: { orgName: string }) {
  return (
    <TableEmptyState Icon={Sparkle} label={<NoAISecrets orgName={orgName} />}>
      <AIProviderLogoStack />
    </TableEmptyState>
  );
}

export function NoAISecretsLink({
  orgName,
  className,
}: {
  orgName: string;
  className?: string;
}) {
  return (
    <Link
      href={`${getOrgLink({ orgName })}/settings/secrets`}
      className={className}
    >
      <AIProviderLogoStack
        iconSize={14}
        iconClassName="size-5 -mr-1.5"
        providerCount={4}
      />
      <span className="text-xs">Add AI providers</span>
    </Link>
  );
}
