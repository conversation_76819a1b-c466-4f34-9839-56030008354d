import { LoaderCircle } from "lucide-react";
import React from "react";
import { cn } from "#/utils/classnames";

export const Spinner = ({
  className,
  "aria-label": ariaLabel = "Loading",
}: {
  className?: string;
  "aria-label"?: string;
}) => (
  <LoaderCircle
    className={cn(
      "animate-smooth-spin size-4 will-change-transform",
      "transition-opacity duration-400",
      className,
    )}
    aria-label={ariaLabel}
    role="status"
    aria-live="polite"
  />
);
