import openAISvg from "#/app/(landing)/img/OpenAI.svg";
import openAIDarkSvg from "#/app/(landing)/img/OpenAI-dark.svg";
import anthropicSvg from "#/app/(landing)/img/Anthropic.svg";
import anthropicDarkSvg from "#/app/(landing)/img/Anthropic-dark.svg";
import googleSvg from "#/app/(landing)/img/Google.svg";
import googleDarkSvg from "#/app/(landing)/img/Google-dark.svg";
import metaSvg from "#/app/(landing)/img/Meta.svg";
import metaDarkSvg from "#/app/(landing)/img/Meta-dark.svg";
import mistralSvg from "#/app/(landing)/img/Mistral.svg";
import mistralDarkSvg from "#/app/(landing)/img/Mistral-dark.svg";
import perplexitySvg from "#/app/(landing)/img/Perplexity.svg";
import perplexityDarkSvg from "#/app/(landing)/img/Perplexity-dark.svg";
import Image from "next/image";

export const OpenAI = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <g clipPath="url(#a)">
      <path
        fill="currentColor"
        d="M41.1367 20.3683c.4469-1.3467.6014-2.7733.4531-4.1844-.1483-1.4112-.596-2.7745-1.3131-3.9989-1.0634-1.8511-2.6872-3.31658-4.6372-4.18526-1.95-.86867-4.1255-1.0956-6.2128-.64808-1.1856-1.31888-2.6973-2.30266-4.3833-2.85255-1.686-.54989-3.487-.64655-5.2221-.28026-1.7352.36628-3.3435 1.18262-4.6634 2.36704-1.3199 1.18442-2.3049 2.69524-2.8562 4.38081-1.3907.2851-2.70445.8637-3.85354 1.6972-1.14909.8335-2.10705 1.9027-2.80984 3.1361-1.07508 1.848-1.53454 3.99-1.31193 6.1163.22261 2.1264 1.11575 4.1268 2.55026 5.712-.44921 1.3462-.60576 2.7728-.45917 4.1843.14658 1.4116.59293 2.7756 1.30917 4.0007 1.0648 1.8518 2.69005 3.3176 4.64155 4.1862 1.9514.8687 4.1283 1.0953 6.2168.6472a9.97516 9.97516 0 0 0 3.3956 2.4877c1.2959.5783 2.7003.8734 4.1194.8656 2.1395.0019 4.2242-.676 5.9533-1.936 1.7291-1.26 3.0131-3.0368 3.6667-5.074 1.3901-.2858 2.7034-.8648 3.8521-1.6982 1.1487-.8335 2.1065-1.9023 2.8096-3.1351 1.0623-1.8452 1.5139-3.9789 1.2903-6.0962-.2236-2.1173-1.111-4.1097-2.5353-5.6922ZM26.1 41.3833c-1.7517.0029-3.4486-.6107-4.7933-1.7333l.235-.135 7.965-4.5967c.1978-.1163.3621-.2821.4766-.4811.1146-.199.1754-.4243.1767-.6539V22.555l3.3667 1.9467c.0166.0084.031.0206.042.0357.011.0151.0183.0325.0213.0509v9.305c-.0044 1.9852-.795 3.8877-2.1986 5.2914-1.4037 1.4037-3.3063 2.1942-5.2914 2.1986ZM9.99999 34.5067c-.87917-1.5174-1.19492-3.2962-.89167-5.0234l.23667.1417 7.97171 4.5983c.1971.1156.4214.1765.65.1765.2285 0 .4528-.0609.65-.1765l9.7383-5.615v3.8867c-.0007.0203-.006.0401-.0156.058a.133112.133112 0 0 1-.0394.0453L20.2333 37.25c-1.7213.991-3.7654 1.2587-5.6839.7444-1.9185-.5143-3.5546-1.7686-4.54941-3.4877Zm-2.1-17.3467c.88495-1.5273 2.28181-2.6922 3.94331-3.2883v9.4616c-.0029.2285.0556.4536.1693.6518.1138.1981.2786.3622.4774.4749l9.6917 5.5917-3.3667 1.9466c-.0182.0097-.0386.0147-.0592.0147s-.0409-.005-.0591-.0147l-8.05-4.6433c-1.71882-.9955-2.97297-2.6317-3.48779-4.5501-.51482-1.9184-.24834-3.9626.74108-5.6849v.04Zm27.66171 6.425L25.84 17.94 29.1983 16c.0183-.0096.0386-.0147.0592-.0147s.0409.0051.0592.0147l8.05 4.6517c1.2315.7098 2.2356 1.7555 2.895 3.0148.6594 1.2594.9468 2.6803.8286 4.0969-.1181 1.4166-.6369 2.7703-1.4958 3.903-.8589 1.1327-2.0224 1.9975-3.3545 2.4936v-9.4633a1.31726 1.31726 0 0 0-.1921-.6456c-.1189-.1949-.2865-.3555-.4862-.4661Zm3.35-5.0383-.235-.1417-7.9567-4.6367a1.293568 1.293568 0 0 0-.6542-.1776c-.23 0-.4558.0613-.6541.1776l-9.73 5.615V15.495c-.0021-.0196.0012-.0393.0094-.0572s.0211-.0332.0372-.0445l8.05-4.645c1.2339-.7101 2.6445-1.05436 4.0668-.99252 1.4223.06184 2.7977.52722 3.9653 1.34182 1.1676.8145 2.0793 1.9445 2.6285 3.258.5491 1.3135.7131 2.7562.4728 4.1594v.0317ZM17.845 25.4383l-3.3667-1.94a.132412.132412 0 0 1-.0423-.0403c-.011-.0165-.0181-.0351-.021-.0547V14.125c.0028-1.4232.4104-2.8162 1.1754-4.0163.7649-1.20015 1.8555-2.15788 3.1444-2.76134 1.2889-.60347 2.7229-.82776 4.1345-.64667 1.4116.18108 2.7425.76005 3.8374 1.66931l-.2367.13333L18.5067 13.1c-.1983.1162-.3629.2819-.4777.4809-.1148.1989-.1759.4244-.1773.6541l-.0067 11.2033Zm1.8283-3.9416 4.3367-2.5 4.345 2.5v4.9983l-4.3283 2.5-4.345-2.5-.0084-4.9983Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M4 4h40v40H4z" />
      </clipPath>
    </defs>
  </svg>
);

export const Anthropic = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <g clipPath="url(#clip0_1_4)">
      <path
        d="M27.1971 10L38.6948 38.9638H45L33.5023 10H27.1971Z"
        fill="currentColor"
      />
      <path
        d="M13.8581 27.5024L17.7923 17.3237L21.7264 27.5024H13.8581ZM14.4959 10L3 38.9638H9.42784L11.7789 32.8813H23.806L26.1567 38.9638H32.5846L21.0886 10H14.4959Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_1_4">
        <rect width="42" height="29" transform="translate(3 10)" />
      </clipPath>
    </defs>
  </svg>
);

export const Meta = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <g>
      <path
        d="M6.29106 28.7838C6.29106 30.5084 6.66953 31.8323 7.16458 32.6333C7.81347 33.6825 8.78123 34.1279 9.7679 34.1279C11.0403 34.1279 12.2045 33.8121 14.4479 30.7101C16.2457 28.2238 18.363 24.7339 19.788 22.5459L22.2011 18.8392C23.8774 16.2649 25.8176 13.4033 28.0423 11.4636C29.859 9.88029 31.8176 9.00076 33.7892 9.00076C37.0995 9.00076 40.2526 10.9185 42.6657 14.5153C45.3067 18.4544 46.5886 23.4162 46.5886 28.5363C46.5886 31.5803 45.9885 33.8169 44.9672 35.5839C43.9807 37.2926 42.0577 39 38.8229 39V34.1271C41.5928 34.1271 42.2839 31.5826 42.2839 28.6706C42.2839 24.521 41.3161 19.916 39.1842 16.6255C37.6714 14.2915 35.7107 12.8655 33.5536 12.8655C31.2205 12.8655 29.3431 14.6249 27.2331 17.761C26.1113 19.4276 24.9598 21.4583 23.6668 23.7499L22.2433 26.2708C19.3843 31.34 18.6599 32.4941 17.2303 34.3988C14.7246 37.7345 12.5847 38.9992 9.7679 38.9992C6.42618 38.9992 4.31316 37.5525 3.0044 35.3725C1.93616 33.5963 1.41141 31.2653 1.41141 28.6095L6.29106 28.7838Z"
        fill="#0081FB"
      />
      <path
        d="M5.25894 14.8585C7.4961 11.411 10.7247 9 14.4276 9C16.5721 9 18.7044 9.63453 20.9302 11.4519C23.3653 13.4383 25.9607 16.7112 29.1987 22.103L30.3597 24.0375C33.1624 28.7054 34.7569 31.107 35.6903 32.2394C36.8904 33.6937 37.731 34.1273 38.8229 34.1273C41.5927 34.1273 42.2838 31.5828 42.2838 28.6708L46.5886 28.5357C46.5886 31.5797 45.9885 33.8163 44.9672 35.5833C43.9807 37.292 42.0577 38.9994 38.8229 38.9994C36.8119 38.9994 35.0304 38.5628 33.0603 36.7046C31.5464 35.2785 29.7751 32.7451 28.413 30.4677L24.3612 23.7015C22.3285 20.3058 20.463 17.7739 19.3843 16.6272C18.2232 15.3941 16.7307 13.9052 14.3489 13.9052C12.4214 13.9052 10.7843 15.2575 9.41439 17.3261L5.25894 14.8585Z"
        fill="url(#paint0_linear_1_5)"
      />
      <path
        d="M14.3489 13.9052C12.4214 13.9052 10.7843 15.2575 9.41441 17.3261C7.4772 20.249 6.29201 24.6027 6.29201 28.7838C6.29201 30.5084 6.67048 31.8323 7.16552 32.6333L3.0044 35.3725C1.93616 33.5963 1.41141 31.2653 1.41141 28.6095C1.41141 23.7798 2.73739 18.7459 5.25897 14.8585C7.49612 11.411 10.7247 9 14.4277 9L14.3489 13.9052Z"
        fill="url(#paint1_linear_1_5)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_1_5"
        x1="11.2048"
        y1="23.2223"
        x2="42.3114"
        y2="24.7937"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0064E1" />
        <stop offset="0.4" stopColor="#0064E1" />
        <stop offset="0.83" stopColor="#0073EE" />
        <stop offset="1" stopColor="#0082FB" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1_5"
        x1="7.91944"
        y1="30.8319"
        x2="7.91944"
        y2="19.3663"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#0082FB" />
        <stop offset="1" stopColor="#0064E0" />
      </linearGradient>
    </defs>
  </svg>
);

export const Mistral = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <g clipPath="url(#clip0_23_21)">
      <path d="M14.6278 9H8.81372V14.7755H14.6278V9Z" fill="#FFD800" />
      <path d="M37.8869 9H32.0728V14.7755H37.8869V9Z" fill="#FFD800" />
      <path
        d="M20.4418 14.7748H8.81372V20.5503H20.4418V14.7748Z"
        fill="#FFAF00"
      />
      <path
        d="M37.8872 14.7748H26.2591V20.5503H37.8872V14.7748Z"
        fill="#FFAF00"
      />
      <path d="M37.8875 20.55H8.81372V26.3251H37.8875V20.55Z" fill="#FF8205" />
      <path
        d="M14.6278 26.3267H8.81372V32.1022H14.6278V26.3267Z"
        fill="#FA500F"
      />
      <path
        d="M26.2575 26.3267H20.4434V32.1022H26.2575V26.3267Z"
        fill="#FA500F"
      />
      <path
        d="M37.8869 26.3267H32.0728V32.1022H37.8869V26.3267Z"
        fill="#FA500F"
      />
      <path d="M20.4457 32.1057H3V37.8811H20.4457V32.1057Z" fill="#E10500" />
      <path
        d="M43.7048 32.1057H26.2591V37.8811H43.7048V32.1057Z"
        fill="#E10500"
      />
    </g>
    <defs>
      <clipPath id="clip0_23_21">
        <rect width="41" height="29" fill="white" transform="translate(3 9)" />
      </clipPath>
    </defs>
  </svg>
);

export const HuggingFace = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <path
      d="M24.3687 38.7727C28.5403 38.7727 32.541 37.1086 35.4908 34.1464C38.4405 31.1841 40.0977 27.1665 40.0977 22.9773C40.0977 18.7881 38.4405 14.7704 35.4908 11.8082C32.541 8.84598 28.5403 7.18182 24.3687 7.18182C20.1972 7.18182 16.1964 8.84598 13.2467 11.8082C10.2969 14.7704 8.63979 18.7881 8.63979 22.9773C8.63979 27.1665 10.2969 31.1841 13.2467 34.1464C16.1964 37.1086 20.1972 38.7727 24.3687 38.7727Z"
      fill="#FFD21E"
    />
    <path
      d="M40.0977 22.9773C40.0977 18.7881 38.4405 14.7704 35.4908 11.8082C32.541 8.84597 28.5403 7.18181 24.3687 7.18181C20.1972 7.18181 16.1964 8.84597 13.2467 11.8082C10.2969 14.7704 8.63979 18.7881 8.63979 22.9773C8.63979 27.1665 10.2969 31.1841 13.2467 34.1463C16.1964 37.1086 20.1972 38.7727 24.3687 38.7727C28.5403 38.7727 32.541 37.1086 35.4908 34.1463C38.4405 31.1841 40.0977 27.1665 40.0977 22.9773ZM6.82926 22.9773C6.82926 20.6642 7.28294 18.3738 8.16438 16.2368C9.04582 14.0998 10.3378 12.1581 11.9665 10.5225C13.5951 8.88697 15.5287 7.58956 17.6567 6.70439C19.7847 5.81922 22.0654 5.36363 24.3687 5.36363C26.6721 5.36363 28.9528 5.81922 31.0808 6.70439C33.2088 7.58956 35.1423 8.88697 36.771 10.5225C38.3997 12.1581 39.6917 14.0998 40.5731 16.2368C41.4545 18.3738 41.9082 20.6642 41.9082 22.9773C41.9082 27.6487 40.0603 32.1288 36.771 35.432C33.4817 38.7352 29.0205 40.5909 24.3687 40.5909C19.717 40.5909 15.2557 38.7352 11.9665 35.432C8.67717 32.1288 6.82926 27.6487 6.82926 22.9773Z"
      fill="#FF9D0B"
    />
    <path
      d="M29.4789 18.6818C30.0583 18.8818 30.2846 20.0727 30.8685 19.7636C31.2639 19.5528 31.5881 19.229 31.8002 18.8331C32.0124 18.4373 32.1028 17.9871 32.0601 17.5396C32.0175 17.0922 31.8436 16.6674 31.5605 16.3192C31.2775 15.9709 30.8979 15.7148 30.4699 15.5832C30.0419 15.4515 29.5846 15.4503 29.1559 15.5797C28.7272 15.7091 28.3463 15.9633 28.0615 16.31C27.7766 16.6568 27.6006 17.0806 27.5556 17.5279C27.5106 17.9751 27.5987 18.4258 27.8087 18.8227C28.0848 19.3455 28.9629 18.4955 29.4835 18.6773L29.4789 18.6818ZM18.8195 18.6818C18.2401 18.8818 18.0093 20.0727 17.4299 19.7636C17.0345 19.5528 16.7103 19.229 16.4982 18.8331C16.2861 18.4373 16.1956 17.9871 16.2383 17.5396C16.281 17.0922 16.4548 16.6674 16.7379 16.3192C17.0209 15.9709 17.4005 15.7148 17.8285 15.5832C18.2565 15.4515 18.7138 15.4503 19.1425 15.5797C19.5712 15.7091 19.9521 15.9633 20.237 16.31C20.5218 16.6568 20.6979 17.0806 20.7429 17.5279C20.7878 17.9751 20.6997 18.4258 20.4897 18.8227C20.2136 19.3455 19.3309 18.4955 18.8149 18.6773L18.8195 18.6818Z"
      fill="#3A3B45"
    />
    <path
      d="M24.2556 29.5864C28.7049 29.5864 30.1398 25.6045 30.1398 23.5591C30.1398 22.4955 29.4292 22.8318 28.2885 23.3955C27.2339 23.9182 25.8172 24.6409 24.2601 24.6409C21.0057 24.6409 18.3759 21.5136 18.3759 23.5591C18.3759 25.6045 19.8062 29.5864 24.2601 29.5864H24.2556Z"
      fill="#FF323D"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.8473 28.5455C21.0861 28.058 21.4224 27.6253 21.8351 27.2741C22.2478 26.923 22.7282 26.661 23.2462 26.5045C23.4273 26.45 23.6128 26.7636 23.8075 27.0864C23.9885 27.3955 24.1786 27.7091 24.3687 27.7091C24.5724 27.7091 24.7761 27.4 24.9707 27.0955C25.1744 26.7773 25.3736 26.4682 25.5682 26.5273C26.5406 26.8373 27.3531 27.5178 27.8314 28.4227C29.5197 27.0864 30.1398 24.9045 30.1398 23.5591C30.1398 22.4955 29.4292 22.8318 28.2885 23.3955L28.2252 23.4273C27.1796 23.95 25.7855 24.6409 24.2556 24.6409C22.7257 24.6409 21.3361 23.95 20.286 23.4273C19.1092 22.8409 18.3714 22.4727 18.3714 23.5591C18.3714 24.9455 19.0322 27.2227 20.8473 28.5455Z"
      fill="#3A3B45"
    />
    <path
      d="M35.0056 20.8182C35.3957 20.8182 35.7699 20.6625 36.0458 20.3855C36.3216 20.1085 36.4766 19.7327 36.4766 19.3409C36.4766 18.9491 36.3216 18.5734 36.0458 18.2963C35.7699 18.0193 35.3957 17.8636 35.0056 17.8636C34.6154 17.8636 34.2413 18.0193 33.9654 18.2963C33.6895 18.5734 33.5345 18.9491 33.5345 19.3409C33.5345 19.7327 33.6895 20.1085 33.9654 20.3855C34.2413 20.6625 34.6154 20.8182 35.0056 20.8182ZM13.9582 20.8182C14.3484 20.8182 14.7225 20.6625 14.9984 20.3855C15.2743 20.1085 15.4293 19.7327 15.4293 19.3409C15.4293 18.9491 15.2743 18.5734 14.9984 18.2963C14.7225 18.0193 14.3484 17.8636 13.9582 17.8636C13.5681 17.8636 13.1939 18.0193 12.918 18.2963C12.6421 18.5734 12.4872 18.9491 12.4872 19.3409C12.4872 19.7327 12.6421 20.1085 12.918 20.3855C13.1939 20.6625 13.5681 20.8182 13.9582 20.8182ZM10.9301 25.8182C10.1968 25.8182 9.54505 26.1182 9.08789 26.6682C8.69806 27.1517 8.48553 27.7551 8.48589 28.3773C8.20089 28.2912 7.90537 28.2453 7.60779 28.2409C6.90621 28.2409 6.27253 28.5091 5.82442 28.9955C5.42464 29.4124 5.17258 29.9498 5.10714 30.5248C5.0417 31.0999 5.16651 31.6805 5.46232 32.1773C5.06173 32.5057 4.77754 32.9553 4.6521 33.4591C4.54347 33.8682 4.43484 34.7318 5.01421 35.6136C4.79702 35.949 4.66778 36.334 4.6385 36.7329C4.60922 37.1319 4.68085 37.5318 4.84674 37.8955C5.30842 38.95 6.46263 39.7773 8.70316 40.6682C10.0927 41.2227 11.3692 41.5773 11.3782 41.5818C12.9907 42.0304 14.6526 42.2748 16.3255 42.3091C18.9779 42.3091 20.8744 41.4909 21.9653 39.8818C23.7215 37.2955 23.4725 34.9273 21.1958 32.6455C19.942 31.3818 19.1046 29.5227 18.9326 29.1136C18.5796 27.9045 17.6472 26.5591 16.1037 26.5591C15.6931 26.5656 15.2899 26.6705 14.9278 26.865C14.5656 27.0594 14.255 27.3379 14.0216 27.6773C13.5689 27.1045 13.1254 26.6545 12.7271 26.3955C12.1955 26.0341 11.5718 25.8337 10.9301 25.8182ZM10.9301 27.6364C11.1609 27.6364 11.4461 27.7364 11.7539 27.9318C12.7225 28.55 14.5828 31.7636 15.2663 33.0136C15.4926 33.4318 15.8864 33.6091 16.2349 33.6091C16.9365 33.6091 17.4797 32.9136 16.3028 32.0273C14.5285 30.6955 15.1486 28.5182 15.9951 28.3864C16.0313 28.3773 16.072 28.3773 16.1037 28.3773C16.8732 28.3773 17.2126 29.7091 17.2126 29.7091C17.2126 29.7091 18.2084 32.2182 19.9194 33.9364C21.6258 35.65 21.7163 37.0273 20.4716 38.8591C19.6206 40.1091 17.9957 40.4864 16.3255 40.4864C14.6009 40.4864 12.8266 40.0773 11.8354 39.8227C11.7856 39.8091 5.74747 38.0955 6.51242 36.6409C6.63916 36.3955 6.85189 36.2955 7.11895 36.2955C8.19621 36.2955 10.1516 37.9045 10.998 37.9045C11.1836 37.9045 11.3148 37.8273 11.3737 37.6318C11.7313 36.3364 5.91495 35.7909 6.40379 33.9182C6.49432 33.5864 6.72516 33.4545 7.05558 33.4545C8.47684 33.4545 11.6724 35.9682 12.3423 35.9682C12.3921 35.9682 12.4328 35.9545 12.4509 35.9227C12.7859 35.3773 12.6003 34.9955 10.2331 33.5591C7.87484 32.1227 6.21368 31.2591 7.15516 30.2273C7.26379 30.1091 7.41768 30.0545 7.60779 30.0545C9.04263 30.0545 12.4328 33.1545 12.4328 33.1545C12.4328 33.1545 13.3472 34.1091 13.9039 34.1091C14.0306 34.1091 14.1393 34.0636 14.2117 33.9364C14.6009 33.2727 10.5635 30.2 10.3372 28.9318C10.1833 28.0682 10.4458 27.6364 10.9301 27.6364Z"
      fill="#FF9D0B"
    />
    <path
      d="M20.4716 38.8591C21.7163 37.0227 21.6258 35.6455 19.9194 33.9318C18.2084 32.2182 17.2126 29.7045 17.2126 29.7045C17.2126 29.7045 16.8415 28.25 15.9951 28.3864C15.1486 28.5227 14.5285 30.6955 16.3028 32.0273C18.0726 33.3591 15.9498 34.2636 15.2663 33.0136C14.5874 31.7636 12.7225 28.55 11.7539 27.9318C10.7898 27.3182 10.1108 27.6591 10.3372 28.9318C10.5635 30.2 14.6055 33.2727 14.2117 33.9318C13.8179 34.6 12.4328 33.1545 12.4328 33.1545C12.4328 33.1545 8.10116 29.1955 7.15516 30.2273C6.21368 31.2591 7.87484 32.1227 10.2331 33.5591C12.6003 34.9955 12.7859 35.3773 12.4509 35.9227C12.1115 36.4682 6.89263 32.0455 6.40379 33.9227C5.91495 35.7909 11.7313 36.3318 11.3737 37.6273C11.0116 38.9227 7.27284 35.1818 6.51242 36.6364C5.74295 38.0955 11.7856 39.8091 11.8354 39.8227C13.7817 40.3318 18.738 41.4091 20.4716 38.8591Z"
      fill="#FFD21E"
    />
    <path
      d="M38.0337 25.8182C38.7669 25.8182 39.4233 26.1182 39.8759 26.6682C40.2657 27.1517 40.4783 27.7551 40.4779 28.3773C40.7644 28.2908 41.0614 28.2449 41.3605 28.2409C42.0621 28.2409 42.6958 28.5091 43.1439 28.9955C43.5437 29.4124 43.7957 29.9498 43.8612 30.5248C43.9266 31.0999 43.8018 31.6805 43.506 32.1773C43.9049 32.5064 44.1875 32.9559 44.3117 33.4591C44.4203 33.8682 44.5289 34.7318 43.9496 35.6136C44.1668 35.949 44.296 36.334 44.3253 36.7329C44.3546 37.1319 44.2829 37.5318 44.1171 37.8955C43.6554 38.95 42.5012 39.7773 40.2652 40.6682C38.8711 41.2227 37.5946 41.5773 37.5856 41.5818C35.9731 42.0304 34.3112 42.2748 32.6383 42.3091C29.9859 42.3091 28.0894 41.4909 26.9985 39.8818C25.2423 37.2955 25.4913 34.9273 27.768 32.6455C29.0263 31.3818 29.8637 29.5227 30.0357 29.1136C30.3887 27.9045 31.3166 26.5591 32.8601 26.5591C33.2707 26.5656 33.6739 26.6705 34.036 26.865C34.3981 27.0594 34.7088 27.3379 34.9422 27.6773C35.3948 27.1045 35.8384 26.6545 36.2413 26.3955C36.7715 26.0349 37.3935 25.8346 38.0337 25.8182ZM38.0337 27.6364C37.8028 27.6364 37.5222 27.7364 37.2099 27.9318C36.2458 28.55 34.3809 31.7636 33.6975 33.0136C33.6052 33.192 33.4663 33.3417 33.2956 33.4466C33.1249 33.5516 32.929 33.6077 32.7288 33.6091C32.0318 33.6091 31.4841 32.9136 32.6655 32.0273C34.4353 30.6955 33.8152 28.5182 32.9687 28.3864C32.9328 28.3805 32.8965 28.3775 32.8601 28.3773C32.0906 28.3773 31.7512 29.7091 31.7512 29.7091C31.7512 29.7091 30.7554 32.2182 29.0489 33.9364C27.338 35.65 27.2475 37.0273 28.4967 38.8591C29.3432 40.1091 30.9726 40.4864 32.6383 40.4864C34.3674 40.4864 36.1372 40.0773 37.1329 39.8227C37.1782 39.8091 43.2208 38.0955 42.4559 36.6409C42.3246 36.3955 42.1164 36.2955 41.8494 36.2955C40.7721 36.2955 38.8122 37.9045 37.9703 37.9045C37.7802 37.9045 37.6489 37.8273 37.5946 37.6318C37.2325 36.3364 43.0488 35.7909 42.56 33.9182C42.474 33.5864 42.2432 33.4545 41.9082 33.4545C40.4869 33.4545 37.2914 35.9682 36.6215 35.9682C36.5762 35.9682 36.5355 35.9546 36.5174 35.9227C36.1824 35.3773 36.3635 34.9955 38.7262 33.5591C41.0935 32.1227 42.7546 31.2591 41.8041 30.2273C41.7 30.1091 41.5461 30.0545 41.3605 30.0545C39.9212 30.0545 36.5309 33.1545 36.5309 33.1545C36.5309 33.1545 35.6166 34.1091 35.0644 34.1091C35.0022 34.1119 34.9404 34.0972 34.8861 34.0667C34.8317 34.0362 34.7869 33.991 34.7566 33.9364C34.3628 33.2727 38.4003 30.2 38.6266 28.9318C38.7805 28.0682 38.518 27.6364 38.0337 27.6364Z"
      fill="#FF9D0B"
    />
    <path
      d="M28.4967 38.8591C27.252 37.0227 27.338 35.6455 29.0489 33.9318C30.7554 32.2182 31.7512 29.7045 31.7512 29.7045C31.7512 29.7045 32.1223 28.25 32.9733 28.3864C33.8152 28.5227 34.4353 30.6955 32.6655 32.0273C30.8912 33.3591 33.0185 34.2636 33.6975 33.0136C34.3809 31.7636 36.2458 28.55 37.2099 27.9318C38.174 27.3182 38.8575 27.6591 38.6266 28.9318C38.4003 30.2 34.3628 33.2727 34.7566 33.9318C35.1459 34.6 36.5309 33.1545 36.5309 33.1545C36.5309 33.1545 40.8672 29.1955 41.8086 30.2273C42.7501 31.2591 41.0935 32.1227 38.7307 33.5591C36.3635 34.9955 36.1824 35.3773 36.5128 35.9227C36.8523 36.4682 42.0712 32.0455 42.56 33.9227C43.0488 35.7909 37.2371 36.3318 37.5946 37.6273C37.9567 38.9227 41.6909 35.1818 42.4559 36.6364C43.2208 38.0955 37.1827 39.8091 37.1329 39.8227C35.1821 40.3318 30.2258 41.4091 28.4967 38.8591Z"
      fill="#FFD21E"
    />
  </svg>
);

export const Perplexity = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M34.566 4.98216V15.9H38.64V31.47H34.566V42.4448L25.049 32.9188V42H23.049V32.9627L13.575 42.4446V31.47H9.5V15.9H13.574V4.98234L23.049 14.4653V5.99999H25.049V14.5082L34.566 4.98216ZM21.6552 15.9L15.574 9.81365V15.9H21.6552ZM13.574 17.9H11.5V29.47H13.5742L13.5751 25.99L13.8676 25.6972L21.6554 17.902H13.574V17.9ZM15.575 26.818V37.6133L23.049 30.133V19.3377L15.575 26.818ZM25.071 19.3166V30.1111L32.566 37.6132V31.47H32.5648L32.5659 26.8179L25.071 19.3166ZM34.566 29.47V25.9901L26.4856 17.902H34.566V17.9H36.64V29.47H34.566ZM32.566 15.9V9.81383L26.4856 15.9H32.566Z"
      fill="#20808D"
    />
  </svg>
);

export const Google = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <path
      d="M40.72 24.3958C40.72 23.1608 40.6092 21.9733 40.4033 20.8333H24V27.5783H33.3733C32.9617 29.7475 31.7267 31.5842 29.8742 32.8192V37.205H35.5267C38.82 34.165 40.72 29.7 40.72 24.3958Z"
      fill="#4285F4"
    />
    <path
      d="M24 41.4167C28.7025 41.4167 32.645 39.865 35.5267 37.205L29.8742 32.8192C28.3225 33.8642 26.3433 34.4975 24 34.4975C19.4717 34.4975 15.6242 31.4417 14.2467 27.325H8.45166V31.8217C11.3175 37.5058 17.1917 41.4167 24 41.4167Z"
      fill="#34A853"
    />
    <path
      d="M14.2467 27.3092C13.8983 26.2642 13.6925 25.1558 13.6925 24C13.6925 22.8442 13.8983 21.7358 14.2467 20.6908V16.1942H8.45166C7.26416 18.5375 6.58333 21.1817 6.58333 24C6.58333 26.8183 7.26416 29.4625 8.45166 31.8058L12.9642 28.2908L14.2467 27.3092Z"
      fill="#FBBC05"
    />
    <path
      d="M24 13.5183C26.565 13.5183 28.845 14.405 30.6658 16.115L35.6533 11.1275C32.6292 8.30916 28.7025 6.58333 24 6.58333C17.1917 6.58333 11.3175 10.4942 8.45166 16.1942L14.2467 20.6908C15.6242 16.5742 19.4717 13.5183 24 13.5183Z"
      fill="#EA4335"
    />
  </svg>
);

export const Azure = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48">
    <path
      d="M17.8908 6.72667H28.74L17.4775 40.0963C17.3617 40.4391 17.1414 40.7371 16.8474 40.9482C16.5534 41.1593 16.2006 41.2728 15.8387 41.2729H7.39541C7.1212 41.273 6.85093 41.2077 6.60695 41.0825C6.36297 40.9574 6.15229 40.7759 5.99235 40.5532C5.83241 40.3305 5.7278 40.0729 5.68718 39.8017C5.64656 39.5305 5.67109 39.2536 5.75874 38.9938L16.2517 7.90333C16.3674 7.56029 16.5878 7.2622 16.8818 7.05103C17.1759 6.83986 17.5288 6.72669 17.8908 6.72667Z"
      fill="url(#paint0_linear_2_2)"
    />
    <path
      d="M33.6562 29.1087H16.4521C16.2921 29.1086 16.1358 29.1566 16.0036 29.2465C15.8713 29.3364 15.7691 29.4641 15.7105 29.6129C15.6518 29.7617 15.6393 29.9247 15.6746 30.0807C15.7099 30.2367 15.7913 30.3785 15.9083 30.4875L26.9633 40.8058C27.2852 41.1061 27.709 41.273 28.1492 41.2729H37.8908L33.6562 29.1087Z"
      fill="#0078D4"
    />
    <path
      d="M17.8908 6.72667C17.5249 6.72526 17.168 6.84092 16.8724 7.05673C16.5769 7.27255 16.358 7.57722 16.2479 7.92625L5.77168 38.9654C5.67813 39.2262 5.64876 39.5056 5.68605 39.7801C5.72335 40.0547 5.82621 40.3162 5.98594 40.5425C6.14567 40.7689 6.35756 40.9534 6.6037 41.0805C6.84984 41.2077 7.12298 41.2737 7.40001 41.2729H16.0613C16.3838 41.2153 16.6853 41.0731 16.935 40.8608C17.1846 40.6485 17.3734 40.3737 17.4821 40.0646L19.5713 33.9075L27.0338 40.8679C27.3465 41.1266 27.7388 41.2696 28.1446 41.2729H37.85L33.5933 29.1088L21.1846 29.1117L28.7792 6.72667H17.8908Z"
      fill="url(#paint1_linear_2_2)"
    />
    <path
      d="M31.7479 7.90167C31.6324 7.55918 31.4123 7.26158 31.1187 7.05078C30.825 6.83999 30.4727 6.72662 30.1112 6.72667H18.02C18.3814 6.72669 18.7338 6.84007 19.0274 7.05086C19.321 7.26165 19.5411 7.55921 19.6567 7.90167L30.15 38.9933C30.2377 39.2532 30.2623 39.5302 30.2217 39.8015C30.1811 40.0727 30.0765 40.3304 29.9166 40.5532C29.7566 40.776 29.546 40.9576 29.3019 41.0828C29.0579 41.208 28.7876 41.2733 28.5133 41.2733H40.605C40.8792 41.2733 41.1495 41.2079 41.3935 41.0826C41.6374 40.9574 41.8481 40.7758 42.008 40.5531C42.1679 40.3303 42.2724 40.0726 42.313 39.8014C42.3535 39.5301 42.3289 39.2532 42.2412 38.9933L31.7479 7.90167Z"
      fill="url(#paint2_linear_2_2)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_2_2"
        x1="21.845"
        y1="9.28667"
        x2="10.5779"
        y2="42.5725"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#114A8B" />
        <stop offset="1" stopColor="#0669BC" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_2_2"
        x1="25.3646"
        y1="24.7988"
        x2="22.7583"
        y2="25.68"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopOpacity="0.3" />
        <stop offset="0.071" stopOpacity="0.2" />
        <stop offset="0.321" stopOpacity="0.1" />
        <stop offset="0.623" stopOpacity="0.05" />
        <stop offset="1" stopOpacity="0" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_2_2"
        x1="23.9312"
        y1="8.31583"
        x2="36.2992"
        y2="41.2663"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3CCBF4" />
        <stop offset="1" stopColor="#2892DF" />
      </linearGradient>
    </defs>
  </svg>
);

export const Ollama = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.5308 5.01096C15.1607 5.06975 14.7166 5.26011 14.4034 5.49526C13.4555 6.20353 12.721 7.70684 12.4107 9.57967C12.294 10.2879 12.2143 11.2706 12.2143 12.0208C12.2143 12.9054 12.3196 14.0364 12.4705 14.8175C12.5046 14.991 12.5217 15.145 12.5075 15.1562C12.4961 15.1674 12.3566 15.2794 12.2 15.4025C11.6648 15.8225 11.0528 16.4692 10.6314 17.0599C9.82295 18.188 9.29914 19.4702 9.07994 20.8587C8.99453 21.4074 8.97176 22.516 9.04008 23.0647C9.19096 24.33 9.57813 25.3994 10.2414 26.3793L10.4578 26.6956L10.3952 26.7992C9.95106 27.5326 9.57243 28.5936 9.39593 29.6126C9.25644 30.4189 9.23936 30.6344 9.23936 31.715C9.23936 32.804 9.25359 33.0196 9.38455 33.7726C9.54112 34.6741 9.85996 35.6287 10.2158 36.2642C10.3325 36.4713 10.6172 36.9024 10.6514 36.9248C10.6628 36.9304 10.6286 37.034 10.5745 37.1544C10.1646 38.0362 9.81441 39.2092 9.66922 40.1974C9.56674 40.8748 9.55251 41.0932 9.55251 41.8071C9.55251 42.7169 9.60375 43.1592 9.79733 43.8843L9.8258 43.9906H11.0442H12.2655L12.1858 43.8423C11.6933 42.9465 11.6478 41.2836 12.0719 39.6235C12.2655 38.8564 12.4847 38.2938 12.8946 37.5183L13.1395 37.048V36.7597C13.1395 36.4909 13.1338 36.4601 13.0455 36.2838C12.9772 36.1494 12.8861 36.0346 12.7238 35.8778C12.4477 35.6147 12.2484 35.3375 12.089 34.996C11.3887 33.5011 11.252 31.2811 11.7445 29.3887C11.9495 28.5992 12.2883 27.8966 12.6441 27.513C12.8861 27.2499 13.0114 26.9559 13.0114 26.6508C13.0114 26.3345 12.8975 26.0741 12.6413 25.8026C11.9068 25.0299 11.4542 24.0893 11.2919 22.9947C11.0613 21.4354 11.4798 19.7361 12.4306 18.3896C13.3615 17.0682 14.6682 16.22 16.1286 15.9933C16.456 15.94 17.0681 15.9485 17.4097 16.0101C17.7826 16.0744 18.016 16.0548 18.2552 15.9428C18.5512 15.8057 18.6993 15.6349 18.8729 15.243C19.0266 14.8931 19.1462 14.7027 19.4679 14.308C19.8551 13.8349 20.228 13.5129 20.8258 13.1238C21.5091 12.6843 22.2862 12.3652 23.0606 12.2112C23.3424 12.1552 23.4733 12.1468 24 12.1468C24.5267 12.1468 24.6576 12.1552 24.9394 12.2112C26.0753 12.438 27.2027 13.0146 28.1022 13.832C28.2958 14.0084 28.7598 14.5739 28.9079 14.8091C28.9648 14.9014 29.0645 15.0974 29.1271 15.243C29.3007 15.6349 29.4488 15.8057 29.7448 15.9428C29.9754 16.052 30.2174 16.0744 30.5761 16.0156C31.1426 15.9205 31.5782 15.9289 32.1333 16.0408C34.0236 16.416 35.669 17.9473 36.3978 19.9993C37.0326 21.7993 36.8533 23.6834 35.9081 25.1223C35.7487 25.3659 35.5893 25.5618 35.3587 25.8026C34.8605 26.3261 34.8605 26.9755 35.3559 27.513C36.1701 28.3893 36.6796 30.5449 36.5259 32.4457C36.4234 33.6998 36.096 34.8224 35.6462 35.4579C35.5665 35.5699 35.4014 35.7602 35.2761 35.8778C35.1139 36.0346 35.0228 36.1494 34.9545 36.2838C34.8662 36.4601 34.8605 36.4909 34.8605 36.7597V37.048L35.1053 37.5183C35.5153 38.2938 35.7345 38.8564 35.9281 39.6235C36.3466 41.2612 36.3096 42.8905 35.8313 43.8171C35.7914 43.8955 35.7573 43.9683 35.7573 43.9767C35.7573 43.9851 36.301 43.9906 36.9672 43.9906H38.1742L38.2055 43.8703C38.213 43.842 38.2227 43.8072 38.2325 43.7724C38.2449 43.7279 38.2573 43.6833 38.2653 43.6519C38.2966 43.5288 38.3592 43.1648 38.4105 42.8149C38.4589 42.4622 38.4589 41.1632 38.4105 40.7713C38.2283 39.3491 37.9237 38.221 37.4255 37.1544C37.3714 37.034 37.3372 36.9304 37.3486 36.9248C37.3629 36.9164 37.4426 36.8045 37.528 36.6785C38.1486 35.7547 38.53 34.5929 38.7236 33.0588C38.7749 32.636 38.7749 30.8192 38.7236 30.4132C38.587 29.3663 38.4219 28.6552 38.1486 27.9357C38.0347 27.6362 37.7329 27.0035 37.6049 26.7992L37.5422 26.6956L37.7586 26.3793C38.4219 25.3994 38.809 24.33 38.9599 23.0647C39.0282 22.516 39.0055 21.4074 38.9201 20.8587C38.698 19.4674 38.177 18.1908 37.3686 17.0599C36.9472 16.4692 36.3352 15.8225 35.8 15.4025C35.6434 15.2794 35.5039 15.1674 35.4925 15.1562C35.4783 15.145 35.4954 14.991 35.5295 14.8175C35.874 13.051 35.8626 10.8479 35.5011 9.12616C35.1879 7.62566 34.6186 6.43308 33.8841 5.74442C33.2976 5.19572 32.6998 4.96057 31.9824 5.00536C30.337 5.10054 29.0104 6.96218 28.4865 9.89601C28.4011 10.3691 28.3272 10.9234 28.3272 11.0746C28.3272 11.1334 28.3157 11.181 28.3015 11.181C28.2873 11.181 28.1763 11.125 28.0567 11.055C26.787 10.3159 25.375 9.92122 24 9.92122C22.625 9.92122 21.213 10.3159 19.9433 11.055C19.8237 11.125 19.7127 11.181 19.6985 11.181C19.6843 11.181 19.6728 11.1334 19.6728 11.0746C19.6728 10.9178 19.596 10.3467 19.5135 9.89601C19.038 7.26172 17.9477 5.51766 16.4987 5.07814C16.2994 5.01935 15.7329 4.98016 15.5308 5.01096ZM16.0148 7.28972C16.4247 7.60886 16.8802 8.52148 17.1421 9.54329C17.1905 9.72804 17.2417 9.94081 17.2559 10.0192C17.2673 10.0948 17.2986 10.2655 17.3243 10.3971C17.4353 10.9906 17.4865 11.6317 17.4922 12.4127L17.4951 13.1826L17.2986 13.4681L17.1022 13.7565H16.6439C16.1087 13.7565 15.5763 13.8237 15.0667 13.958C14.9386 13.9895 14.8133 14.021 14.7383 14.0399L14.6739 14.056C14.6198 14.0672 14.6113 14.0504 14.58 13.8209C14.412 12.5751 14.4205 11.195 14.6056 10.0472C14.8105 8.76783 15.2888 7.60886 15.7557 7.26733C15.8667 7.18614 15.8866 7.18894 16.0148 7.28972ZM32.2472 7.27012C32.529 7.47448 32.8393 8.01758 33.0699 8.71184C33.5339 10.1004 33.6649 12.0068 33.42 13.8209C33.3887 14.0504 33.3802 14.0672 33.3261 14.056L33.2616 14.0399C33.1867 14.021 33.0614 13.9895 32.9333 13.958C32.4237 13.8237 31.8913 13.7565 31.3561 13.7565H30.8978L30.7014 13.4681L30.5049 13.1826L30.5078 12.4127C30.5135 11.3265 30.616 10.4783 30.8608 9.53489C31.1198 8.52148 31.5782 7.60886 31.9853 7.28972C32.1134 7.18894 32.1333 7.18614 32.2472 7.27012ZM23.5587 21.3598C22.9409 21.4186 22.773 21.441 22.4769 21.4998C21.9958 21.5977 21.3524 21.8161 20.9055 22.0317C19.3512 22.7791 18.2808 24.0249 17.9534 25.4666C17.8879 25.7522 17.8793 25.8473 17.8793 26.3288C17.8793 26.8047 17.8879 26.9083 17.9505 27.1799C18.3861 29.0639 20.1511 30.4553 22.4342 30.71C22.9296 30.7632 25.0704 30.7632 25.5657 30.71C27.3991 30.5056 28.9762 29.5286 29.685 28.1569C29.8729 27.7902 29.964 27.5522 30.0494 27.1799C30.1121 26.9083 30.1206 26.8047 30.1206 26.3288C30.1206 25.8473 30.1121 25.7522 30.0466 25.4666C29.5711 23.3726 27.5044 21.7237 24.9707 21.4102C24.6405 21.371 23.7751 21.3374 23.5587 21.3598ZM24.6234 22.8827C25.4689 22.9723 26.3201 23.269 27.0033 23.7169C27.3706 23.9577 27.8887 24.4616 28.1107 24.792C28.384 25.2006 28.5406 25.6178 28.6118 26.1245C28.6431 26.3568 28.626 26.5332 28.5406 26.9083C28.4068 27.4682 27.9912 28.0533 27.4304 28.462C27.1684 28.6496 26.6247 28.9212 26.2917 29.0275C25.6597 29.2263 25.2469 29.2627 23.7722 29.2515C22.81 29.2431 22.6392 29.2347 22.3631 29.1843C21.4208 29.0107 20.6749 28.6412 20.134 28.0785C19.6956 27.625 19.4963 27.2107 19.3882 26.5416C19.3398 26.2308 19.4309 25.7158 19.6159 25.2818C19.8408 24.7527 20.4215 24.0949 20.9966 23.7169C21.6627 23.2802 22.5396 22.9695 23.3452 22.8855C23.6555 22.8519 24.3131 22.8519 24.6234 22.8827ZM22.5909 25.5478C22.5453 25.3322 22.6962 25.0411 22.9126 24.9263C22.9894 24.8871 23.0748 24.8731 23.2314 24.8731C23.4506 24.8704 23.5161 24.8955 23.8776 25.1055L23.9659 25.1587L24.1054 25.0719C24.3502 24.9179 24.484 24.8704 24.6918 24.8704C24.8256 24.8704 24.9167 24.8871 24.9907 24.9235C25.2925 25.0775 25.4178 25.4107 25.2811 25.7046C25.2099 25.8586 24.9708 26.0853 24.7459 26.2141C24.5324 26.3345 24.5153 26.3961 24.6007 26.7908C24.7032 27.2639 24.632 27.5102 24.353 27.6446C24.2449 27.695 24.1879 27.7006 23.9175 27.6922C23.6214 27.6838 23.6015 27.681 23.4961 27.597C23.3766 27.5019 23.2627 27.3059 23.2598 27.1855C23.2598 27.1407 23.2855 26.9783 23.3197 26.8244C23.351 26.6676 23.3737 26.4912 23.368 26.4296C23.3595 26.3261 23.3481 26.3121 23.1744 26.2085C22.8499 26.0153 22.6421 25.7801 22.5909 25.5478ZM16.0005 21.6762C15.4966 21.8329 15.1208 22.1969 14.9273 22.7148C14.8333 22.9611 14.7878 23.3503 14.8276 23.5602C14.9216 24.0613 15.34 24.5176 15.8155 24.6436C16.4133 24.7976 16.8602 24.6968 17.2559 24.3105C17.4865 24.0893 17.6118 23.8961 17.7371 23.5826C17.8281 23.3615 17.8338 23.3223 17.8338 23.0087L17.8367 22.6728L17.7171 22.432C17.5264 22.0513 17.1819 21.7685 16.7834 21.665C16.5585 21.609 16.1969 21.6118 16.0005 21.6762ZM30.2829 22.432C30.4679 22.0569 30.8152 21.7714 31.2052 21.6678C31.4131 21.6118 31.7831 21.6118 31.9739 21.6678C32.6258 21.8554 33.0642 22.39 33.1695 23.1263C33.2151 23.4482 33.1809 23.6526 33.0386 23.9353C32.8735 24.2685 32.529 24.554 32.1846 24.6436C31.5867 24.7976 31.1398 24.6968 30.7441 24.3105C30.5135 24.0893 30.3882 23.8961 30.2629 23.5826C30.1719 23.3615 30.1662 23.3223 30.1662 23.0087L30.1633 22.6728L30.2829 22.432Z"
      fill="currentColor"
    />
  </svg>
);

export const Replicate = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M39 12.396V9H9V39H12.792V12.396H39ZM39 15.414V18.81H19.944V39H16.152V15.414H39ZM39 21.828V25.218H27.102V39H23.31V21.828H39Z"
      fill="currentColor"
    />
  </svg>
);

export const Groq = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M24.002 6C17.3837 6 12 11.3837 12 18.0014C12 24.619 17.3837 30.0034 24.002 30.0034H27.9491V25.5024H24.002C19.8658 25.5024 16.5009 22.1376 16.5009 18.0014C16.5009 13.8651 19.8658 10.5003 24.002 10.5003C28.1382 10.5003 31.5207 13.8651 31.5207 18.0014V29.0544C31.5207 33.1636 28.1747 36.5103 24.0756 36.5535C22.1143 36.5373 20.2386 35.7491 18.8554 34.3584L15.6729 37.5409C17.8788 39.7583 20.8675 41.02 23.9946 41.0537V41.0558C24.0216 41.0558 24.0486 41.0558 24.075 41.0558H24.1594V41.0537C30.6832 40.9653 35.9656 35.6471 35.9959 29.1085L36 17.7069C35.8433 11.2256 30.5211 6 24.002 6Z"
      fill="currentColor"
    />
  </svg>
);

export const Together = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <circle cx="14.5" cy="14.5" r="8.5" fill="#0F70FF" />
    <path
      d="M14.5 25C19.1944 25 23 28.8056 23 33.5C23 38.1944 19.1944 42 14.5 42C9.80558 42 6 38.1944 6 33.5C6 28.8056 9.80558 25 14.5 25ZM33.5 25C38.1944 25 42 28.8056 42 33.5C42 38.1944 38.1944 42 33.5 42C28.8056 42 25 38.1944 25 33.5C25 28.8056 28.8056 25 33.5 25ZM33.5 6C38.1944 6 42 9.80558 42 14.5C42 19.1944 38.1944 23 33.5 23C28.8056 23 25 19.1944 25 14.5C25 9.80558 28.8056 6 33.5 6Z"
      fill="#C0BFBD"
    />
  </svg>
);

export const Bedrock = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M25.75 29.855H30.8833C31.24 29.855 31.5316 30.15 31.5316 30.5117V33.545C32.1499 33.6975 32.699 34.0533 33.0907 34.5554C33.4825 35.0574 33.6941 35.6765 33.6916 36.3133C33.6916 37.885 32.4333 39.16 30.8833 39.16C29.3316 39.16 28.0733 37.885 28.0733 36.3133C28.0733 34.9683 28.9966 33.84 30.235 33.5433V31.1683H25.7516V38.94C25.7521 39.0529 25.7235 39.164 25.6686 39.2626C25.6138 39.3613 25.5345 39.4442 25.4383 39.5033L20.955 42.2383C20.8533 42.3005 20.7362 42.3332 20.617 42.3326C20.4978 42.332 20.3811 42.2982 20.28 42.235L12.07 37.0917C11.9752 37.0322 11.8972 36.9496 11.8433 36.8516C11.7893 36.7536 11.7612 36.6435 11.7616 36.5317V31.1667L7.65998 28.7833C7.5667 28.7293 7.48813 28.6531 7.43117 28.5616C7.37422 28.47 7.34061 28.3659 7.33331 28.2583V28.2133V19.6767C7.33331 19.4433 7.45498 19.2267 7.65331 19.11L11.7616 16.6733V11.4133C11.7616 11.1983 11.865 10.9983 12.0366 10.8767L12.0716 10.8533L20.2833 5.76333C20.3851 5.69996 20.5026 5.66637 20.6225 5.66637C20.7424 5.66637 20.8599 5.69996 20.9616 5.76333L25.445 8.555C25.5397 8.61446 25.6177 8.69708 25.6717 8.79506C25.7256 8.89304 25.7537 9.00315 25.7533 9.115V16.6667H32.18V13.4717C31.5614 13.3191 31.0121 12.963 30.6204 12.4607C30.2286 11.9583 30.0172 11.3387 30.02 10.7017C30.02 9.13 31.2783 7.855 32.8283 7.855C34.38 7.855 35.6366 9.13 35.6366 10.7017C35.6366 12.0467 34.715 13.175 33.4766 13.4717V17.3233C33.4773 17.409 33.461 17.4941 33.4288 17.5735C33.3966 17.6529 33.349 17.7252 33.2887 17.7862C33.2285 17.8472 33.1568 17.8957 33.0778 17.929C32.9988 17.9622 32.914 17.9796 32.8283 17.98H25.7533V20.99H36.7933C36.9351 20.3703 37.2824 19.8168 37.7786 19.4195C38.2749 19.0222 38.8909 18.8045 39.5266 18.8017C41.0766 18.8017 42.335 20.075 42.335 21.6467C42.335 23.2183 41.0783 24.4933 39.5266 24.4933C38.8907 24.4905 38.2745 24.2725 37.7782 23.8748C37.2819 23.4772 36.9348 22.9233 36.7933 22.3033H25.75V25.5317H34.005L35.53 27.4983C35.9547 27.25 36.438 27.1194 36.93 27.12C38.4816 27.12 39.7383 28.3933 39.7383 29.965C39.7383 31.5367 38.4816 32.8117 36.93 32.8117C35.38 32.8117 34.1216 31.5367 34.1216 29.965C34.1216 29.3883 34.2916 28.8517 34.5816 28.4033L33.375 26.845H25.75V29.855ZM20.6216 7.09333L17.2166 9.20333V14.15H15.92V10.0067L13.0583 11.7817V16.69L16.575 18.9567L20.1866 16.6833V12.89H21.4833V17.05C21.4833 17.2767 21.3666 17.4883 21.1766 17.6083L17.2716 20.0633V23.5317L19.6416 25.2133L18.8983 26.29L16.555 24.6267L14.005 26.3017L13.3 25.2017L15.975 23.4433V20.1267L12.3966 17.8167L8.62998 20.05V22.8433L11.9166 20.8617L12.58 21.99L8.62998 24.3717V27.8333L12.24 29.93L16.0233 27.65L16.685 28.7783L13.0583 30.9633V36.1667L16.185 38.125L20.1266 35.7483L20.79 36.8783L17.4283 38.905L20.625 40.9067L24.455 38.5683V28.935L16.525 33.7567L15.8583 32.6317L24.455 27.405V9.48166L20.6216 7.09333ZM30.8833 34.7833C30.6832 34.7844 30.4853 34.825 30.3009 34.9027C30.1164 34.9803 29.9492 35.0937 29.8086 35.2361C29.668 35.3785 29.5569 35.5473 29.4817 35.7327C29.4064 35.9181 29.3684 36.1166 29.37 36.3167C29.37 37.1617 30.0466 37.8467 30.8833 37.8467C31.0831 37.8454 31.2808 37.8047 31.4649 37.727C31.649 37.6493 31.816 37.5361 31.9564 37.3939C32.0967 37.2517 32.2077 37.0832 32.283 36.8981C32.3582 36.713 32.3963 36.5148 32.395 36.315C32.3965 36.115 32.3586 35.9167 32.2835 35.7314C32.2083 35.5461 32.0974 35.3774 31.957 35.235C31.8166 35.0926 31.6495 34.9793 31.4653 34.9015C31.281 34.8237 31.0833 34.7846 30.8833 34.7833ZM36.9333 28.4333C36.7332 28.4344 36.5353 28.475 36.3509 28.5527C36.1664 28.6303 35.9992 28.7437 35.8586 28.8861C35.718 29.0285 35.6069 29.1973 35.5317 29.3827C35.4564 29.5681 35.4184 29.7666 35.42 29.9667C35.42 30.8133 36.0966 31.5 36.9316 31.5C37.1318 31.4989 37.3297 31.4584 37.5141 31.3807C37.6985 31.303 37.8658 31.1897 38.0064 31.0472C38.1469 30.9048 38.258 30.7361 38.3333 30.5506C38.4086 30.3652 38.4465 30.1668 38.445 29.9667C38.4465 29.7666 38.4086 29.5681 38.3333 29.3827C38.258 29.1973 38.1469 29.0285 38.0064 28.8861C37.8658 28.7437 37.6985 28.6303 37.5141 28.5527C37.3297 28.475 37.1334 28.4344 36.9333 28.4333ZM39.525 20.1167C39.3249 20.1178 39.1269 20.1583 38.9425 20.236C38.7581 20.3137 38.5908 20.427 38.4502 20.5694C38.3097 20.7118 38.1986 20.8806 38.1233 21.066C38.0481 21.2514 38.0101 21.4499 38.0116 21.65C38.0116 22.495 38.69 23.18 39.525 23.18C39.7248 23.1787 39.9224 23.138 40.1065 23.0603C40.2907 22.9827 40.4577 22.8695 40.598 22.7272C40.7384 22.585 40.8494 22.4165 40.9247 22.2314C40.9999 22.0463 41.038 21.8482 41.0366 21.6483C41.0382 21.4484 41.0003 21.2501 40.9251 21.0647C40.85 20.8794 40.739 20.7107 40.5986 20.5683C40.4582 20.4259 40.2912 20.3126 40.1069 20.2348C39.9227 20.157 39.725 20.1163 39.525 20.115V20.1167ZM32.8266 9.16666C32.6267 9.16797 32.4289 9.20868 32.2447 9.28647C32.0605 9.36426 31.8934 9.47759 31.753 9.62C31.6126 9.76241 31.5017 9.93109 31.4265 10.1164C31.3513 10.3017 31.3134 10.5 31.315 10.7C31.315 11.5467 31.9916 12.2333 32.8266 12.2333C33.0268 12.2322 33.2247 12.1917 33.4091 12.114C33.5935 12.0363 33.7608 11.923 33.9014 11.7806C34.042 11.6382 34.153 11.4694 34.2283 11.284C34.3036 11.0986 34.3415 10.9001 34.34 10.7C34.3415 10.4999 34.3036 10.3014 34.2283 10.116C34.153 9.9306 34.042 9.76184 33.9014 9.61941C33.7608 9.47698 33.5935 9.36368 33.4091 9.28599C33.2247 9.2083 33.0268 9.16775 32.8266 9.16666Z"
      fill="url(#paint0_linear_23_37)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_23_37"
        x1="2807.47"
        y1="738.991"
        x2="609.865"
        y2="2836.83"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#6350FB" />
        <stop offset="0.5" stopColor="#3D8FFF" />
        <stop offset="1" stopColor="#9AD8F8" />
      </linearGradient>
    </defs>
  </svg>
);

export const Lepton = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M40.5035 26.7012V21.2988C40.5035 20.3341 40.5035 19.8035 40.4541 19.4176C40.4541 19.2247 40.4047 19.1282 40.4047 19.08C40.3553 18.9353 40.3059 18.8388 40.2071 18.7424C40.1576 18.7424 40.1082 18.6459 39.9106 18.5494C39.5647 18.3082 39.12 18.0671 38.28 17.5847L33.4871 14.8835C32.6471 14.4012 32.1529 14.16 31.8071 13.9671C31.6094 13.8706 31.5106 13.8224 31.5106 13.8224C31.3623 13.7741 31.2141 13.7741 31.0659 13.8224C31.0165 13.8224 30.9176 13.8706 30.7694 13.9671C30.4235 14.16 29.9294 14.4012 29.0894 14.8835L24.2965 17.5847C23.4565 18.0671 22.9623 18.3082 22.6659 18.5494C22.5176 18.6459 22.4188 18.7424 22.3694 18.7424C22.2706 18.8388 22.2212 18.9835 22.1718 19.08C22.1718 19.1282 22.1718 19.2247 22.1223 19.4176C22.1223 19.8035 22.0729 20.3341 22.0729 21.2988V26.7012C22.0729 27.6659 22.0729 28.1965 22.1223 28.5824C22.1223 28.7753 22.1718 28.8718 22.1718 28.92C22.2212 29.0647 22.2706 29.1612 22.3694 29.2576C22.4188 29.2576 22.4682 29.3541 22.6659 29.4506C23.0118 29.6918 23.4565 29.9329 24.2965 30.4153L29.0894 33.1165C29.9294 33.5988 30.4235 33.84 30.7694 34.0329C30.9671 34.1294 31.0659 34.1776 31.0659 34.1776C31.2141 34.2259 31.3623 34.2259 31.5106 34.1776C31.56 34.1776 31.6588 34.1294 31.8071 34.0329C32.1529 33.84 32.6471 33.5988 33.4871 33.1165L38.28 30.4153C39.12 29.9329 39.6141 29.6918 39.9106 29.4506C40.0588 29.3541 40.1576 29.2576 40.2071 29.2576C40.3059 29.1612 40.3553 29.0165 40.4047 28.92C40.4047 28.8718 40.4047 28.7753 40.4541 28.5824C40.5035 28.1965 40.5035 27.6659 40.5035 26.7012ZM43.4682 15.8482C42.8753 15.2212 42.0847 14.7388 40.5035 13.8706L35.7106 11.1694C34.1294 10.2529 33.2894 9.77059 32.4494 9.62588C31.7082 9.48118 30.9176 9.48118 30.1765 9.62588C29.3365 9.81882 28.5459 10.2529 26.9153 11.1694L22.1223 13.8706C20.5412 14.7871 19.7012 15.2212 19.1576 15.8482C18.6635 16.4271 18.2682 17.0541 18.0212 17.7776C17.7741 18.5976 17.7741 19.5141 17.7741 21.2988V26.7012C17.7741 28.5341 17.7741 29.4024 18.0212 30.2224C18.2682 30.9459 18.6635 31.6212 19.1576 32.1518C19.7506 32.7788 20.5412 33.2612 22.1223 34.1294L26.9153 36.8306C28.4965 37.7471 29.3365 38.1812 30.1765 38.3741C30.9176 38.5188 31.7082 38.5188 32.4494 38.3741C33.2894 38.1812 34.08 37.7471 35.7106 36.8306L40.5035 34.1294C42.0847 33.2129 42.9247 32.7788 43.4682 32.1518C43.9623 31.5729 44.3576 30.9459 44.6047 30.2224C44.8518 29.4024 44.8518 28.4859 44.8518 26.7012V21.2988C44.8518 19.4659 44.8518 18.5976 44.6047 17.7776C44.3576 17.0541 43.9623 16.3788 43.4682 15.8482Z"
      fill="#2D9CDB"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M25.8776 26.7012V21.2988C25.8776 20.3341 25.8776 19.8035 25.8282 19.4176C25.8282 19.2247 25.7788 19.1282 25.7788 19.08C25.7294 18.9353 25.68 18.8388 25.5812 18.7424C25.5318 18.7424 25.4823 18.6459 25.2847 18.5494C24.9388 18.3082 24.4941 18.0671 23.6541 17.5847L18.8612 14.8835C18.0212 14.4012 17.527 14.16 17.1812 13.9671C16.9835 13.8706 16.8847 13.8224 16.8847 13.8224C16.7365 13.7741 16.5882 13.7741 16.44 13.8224C16.3906 13.8224 16.2918 13.8706 16.1435 13.9671C15.7976 14.16 15.3035 14.4012 14.4635 14.8835L9.67058 17.5847C8.83058 18.0671 8.33646 18.3082 8.03999 18.5494C7.89175 18.6459 7.79293 18.7424 7.74352 18.7424C7.64469 18.8388 7.59528 18.9835 7.54587 19.08C7.54587 19.1282 7.54587 19.2247 7.49646 19.4176C7.49646 19.8035 7.44705 20.3341 7.44705 21.2988V26.7012C7.44705 27.6659 7.44705 28.1965 7.49646 28.5824C7.49646 28.7753 7.54587 28.8718 7.54587 28.92C7.59528 29.0647 7.64469 29.1612 7.74352 29.2576C7.79293 29.2576 7.84234 29.3541 8.03999 29.4506C8.38587 29.6918 8.83058 29.9329 9.67058 30.4153L14.4635 33.1165C15.3035 33.5988 15.7976 33.84 16.1435 34.0329C16.3412 34.1294 16.44 34.1776 16.44 34.1776C16.5882 34.2259 16.7365 34.2259 16.8847 34.1776C16.9341 34.1776 17.0329 34.1294 17.1812 34.0329C17.527 33.84 18.0212 33.5988 18.8612 33.1165L23.6541 30.4153C24.4941 29.9329 24.9882 29.6918 25.2847 29.4506C25.4329 29.3541 25.5318 29.2576 25.5812 29.2576C25.68 29.1612 25.7294 29.0165 25.7788 28.92C25.7788 28.8718 25.7788 28.7753 25.8282 28.5824C25.8776 28.1965 25.8776 27.6659 25.8776 26.7012ZM28.8423 15.8482C28.2494 15.2212 27.4588 14.7388 25.8776 13.8706L21.0847 11.1694C19.5035 10.2529 18.7129 9.77059 17.8235 9.62588C17.0823 9.48118 16.2918 9.48118 15.5506 9.62588C14.7106 9.81882 13.92 10.2529 12.2894 11.1694L7.49646 13.8706C5.91528 14.7871 5.07528 15.2212 4.53175 15.8482C4.03764 16.4271 3.64234 17.0541 3.39528 17.7776C3.14822 18.5976 3.14822 19.5141 3.14822 21.2988V26.7012C3.14822 28.5341 3.14822 29.4024 3.39528 30.2224C3.64234 30.9459 4.03764 31.6212 4.53175 32.1518C5.12469 32.7788 5.91528 33.2612 7.49646 34.1294L12.2894 36.8306C13.8706 37.7471 14.7106 38.1812 15.5506 38.3741C16.2918 38.5188 17.0823 38.5188 17.8235 38.3741C18.6635 38.1812 19.4541 37.7471 21.0847 36.8306L25.8776 34.1294C27.4588 33.2129 28.2988 32.7788 28.8423 32.1518C29.3365 31.5729 29.7318 30.9459 29.9788 30.2224C30.2259 29.4024 30.2259 28.4859 30.2259 26.7012V21.2988C30.2259 19.4659 30.2259 18.5976 29.9788 17.7776C29.7812 17.0541 29.3859 16.3788 28.8423 15.8482Z"
      fill="#2F80ED"
    />
    <path
      d="M24 30.2706C24.0988 30.3188 24.1976 30.4153 24.3459 30.4635L28.2988 32.6824C27.7553 33.1165 27.0141 33.5024 25.8776 34.1776L24 35.2388L22.1223 34.1776C20.9859 33.5024 20.2447 33.1165 19.7012 32.6824L23.6541 30.4635C23.8023 30.3671 23.9012 30.3188 24 30.2706Z"
      fill="#2F80ED"
    />
    <path
      d="M28.2988 15.3176C27.7553 14.8835 27.0141 14.4976 25.8776 13.8224L24 12.7612L22.1223 13.8224C20.9859 14.4976 20.2447 14.8835 19.7012 15.3176L23.6541 17.5365C23.7529 17.5847 23.9012 17.6812 24 17.7294C24.0988 17.6812 24.1976 17.5847 24.3459 17.5365L28.2988 15.3176Z"
      fill="#2D9CDB"
    />
  </svg>
);

export const Fireworks = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.9671 28.8956C22.6355 28.8956 21.4404 28.095 20.9309 26.8555L14.7874 12H18.3831L23.9881 25.5894L29.5879 12H33.1836L27.0034 26.8608C26.4912 28.095 25.2988 28.8956 23.9671 28.8956ZM31.9833 35.9949C30.6569 35.9949 29.4671 35.1995 28.9523 33.9707C28.4349 32.7312 28.7027 31.3161 29.6378 30.3559L40.8268 18.8785L42.2241 22.2167L31.9806 32.7046L46.5816 32.6222L47.9789 35.9603L31.9859 36.0002L31.9806 35.9949H31.9833ZM1.39732 32.6168L0 35.9549L0.00525307 35.9496L15.9982 35.9868C17.3193 35.9868 18.5145 35.1942 19.0292 33.9627C19.5493 32.7258 19.2788 31.3054 18.3438 30.3479L7.15469 18.8705L5.75737 22.2086L15.9982 32.6992L1.39732 32.6168Z"
      fill="#5019C5"
    />
  </svg>
);

export const Cerebras = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24.4185 7.29156C14.9594 7.29156 7.29156 14.9594 7.29156 24.4185C7.29156 33.8775 14.9594 41.5454 24.4185 41.5454V44.1285C13.5328 44.1285 4.70844 35.3042 4.70844 24.4185C4.70844 13.5328 13.5328 4.70844 24.4185 4.70844V7.29156ZM33.1698 14.0637C27.4401 9.25603 18.8982 10.0031 14.0907 15.733C9.28311 21.4627 10.0302 30.0046 15.76 34.8121L14.0997 36.7909C7.27681 31.0663 6.38739 20.8952 12.1119 14.0726C17.8365 7.24974 28.0077 6.36033 34.8302 12.0848L33.1698 14.0637ZM24.4186 18.045C20.8984 18.045 18.0445 20.8989 18.0445 24.419C18.0445 27.9392 20.8984 30.7931 24.4186 30.7931V33.3763C19.4718 33.3763 15.4614 29.3658 15.4614 24.419C15.4614 19.4723 19.4718 15.4618 24.4186 15.4618V18.045Z"
      fill="#F15A29"
    />
    <path
      d="M18.0445 24.419C18.0445 20.8989 20.8984 18.045 24.4186 18.045V15.4618C19.4718 15.4618 15.4614 19.4723 15.4614 24.419C15.4614 29.3658 19.4718 33.3763 24.4186 33.3763V30.7931C20.8984 30.7931 18.0445 27.9392 18.0445 24.419Z"
      fill="#F15A29"
    />
    <path
      d="M15.6463 19.7581C18.2138 14.8938 24.2371 13.0327 29.1017 15.6003L30.3074 13.3158C24.1815 10.0825 16.5954 12.4262 13.3619 18.5524C10.1284 24.677 12.4734 32.2644 18.5983 35.4978L19.8043 33.2135C14.9407 30.646 13.079 24.6209 15.6463 19.7581Z"
      fill="#F15A29"
    />
  </svg>
);

export const xAI = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M24.6679 28.3223L39.9575 6.48661H32.5681L20.9739 23.046L24.6679 28.3223Z"
      fill="currentColor"
    />
    <path
      d="M15.4319 41.5134L19.1265 36.2371L15.4319 30.9608L8.04252 41.5134H15.4319Z"
      fill="currentColor"
    />
    <path
      d="M24.6679 41.5134H32.0573L15.4319 17.7692H8.04252L24.6679 41.5134Z"
      fill="currentColor"
    />
    <path
      d="M39.9575 9.12507L33.9046 17.7692L34.5099 41.5134H39.3522L39.9575 9.12507Z"
      fill="currentColor"
    />
  </svg>
);

export const OpenAIImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={openAIDarkSvg}
      alt="OpenAI"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={openAISvg}
      alt="OpenAI"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const AnthropicImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={anthropicDarkSvg}
      alt="Anthropic"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={anthropicSvg}
      alt="Anthropic"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const GoogleImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={googleDarkSvg}
      alt="Google"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={googleSvg}
      alt="Google"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const MetaImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={metaDarkSvg}
      alt="Meta"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={metaSvg}
      alt="Meta"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const MistralImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={mistralDarkSvg}
      alt="Mistral"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={mistralSvg}
      alt="Mistral"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const PerplexityImage = ({ size = 48 }: { size?: number }) => (
  <picture>
    <Image
      src={perplexityDarkSvg}
      alt="Perplexity"
      width={size}
      height={size}
      className="hidden dark:block"
    />
    <Image
      src={perplexitySvg}
      alt="Perplexity"
      width={size}
      height={size}
      className="dark:hidden"
    />
  </picture>
);

export const XAI = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M24.6679 28.3223L39.9575 6.48661H32.5681L20.9739 23.046L24.6679 28.3223Z"
      fill="currentColor"
    />
    <path
      d="M15.4319 41.5134L19.1265 36.2371L15.4319 30.9608L8.04252 41.5134H15.4319Z"
      fill="currentColor"
    />
    <path
      d="M24.6679 41.5134H32.0573L15.4319 17.7692H8.04252L24.6679 41.5134Z"
      fill="currentColor"
    />
    <path
      d="M39.9575 9.12507L33.9046 17.7692L34.5099 41.5134H39.3522L39.9575 9.12507Z"
      fill="currentColor"
    />
  </svg>
);

export const Amazon = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M37.7134 35.6793C22.331 43 12.7846 36.875 6.6736 33.1548C6.29545 32.9203 5.65274 33.2096 6.21038 33.8501C8.24625 36.3187 14.9182 42.2686 23.6271 42.2686C32.342 42.2686 37.5266 37.5133 38.1751 36.6838C38.8193 35.8613 38.3643 35.4076 37.7132 35.6793H37.7134ZM42.0335 33.2935C41.6204 32.7556 39.5216 32.6553 38.2008 32.8176C36.878 32.9751 34.8924 33.7836 35.065 34.269C35.1536 34.4509 35.3344 34.3693 36.2432 34.2876C37.1544 34.1967 39.7071 33.8745 40.2391 34.5699C40.7735 35.27 39.4249 38.6054 39.1786 39.1433C38.9406 39.6812 39.2695 39.8199 39.7165 39.4616C40.1573 39.1035 40.9554 38.1761 41.491 36.8636C42.0229 35.5439 42.3474 33.7031 42.0335 33.2935Z"
      fill="#FF9900"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M27.3704 20.7415C27.3704 22.6623 27.419 24.2642 26.4481 25.97C25.6645 27.357 24.4232 28.21 23.0363 28.21C21.1431 28.21 20.0405 26.7675 20.0405 24.6387C20.0405 20.4363 23.8059 19.6735 27.3704 19.6735V20.7415ZM32.3424 32.7591C32.0165 33.0503 31.5449 33.0712 31.1775 32.8769C29.5409 31.5178 29.2496 30.8868 28.3482 29.59C25.6437 32.35 23.7297 33.1751 20.2209 33.1751C16.074 33.1751 12.8425 30.6162 12.8425 25.4916C12.8425 21.4905 15.0131 18.7651 18.0988 17.4338C20.7756 16.2547 24.5134 16.0467 27.3704 15.7209V15.0829C27.3704 13.9109 27.4605 12.524 26.7741 11.5116C26.1707 10.6032 25.0196 10.2287 24.007 10.2287C22.1279 10.2287 20.4497 11.1925 20.0405 13.1897C19.9572 13.6336 19.6314 14.0705 19.1876 14.0913L14.4027 13.5782C14.0006 13.4879 13.5568 13.1621 13.6678 12.5448C14.7703 6.74762 20.0057 5 24.6934 5C27.0929 5 30.2272 5.63804 32.1204 7.45495C34.5198 9.69473 34.2909 12.6835 34.2909 15.9358V23.6193C34.2909 25.9285 35.2479 26.9409 36.1493 28.1892C36.4683 28.633 36.5377 29.1671 36.1354 29.4998C35.1299 30.3388 33.3408 31.8992 32.3562 32.773L32.3423 32.7591"
      fill="currentColor"
    />
    <path
      d="M37.7134 35.6793C22.331 43 12.7846 36.875 6.6736 33.1548C6.29545 32.9203 5.65274 33.2096 6.21038 33.8501C8.24625 36.3187 14.9182 42.2686 23.6271 42.2686C32.342 42.2686 37.5266 37.5133 38.1751 36.6838C38.8193 35.8613 38.3643 35.4076 37.7132 35.6793H37.7134ZM42.0335 33.2935C41.6204 32.7556 39.5216 32.6553 38.2008 32.8176C36.878 32.9751 34.8924 33.7836 35.065 34.269C35.1536 34.4509 35.3344 34.3693 36.2432 34.2876C37.1544 34.1967 39.7071 33.8745 40.2391 34.5699C40.7735 35.27 39.4249 38.6054 39.1786 39.1433C38.9406 39.6812 39.2695 39.8199 39.7165 39.4616C40.1573 39.1035 40.9554 38.1761 41.491 36.8636C42.0229 35.5439 42.3474 33.7031 42.0335 33.2935Z"
      fill="#FF9900"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M27.3704 20.7415C27.3704 22.6623 27.419 24.2642 26.4481 25.97C25.6645 27.357 24.4232 28.21 23.0363 28.21C21.1431 28.21 20.0405 26.7675 20.0405 24.6387C20.0405 20.4363 23.8059 19.6735 27.3704 19.6735V20.7415ZM32.3424 32.7591C32.0165 33.0503 31.5449 33.0712 31.1775 32.8769C29.5409 31.5178 29.2496 30.8868 28.3482 29.59C25.6437 32.35 23.7297 33.1751 20.2209 33.1751C16.074 33.1751 12.8425 30.6162 12.8425 25.4916C12.8425 21.4905 15.0131 18.7651 18.0988 17.4338C20.7756 16.2547 24.5134 16.0467 27.3704 15.7209V15.0829C27.3704 13.9109 27.4605 12.524 26.7741 11.5116C26.1707 10.6032 25.0196 10.2287 24.007 10.2287C22.1279 10.2287 20.4497 11.1925 20.0405 13.1897C19.9572 13.6336 19.6314 14.0705 19.1876 14.0913L14.4027 13.5782C14.0006 13.4879 13.5568 13.1621 13.6678 12.5448C14.7703 6.74762 20.0057 5 24.6934 5C27.0929 5 30.2272 5.63804 32.1204 7.45495C34.5198 9.69473 34.2909 12.6835 34.2909 15.9358V23.6193C34.2909 25.9285 35.2479 26.9409 36.1493 28.1892C36.4683 28.633 36.5377 29.1671 36.1354 29.4998C35.1299 30.3388 33.3408 31.8992 32.3562 32.773L32.3423 32.7591"
      fill="currentColor"
    />
  </svg>
);

export const Cohere = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.016 27.264C18.9227 27.264 20.736 27.2187 23.2747 26.176C26.2213 24.952 32.024 22.776 36.24 20.5093C39.1867 18.9227 40.456 16.8373 40.456 14.0267C40.456 10.1733 37.328 7 33.4293 7H17.1093C11.5333 7 7 11.5333 7 17.1093C7 22.6853 11.2613 27.264 18.016 27.264Z"
      fill="#39594D"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.7813 34.2C20.7813 31.48 22.4133 28.9867 24.952 27.944L30.0746 25.8133C35.288 23.6827 41 27.4907 41 33.112C41 37.464 37.464 41 33.112 41H27.536C23.8186 41 20.7813 37.9627 20.7813 34.2Z"
      fill="#D18EE2"
    />
    <path
      d="M12.848 28.5787C9.62933 28.5787 7 31.208 7 34.4267V35.1973C7 38.3707 9.62933 41 12.848 41C16.0667 41 18.696 38.3707 18.696 35.152V34.3813C18.6507 31.208 16.0667 28.5787 12.848 28.5787Z"
      fill="#FF7759"
    />
  </svg>
);

export const DeepSeek = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M45.5699 11.5179C45.1356 11.3063 44.9485 11.7096 44.6946 11.9144C44.6077 11.9805 44.5342 12.0664 44.4607 12.1456C43.826 12.8196 43.0844 13.2625 42.1155 13.2096C40.699 13.1302 39.4896 13.5731 38.4206 14.6503C38.1934 13.322 37.4384 12.5288 36.2892 12.0201C35.6879 11.7557 35.0799 11.4913 34.6589 10.9163C34.3649 10.5067 34.2847 10.0507 34.1377 9.60126C34.0441 9.33035 33.9506 9.05268 33.6365 9.00641C33.2958 8.9536 33.1621 9.23765 33.0285 9.47571C32.494 10.4472 32.2869 11.5178 32.3069 12.6017C32.3536 15.0403 33.3893 16.9832 35.4472 18.3645C35.681 18.5231 35.7412 18.6816 35.6677 18.913C35.5273 19.3889 35.3603 19.8514 35.2134 20.3273C35.1198 20.6313 34.9794 20.6974 34.6521 20.5651C33.5229 20.096 32.5473 19.402 31.6854 18.5628C30.2222 17.1551 28.8992 15.6021 27.2489 14.386C26.8614 14.1018 26.4738 13.8376 26.0729 13.5863C24.3891 11.9607 26.2934 10.6256 26.7344 10.4671C27.1954 10.3019 26.8947 9.73342 25.4047 9.74017C23.9147 9.74669 22.5517 10.2424 20.8145 10.9033C20.5607 11.0024 20.2933 11.075 20.0194 11.1345C18.4425 10.8372 16.8055 10.771 15.095 10.9627C11.8745 11.3195 9.30219 12.833 7.41131 15.417C5.13963 18.5231 4.60512 22.0521 5.25993 25.7332C5.94814 29.6124 7.93918 32.8242 10.9994 35.3357C14.1732 37.9394 17.828 39.2149 21.9973 38.9704C24.5296 38.825 27.3492 38.488 30.5296 35.8115C31.3314 36.2081 32.1733 36.3666 33.5698 36.4855C34.6455 36.5847 35.6812 36.4327 36.4829 36.2675C37.7391 36.0032 37.6522 34.8466 37.1979 34.6352C33.5164 32.9302 34.3247 33.624 33.5898 33.0622C35.4607 30.8616 38.2803 28.5749 39.3828 21.1665C39.4696 20.5784 39.3961 20.2082 39.3828 19.7324C39.3761 19.4417 39.4429 19.3294 39.777 19.2962C40.699 19.1905 41.5943 18.9394 42.4162 18.49C44.8015 17.1948 45.7636 15.0667 45.9908 12.5158C46.0243 12.1258 45.9841 11.7226 45.5699 11.5179ZM24.7835 34.4764C21.2155 31.6877 19.4851 30.769 18.7701 30.8087C18.1019 30.8483 18.2222 31.6084 18.3691 32.1041C18.5228 32.593 18.7233 32.9302 19.0039 33.3596C19.1976 33.6438 19.3314 34.0667 18.8102 34.384C17.6609 35.0912 15.6631 34.1461 15.5695 34.0998C13.2444 32.7384 11.3001 30.9407 9.93029 28.4825C8.60734 26.1164 7.83902 23.5788 7.71201 20.8692C7.6786 20.215 7.87236 19.9835 8.52716 19.8647C9.38907 19.7061 10.2778 19.6729 11.1397 19.7985C14.7812 20.3273 17.8814 21.9464 20.4805 24.5105C21.9638 25.971 23.0863 27.7158 24.2422 29.4208C25.4716 31.2315 26.7946 32.9566 28.4783 34.3708C29.073 34.8664 29.5474 35.2431 30.0017 35.5207C28.632 35.6727 26.3469 35.7058 24.7835 34.4764ZM26.4939 23.5391C26.4939 23.2483 26.7278 23.0171 27.0219 23.0171C27.0887 23.0171 27.1488 23.0301 27.2023 23.0499C27.2758 23.0765 27.3426 23.1161 27.396 23.1755C27.4896 23.2681 27.543 23.4002 27.543 23.539C27.543 23.8298 27.3092 24.061 27.0153 24.061C26.7214 24.061 26.4939 23.8299 26.4939 23.5391ZM31.8058 26.2487C31.4651 26.3875 31.1243 26.5063 30.7969 26.5196C30.2891 26.546 29.7345 26.3411 29.4339 26.09C28.9662 25.7 28.6321 25.4821 28.4918 24.8013C28.4317 24.5105 28.4651 24.0611 28.5186 23.8035C28.6388 23.2483 28.5052 22.8915 28.111 22.5676C27.7903 22.3032 27.3827 22.2306 26.935 22.2306C26.7679 22.2306 26.6144 22.1578 26.5007 22.0983C26.3136 22.0058 26.16 21.7745 26.307 21.4903C26.3537 21.3979 26.581 21.1732 26.6344 21.1335C27.2425 20.7898 27.9441 20.9022 28.5921 21.16C29.1935 21.4045 29.6479 21.8539 30.3026 22.4883C30.9708 23.2548 31.091 23.4665 31.4719 24.0413C31.7726 24.4907 32.0466 24.9533 32.2336 25.4821C32.3472 25.8125 32.2001 26.0835 31.8058 26.2487Z"
      fill="#4D6BFE"
    />
  </svg>
);

export const Qwen = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M24.4815 6.17749C25.1201 7.29874 25.7555 8.42324 26.3892 9.54937C26.4149 9.5944 26.452 9.63183 26.4968 9.65781C26.5416 9.68379 26.5926 9.6974 26.6444 9.69724H35.6664C35.9491 9.69724 36.1896 9.87599 36.3911 10.2286L38.7539 14.4049C39.0626 14.9525 39.1439 15.1816 38.7929 15.765C38.3704 16.4637 37.9592 17.169 37.5579 17.8775L36.9615 18.9467C36.7892 19.2652 36.5991 19.4017 36.8965 19.7787L41.206 27.3139C41.4855 27.803 41.3864 28.1166 41.1361 28.5651C40.426 29.8407 39.7029 31.1066 38.9667 32.3676C38.7084 32.8096 38.3947 32.977 37.8617 32.9689C36.5991 32.9429 35.3397 32.9526 34.0804 32.9949C34.0534 32.9963 34.0272 33.0044 34.0042 33.0186C33.9811 33.0328 33.9621 33.0526 33.9487 33.0761C32.4957 35.6506 31.0305 38.2181 29.5531 40.7786C29.2785 41.2547 28.9356 41.3685 28.375 41.3701C26.7549 41.375 25.1217 41.3766 23.4724 41.3734C23.3188 41.3729 23.1681 41.332 23.0354 41.2547C22.9027 41.1774 22.7928 41.0664 22.7167 40.933L20.5474 37.1581C20.5348 37.1334 20.5154 37.1128 20.4915 37.0987C20.4676 37.0846 20.4402 37.0776 20.4125 37.0785H12.0957C11.6326 37.1272 11.1971 37.0769 10.7876 36.929L8.18275 32.4277C8.10571 32.2945 8.06488 32.1434 8.06431 31.9894C8.06374 31.8355 8.10345 31.6841 8.1795 31.5502L10.1409 28.1052C10.1688 28.0565 10.1835 28.0013 10.1835 27.9452C10.1835 27.889 10.1688 27.8338 10.1409 27.7851C9.11919 26.0163 8.10356 24.2439 7.094 22.4681L5.81025 20.2012C5.55025 19.6975 5.52912 19.3952 5.96462 18.6331C6.72025 17.312 7.471 15.9925 8.2185 14.6746C8.433 14.2944 8.7125 14.1319 9.1675 14.1302C10.5699 14.1243 11.9723 14.1238 13.3746 14.1286C13.41 14.1283 13.4448 14.1187 13.4753 14.1008C13.5058 14.0828 13.5311 14.0571 13.5485 14.0262L18.1082 6.07187C18.1774 5.95086 18.2771 5.8502 18.3975 5.78002C18.5179 5.70984 18.6547 5.67262 18.794 5.67212C19.6455 5.67049 20.5051 5.67212 21.3664 5.66237L23.019 5.62499C23.5731 5.62012 24.1955 5.67699 24.4815 6.17749ZM18.9045 6.83237C18.8874 6.83236 18.8705 6.83686 18.8557 6.84542C18.8409 6.85397 18.8286 6.86629 18.82 6.88112L14.1627 15.0305C14.1404 15.0689 14.1084 15.1008 14.0699 15.123C14.0314 15.1453 13.9878 15.1571 13.9434 15.1572H9.28612C9.19512 15.1572 9.17237 15.1979 9.2195 15.2775L18.6607 31.781C18.7014 31.8492 18.6819 31.8817 18.6055 31.8834L14.0636 31.9077C13.9972 31.9055 13.9315 31.922 13.8741 31.9554C13.8166 31.9887 13.7697 32.0375 13.7386 32.0962L11.5936 35.85C11.5221 35.9767 11.5595 36.0417 11.7041 36.0417L20.9926 36.0547C21.0674 36.0547 21.1226 36.0872 21.1616 36.1539L23.4415 40.1416C23.5162 40.2732 23.591 40.2749 23.6674 40.1416L31.8021 25.9066L33.0745 23.6609C33.0823 23.647 33.0936 23.6354 33.1073 23.6274C33.121 23.6194 33.1366 23.6151 33.1525 23.6151C33.1684 23.6151 33.184 23.6194 33.1977 23.6274C33.2114 23.6354 33.2227 23.647 33.2305 23.6609L35.5445 27.7721C35.5619 27.8029 35.5872 27.8284 35.6177 27.8462C35.6483 27.8639 35.683 27.8731 35.7184 27.8729L40.2082 27.8404C40.2198 27.8405 40.2311 27.8375 40.2411 27.8318C40.2511 27.8261 40.2594 27.8178 40.2651 27.8079C40.2707 27.7979 40.2736 27.7867 40.2736 27.7754C40.2736 27.764 40.2707 27.7528 40.2651 27.7429L35.5526 19.4781C35.5357 19.4505 35.5267 19.4187 35.5267 19.3863C35.5267 19.3539 35.5357 19.3221 35.5526 19.2945L36.0287 18.4706L37.8487 15.258C37.8877 15.1914 37.8682 15.1572 37.7919 15.1572H18.95C18.8541 15.1572 18.8314 15.115 18.8801 15.0321L21.2104 10.9615C21.2278 10.9338 21.2371 10.9016 21.2371 10.8689C21.2371 10.8361 21.2278 10.804 21.2104 10.7762L18.9906 6.88274C18.9821 6.86737 18.9697 6.85457 18.9545 6.84571C18.9393 6.83684 18.9221 6.83223 18.9045 6.83237ZM29.1257 19.8649C29.2005 19.8649 29.22 19.8974 29.181 19.9624L27.829 22.343L23.5829 29.7936C23.5749 29.8081 23.5631 29.8202 23.5488 29.8285C23.5345 29.8368 23.5182 29.841 23.5016 29.8407C23.4851 29.8407 23.469 29.8363 23.4547 29.828C23.4405 29.8198 23.4286 29.8079 23.4204 29.7936L17.8092 19.9916C17.7767 19.9364 17.793 19.9071 17.8547 19.9039L18.2057 19.8844L29.129 19.8649H29.1257Z"
      fill="currentColor"
    />
  </svg>
);

export const Gemini = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M24 43C23.2758 38.2197 21.0408 33.7967 17.6221 30.3779C14.2033 26.9592 9.78028 24.7242 5 24C9.78028 23.2758 14.2033 21.0408 17.6221 17.6221C21.0408 14.2033 23.2758 9.78028 24 5C24.7245 9.78018 26.9595 14.2031 30.3782 17.6218C33.7969 21.0405 38.2198 23.2755 43 24C38.2198 24.7245 33.7969 26.9595 30.3782 30.3782C26.9595 33.7969 24.7245 38.2198 24 43Z"
      fill="url(#paint0_linear_23_2)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_23_2"
        x1="4.99992"
        y1="3805"
        x2="2616.74"
        y2="1160.01"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#1C7DFF" />
        <stop offset="0.52021" stopColor="#1C69FF" />
        <stop offset="1" stopColor="#F0DCD6" />
      </linearGradient>
    </defs>
  </svg>
);

export const Nvidia = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <g clipPath="url(#clip0_28_2)">
      <path
        d="M43.7208 38.0508C43.7208 38.488 43.4057 38.7814 43.0326 38.7814V38.7787C42.649 38.7814 42.3396 38.4879 42.3396 38.0509C42.3396 37.614 42.649 37.3214 43.0326 37.3214C43.4058 37.3213 43.7208 37.6139 43.7208 38.0508ZM44 38.0508C44 37.4509 43.5425 37.1026 43.0326 37.1026C42.5192 37.1026 42.0617 37.4509 42.0617 38.0508C42.0617 38.6504 42.5193 39 43.0326 39C43.5426 39 44 38.6504 44 38.0508ZM42.8722 38.131H42.9757L43.2157 38.5603H43.4793L43.2135 38.1129C43.3508 38.103 43.4639 38.0363 43.4639 37.8479C43.4639 37.6139 43.3054 37.5386 43.0371 37.5386H42.6489V38.5603H42.872V38.131M42.8722 37.9581V37.7121H43.0274C43.1118 37.7121 43.2269 37.719 43.2269 37.8239C43.2269 37.9381 43.1674 37.9581 43.0678 37.9581H42.8722Z"
        fill="currentColor"
      />
      <path
        d="M37.4852 32.8246L38.6914 36.1857H36.2417L37.4852 32.8246ZM36.1935 31.5159L33.4139 38.6894H35.3767L35.8163 37.4219H39.1058L39.5219 38.6894H41.6527L38.8523 31.515L36.1935 31.5159ZM30.613 38.6921H32.6043V31.5137L30.6126 31.5133L30.613 38.6921ZM16.7887 31.5133L15.1273 37.2027L13.5358 31.5137L11.3871 31.5133L13.6602 38.6921H16.529L18.8204 31.5133H16.7887ZM24.838 33.0764H25.6939C26.9355 33.0764 27.7386 33.6442 27.7386 35.1178C27.7386 36.5916 26.9355 37.1596 25.6939 37.1596H24.838V33.0764ZM22.8634 31.5133V38.6921H26.0917C27.8118 38.6921 28.3734 38.4009 28.9807 37.7475C29.4097 37.2888 29.6871 36.2821 29.6871 35.1816C29.6871 34.1723 29.4523 33.2725 29.043 32.7119C28.3054 31.7093 27.2428 31.5133 25.6571 31.5133H22.8634ZM4 31.5033V38.6921H6.00819V33.2336L7.56422 33.2341C8.07944 33.2341 8.44671 33.3648 8.69482 33.6348C9.0095 33.9762 9.138 34.5273 9.138 35.5354V38.6921H11.0839V34.7202C11.0839 31.8855 9.31008 31.5033 7.57446 31.5033H4ZM19.6585 31.5133L19.6593 38.6921H21.6497V31.5133H19.6585Z"
        fill="currentColor"
      />
      <path
        d="M9.35173 17.2194C9.35173 17.2194 11.9129 13.3703 17.0268 12.9721V11.5757C11.3625 12.039 6.45752 16.9255 6.45752 16.9255C6.45752 16.9255 9.23564 25.1059 17.0268 25.8549V24.3706C11.3093 23.6379 9.35173 17.2194 9.35173 17.2194ZM17.0268 21.4186V22.778C12.7056 21.9933 11.5062 17.4179 11.5062 17.4179C11.5062 17.4179 13.5809 15.0767 17.0268 14.6971V16.1887C17.0242 16.1887 17.0224 16.1879 17.0202 16.1879C15.2116 15.9668 13.7988 17.6878 13.7988 17.6878C13.7988 17.6878 14.5907 20.5849 17.0268 21.4186ZM17.0268 9V11.5757C17.1931 11.5627 17.3594 11.5517 17.5265 11.5459C23.9662 11.3248 28.1621 16.9255 28.1621 16.9255C28.1621 16.9255 23.343 22.8944 18.3223 22.8944C17.8622 22.8944 17.4314 22.8509 17.0268 22.7778V24.3706C17.3728 24.4153 17.7315 24.4416 18.1058 24.4416C22.7779 24.4416 26.1566 22.0112 29.4282 19.1347C29.9706 19.5772 32.1913 20.6537 32.6478 21.125C29.5371 23.7777 22.2875 25.9161 18.1774 25.9161C17.7813 25.9161 17.4008 25.8916 17.0268 25.8549V28.093H34.785V9H17.0268ZM17.0268 14.6971V12.9721C17.1914 12.9604 17.3572 12.9515 17.5265 12.946C22.1573 12.7979 25.1954 16.9994 25.1954 16.9994C25.1954 16.9994 21.914 21.6414 18.3956 21.6414C17.8893 21.6414 17.4354 21.5585 17.0268 21.4186V16.1887C18.8296 16.4106 19.1923 17.2217 20.2761 19.0621L22.6866 16.9917C22.6866 16.9917 20.927 14.6411 17.9607 14.6411C17.6383 14.641 17.3298 14.664 17.0268 14.6971Z"
        fill="#77B900"
      />
    </g>
    <defs>
      <clipPath id="clip0_28_2">
        <rect width="40" height="30" fill="white" transform="translate(4 9)" />
      </clipPath>
    </defs>
  </svg>
);

export const Microsoft = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <path
      d="M5.73914 5.73914H23.1304V23.1304H5.73914V5.73914Z"
      fill="#F35325"
    />
    <path
      d="M24.8696 5.73914H42.2609V23.1304H24.8696V5.73914Z"
      fill="#81BC06"
    />
    <path
      d="M5.73914 24.8696H23.1304V42.2609H5.73914V24.8696Z"
      fill="#05A6F0"
    />
    <path
      d="M24.8696 24.8696H42.2609V42.2609H24.8696V24.8696Z"
      fill="#FFBA08"
    />
  </svg>
);

export const Databricks = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <g clipPath="url(#clip0_28_22)">
      <path
        d="M44.1 34.1V26.7L43.3 26.2L24.1 36.7L5.9 26.7V22.4L24.1 32.3L44.2 21.4V14.1L43.4 13.6L24.1 24.2L6.6 14.6L24.1 5L38.2 12.7L39.3 12.1V11.3L24.1 3L4 13.9V15L24.1 26L42.3 16V20.4L24.1 30.4L4.8 19.8L4 20.3V27.7L24.1 38.6L42.3 28.7V33L24.1 43L4.8 32.5L4 33V34.1L24.1 45L44.1 34.1Z"
        fill="#FF3621"
      />
    </g>
    <defs>
      <clipPath id="clip0_28_22">
        <rect
          width="40.1"
          height="42"
          fill="white"
          transform="translate(4 3)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const GoogleCloud = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" fill="none">
    <g clipPath="url(#clip0_37_33)">
      <path
        d="M29.455 16.805L30.7362 16.8269L34.2175 13.3456L34.3863 11.8706C31.625 9.41063 27.9794 7.91251 23.9956 7.91251C16.7831 7.91251 10.6956 12.82 8.89375 19.4675C9.27375 19.2025 10.0862 19.4013 10.0862 19.4013L17.0425 18.2575C17.0425 18.2575 17.4 17.6656 17.5812 17.695C18.3946 16.8014 19.3858 16.0875 20.4911 15.5992C21.5964 15.1109 22.7916 14.8589 24 14.8594C26.0625 14.8638 27.9625 15.5925 29.455 16.7981V16.805Z"
        fill="#EA4335"
      />
      <path
        d="M39.1062 19.4813C38.2981 16.5 36.6325 13.8675 34.3844 11.8706L29.455 16.8C31.43 18.3938 32.6969 20.8325 32.6969 23.5625V24.4325C35.095 24.4325 37.0431 26.385 37.0431 28.7788C37.0431 31.1769 35.0906 33.125 32.6969 33.125H24.0044L23.1344 34V39.2163L24.0044 40.0819H32.6969C35.6933 40.0778 38.5658 38.8857 40.6846 36.7669C42.8034 34.6482 43.9957 31.7758 44 28.7794C43.9956 24.9325 42.0625 21.5288 39.1062 19.4813Z"
        fill="#4285F4"
      />
      <path
        d="M15.3031 40.0875H23.9906V33.125H15.3031C14.6859 33.1251 14.0759 32.9925 13.5144 32.7362L12.26 33.1206L8.77875 36.6019L8.47375 37.7769C10.4356 39.2735 12.835 40.0836 15.3025 40.0825L15.3031 40.0875Z"
        fill="#34A853"
      />
      <path
        d="M15.3031 17.4813C12.3067 17.4854 9.43423 18.6775 7.31539 20.7962C5.19656 22.9149 4.0043 25.7874 4 28.7838C4 32.4544 5.75812 35.7181 8.47875 37.7856L13.5187 32.7456C12.7559 32.4016 12.1084 31.8449 11.654 31.1422C11.1996 30.4395 10.9576 29.6206 10.9569 28.7838C10.9569 26.3856 12.9094 24.4375 15.3031 24.4375C17.0656 24.4375 18.5806 25.5 19.2656 27L24.3056 21.96C22.2387 19.2394 18.9744 17.4813 15.3038 17.4813H15.3031Z"
        fill="#FBBC05"
      />
    </g>
    <defs>
      <clipPath id="clip0_37_33">
        <rect width="40" height="40" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
);
