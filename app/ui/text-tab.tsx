import { cn } from "#/utils/classnames";
import { TabHeader } from "#/app/app/[org]/clientpage";

export function TextTabs<T extends string>({
  options,
  selected,
  setSelected,
  textSize = "text-sm",
}: {
  options: T[] | readonly T[];
  selected: T;
  setSelected: (option: T) => void;
  textSize?: "text-sm" | "text-base" | "text-lg";
}) {
  return (
    <div className={cn("flex flex-row gap-x-5 font-inter", textSize)}>
      {options.map((option) => (
        <TabHeader
          key={option}
          onClick={() => setSelected(option)}
          isSelected={option == selected}
          label={option}
        />
      ))}
    </div>
  );
}

export function TextTabContent({
  children,
  selected,
  className,
  preRender,
}: {
  children: React.ReactNode;
  selected?: boolean;
  className?: string;
  preRender?: boolean;
}) {
  if (!selected && !preRender) {
    return null;
  }
  return (
    <div className={cn("mt-4 flex flex-col", !selected && "hidden", className)}>
      {children}
    </div>
  );
}
