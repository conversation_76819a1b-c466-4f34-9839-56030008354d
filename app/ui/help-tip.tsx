import { cn } from "#/utils/classnames";
import { useState } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { HelpCircle } from "lucide-react";
import { TooltipPortal } from "@radix-ui/react-tooltip";

export function HelpTip({
  children,
  help,
  side = "bottom",
  align,
  hide = false,
  className,
}: {
  children?: React.ReactNode;
  help: React.ReactNode | string;
  side?: "top" | "bottom" | "left" | "right";
  align?: "start" | "center" | "end";
  hide?: boolean;
  className?: string;
}) {
  const [openedState, setOpenedState] = useState<
    "delayed-open" | "instant-open" | "closed"
  >("closed");

  if (hide) {
    return <>{children}</>;
  }

  return (
    <div className="flex flex-row gap-1">
      <div className={cn("flex", className)}>{children}</div>
      <div className="flex items-center">
        <Tooltip
          open={openedState == "delayed-open" || openedState == "instant-open"}
          onOpenChange={(open) => {
            if (open) {
              setOpenedState("delayed-open");
            } else {
              setOpenedState("closed");
            }
          }}
        >
          <TooltipTrigger asChild>
            {/*
              For some reason I don't have time to figure out right now,
              using a function component here causes:
                https://github.com/radix-ui/primitives/issues/1240
              */}
            <button
              className="flex h-full min-h-[1em] min-w-[1em] cursor-help items-center justify-center px-1"
              onClick={() => {
                setOpenedState("instant-open");
              }}
            >
              <HelpCircle className="size-3 text-primary-400" />
            </button>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent
              side={side}
              align={align}
              className="max-w-lg px-[18px] py-5 text-sm font-normal normal-case bg-background text-primary-800"
            >
              {help}
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </div>
    </div>
  );
}

export function GreenHelpIcon({ className }: { className?: string }) {
  return (
    <HelpCircle className={cn("ml-1 h-4 w-4", className)} color="#04BF9D" />
  );
}
