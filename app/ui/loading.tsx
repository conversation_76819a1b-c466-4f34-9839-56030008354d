import { cn } from "#/utils/classnames";
import { useEffect, useState } from "react";
import { Spinner } from "./icons/spinner";

export function Loading({
  className,
  instant,
}: {
  className?: string;
  instant?: boolean;
}) {
  const [active, setActive] = useState(!!instant);
  useEffect(() => {
    const timeoutId = setTimeout(() => setActive(true), 250);
    return () => clearTimeout(timeoutId);
  }, []);
  return active ? (
    <div
      style={{ width: "100%" }}
      className={cn(
        "flex items-center justify-center align-center h-full self-center m-auto text-primary-500",
        className,
      )}
    >
      <Spinner />
    </div>
  ) : null;
}
