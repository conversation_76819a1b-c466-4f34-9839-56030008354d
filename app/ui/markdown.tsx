import { cn } from "#/utils/classnames";
import Markdown, {
  type Options,
  type Components,
  defaultUrlTransform,
} from "react-markdown";
import remarkGfm from "remark-gfm";
import { SyntaxHighlight } from "./syntax-highlighter";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";
import { Copy } from "lucide-react";
import { memo, useMemo } from "react";
import { type BundledLanguage } from "./highlight";
import { isJSON } from "#/utils/is-json";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { externalLinks } from "#/lib/external-links";

const DEBOUNCE_DELAY = 300;
const LARGE_CONTENT_THRESHOLD = 100 * 1024; // 100KB

export const MD_PROSE_CLASS_NAMES =
  "prose prose-sm prose-headings:font-semibold max-w-full break-words py-2 font-inter leading-snug dark:prose-invert prose-pre:p-0 prose-ol:list-decimal prose-ul:list-disc prose-a:underline prose-a:font-medium prose-zinc prose-a:underline-offset-2 hover:prose-a:text-accent-600 prose-hr:my-4 prose-pre:m-0 prose-code:text-xs prose-code:whitespace-pre-wrap";

const CodeRenderer: Components["code"] = memo(
  ({ children, className, node: _node, ref: _ref, ...rest }) => {
    const match = useMemo(
      () => /language-(\w+)/.exec(className || ""),
      [className],
    );
    const value = useMemo(
      () => String(children).replace(/\n$/, ""),
      [children],
    );

    if (children === undefined) {
      return null;
    }

    return match ? (
      <div className="group/codeblock mb-3 flex flex-col rounded-md border bg-primary-50">
        <div className="flex flex-none items-center justify-between pl-3 pr-2 pt-2">
          <span className="font-inter text-[10px] uppercase tracking-wide text-primary-400">
            {match[1]}
          </span>
          <CopyToClipboardButton
            textToCopy={value}
            size="xs"
            variant="ghost"
            className=" opacity-0 transition-opacity text-primary-500 group-hover/codeblock:opacity-100"
          >
            <Copy className="size-3" />
          </CopyToClipboardButton>
        </div>
        <SyntaxHighlight
          {...rest}
          className="px-3 pb-2 bg-transparent"
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          language={match[1] as BundledLanguage}
          content={value}
        />
      </div>
    ) : (
      <code {...rest} className={className}>
        {children}
      </code>
    );
  },
);
CodeRenderer.displayName = "CodeRenderer";

const CodeRendererWithoutHighlighting: Components["code"] = memo(
  ({ children, className, node: _node, ref: _ref, ...rest }) => {
    const isCodeBlock = useMemo(
      () => /language-(\w+)/.exec(className || ""),
      [className],
    );

    if (!isCodeBlock) {
      return (
        <code {...rest} className={className}>
          {children}
        </code>
      );
    }

    return (
      <span
        {...rest}
        className={cn(
          className,
          "border rounded-md !p-2.5 font-mono block border-primary-200/80 whitespace-pre-wrap text-primary-600",
        )}
      >
        {children}
      </span>
    );
  },
);

CodeRendererWithoutHighlighting.displayName = "CodeRendererWithoutHighlighting";

const REHYPE_PLUGINS: Options["rehypePlugins"] = [remarkGfm, externalLinks];

const REHYPE_PLUGINS_LIGHT: Options["rehypePlugins"] = [externalLinks];

const COMPONENTS = {
  code: CodeRenderer,
};

const COMPONENTS_WITHOUT_HIGHLIGHTING = {
  code: CodeRendererWithoutHighlighting,
};

const checkContentSize = (
  content: string,
): {
  isLarge: boolean;
  size: number;
  error?: string;
} => {
  const size = new Blob([content]).size;

  return {
    isLarge: size > LARGE_CONTENT_THRESHOLD,
    size,
  };
};

function MarkdownViewerComponent({
  value,
  disableHighlighting,
  className,
  components: customComponents,
  urlTransform: customUrlTransform,
}: {
  value: string;
  disableHighlighting?: boolean;
  className?: string;
  components?: Partial<Components>;
  urlTransform?: Options["urlTransform"];
}) {
  const contentInfo = useMemo(() => checkContentSize(value), [value]);

  const debouncedValue = useDebounce(
    value,
    contentInfo.isLarge ? DEBOUNCE_DELAY : 0,
  );

  const processedValue = contentInfo.isLarge ? debouncedValue : value;

  const jsonString = useMemo(() => {
    if (!isJSON(processedValue)) {
      return;
    }
    let jsonString = processedValue.trim();
    try {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      jsonString = JSON.stringify(JSON.parse(jsonString as string), null, 2);
      return `\`\`\`json\n${jsonString}\n\`\`\``;
    } catch (e) {
      return undefined;
    }
  }, [processedValue]);

  const rehypePlugins = useMemo(() => {
    return contentInfo.isLarge ? REHYPE_PLUGINS_LIGHT : REHYPE_PLUGINS;
  }, [contentInfo.isLarge]);

  const components = useMemo(() => {
    const shouldDisableHighlighting =
      disableHighlighting || contentInfo.isLarge;
    const baseComponents = shouldDisableHighlighting
      ? COMPONENTS_WITHOUT_HIGHLIGHTING
      : COMPONENTS;
    return customComponents
      ? { ...baseComponents, ...customComponents }
      : baseComponents;
  }, [disableHighlighting, contentInfo.isLarge, customComponents]);

  const urlTransform = customUrlTransform || defaultUrlTransform;

  return (
    <article className={cn(MD_PROSE_CLASS_NAMES, className)}>
      <Markdown
        components={components}
        rehypePlugins={rehypePlugins}
        urlTransform={urlTransform}
      >
        {jsonString ?? processedValue}
      </Markdown>
    </article>
  );
}

export const MarkdownViewer = memo(MarkdownViewerComponent);
MarkdownViewer.displayName = "MarkdownViewer";
