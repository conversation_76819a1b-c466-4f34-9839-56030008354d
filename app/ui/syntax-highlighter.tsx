"use client";
import React, { type JSX, useLayoutEffect, useState } from "react";
import { cn } from "#/utils/classnames";
import { type BundledLanguage, highlight } from "./highlight";

interface SyntaxHighlightProps {
  language?: BundledLanguage;
  className?: string;
  content: string;
}

const SyntaxHighlightComponent: React.FC<SyntaxHighlightProps> = ({
  language = "typescript",
  className,
  content,
}) => {
  const [nodes, setNodes] = useState<JSX.Element | string>(content);

  useLayoutEffect(() => {
    let isMounted = true;
    void highlight({
      code: content,
      lang: language,
    }).then((result) => {
      if (isMounted) {
        setNodes(result);
      }
    });
    return () => {
      isMounted = false;
    };
  }, [content, language]);

  const containerClassName = cn(
    "bg-primary-50 font-mono text-xs whitespace-pre-wrap",
    className,
  );

  return (
    <div className={containerClassName}>
      <pre className="not-prose">
        <code className="not-prose shiki whitespace-pre-wrap">{nodes}</code>
      </pre>
    </div>
  );
};

export const SyntaxHighlight = React.memo(
  SyntaxHighlightComponent,
  (prevProps, nextProps) => {
    return (
      prevProps.language === nextProps.language &&
      prevProps.className === nextProps.className &&
      prevProps.content === nextProps.content
    );
  },
);
