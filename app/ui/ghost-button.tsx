import { cn } from "#/utils/classnames";
import { forwardRef } from "react";

export const GhostButton = forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(function GhostButton(props, ref) {
  const { className, children, ...otherProps } = props;

  return (
    <button
      className={cn(
        `text-sm py-2 px-3 border-2 border-transparent`,
        `rounded text-accent-500 font-medium`,
        className,
      )}
      ref={ref}
      {...otherProps}
    >
      {children}
    </button>
  );
});
