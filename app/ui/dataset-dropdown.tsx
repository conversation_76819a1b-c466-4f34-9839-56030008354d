import { type ProjectContextDataset } from "#/app/app/[org]/p/[project]/project-actions";
import { isEmpty } from "#/utils/object";
import {
  forwardRef,
  type HTMLAttributes,
  type PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { ArrowUpRight, Blend, Plus, Upload, X } from "lucide-react";
import { cn } from "#/utils/classnames";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "./nested-dropdown";
import { DropdownMenuCheckboxItem } from "./dropdown-menu";
import { useRouter } from "next/navigation";
import { getDatasetLink } from "#/app/app/[org]/p/[project]/datasets/[dataset]/getDatasetLink";
import { useOrg } from "#/utils/user";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";

const DatasetOptionLabel = ({
  datasetName,
  projectName,
  className,
}: {
  datasetName: string;
  projectName?: string;
  className?: string;
}) => {
  return (
    <span
      className={cn("flex items-center gap-2 w-full flex-1", className)}
      title={`${datasetName} from ${projectName ?? "this project"}`}
    >
      <span className="flex-1">{datasetName}</span>
      {projectName && (
        <span className="max-w-24 flex-none truncate text-primary-500">
          {projectName}
        </span>
      )}
    </span>
  );
};

export function DatasetDropdown({
  datasets,
  children,
  selectedDatasetId,
  onSelectDataset,
  onCreateNewDataset,
  onUploadDataset,
  isLoopEnabled = false,
  align,
  open,
  setOpen,
  onClear,
  showOpenDatasetOption,
}: PropsWithChildren<{
  datasets: ProjectContextDataset[] | undefined;
  selectedDatasetId?: string;
  onSelectDataset: (dataset: ProjectContextDataset) => void;
  onCreateNewDataset?: (name: string) => void;
  onUploadDataset?: VoidFunction;
  isLoopEnabled?: boolean;
  align?: DropdownMenuContentProps["align"];
  open?: boolean;
  setOpen?: (open: boolean) => void;
  onClear?: VoidFunction;
  showOpenDatasetOption?: boolean;
}>) {
  const org = useOrg();
  const router = useRouter();
  const { projectId, projectName, mutateDatasets } = useContext(ProjectContext);

  const { setIsChatOpen, handleSendMessage } = useGlobalChat();

  useEffect(() => {
    if (open) {
      // When the dropdown opens, refetch the dataset list
      mutateDatasets();
    }
  }, [open, mutateDatasets]);

  const groupedDatasets = useMemo(() => {
    const datasetsByProject = (datasets ?? []).reduce<{
      [projectId: string]: {
        projectId: string;
        projectName: string;
        datasets: ProjectContextDataset[];
      };
    }>((acc, dataset) => {
      const { project_id, project_name } = dataset;
      if (!acc[project_id]) {
        acc[project_id] = {
          projectId: project_id,
          projectName: project_name,
          datasets: [],
        };
      }

      acc[project_id].datasets.push(dataset);
      return acc;
    }, {});

    return Object.values(datasetsByProject).sort((a, b) =>
      a.projectName === projectName
        ? -1
        : b.projectName === projectName
          ? 1
          : a.projectName.localeCompare(b.projectName),
    );
  }, [datasets, projectName]);

  const dropdownData = useMemo(() => {
    if (isEmpty(groupedDatasets)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      groupedDatasets[0]?.projectName === projectName
        ? groupedDatasets[0].datasets.sort((a, b) =>
            (a.name ?? "").localeCompare(b.name ?? ""),
          )
        : undefined;

    const otherProjects = groupedDatasets
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ datasets }) =>
        datasets.sort((a, b) => (a.name ?? "").localeCompare(b.name ?? "")),
      );

    const subGroups =
      otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : undefined;

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [groupedDatasets, projectName]);

  const DatasetMenuItem = forwardRef<
    HTMLDivElement,
    { item: ProjectContextDataset } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: dataset, ...rest }, ref) => {
        return (
          <DropdownMenuCheckboxItem
            {...rest}
            ref={ref}
            onSelect={() => onSelectDataset(dataset)}
            checked={dataset.id === selectedDatasetId}
          >
            <DatasetOptionLabel
              datasetName={dataset.name}
              projectName={
                dataset.project_id !== projectId
                  ? dataset.project_name
                  : undefined
              }
            />
          </DropdownMenuCheckboxItem>
        );
      },
      [onSelectDataset, selectedDatasetId, projectId],
    ),
  );

  return (
    <NestedDropdown
      align={align}
      objectType="dataset"
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      DropdownItemComponent={DatasetMenuItem}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      open={open}
      setOpen={setOpen}
      additionalActions={[
        ...(isLoopEnabled && !selectedDatasetId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Blend className="size-3" />
                    Generate new dataset
                  </div>
                ),
                onSelect: () => {
                  setIsChatOpen(true);
                  handleSendMessage({
                    id: crypto.randomUUID(),
                    type: "user_message",
                    message:
                      "Generate a new dataset with 5 rows appropriate for the task in my playground.",
                  });
                },
              },
            ]
          : []),
        ...(onCreateNewDataset
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Plus className="size-3" />
                    Create new dataset
                  </div>
                ),
                onSelect: () => onCreateNewDataset("New dataset"),
              },
            ]
          : []),
        ...(onUploadDataset
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Upload className="size-3" />
                    Upload dataset
                  </div>
                ),
                onSelect: () => onUploadDataset(),
              },
            ]
          : []),
        ...(onClear && selectedDatasetId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <X className="size-3" />
                    Clear dataset
                  </div>
                ),
                onSelect: onClear,
              },
            ]
          : []),
        ...(showOpenDatasetOption && selectedDatasetId
          ? [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <ArrowUpRight className="size-3" />
                    Go to dataset
                  </div>
                ),
                onSelect: () => {
                  const dataset = datasets?.find(
                    (d) => d.id === selectedDatasetId,
                  );
                  if (dataset) {
                    router.push(
                      getDatasetLink({
                        orgName: org.name,
                        projectName: dataset.project_name,
                        datasetName: dataset.name,
                      }),
                    );
                  }
                },
              },
            ]
          : []),
      ]}
    >
      {children}
    </NestedDropdown>
  );
}
