import { Button } from "#/ui/button";
import { Input } from "#/ui/input";
import { Slider } from "#/ui/slider";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type TransactionId } from "#/utils/duckdb";
import { isEmpty } from "#/utils/object";
import { useOptimisticState } from "#/utils/optimistic-update";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { XIcon } from "lucide-react";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";

export interface NumericSliderHandle {
  save: (v: number | undefined) => Promise<void>;
}

export interface NumericSliderProps {
  title: string;
  value: number | undefined;
  setValue: (
    v: number | undefined,
    isBlur?: boolean,
  ) => Promise<TransactionId | null>;
  min?: number;
  max?: number;
  step?: number;
  disabled?: boolean;
  helpText?: React.ReactNode;
  unit?: string;
  compact?: boolean;
  rowKey?: string;
  xactId?: TransactionId | null;
  className?: string;
  sliderClassName?: string;
  updateOnBlur?: boolean;
}

export const UncontrolledNumericSlider = forwardRef<
  NumericSliderHandle,
  NumericSliderProps
>(
  (
    {
      updateOnBlur,
      title,
      value: valueProp,
      setValue: saveValueProp,
      min,
      max,
      step,
      disabled,
      helpText,
      unit,
      compact,
      rowKey,
      xactId,
      className,
      sliderClassName,
    },
    ref,
  ) => {
    const [value, setStateValue] = useState<number | undefined>(valueProp);

    const [sliderValue, setSliderValue] = useState([value ?? 0]);
    const [textInput, setTextInput] = useState(
      disabled ? undefined : `${value}`,
    );

    const optimisticSave = useCallback(
      async (v: number | undefined, isBlur?: boolean) => {
        setStateValue(v);
        return await saveValueProp(v, isBlur);
      },
      [saveValueProp, setStateValue],
    );

    const { save: setValue } = useOptimisticState({
      value: valueProp,
      save: optimisticSave,
      rowKey,
      xactId: xactId ?? null,
      onUpdatedValue: setStateValue,
    });

    useEffect(() => {
      setSliderValue([value ?? 0]);
      setTextInput(disabled || value === undefined ? undefined : `${value}`);
    }, [disabled, value]);

    const debouncedSetValue = useDebouncedCallback(setValue, 400);

    const clampValue = useCallback(
      (v: number | undefined) => {
        if (min === undefined || max === undefined || v === undefined) {
          return v;
        }
        return Math.max(min, Math.min(max, v));
      },
      [min, max],
    );

    const updateNumericValue = useCallback(
      (v: number | undefined, isBlur?: boolean) => {
        if (v === undefined) {
          setSliderValue([0]);
          setTextInput(undefined);
          setValue(undefined, isBlur);
        } else {
          const clampedValue = clampValue(v);
          setSliderValue([clampedValue!]);
          debouncedSetValue(clampedValue, isBlur);
        }
      },
      [debouncedSetValue, setValue, clampValue],
    );

    useImperativeHandle(
      ref,
      () => ({
        save: async (v: number | undefined) => {
          if (v === undefined) {
            setSliderValue([0]);
            setTextInput(undefined);
            return setValue(undefined, updateOnBlur);
          } else {
            const clampedValue = clampValue(v);
            setSliderValue([clampedValue!]);
            setTextInput(`${clampedValue}`);
            return setValue(clampedValue, updateOnBlur);
          }
        },
      }),
      [setValue, updateOnBlur, clampValue],
    );

    const handleInputChange = (
      e: React.ChangeEvent<HTMLInputElement>,
      isBlur?: boolean,
    ) => {
      const v = e.target.value;
      setTextInput(v);
      const n = parseFloat(v);
      if (!isNaN(n)) {
        updateNumericValue(n, isBlur);
      }
    };

    return (
      <NumericSlider
        min={min}
        max={max}
        step={step}
        value={sliderValue}
        onChange={(v: number[]) => {
          setTextInput(`${v[0]}`);
          updateNumericValue(v[0]);
        }}
        onCommit={
          updateOnBlur
            ? (v: number[]) => {
                setTextInput(`${v[0]}`);
                updateNumericValue(v[0], true);
              }
            : undefined
        }
        disabled={disabled}
        compact={compact}
        title={title}
        unit={unit}
        helpText={helpText}
        textInputValue={textInput ?? ""}
        onTextInputChange={handleInputChange}
        onTextInputBlur={(e) => {
          if (updateOnBlur) {
            handleInputChange(e, true);
          }
        }}
        onClear={(e) => {
          e.preventDefault();
          updateNumericValue(undefined, updateOnBlur);
        }}
        className={className}
        sliderClassName={sliderClassName}
      />
    );
  },
);
UncontrolledNumericSlider.displayName = "UncontrolledNumericSlider";

export const NumericSlider = ({
  value,
  onChange,
  onCommit,
  disabled,
  compact,
  title,
  unit,
  helpText,
  textInputValue,
  onTextInputChange,
  onTextInputBlur,
  onClear,
  className,
  sliderClassName,
  ...props
}: {
  value?: number[];
  onChange: (v: number[]) => void;
  onCommit?: (v: number[]) => void;
  disabled?: boolean;
  compact?: boolean;
  title: string;
  unit?: string;
  helpText?: React.ReactNode;
  textInputValue?: string;
  onTextInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onTextInputBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onClear: (e: React.MouseEvent<HTMLButtonElement>) => void;
  min?: number;
  max?: number;
  step?: number;
  className?: string;
  sliderClassName?: string;
}) => {
  const inputDiv = (
    <div className="relative flex items-center gap-1">
      <div
        className={cn(
          "flex relative",
          compact ? "w-[3.5rem]" : "w-[4.5rem]",
          className,
        )}
      >
        <Input
          {...props}
          placeholder={disabled && value?.[0] ? `${value[0]}` : "-"}
          value={textInputValue}
          onChange={onTextInputChange}
          onBlur={onTextInputBlur}
          type="number"
          className={cn(
            "p-1 w-full h-6 !bg-transparent tabular-nums outline-none focus:ring-primary-200 transition-colors",
            "placeholder-opacity-50 disabled:cursor-not-allowed hover:!bg-primary-100 focus-visible:!bg-primary-100",
            "disabled:opacity-50 ring-0 border-transparent inline-block text-right text-xs focus:ring-0 focus:border-primary-200",
            {
              "pr-5": unit,
            },
          )}
        />
        {unit && (
          <span
            className={cn(
              "text-xs absolute right-1.5 top-0 bottom-0 flex items-center justify-center opacity-50",
            )}
          >
            {unit}
          </span>
        )}
      </div>

      {textInputValue !== undefined && (
        <Button
          size="icon"
          onClick={onClear}
          disabled={disabled}
          className="ml-1 size-6 text-primary-400 disabled:opacity-50"
          Icon={XIcon}
        />
      )}
    </div>
  );

  const mainContent = (
    <div className="flex flex-col">
      {!compact && (
        <div className="mb-2 flex flex-row justify-between">
          <div className={cn("py-1 text-xs", disabled && "opacity-50")}>
            {title}
          </div>
          {inputDiv}
        </div>
      )}
      <div className={cn("flex min-w-[200px] gap-2", sliderClassName)}>
        <div className="flex grow">
          <Slider
            {...props}
            value={isEmpty(value) ? [0] : value}
            onValueChange={onChange}
            onValueCommit={onCommit}
            onKeyDown={(e) => {
              e.stopPropagation();
            }}
            className="[&_[role=slider]]:size-4"
            aria-label={title}
            disabled={disabled}
          />
        </div>
        {!!compact && inputDiv}
      </div>
    </div>
  );

  return helpText ? (
    <BasicTooltip
      side="left"
      sideOffset={16}
      disableHoverableContent={false}
      tooltipContent={helpText}
      className="leading-normal"
    >
      {mainContent}
    </BasicTooltip>
  ) : (
    mainContent
  );
};
