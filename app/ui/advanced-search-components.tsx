import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from "react";
import { Button } from "./button";
import { Checkbox } from "./checkbox";
import { SearchIcon } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  type Search,
  type Clause,
  removeClause,
  addClause,
} from "#/utils/search/search";
import { Bubble } from "./table/bubble";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { Input } from "./input";
import { Spinner } from "./icons/spinner";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { cn } from "#/utils/classnames";

export interface AdvancedSearchState {
  disableScroll: boolean;
  setDisableScroll: Dispatch<SetStateAction<boolean>>;
  search: Clause<"match"> | undefined;
  setSearch: (clause: Clause<"match"> | undefined) => void;
  isSearchTransitioning: boolean;
}
export function useAdvancedSearchState({
  setSearch: setSearchProp,
}: {
  setSearch: React.Dispatch<React.SetStateAction<Search>>;
}): AdvancedSearchState {
  const [disableScroll, setDisableScroll] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: "disableInfiniteScroll-global",
    key: "disableInfiniteScroll",
    defaultValue: false,
  });
  const [search, setSearchState] = useState<Clause<"match"> | undefined>(
    undefined,
  );

  const [isSearchTransitioning, startSearchTransition] = useTransition();

  const setSearch = useCallback(
    (clause: Clause<"match"> | undefined) => {
      startSearchTransition(() => {
        setSearchProp((s) => {
          if (s.match && s.match.length > 0) {
            s = removeClause(s, s.match[0]);
          }
          if (clause && clause.text.length > 0) {
            s = addClause(s, clause);
          }
          return s;
        });
        setSearchState(clause);
      });
    },
    [setSearchProp],
  );

  return useMemo(
    () => ({
      disableScroll,
      setDisableScroll,
      search,
      setSearch,
      isSearchTransitioning,
    }),
    [disableScroll, isSearchTransitioning, search, setDisableScroll, setSearch],
  );
}

export const AdvancedSearchComponents = ({
  disableScroll,
  setDisableScroll,
  search,
  setSearch,
  objectType,
  isSearchTransitioning,
}: AdvancedSearchState & {
  objectType: DataObjectType;
}) => {
  const {
    flags: { advancedSearch, brainstore },
  } = useFeatureFlags();

  const [searchTextRaw, setSearchText] = useState(search?.text ?? "");

  const debouncedSetSearch = useDebouncedCallback((text: string) => {
    setSearch({
      type: "match",
      text,
      bubble: new Bubble({
        type: "match",
        hidden: true,
        label: (
          <span className="flex items-center gap-1">
            <SearchIcon className="size-3 opacity-80" />
            {text}
          </span>
        ),
        clear: () => {
          setSearchText("");
          setSearch(undefined);
        },
      }),
    });
  }, 500);

  useEffect(() => {
    debouncedSetSearch(searchTextRaw);
  }, [searchTextRaw, debouncedSetSearch]);

  useEffect(() => {
    if (searchTextRaw === "" && search !== undefined) {
      search.bubble.clear();
    }
  }, [searchTextRaw, search]);

  if (!brainstore) {
    return null;
  }

  return (
    <div className="flex flex-1 gap-2">
      <div className="relative flex flex-1 gap-2">
        {isSearchTransitioning ? (
          <Spinner className="absolute left-2 top-2 !size-3 text-primary-500" />
        ) : (
          <SearchIcon className="pointer-events-none absolute left-2 top-1/2 size-3 -translate-y-1/2 text-primary-500" />
        )}
        <Input
          className={cn(
            "h-7 flex-1 border-none pl-7 text-xs transition-colors bg-primary-50 hover:bg-primary-100 focus-visible:ring-0 focus-visible:bg-primary-100",
            {
              "pr-7": advancedSearch,
            },
          )}
          placeholder={`Search ${objectType === "project_logs" ? "logs" : objectType}`}
          value={searchTextRaw}
          onChange={(e) => {
            setSearchText(e.target.value);
          }}
        />
      </div>
      {advancedSearch && (
        <Button
          size="xs"
          variant="ghost"
          className="gap-2 text-primary-500"
          onClick={() => setDisableScroll(!disableScroll)}
        >
          <Checkbox
            inputClassName="!size-3"
            checked={!disableScroll}
            readOnly
          />
          Infinite scroll
        </Button>
      )}
    </div>
  );
};
