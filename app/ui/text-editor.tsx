import { type autocompletion as codemirrorAutocompletion } from "@codemirror/autocomplete";
import { ensureSyntaxTree } from "@codemirror/language";
import { type Diagnostic, forEachDiagnostic } from "@codemirror/lint";
import { type EditorView, type ViewUpdate } from "@codemirror/view";
import { SearchQuery, setSearchQuery } from "@codemirror/search";
import CodeMirror, {
  type EditorState,
  type ReactCodeMirrorProps,
  type ReactCodeMirrorRef,
} from "@uiw/react-codemirror";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { clearLocalCache as clearCopilotCache } from "codemirror-copilot";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import "#/ui/TextEditor.css";

import { type CopilotEditorContext } from "@braintrust/local/copilot";
import { type SetValue } from "#/lib/clientDataStorage";
import { Skeleton } from "./skeleton";
import React from "react";
import { useCodemirrorExtensions } from "./codemirror/codemirror-extensions";
import { useCodemirrorTheme } from "./codemirror/codemirror-theme";
import { foldPaths } from "./codemirror/codemirror-fold";
import { CodemirrorSearch } from "./codemirror/codemirror-search";
import { useIsClient } from "#/utils/use-is-client";
import { isEmpty } from "@braintrust/core";

export const SYNTAX_TREE_PARSING_TIMEOUT = 1000;

export type CompletionConfig = Parameters<typeof codemirrorAutocompletion>[0];

export interface TextEditorHandle<EditorData = string> {
  focus(up: boolean): void;
  getValue(): EditorData | undefined;
  setValue(value: EditorData): void;
  getLintErrors: () => Diagnostic[];
  editorRef: () => ReactCodeMirrorRef | null;
}

export type EditorAutoCompleteContextFn = () =>
  | CopilotEditorContext
  | Promise<CopilotEditorContext>
  | undefined;

export type TextEditorProps = {
  styled?: boolean;
  placeholder?: string;
  autoFocus?: boolean;
  onMetaEnter?: (type: "shift" | "cmd") => void;
  jump?: (up: boolean) => void;
  wrap?: boolean;
  onSave?: (value: string | undefined) => void;
  initialCursorPos?: { x: number; y: number };
  formatter?: (text: string) => string;
  isMonospace?: boolean;
  tabAutocomplete?: boolean;
  isCollapsed?: boolean;
  searchQuery?: string;
  getAutoCompleteContext?: EditorAutoCompleteContextFn;
  foldStateIdentifier?: string;
  foldState?: Record<string, boolean>;
  setFoldState?: SetValue<Record<string, boolean>>;
  diffValue?: string | null;
  spellcheck?: boolean;
  diffMessage?: string;
  editorId?: string;
} & ReactCodeMirrorProps;

const TextEditor = forwardRef<TextEditorHandle<string>, TextEditorProps>(
  (
    {
      placeholder: placeholderProp,
      autoFocus,
      onMetaEnter,
      jump,
      readOnly,
      wrap,
      onSave,
      value,
      onBlur: onBlurProp,
      onChange: onChangeProp,
      initialCursorPos,
      formatter,
      extensions: extensionsProp,
      styled,
      isMonospace = true,
      tabAutocomplete,
      isCollapsed,
      searchQuery,
      getAutoCompleteContext,
      foldStateIdentifier,
      foldState,
      setFoldState,
      onCreateEditor: onCreateEditorProp,
      diffValue,
      spellcheck,
      diffMessage,
      editorId,
      ...props
    },
    ref,
  ) => {
    const editorTheme = useCodemirrorTheme({
      isMonospace,
      styled,
    });

    const [editorValue, setEditorValue] = useState(
      (value === "null" && !readOnly ? undefined : value) ?? undefined,
    );

    const changed = useRef(false);

    useEffect(() => {
      // If the initial value hasn't been set, then set it (unless the value has changed)
      setEditorValue((prev) => prev ?? (changed.current ? undefined : value));
    }, [value]);

    const editorRef = useRef<ReactCodeMirrorRef | null>(null);
    useImperativeHandle(
      ref,
      () => ({
        getValue(): string | undefined {
          return editorValue;
        },
        setValue(value: string) {
          hardResetEditorToValue(editorRef.current?.view, value);
        },
        focus(_): void {
          editorRef.current?.view?.focus();
        },
        getLintErrors: () => {
          const errors: Diagnostic[] = [];

          if (editorRef.current?.view) {
            forEachDiagnostic(editorRef.current.view.state, (d) => {
              if (d.severity === "error") {
                errors.push(d);
              }
            });
          }

          return errors;
        },
        editorRef: () => editorRef.current ?? null,
      }),
      [editorValue],
    );

    // Pull editor out so that if it changes, we re-run the effect
    // eslint-disable-next-line react-compiler/react-compiler
    const editor = editorRef.current?.view;
    // trigger a re-render manually when things are initialized since we are using refs as dependencies
    const [initialized, setInitialized] = useState<boolean>();

    const foldStateRef = useRef(foldState);
    useEffect(() => {
      foldStateRef.current = foldState;
    }, [foldState]);

    useEffect(() => {
      // Do not apply this update if:
      //  - Despite the fact that there's an active editor, there's an active change and the editor is focused
      //  - The value is undefined (which means we're still waiting for the initial value)
      if (
        (editor && changed.current && editor.hasFocus) ||
        value === undefined
      ) {
        return;
      }

      editor?.dispatch({
        changes: {
          from: 0,
          to: editor?.state.doc.length,
          insert: value,
        },
      });

      if (editor) {
        changed.current = false;
        // https://github.com/codemirror/dev/issues/762
        ensureParsedSyntaxTree(editor?.state, editor);
        foldPaths(editor, foldStateRef.current ?? {}, isCollapsed);
      }
      // eslint-disable-next-line react-compiler/react-compiler
    }, [editor, value, isCollapsed]);

    useEffect(() => {
      if (!editor) return;
      editor.dispatch({
        selection: { anchor: editor.state.selection.main.head },
        effects: setSearchQuery.of(
          new SearchQuery({
            search: searchQuery ?? "",
          }),
        ),
      });
      // eslint-disable-next-line react-compiler/react-compiler
    }, [searchQuery, editor]);

    const saveValue = useCallback(
      (value: string | undefined, editorValue: string | undefined) => {
        // Skip saving if no value is provided and the editor value is empty or undefined
        if (
          value === undefined &&
          (editorValue === "" || editorValue === undefined)
        ) {
          return;
        }

        // Skip saving if the value is the same as the editor value
        if (value === editorValue) {
          return;
        }

        if (!initialized) {
          return;
        }

        onSave?.(value);
      },
      [onSave, initialized],
    );

    const debouncedSave = useDebouncedCallback(saveValue, 1000, false);

    const onChange = useCallback(
      (value: string, viewUpdate: ViewUpdate) => {
        setEditorValue(value);
        changed.current = true;
        debouncedSave(value, undefined);

        if (onChangeProp) {
          onChangeProp(value, viewUpdate);
        }
      },
      [onChangeProp, debouncedSave],
    );

    const formatValue = useCallback(
      (value?: string) => {
        // Check this before callling toString() which can be expensive.
        const formatted = formatter && value ? formatter(value) : value;
        if (!formatted || formatted === value) {
          return value;
        }

        hardResetEditorToValue(editorRef.current?.view, formatted);
        return formatted;
      },
      [formatter],
    );

    const onBlur = useCallback(
      (event: React.FocusEvent<HTMLDivElement>) => {
        const formattedValue = formatValue(editorValue);
        clearCopilotCache();
        saveValue(formattedValue, editorValue);
        onBlurProp?.(event);
      },
      [onBlurProp, formatValue, saveValue, editorValue],
    );

    const { extensions, unifiedMergeViewError } = useCodemirrorExtensions({
      editorRef,
      placeholderProp,
      extensionsProp,
      readOnly,
      getAutoCompleteContext,
      onMetaEnter,
      jump,
      tabAutocomplete,
      wrap,
      setFoldState,
      diffValue,
      spellcheck,
      editorId,
    });

    const showDiffMessage = useMemo(
      () => !isEmpty(diffValue) && !isEmpty(diffMessage) && diffValue !== value,
      [diffValue, diffMessage, value],
    );

    // https://github.com/uiwjs/react-codemirror/issues/314#issuecomment-1557816378
    function editorRefCallback(editor: ReactCodeMirrorRef) {
      if (
        !editorRef.current &&
        editor?.editor &&
        editor?.state &&
        editor?.view
      ) {
        editorRef.current = editor;
        setInitialized(true);
      }
    }

    const isClient = useIsClient();

    const onCreateEditor = useCallback(
      (view: EditorView, state: EditorState) => {
        ensureParsedSyntaxTree(state, view);
        if (initialCursorPos) {
          const newPosition = view.posAtCoords(initialCursorPos);
          if (newPosition) {
            view.dispatch({
              selection: {
                anchor: newPosition,
              },
            });
          }
        }

        foldPaths(view, foldState ?? {}, isCollapsed);

        onCreateEditorProp?.(view, state);
      },
      [initialCursorPos, foldState, isCollapsed, onCreateEditorProp],
    );

    if (!isClient) {
      return (
        <div className="m-1">
          <Skeleton className="h-8" />
        </div>
      );
    }

    return (
      <>
        {unifiedMergeViewError && (
          <div className="rounded-md px-2 py-1 text-xs bg-bad-50 text-primary-800">
            There was an error rendering the diff. Please contact
            <EMAIL> for support.
          </div>
        )}
        {showDiffMessage && (
          <div className="mb-0.5 text-xs text-primary-400">{diffMessage}</div>
        )}
        <CodemirrorSearch
          // eslint-disable-next-line react-compiler/react-compiler
          editor={editor}
          searchQuery={searchQuery}
          value={value}
        />
        <CodeMirror
          ref={editorRefCallback}
          extensions={extensions}
          theme={editorTheme}
          basicSetup={false}
          autoFocus={autoFocus}
          // to ensure that the editor is always controlled, never pass a value of undefined
          value={editorValue ?? ""}
          onBlur={onBlur}
          onChange={onChange}
          indentWithTab={!tabAutocomplete}
          onCreateEditor={onCreateEditor}
          {...props}
        />
      </>
    );
  },
);
TextEditor.displayName = "TextEditor";

export default TextEditor;

const hardResetEditorToValue = (editor?: EditorView, value?: string) => {
  if (!editor) return;

  editor.dispatch({
    changes: {
      from: 0,
      to: editor.state.doc.length,
      insert: value,
    },
  });
};

const ensureParsedSyntaxTree = (state: EditorState, view: EditorView) => {
  // To process entire document instead of only visible part
  // taken from https://discuss.codemirror.net/t/folding-functions/4189/2
  ensureSyntaxTree(state, state.doc.length, SYNTAX_TREE_PARSING_TIMEOUT);
  view.dispatch({});
};
