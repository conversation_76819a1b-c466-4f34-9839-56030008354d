import React from "react";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ArrowUpRight } from "lucide-react";

export const ApplyButton = ({
  url = "https://jobs.ashbyhq.com/Braintrust/form/application",
}: {
  url?: string;
}) => {
  return (
    <a
      href={url}
      target="_blank"
      className={cn(
        buttonVariants({
          variant: "primary",
          size: "lg",
        }),
        "text-warm not-prose mt-4 bg-primary-900 dark:bg-primary-900 text-base dark:hover:bg-white/80 hover:text-warm dark:hover:text-black dark:text-black no-underline",
      )}
    >
      Apply
      <ArrowUpRight className="size-4" />
    </a>
  );
};
