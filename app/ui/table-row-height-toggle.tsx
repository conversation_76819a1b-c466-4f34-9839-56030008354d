import { MoveVertical, Rows2, Rows4 } from "lucide-react";
import { Button } from "./button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { BasicTooltip } from "./tooltip";

export const TABLE_ROW_HEIGHTS = ["compact", "tall"] as const;
export type TableRowHeight = (typeof TABLE_ROW_HEIGHTS)[number];

export const TableRowHeightToggle = ({
  tableRowHeight,
  onSetRowHeight,
}: {
  tableRowHeight: TableRowHeight;
  onSetRowHeight: (t: TableRowHeight) => void;
}) => (
  <>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>
          <BasicTooltip tooltipContent="Row height">
            <Button size="xs" Icon={MoveVertical} variant="ghost">
              <span className="hidden @lg/controls:block">Height</span>
            </Button>
          </BasicTooltip>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuGroup>
          <DropdownMenuCheckboxItem
            checked={tableRowHeight === "compact"}
            onSelect={() => onSetRowHeight("compact")}
          >
            <Rows4 className="mr-2 size-3" />
            Compact
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={tableRowHeight === "tall"}
            onSelect={() => onSetRowHeight("tall")}
          >
            <Rows2 className="mr-2 size-3" />
            Tall
          </DropdownMenuCheckboxItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  </>
);
