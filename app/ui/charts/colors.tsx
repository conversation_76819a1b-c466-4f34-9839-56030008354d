// stroke for lines, fill for circles, bg/border/text for any traditional html elements

import { cn } from "#/utils/classnames";

// taken from https://tailwindcss.com/docs/customizing-colors
// arbitrarily pick a starting color, then nextColor = (index - 6) % 17
export const COLOR_CLASSNAMES = [
  "stroke-blue-500 fill-blue-500 bg-blue-500 border-blue-500 text-blue-700 dark:text-blue-200",
  "stroke-orange-500 fill-orange-500 bg-orange-500 border-orange-500 text-orange-700 dark:text-orange-200",
  "stroke-lime-500 fill-lime-500 bg-lime-500 border-lime-500 text-lime-700 dark:text-lime-200",
  "stroke-pink-500 fill-pink-500 bg-pink-500 border-pink-500 text-pink-700 dark:text-pink-200",
  "stroke-yellow-500 fill-yellow-500 bg-yellow-500 border-yellow-500 text-yellow-700 dark:text-yellow-200",
  "stroke-sky-500 fill-sky-500 bg-sky-500 border-sky-500 text-sky-700 dark:text-sky-200",
  "stroke-fuchsia-500 fill-fuchsia-500 bg-fuchsia-500 border-fuchsia-500 text-fuchsia-700 dark:text-fuchsia-200",
  "stroke-cyan-500 fill-cyan-500 bg-cyan-500 border-cyan-500 text-cyan-700 dark:text-cyan-200",
  "stroke-amber-500 fill-amber-500 bg-amber-500 border-amber-500 text-amber-700 dark:text-amber-200",
  "stroke-purple-500 fill-purple-500 bg-purple-500 border-purple-500 text-purple-700 dark:text-purple-200",
  "stroke-teal-500 fill-teal-500 bg-teal-500 border-teal-500 text-teal-700 dark:text-teal-200",
  "stroke-violet-500 fill-violet-500 bg-violet-500 border-violet-500 text-violet-700 dark:text-violet-200",
  "stroke-red-500 fill-red-500 bg-red-500 border-red-500 text-red-700 dark:text-red-200",
  "stroke-indigo-500 fill-indigo-500 bg-indigo-500 border-indigo-500 text-indigo-700 dark:text-indigo-200",
  "stroke-emerald-500 fill-emerald-500 bg-emerald-500 border-emerald-500 text-emerald-700 dark:text-emerald-200",
  "stroke-rose-500 fill-rose-500 bg-rose-500 border-rose-500 text-rose-700 dark:text-rose-200",
];

export const EXPERIMENT_PRIMARY_COLOR_CLASSNAME =
  "stroke-primary-600 fill-primary-500 bg-primary-500 border-primary-600 text-primary-700";
// to preserve backwards compatibility with existing color ordering
export const EXPERIMENT_COMPARISON_COLOR_CLASSNAMES = [
  "stroke-comparison-500 fill-comparison-500 bg-comparison-600 border-comparison-600 text-comparison-700",
  ...COLOR_CLASSNAMES,
];

export const ALL_EXPERIMENT_COLOR_CLASSNAMES = [
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
  ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
];

export const DEFAULT_COLOR_CLASSNAME =
  "stroke-primary-500 fill-primary-500 bg-primary-500 border-primary-500 text-primary-700";

export function nextColor(usedColorIndexes: (number | null)[]) {
  const histogram = usedColorIndexes.reduce(
    (acc, index) => {
      if (index == null) return acc;
      acc[index]++;
      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    new Array(COLOR_CLASSNAMES.length).fill(0) as number[],
  );

  const minValue = Math.min(...histogram);
  for (let i = 0; i < histogram.length; i++) {
    if (histogram[i] === minValue) return [i, COLOR_CLASSNAMES[i]] as const;
  }

  return [0, COLOR_CLASSNAMES[0]] as const;
}

export function ExperimentColorSwatch({
  index,
  children,
  className,
  swatchClassName,
}: {
  index?: number;
  className?: string;
  swatchClassName?: string;
  children?: React.ReactNode;
}) {
  if (index == null) {
    return children;
  }
  return (
    <span className={cn("flex flex-none items-center gap-1.5", className)}>
      <span
        className={cn(
          "flex-none rounded-full size-1.5",
          swatchClassName,
          [
            EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
            ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
          ][index],
        )}
      />
      {children}
    </span>
  );
}
