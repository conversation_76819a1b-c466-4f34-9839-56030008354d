import * as d3 from "d3";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import { useDarkMode } from "#/utils/useDarkMode";
import { Rectangle } from "./rectangle";

const MARGIN = { top: 4, right: 12, bottom: 24, left: 2 };
const X_SCALE_RANGE_START = 10;
const IDEAL_NUMBER_OF_BINS = 10;
const BUCKET_PADDING = 10;

type HistogramProps = {
  width: number;
  height: number;
  data: {
    name: string;
    values: number[];
    className: string;
  }[];
  onBrushCancel: () => void;
  onBrush: (param: [number, number]) => void;
  selectedScore: string;
  domain?: [number, number];
};
/**
 * Based on https://www.react-graph-gallery.com/histogram
 */
export const Histogram = forwardRef<
  { removeBrush: () => void },
  HistogramProps
>(function Histogram(
  { width, height, data, onBrush, domain, onBrushCancel },
  ref,
) {
  const groupsNumber = data.length;
  const axesRef = useRef(null);
  const brushRef = useRef<SVGGElement>(null);
  const boundsWidth = width - MARGIN.right - MARGIN.left;
  const boundsHeight = height - MARGIN.top - MARGIN.bottom;

  const xScale = useMemo(() => {
    const maxPerGroup = data.map((group) => Math.max(...group.values));
    const max = Math.max(...maxPerGroup);
    return d3
      .scaleLinear()
      .domain(domain ?? [0, max])
      .range([X_SCALE_RANGE_START, boundsWidth]);
  }, [data, domain, boundsWidth]);

  const bucketGenerator = useMemo(() => {
    return (
      d3
        .bin()
        .value((d) => d)
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        .domain(xScale.domain() as [number, number])
        .thresholds(xScale.ticks(IDEAL_NUMBER_OF_BINS))
    );
  }, [xScale]);

  // The way d3.js works, it will sometimes generate one more bin than the number of ticks
  const numberOfBins = useMemo(
    () => xScale.ticks(IDEAL_NUMBER_OF_BINS).length - 1,
    [xScale],
  );

  const groupBuckets = useMemo(() => {
    return data.map((group) => {
      const generatedBuckets = bucketGenerator(group.values);
      const correctedBuckets = generatedBuckets.slice(0, -1);
      correctedBuckets[correctedBuckets.length - 1].push(
        ...generatedBuckets[generatedBuckets.length - 1],
      );
      return {
        name: group.name,
        buckets: correctedBuckets,
        className: group.className,
      };
    });
  }, [bucketGenerator, data]);

  const yScale = useMemo(() => {
    const max = Math.max(
      ...groupBuckets.map((group) =>
        Math.max(...group.buckets.map((bucket) => bucket?.length)),
      ),
    );
    return d3.scaleLinear().range([boundsHeight, 0]).domain([0, max]).nice();
  }, [boundsHeight, groupBuckets]);

  // Render the X axis using d3.js, not react
  useEffect(() => {
    const svgElement = d3.select(axesRef.current);
    svgElement.selectAll("*").remove();

    const xAxisGenerator = d3
      .axisBottom(xScale)
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      .tickFormat((value) => `${(value as number) * 100}%`);
    svgElement
      .append("g")
      .attr("transform", `translate(0,${boundsHeight + 2})`)
      .attr("class", "text-primary-500")
      .call(xAxisGenerator);
  }, [xScale, yScale, boundsHeight]);

  const brush = useMemo(() => {
    const round = (n: number) => {
      const [x0, x1] = xScale.domain();
      const stepSize = (x1 - x0) / numberOfBins;
      return Number(
        (Math.round(n / stepSize) * stepSize).toFixed(
          getDecimalPlaces(stepSize),
        ),
      );
    };
    function brushed(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      this: any,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      event: { sourceEvent: any; selection: d3.NumberValue[] },
    ) {
      if (!event.sourceEvent) return;
      if (!event.selection) {
        onBrushCancel();
        return;
      }

      const d0 = event.selection.map(xScale.invert);
      const d1 = d0.map(round);

      d3.select(this).call(
        brush.move,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        d1.map((d) => xScale(d)) as [number, number],
      );
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      onBrush(d1 as [number, number]);
    }

    const [r0, r1] = xScale.range();
    const brush = d3
      .brushX()
      .extent([
        [r0, MARGIN.top],
        [r1, height - MARGIN.bottom],
      ])
      .on("brush end", brushed);
    return brush;
  }, [xScale, height, numberOfBins, onBrush, onBrushCancel]);

  // Add brush listener
  useEffect(() => {
    const brushNode = brushRef.current;
    if (!brushNode || !brush) return;
    d3.select(brushNode).call(brush);
  }, [brush]);

  useImperativeHandle(ref, () => {
    return {
      removeBrush() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        d3.select(brushRef.current).call(brush?.move as any, null);
      },
    };
  }, [brush]);

  const isDarkMode = useDarkMode();

  const allRects = groupBuckets.map((group, i) =>
    group.buckets.map((bucket, j) => {
      const { x0, x1 } = bucket;
      if (x0 == undefined || x1 == undefined) {
        return null;
      }
      const widthOfBar =
        (xScale(x1) - xScale(x0) - BUCKET_PADDING) / groupsNumber;

      return (
        <Rectangle
          key={x0 + "_" + group.name}
          fill="currentColor"
          className={group.className}
          x={xScale(x0) + BUCKET_PADDING / 2 + widthOfBar * i}
          width={widthOfBar}
          y={yScale(bucket.length)}
          rx={3}
          ry={3}
          stroke={isDarkMode ? "black" : "white"}
          strokeWidth={2}
          height={boundsHeight - yScale(bucket.length)}
        />
      );
    }),
  );

  return (
    <svg width={width} height={height}>
      <g
        width={boundsWidth}
        height={boundsHeight}
        transform={`translate(${[MARGIN.left, MARGIN.top].join(",")})`}
      >
        {allRects}
      </g>
      <g
        width={boundsWidth}
        height={boundsHeight}
        ref={axesRef}
        className="text-bt-neutral-500"
        transform={`translate(${[MARGIN.left, MARGIN.top].join(",")})`}
      />
      <g
        ref={brushRef}
        transform={`translate(${[MARGIN.left, 0].join(",")})`}
      />
    </svg>
  );
});

function getDecimalPlaces(num: number) {
  // Convert step size to string to count decimal places if any
  const numStr = num.toString();
  // Check for the presence of decimals
  if (numStr.includes(".")) {
    return numStr.split(".")[1].length;
  }
  return 0; // No decimal places
}
