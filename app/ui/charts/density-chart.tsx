import { useMemo, useCallback } from "react";
import { useChartResponsiveWidth } from "#/ui/charts/responsiveHooks";
import * as d3 from "d3"; // we will need d3.js
import { cn } from "#/utils/classnames";

// from https://www.react-graph-gallery.com/density-plot
export const useDensityChart = (
  height: number,
  bucketCount: number = 10,
  bounds: [number, number] = [0, 1],
  data: number[][],
) => {
  const { ref, boundsWidth, onLeftAxisWidth } = useChartResponsiveWidth();

  const xScale = useMemo(() => {
    return d3.scaleLinear().domain(bounds).range([0, boundsWidth]);
  }, [boundsWidth, bounds]);

  const densities = useMemo(() => {
    return data.map((d) => {
      const computeKde = kernelDensityEstimator(
        // maybe can figure out a better bandwidth estimation
        // https://aakinshin.net/posts/kde-bw/
        kernelEpanechnikov(nrd(d, (v) => v)),
        xScale.ticks(bucketCount),
      );
      return computeKde(d);
    });
  }, [bucketCount, xScale, data]);

  const yScale = useMemo(() => {
    const yValues = densities.flatMap((density) => {
      return density.flatMap((d) => d[1]);
    });
    const domain = [
      Math.min(...yValues),
      // scale y axis values by the number of data points so that we can see a difference between experiments with differing numbers of data points
      Math.max(...yValues) * Math.max(...data.map((d) => d.length)),
    ];
    return d3.scaleLinear().domain(domain).range([0, height]);
  }, [height, densities, data]);

  const chartContent = useCallback(() => {
    const areaGenerator = d3
      .area()
      .x((d) => xScale(d[0]))
      .y1((d) => yScale(d[1]))
      .y0(() => yScale.range()[0])
      // https://stackoverflow.com/questions/14488283/prevent-d3-js-area-chart-from-dropping-below-0-or-x-axis
      .curve(d3.curveMonotoneX);

    const areas = densities.map(
      // scale by number of values in the data set so that data sets with less data have a smaller amplitude
      (d, i) => areaGenerator(d.map(([x, v]) => [x, v * data[i].length])),
    );

    return areas.map((area, i) => (
      <path
        key={i}
        className={cn(
          i === 0
            ? "fill-primary-600"
            : "dark:fill-comparison-50 fill-comparison-300",
        )}
        fillOpacity="0.5"
        d={area ?? undefined}
      />
    ));
  }, [xScale, yScale, densities, data]);

  return {
    chartProps: {
      ref,
      xScale,
      yScale,
      chartContent,
    },
    leftAxisProps: {
      onLeftAxisWidth,
      xScale,
      yScale,
    },
    bottomAxisProps: {
      xScale,
      yScale,
    },
  };
};

// https://d3-graph-gallery.com/graph/density_basic.html
function kernelDensityEstimator(kernel: (n: number) => number, X: number[]) {
  return function (V: number[]) {
    return X.map(function (x): [number, number] {
      return [
        x,
        d3.mean(V, function (v) {
          return kernel(x - v);
        }) ?? 0,
      ];
    });
  };
}

function kernelEpanechnikov(bandwidth: number) {
  return (x: number) =>
    Math.abs((x /= bandwidth)) <= 1 ? (0.75 * (1 - x * x)) / bandwidth : 0;
}

// from https://github.com/uwdata/fast-kde
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function nrd(data: number[], x: (o: any) => number) {
  const values = data.map(x).filter((v) => v != null && v >= v);
  values.sort((a, b) => a - b);
  const sd = stdev(values);
  const q1 = quantile(values, 0.25);
  const q3 = quantile(values, 0.75);

  const n = values.length,
    h = (q3 - q1) / 1.34,
    v = Math.min(sd, h) || sd || Math.abs(q1) || 1;

  return 1.06 * v * Math.pow(n, -0.2);
}

function stdev(values: number[]) {
  const n = values.length;
  let count = 0;
  let delta;
  let mean = 0;
  let sum = 0;
  for (let i = 0; i < n; ++i) {
    const value = values[i];
    delta = value - mean;
    mean += delta / ++count;
    sum += delta * (value - mean);
  }
  return count > 1 ? Math.sqrt(sum / (count - 1)) : NaN;
}

function quantile(values: number[], p: number) {
  const n = values.length;

  if (!n) return NaN;
  if ((p = +p) <= 0 || n < 2) return values[0];
  if (p >= 1) return values[n - 1];

  const i = (n - 1) * p;
  const i0 = Math.floor(i);
  const v0 = values[i0];
  return v0 + (values[i0 + 1] - v0) * (i - i0);
}
