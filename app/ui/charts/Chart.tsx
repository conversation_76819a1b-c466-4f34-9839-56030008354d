"use client";

import { BottomAxis } from "#/ui/charts/axis/BottomAxis";
import { LeftAxis } from "#/ui/charts/axis/LeftAxis";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import { Skeleton } from "#/ui/skeleton";
import { Tooltip, TooltipContent } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { isEmpty } from "#/utils/object";
import { TooltipPortal } from "@radix-ui/react-tooltip";
import { LineChart } from "lucide-react";
import * as React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Annotation } from "./annotations";
import { ChartSymbol } from "./symbols";
import { TimeCursor } from "./cursor/TimeCursor";
import type { XScale, YScale } from "#/ui/charts/chart.types";
import { useAtom } from "jotai";
import { setGlobalTimeCursor<PERSON>tom } from "./store/atoms";
import { type ChartPadding } from "./padding/calc-padding";
import { nanoid } from "ai";

const BOTTOM_AXIS_GAP = 0;
const CLIP_PADDING = 4; // points go slightly outside viz area

export type Coords = {
  x: number | null;
  y: number | null;
};

export type ChartProps<T> = {
  chartRef: React.Ref<HTMLDivElement>;
  className?: string;
  loading?: boolean;
  brushRef?: React.Ref<SVGGElement>;
  leftAxisWidth: number;
  getNearestItems?: (mouseCoords: Coords) => T | null;
  renderChartContent: (
    nearestItems: T | null,
    seriesEnabled: boolean[] | null,
  ) => React.ReactNode;
  xScale: XScale;
  yScale: YScale;
  shouldClipArea?: boolean;
  leftAxisProps?: React.ComponentProps<typeof LeftAxis>;
  bottomAxisProps?: React.ComponentProps<typeof BottomAxis>;
  // using an array of entries instead of an object in the case where keys are numbers
  // since javascript objects sort number keys weirdly
  seriesLabels?: { label: string; colorIndex: number }[];
  symbolLabels?: { label: string; symbolIndex: number }[];
  renderTooltip?: (nearestItems: T | null) => React.ReactNode;
  enableSeriesToggles?: boolean;
  onClick?: (coords: Coords, event: React.MouseEvent) => void;
  onContextMenu?: (coords: Coords) => void;
  annotations?: {
    render: (point: { x: number; y?: number; id: string }) => React.ReactNode;
    points: { x: number; y?: number; id: string }[];
  };
  onMouseMove?: (mouseCoords: Coords) => void;
  height?: number;
  isResponsive?: boolean;
  placeholder?: string;
  renderPlaceholder?: () => React.ReactNode;
  // for multi-cursor on charts
  mouseCoords?: Coords;
};

export const Chart = <T,>({
  chartRef,
  className,
  loading,
  brushRef,
  leftAxisWidth,
  renderChartContent,
  seriesLabels,
  symbolLabels,
  xScale,
  yScale,
  shouldClipArea,
  leftAxisProps,
  bottomAxisProps,
  enableSeriesToggles,
  mouseCoords: _mouseCoords,
  onMouseMove: onMouseMove,
  onClick,
  onContextMenu,
  getNearestItems,
  renderTooltip,
  isResponsive = false,
  placeholder = "No data",
  annotations,
  renderPlaceholder,
}: ChartProps<T>) => {
  const [seriesEnabled, setSeriesEnabled] = useState(
    (seriesLabels ?? []).map(() => true),
  );
  useEffect(() => {
    setSeriesEnabled((seriesLabels ?? []).map(() => true));
  }, [seriesLabels]);

  const { mouseCoords, setMouseCoords } = useDefaultMouseCoords(
    _mouseCoords,
    onMouseMove,
  );
  const [tooltipOffsetX, setTooltipOffsetX] = useState(0);
  const [tooltipOffsetY, setTooltipOffsetY] = useState(0);
  const [_, setTimestamp] = useAtom(setGlobalTimeCursorAtom);

  const [bottomAxisHeight, setBottomAxisHeight] = useState(0);

  // Remember that svg clip paths ids are global, so use unique id per chart
  const vizAreaClipPathId = useMemo(() => {
    return `viz-area-${nanoid()}`;
  }, []);

  const chartPadding: ChartPadding = useMemo(() => {
    return {
      top: 0,
      left: leftAxisWidth,
      right: 0, // todo
      bottom: bottomAxisHeight + BOTTOM_AXIS_GAP,
    };
  }, [leftAxisWidth, bottomAxisHeight]);

  const chartWidth = xScale.range()[1];
  const chartHeight = yScale.range()[1];
  const onChartMouseMove = useCallback(
    ({ x, y }: Coords, { x: offsetX, y: offsetY }: Coords) => {
      if (!setMouseCoords) return;
      if (x == null || y == null) {
        setMouseCoords({ x: null, y: null });
        setTooltipOffsetX(0);
        setTooltipOffsetY(0);
        setTimestamp(0);
        return;
      }
      const dx = x - chartPadding.left;

      // scale the position out of 100 so that we can sync with other charts
      // to support multi-chart mouseover
      // also set the x's 0 position at the start of the graph and not the left side of the axis
      setMouseCoords({
        x: dx / chartWidth,
        y: y / chartHeight,
      });
      setTooltipOffsetX(offsetX ?? 0);
      setTooltipOffsetY(offsetY ?? 0);

      if ("ticks" in xScale) {
        const timestamp = xScale.invert(dx);
        // hack way to exclude non time scales
        if (timestamp > 1e6) {
          setTimestamp(timestamp);
        }
      }
    },
    [
      setMouseCoords,
      chartPadding,
      chartWidth,
      chartHeight,
      xScale,
      setTimestamp,
    ],
  );

  const nearestItems = useMemo(
    () => (getNearestItems ? getNearestItems(mouseCoords) : null),
    [getNearestItems, mouseCoords],
  );

  const tooltipX = useMemo(() => {
    if (mouseCoords.x == null) {
      return null;
    }

    const TOOLTIP_WIDTH = 224;
    const x = mouseCoords.x * chartWidth + chartPadding.left + tooltipOffsetX;
    if (x + TOOLTIP_WIDTH + 8 < window.innerWidth) {
      return x + 8;
    }
    return x - TOOLTIP_WIDTH - 8;
  }, [mouseCoords.x, chartWidth, chartPadding, tooltipOffsetX]);

  const tooltipY = useMemo(() => {
    if (mouseCoords.y == null) {
      return null;
    }

    return mouseCoords.y * chartHeight + tooltipOffsetY + 8;
  }, [mouseCoords.y, chartHeight, tooltipOffsetY]);

  const chartContent = useMemo(
    () => renderChartContent(nearestItems, seriesLabels ? seriesEnabled : null),
    [renderChartContent, nearestItems, seriesLabels, seriesEnabled],
  );

  const tooltip = useMemo(() => {
    return !isEmpty(nearestItems) && renderTooltip
      ? renderTooltip(nearestItems)
      : null;
  }, [nearestItems, renderTooltip]);

  const height = chartHeight + chartPadding.top + chartPadding.bottom;

  // hack to mimic the spacing of a properly rendered chart
  const bottomAxisSpacerClassName = bottomAxisProps
    ? bottomAxisProps.label
      ? "h-[43px]"
      : "h-[38px]"
    : "h-4";
  if (loading) {
    return (
      <div>
        <Skeleton
          className="w-full"
          {...(!isResponsive && { style: { height } })}
        />
        <div className={bottomAxisSpacerClassName} />
        {seriesLabels && <div className="h-4" />}
      </div>
    );
  }

  if (!chartContent) {
    if (renderPlaceholder) {
      return renderPlaceholder();
    }
    return (
      <div className="grow">
        <div
          className="flex w-full items-center justify-center gap-2 text-primary-400"
          {...(!isResponsive && { style: { height } })}
        >
          <LineChart className="size-3" />
          {placeholder}
        </div>
        <div className={bottomAxisSpacerClassName} />
        {seriesLabels && <div className="h-4" />}
      </div>
    );
  }

  return (
    <div className={className} ref={chartRef}>
      <div
        style={!isResponsive ? { height } : { height: "100%" }}
        onMouseMove={(e) => {
          onChartMouseMove(
            {
              x: e.nativeEvent.offsetX,
              y: e.nativeEvent.offsetY,
            },
            {
              x: e.nativeEvent.clientX - e.nativeEvent.offsetX,
              y: e.nativeEvent.clientY - e.nativeEvent.offsetY,
            },
          );
        }}
        onMouseLeave={() =>
          onChartMouseMove({ x: null, y: null }, { x: null, y: null })
        }
      >
        <svg
          width="100%"
          {...(!isResponsive && { style: { height: chartHeight } })}
          className={cn("relative z-20 overflow-visible")}
          onClick={(e) => mouseCoords && onClick && onClick(mouseCoords, e)}
          onAuxClick={(e) => {
            // Handle middle click
            if (mouseCoords && onClick && e.button === 1) {
              onClick(mouseCoords, e);
            }
            e.stopPropagation();
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            if (mouseCoords && onContextMenu) {
              onContextMenu(mouseCoords);
            }
          }}
        >
          <defs>
            {shouldClipArea && (
              <clipPath id={vizAreaClipPathId}>
                <rect
                  x={-CLIP_PADDING}
                  y={-CLIP_PADDING}
                  width={chartWidth + 2 * CLIP_PADDING}
                  height={chartHeight + 2 * CLIP_PADDING}
                />
              </clipPath>
            )}
          </defs>
          {leftAxisProps && (
            <g transform={`translate(${chartPadding.left})`}>
              <LeftAxis {...leftAxisProps} />
            </g>
          )}
          {bottomAxisProps && (
            <g
              ref={(node) => {
                if (!isResponsive) {
                  const bottomAxisHeight = node ? node.getBBox().height : 0;
                  setBottomAxisHeight(bottomAxisHeight);
                }
              }}
              transform={`translate(${chartPadding.left}, ${
                chartHeight + BOTTOM_AXIS_GAP
              })`}
            >
              <BottomAxis
                shiftFirstTickLabel={!leftAxisProps}
                {...bottomAxisProps}
              />
            </g>
          )}
          <TimeCursor
            xScale={xScale}
            yScale={yScale}
            padLeft={chartPadding.left}
          />
          <g
            width="100%"
            {...(!isResponsive && { style: { height: chartHeight } })}
            style={{
              transform: `translate(${chartPadding.left}px)`,
            }}
          >
            {annotations?.points.map((p, i) => (
              <React.Fragment key={p.id}>
                {p.y == null && (
                  <line
                    className="stroke-primary-400 stroke-1"
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    x1={"ticks" in xScale ? xScale(p.x) : xScale(p.id as any)}
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    x2={"ticks" in xScale ? xScale(p.x) : xScale(p.id as any)}
                    y1={0}
                    y2={chartHeight + 15}
                    strokeDasharray={4}
                    strokeWidth={2}
                  />
                )}
                <Annotation
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  x={"ticks" in xScale ? xScale(p.x) : xScale(p.id as any)}
                  y={p.y ? yScale(p.y) : chartHeight + 20}
                  onMouseMove={(e) => {
                    // mouse movement on this component triggers on the chart onMouseMove,
                    // even thought it may be rendered outside of the bounding box, so disable it here
                    e.stopPropagation();
                    onChartMouseMove(
                      { x: null, y: null },
                      { x: null, y: null },
                    );
                  }}
                >
                  {annotations.render(p)}
                </Annotation>
              </React.Fragment>
            ))}
            <g
              style={{
                transform: `translateY(${chartHeight}px) scaleY(-1)`,
              }}
              {...(shouldClipArea && {
                clipPath: `url(#${vizAreaClipPathId})`,
              })}
            >
              {chartContent}
            </g>
            {brushRef && <g ref={brushRef}></g>}
          </g>
        </svg>
        {tooltip && tooltipX !== null && tooltipY !== null && (
          <Tooltip open delayDuration={0} disableHoverableContent>
            <TooltipPortal>
              <TooltipContent
                hideWhenDetached
                className="pointer-events-none absolute w-56 will-change-transform"
                style={{
                  transform: `translate(${tooltipX}px, ${tooltipY}px)`,
                }}
              >
                {tooltip}
              </TooltipContent>
            </TooltipPortal>
          </Tooltip>
        )}
      </div>
      <div
        className={cn("flex flex-wrap gap-2")}
        style={{ marginLeft: chartPadding.left }}
      >
        {seriesLabels
          ? seriesLabels.map(({ label, colorIndex }, i) => {
              if (colorIndex == null) return null;

              return (
                <div
                  key={i}
                  className={cn(
                    "flex items-center",
                    enableSeriesToggles && "cursor-pointer",
                  )}
                  onClick={() => {
                    if (
                      !enableSeriesToggles ||
                      Object.keys(seriesLabels).length <= 1
                    ) {
                      return;
                    }
                    const newSeriesEnabled = seriesEnabled.map((v, j) =>
                      j === i ? !seriesEnabled[j] : v,
                    );
                    setSeriesEnabled(newSeriesEnabled);
                  }}
                >
                  <div
                    className={cn(
                      "mr-1 size-2 rounded-full border-2",
                      COLOR_CLASSNAMES[colorIndex] || DEFAULT_COLOR_CLASSNAME,
                      !seriesEnabled[i] && "bg-transparent",
                    )}
                  />
                  <span className="max-w-32 truncate text-xs">{label}</span>
                </div>
              );
            })
          : null}
        {symbolLabels
          ? symbolLabels.map(({ label, symbolIndex }, i) => (
              <div key={i} className="flex items-center">
                <ChartSymbol
                  className="fill-primary-800"
                  index={symbolIndex}
                  size={4}
                />
                <span className="max-w-32 truncate text-xs">{label}</span>
              </div>
            ))
          : null}
      </div>
    </div>
  );
};
Chart.displayName = "Chart";

function useDefaultMouseCoords(
  initialMouseCoords?: Coords,
  initialSetMouseCoords?: (v: Coords) => void,
) {
  const [mouseCoords, _setMouseCoords] = useState<Coords>({ x: null, y: null });
  const setMouseCoords = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    (v: any) => {
      _setMouseCoords(v);
      if (initialSetMouseCoords) {
        initialSetMouseCoords(v);
      }
    },
    [_setMouseCoords, initialSetMouseCoords],
  );

  return {
    mouseCoords: initialMouseCoords ?? mouseCoords,
    setMouseCoords,
  };
}
