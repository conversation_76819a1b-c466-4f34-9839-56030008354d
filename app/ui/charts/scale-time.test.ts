import { describe, expect, test } from "vitest";
import { scaleTime } from "#/ui/charts/scale-time";
import { TIME_RANGE_TO_MILLISECONDS } from "#/app/app/[org]/monitor/time-controls/time-range";

describe("scaleTime", () => {
  test("generates correct time range for 1 hour with minute bucket", () => {
    const result = scaleTime({
      startTime: "2024-10-02T07:01:55.999Z",
      totalDurationMS: TIME_RANGE_TO_MILLISECONDS["1h"],
      timeBucket: "minute",
    });
    expect(result).toEqual([
      1727852460000, 1727852520000, 1727852580000, 1727852640000, 1727852700000,
      1727852760000, 1727852820000, 1727852880000, 1727852940000, 1727853000000,
      1727853060000, 1727853120000, 1727853180000, 1727853240000, 1727853300000,
      1727853360000, 1727853420000, 1727853480000, 1727853540000, 1727853600000,
      1727853660000, 1727853720000, 1727853780000, 1727853840000, 1727853900000,
      1727853960000, 1727854020000, 1727854080000, 1727854140000, 1727854200000,
      1727854260000, 1727854320000, 1727854380000, 1727854440000, 1727854500000,
      1727854560000, 1727854620000, 1727854680000, 1727854740000, 1727854800000,
      1727854860000, 1727854920000, 1727854980000, 1727855040000, 1727855100000,
      1727855160000, 1727855220000, 1727855280000, 1727855340000, 1727855400000,
      1727855460000, 1727855520000, 1727855580000, 1727855640000, 1727855700000,
      1727855760000, 1727855820000, 1727855880000, 1727855940000, 1727856000000,
      1727856060000,
    ]);
  });

  test("generates correct time range for 3 days with hour bucket", () => {
    const result = scaleTime({
      startTime: "2024-10-02T00:55:22.999Z",
      totalDurationMS: TIME_RANGE_TO_MILLISECONDS["3d"],
      timeBucket: "hour",
    });
    expect(result).toEqual([
      1727827200000, 1727830800000, 1727834400000, 1727838000000, 1727841600000,
      1727845200000, 1727848800000, 1727852400000, 1727856000000, 1727859600000,
      1727863200000, 1727866800000, 1727870400000, 1727874000000, 1727877600000,
      1727881200000, 1727884800000, 1727888400000, 1727892000000, 1727895600000,
      1727899200000, 1727902800000, 1727906400000, 1727910000000, 1727913600000,
      1727917200000, 1727920800000, 1727924400000, 1727928000000, 1727931600000,
      1727935200000, 1727938800000, 1727942400000, 1727946000000, 1727949600000,
      1727953200000, 1727956800000, 1727960400000, 1727964000000, 1727967600000,
      1727971200000, 1727974800000, 1727978400000, 1727982000000, 1727985600000,
      1727989200000, 1727992800000, 1727996400000, 1728000000000, 1728003600000,
      1728007200000, 1728010800000, 1728014400000, 1728018000000, 1728021600000,
      1728025200000, 1728028800000, 1728032400000, 1728036000000, 1728039600000,
      1728043200000, 1728046800000, 1728050400000, 1728054000000, 1728057600000,
      1728061200000, 1728064800000, 1728068400000, 1728072000000, 1728075600000,
      1728079200000, 1728082800000, 1728086400000,
    ]);
  });

  test("throws error for invalid bucket type", () => {
    expect(() => {
      scaleTime({
        startTime: "2024-10-02T00:00:00.000Z",
        totalDurationMS: TIME_RANGE_TO_MILLISECONDS["1h"],
        // @ts-expect-error -- testing invalid bucket type
        timeBucket: "invalid",
      });
    }).toThrow("Invalid bucket type, valid values are 'minute', 'hour', 'day'");
  });
});
