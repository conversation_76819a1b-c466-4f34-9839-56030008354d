import * as d3 from "d3";

export type TIME_BUCKETS = "minute" | "hour" | "day";

// Generate an array of epoch ms times starting from the given start time and ending at the given time range
export function scaleTime({
  startTime,
  timeBucket,
  totalDurationMS,
}: {
  startTime: string;
  timeBucket: TIME_BUCKETS;
  // Total duration in milliseconds
  totalDurationMS: number;
}) {
  const startDate = new Date(startTime);

  // Calculate the duration in milliseconds
  const endTime = new Date(startDate.getTime() + totalDurationMS);

  // The scale should start at midnight for "day", the start of the hour for "hour",
  // and the start of the minute for "minute"
  if (timeBucket === "day") {
    startDate.setHours(0, 0, 0, 0);
  } else if (timeBucket === "hour") {
    startDate.setMinutes(0, 0, 0);
  } else if (timeBucket === "minute") {
    startDate.setSeconds(0, 0);
  } else {
    throw new Error(
      "Invalid bucket type, valid values are 'minute', 'hour', 'day'",
    );
  }

  const scale = d3
    .scaleTime()
    .domain([startDate, endTime])
    .ticks(
      timeBucket === "minute"
        ? d3.timeMinute
        : timeBucket === "hour"
          ? d3.timeHour
          : d3.timeDay,
    );

  return scale.map((date) => date.getTime());
}

export const timeBucketToDuration = (timeBucket: TIME_BUCKETS) => {
  if (timeBucket === "day") {
    return 86400_000;
  }
  if (timeBucket === "minute") {
    return 60_000;
  }
  return 3600_000;
};
