import { type Coords } from "#/ui/charts/Chart";
import {
  isDatapointHighlighted,
  type HighlightState,
} from "#/ui/charts/highlight";
import { useChartResponsiveWidth } from "#/ui/charts/responsiveHooks";
import { cn } from "#/utils/classnames";
import * as d3 from "d3";
import React, { useCallback, useMemo } from "react";
import { DEFAULT_COLOR_CLASSNAME } from "./colors";
import { type SelectionType } from "./selectionTypes";
import { ChartSymbol } from "./symbols";

export type DataPoint<Metadata> = {
  point: [string | number, number];
  className: string;
  symbolIndex: number;
  colorGroup: SelectionType;
  symbolGroup: SelectionType;
  isSelected?: boolean;
  metadata: Metadata;
};

export type NearestScatterplotItems<T> = {
  x: string | number;
  y: number;
  dataPoints: DataPoint<T>[];
};

type ScatterplotChartProps<DataPointMetadata> = {
  height: number;
  data: DataPoint<DataPointMetadata>[];
  yAxisBounds?: [number, number];
  highlightState?: HighlightState;
  renderTooltip?: (d: DataPoint<DataPointMetadata>) => React.ReactNode;
  onClick?: (d: DataPointMetadata[]) => void;
  onHover?: (d: DataPointMetadata[]) => void;
};

export function useScatterPlot<T>({
  height,
  data,
  yAxisBounds,
  highlightState,
  renderTooltip,
  onClick,
  onHover,
}: ScatterplotChartProps<T>) {
  const { ref, boundsWidth, leftAxisWidth, onLeftAxisWidth } =
    useChartResponsiveWidth();

  const xScale = useMemo(() => {
    let hasStringValue = false;
    const xValuesSet = data.reduce((acc, d) => {
      if (!hasStringValue && typeof d.point[0] === "string") {
        hasStringValue = true;
      }

      acc.add(d.point[0]);
      return acc;
    }, new Set<string | number>());

    if (hasStringValue) {
      return d3
        .scalePoint()
        .domain([...xValuesSet.keys()].toSorted().map((v) => `${v}`))
        .range([0, boundsWidth])
        .padding(0.5);
    }

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const xValues = [...xValuesSet.keys()] as number[];
    return d3
      .scaleLinear()
      .domain([Math.min(...xValues), Math.max(...xValues)])
      .range([0, boundsWidth])
      .clamp(true);
  }, [boundsWidth, data]);

  const yScale = useMemo(() => {
    const yValues = data.map((d) => d.point[1]);
    const domain = yAxisBounds ?? [Math.min(...yValues), Math.max(...yValues)];
    return d3
      .scaleLinear()
      .domain(domain)
      .range([0, height])
      .nice()
      .clamp(true);
  }, [height, data, yAxisBounds]);

  const getNearestPoints = useCallback(
    (coords: Coords) => {
      if (coords.x == null || coords.y == null) {
        return null;
      }

      // use screen coordinates so that values aren't skewed by x/y datapoint ranges
      const x = coords.x * xScale.range()[1];
      const y = (1 - coords.y) * yScale.range()[1];
      let min = null;
      let results: DataPoint<T>[] = [];
      for (const d of data) {
        if (min && min.x === d.point[0] && min.y === d.point[1]) {
          results.push(d);
          continue;
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        const xScaled = xScale(d.point[0] as any);
        if (xScaled == null) {
          continue;
        }
        const dist = distance2([x, y], [xScaled, yScale(d.point[1])]);
        if ((min == null || dist < min.dist) && dist < 2500) {
          min = {
            x: d.point[0],
            y: d.point[1],
            dist,
          };
          results = [d];
        }
      }

      return min == null ? null : { x: min.x, y: min.y, dataPoints: results };
    },
    [xScale, yScale, data],
  );

  const onChartHover = useCallback(
    (coords: Coords) => {
      if (!onHover) return;

      const nearestPoints = getNearestPoints(coords);
      onHover(nearestPoints?.dataPoints.map((d) => d.metadata) ?? []);
    },
    [onHover, getNearestPoints],
  );

  const onPointsClick = useCallback(
    (coords: Coords) => {
      if (!onClick) return;
      const nearestPoints = getNearestPoints(coords);
      if (nearestPoints == null) {
        return null;
      }
      onClick(nearestPoints.dataPoints.map((d) => d.metadata));
    },
    [onClick, getNearestPoints],
  );

  const renderTooltipForData = useCallback(
    (nearestPoints: NearestScatterplotItems<T> | null) => {
      if (!renderTooltip || nearestPoints == null) return;

      const numPoints = nearestPoints.dataPoints.length;
      return (
        <div>
          {numPoints > 1 && (
            <div className="border-b pb-2">{numPoints} points</div>
          )}
          <div className="divide-y">
            {/*
              with overlapping points, the last point gets rendered on top
              so reverse the tooltip order so that the tooltip order follows the point render order
            */}
            {nearestPoints.dataPoints.toReversed().map((d, i) => (
              <div
                key={i}
                className={cn(
                  numPoints > 1 && (i === numPoints - 1 ? "pt-2" : "py-2"),
                )}
              >
                {renderTooltip(d)}
              </div>
            ))}
          </div>
        </div>
      );
    },
    [renderTooltip],
  );

  const renderChartContent = useCallback(
    (nearestPoints: NearestScatterplotItems<T> | null) => {
      if (data.length === 0) return null;

      let hasSelectedPoints = false;
      for (const p of data) {
        if (p.isSelected) {
          hasSelectedPoints = true;
          break;
        }
      }

      const isHighlighting = nearestPoints != null || hasSelectedPoints;
      const sortedData = data.toSorted((a, b) => {
        // render the highlighted points last so it gets painted on top
        const aColored = calculateIsColored({
          isHighlighted: isDatapointHighlighted(a, highlightState),
          isHighlighting,
          isFocused: undefined,
          isSelected: a.isSelected,
        });

        const bColored = calculateIsColored({
          isHighlighted: isDatapointHighlighted(b, highlightState),
          isHighlighting,
          isFocused: undefined,
          isSelected: b.isSelected,
        });

        return aColored === bColored ? 0 : aColored ? 1 : -1;
      });
      const chart = sortedData.map((datum, i) => {
        const {
          point: [x, y],
          className,
          symbolIndex,
          isSelected,
        } = datum;
        const isFocused = nearestPoints?.x === x && nearestPoints.y === y;
        const isColored = calculateIsColored({
          isHighlighted: isDatapointHighlighted(datum, highlightState),
          isHighlighting,
          isFocused,
          isSelected,
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        const cx = xScale(x as any);
        const cy = yScale(y);
        return (
          <ChartSymbol
            key={i}
            className={cn("transition-opacity", {
              [className]: isColored,
              [`${DEFAULT_COLOR_CLASSNAME} opacity-30`]: !isColored,
            })}
            index={symbolIndex ?? 0}
            size={8}
            isFocused={isFocused}
            x={cx}
            y={cy}
          />
        );
      });

      return chart;
    },
    [data, xScale, yScale, highlightState],
  );

  return {
    chartProps: {
      chartRef: ref,
      height,
      leftAxisWidth,
      renderChartContent,
      renderTooltip: renderTooltipForData,
      getNearestItems: getNearestPoints,
      xScale,
      yScale,
      onClick: onPointsClick,
      onMouseMove: onChartHover,
    },
    leftAxisProps: {
      onLeftAxisWidth,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      xScale: xScale as d3.ScaleLinear<number, number>,
      yScale,
    },
    bottomAxisProps: {
      xScale,
      yScale,
    },
  };
}

function distance2(p1: [number, number], p2: [number, number]) {
  const dx = p1[0] - p2[0];
  const dy = p1[1] - p2[1];
  return dx * dx + dy * dy;
}

function calculateIsColored({
  isHighlighted,
  isHighlighting,
  isFocused,
  isSelected,
}: {
  isHighlighted: boolean;
  isHighlighting: boolean;
  isFocused?: boolean;
  isSelected?: boolean;
}) {
  if (isHighlighted) {
    return true;
  }

  if (isFocused || isSelected) {
    return true;
  }

  if (!isHighlighting) {
    return true;
  }

  return false;
}
