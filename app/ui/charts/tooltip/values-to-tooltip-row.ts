import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import { NULL_DASH } from "#/ui/table/formatters/null-formatter";

interface ConversionProps {
  values: ({ value: number } | null)[];
  labels: string[];
  seriesColorMap?: { [key: string]: number | null };
  valueFormatOptions?: Intl.NumberFormatOptions;
  nearestIndex?: number;
  isStackedTotal?: boolean;
  numberFormatter?: (value: number) => string;
}

export const valuesToTooltipRows = (props: ConversionProps) => {
  const {
    values,
    labels,
    seriesColorMap,
    valueFormatOptions,
    nearestIndex,
    isStackedTotal,
    numberFormatter,
  } = props;
  return values.map((v, i) => {
    const labelName = labels[i];
    const seriesColor = (seriesColorMap ?? {})[labelName];
    const colorClassName =
      seriesColorMap == null
        ? (COLOR_CLASSNAMES[i] ?? DEFAULT_COLOR_CLASSNAME)
        : seriesColor != null
          ? COLOR_CLASSNAMES[seriesColor]
          : DEFAULT_COLOR_CLASSNAME;

    const showNull =
      v == null || Number.isNaN(v?.value) || v.value === undefined;
    return {
      className: colorClassName,
      label: labels[i],
      isNearest: nearestIndex === i || Boolean(isStackedTotal),
      // for sorting
      value: v || 0,
      valueString: showNull
        ? NULL_DASH
        : numberFormatter
          ? numberFormatter(v.value)
          : v.value.toLocaleString(undefined, {
              maximumFractionDigits: 2,
              ...valueFormatOptions,
            }),
    };
  });
};
