import type React from "react";
import { useState, useEffect, useCallback } from "react";

/**
 * Watch width and height of given div ref
 */
export function useResizeObserver(ref: React.RefObject<HTMLDivElement | null>) {
  const [dimensions, setDimensions] = useState({
    width: 0,
    height: 0,
  });

  const handleResize = useCallback(
    (entries: ResizeObserverEntry[]) => {
      if (!entries || entries.length === 0) {
        return;
      }
      const entry = entries[0];
      const { width, height } = entry.contentRect;
      setDimensions({ width, height });
    },
    [setDimensions],
  );

  useEffect(() => {
    if (!ref || !ref.current) {
      return;
    }

    const observer = new ResizeObserver(handleResize);
    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, [ref, handleResize]);

  return dimensions;
}
