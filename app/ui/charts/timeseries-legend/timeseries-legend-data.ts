import { type HighlightedGroupTypeEnum } from "#/ui/charts/highlight";
import {
  formatSelectionTypeName,
  type SelectionTypesEnum,
} from "#/ui/charts/selectionTypes";

import { type TimeseriesLegendLabelData } from "./timeseries-legend.types";

export function toLegendLabelData(
  groupData: Record<string, Record<SelectionTypesEnum, number>>,
  legendType: HighlightedGroupTypeEnum,
): TimeseriesLegendLabelData[] {
  const result = Object.entries(groupData).flatMap(([groupName, groupTypes]) =>
    Object.entries(groupTypes).map(([type, v]) => {
      const selectionType = {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        type: type as SelectionTypesEnum,
        value: groupName,
      };
      return {
        groupVal: selectionType,
        label: formatSelectionTypeName(selectionType),
        ...(legendType === "points"
          ? { type: "points" as const, colorIndex: v }
          : { type: "symbols" as const, symbolIndex: v }),
      };
    }),
  );
  return result;
}
