import { useMemo, useCallback } from "react";
import * as d3 from "d3"; // we will need d3.js
import { useChartResponsiveWidth } from "#/ui/charts/responsiveHooks";
import { cn } from "#/utils/classnames";
import { useBrush } from "./brush";
import {
  EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
  EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
} from "./colors";

type HistogramChartProps = {
  height: number;
  data: number[][];
  bucketCount?: number;
  bounds?: [number, number];
  onBrush?: (v: [number, number] | null) => void;
  dimComparisons?: boolean;
};

const DEFAULT_BUCKET_COUNT = 20;
// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
const DEFAULT_BOUNDS = [0, 1] as [number, number];

export const useHistogramChart = ({
  height,
  data,
  bucketCount = DEFAULT_BUCKET_COUNT,
  bounds = DEFAULT_BOUNDS,
  dimComparisons,
  onBrush,
}: HistogramChartProps) => {
  const { ref, boundsWidth, leftAxisWidth, onLeftAxisWidth } =
    useChartResponsiveWidth();

  const buckets = useMemo(() => {
    // Generate bucket boundaries
    const bucketBoundaries = d3.range(
      bounds[0],
      bounds[1],
      (bounds[1] - bounds[0]) / bucketCount,
    );
    return bucketBoundaries;
  }, [bounds, bucketCount]);

  const { xScale, ticks } = useMemo(() => {
    const scale = d3.scaleLinear().domain(bounds).range([0, boundsWidth]);
    return {
      xScale: scale,
      ticks: scale.ticks(bucketCount),
    };
  }, [boundsWidth, bounds, bucketCount]);

  const hasData = useMemo(() => {
    return data.some((d) => d.length > 0);
  }, [data]);

  const bucketData = useMemo(() => {
    const bucketGenerator = d3
      .bin()
      .value((d) => d)
      .domain(bounds)
      .thresholds(buckets);
    return data.map((d) => bucketGenerator(d));
  }, [data, bounds, buckets]);

  const yScale = useMemo(() => {
    const yValues = bucketData.flatMap((histogramData) => {
      return histogramData.map((d) => d.length);
    });
    const domain = [Math.min(...yValues), Math.max(...yValues)];
    return d3.scaleLinear().domain(domain).range([0, height]);
  }, [height, bucketData]);

  const renderChartContent = useCallback(() => {
    if (!hasData) {
      return null;
    }

    return bucketData.map((dataset, datasetIndex) => (
      <g key={datasetIndex}>
        {dataset.map((bin, binIndex) => {
          const barWidth = boundsWidth / bucketCount / bucketData.length;
          const x = xScale(bin.x0 ?? 0) + barWidth * datasetIndex;
          const barHeight = yScale(bin.length);

          return (
            <rect
              key={binIndex}
              x={x}
              y={1}
              width={barWidth}
              height={barHeight}
              rx={2}
              className={cn(
                [
                  cn(
                    EXPERIMENT_PRIMARY_COLOR_CLASSNAME,
                    "bg-primary-600 fill-primary-600",
                  ),
                  ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
                ][datasetIndex],
                "stroke-background",
                {
                  "opacity-50": dimComparisons && datasetIndex > 0,
                },
              )}
            />
          );
        })}
      </g>
    ));
  }, [
    xScale,
    yScale,
    bucketData,
    hasData,
    boundsWidth,
    bucketCount,
    dimComparisons,
  ]);

  const { brushRef, removeBrush } = useBrush({
    xScale,
    yScale,
    onBrush,
    snapTicks: ticks,
  });

  return {
    chartProps: {
      chartRef: ref,
      brushRef,
      leftAxisWidth,
      renderChartContent,
      xScale,
      yScale,
    },
    leftAxisProps: {
      onLeftAxisWidth,
      xScale,
      yScale,
    },
    bottomAxisProps: {
      xScale,
      yScale,
    },
    removeBrush,
  };
};
