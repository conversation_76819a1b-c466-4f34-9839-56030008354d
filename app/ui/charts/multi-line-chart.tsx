"use client";

import { type Coords } from "#/ui/charts/Chart";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import {
  isHighlightStateEmpty,
  isLineHighlighted,
  isLineSelected,
  isPointHighlighted,
  type HighlightState,
} from "#/ui/charts/highlight";
import { useChartResponsiveWidth } from "#/ui/charts/responsiveHooks";
import { cn } from "#/utils/classnames";
import * as d3 from "d3";
import * as React from "react";
import { useCallback, useMemo, useState } from "react";
import type { AnnotationData, Annotations } from "./annotations";
import { useBrush } from "./brush";
import {
  isEqualSelectionType,
  type SelectionType,
  type SelectionTypesEnum,
} from "./selectionTypes";
import { ChartSymbol } from "./symbols";
import { getNearestChartPoint } from "./tooltip/get-nearest-chart-point";

export type Datapoint<Metadata> = {
  x: number;
  y: ({
    value: number;
    symbolIndex?: number;
    symbolGroup?: SelectionType;
  } | null)[];
  metadata: Metadata;
  isSelected?: boolean;
};

export interface NearestMultilineItems {
  valueIndex: number;
  seriesIndex: number;
}

export type XTimeRange<DataPointMetadata> = {
  allXValues: number[];
  getEmptyMetadata: (value: number) => DataPointMetadata;
};

export type MultiLineChartProps<DataPointMetadata> = {
  height: number;
  // should be sorted by X
  data: Datapoint<DataPointMetadata>[];
  yAxisBounds?: [number, number];
  xAxisBounds?: [number, number];
  seriesMetadata: SelectionType[];
  // used to preserve line colors if lines have been selected/deselected
  seriesColorMap?: Record<string, Record<SelectionTypesEnum, number>>;
  highlightState?: HighlightState;
  pointSize?: number;
  hidePoints?: boolean;
  onClick?: (d: DataPointMetadata, event: React.MouseEvent) => void;
  onHover?: (d: DataPointMetadata | null) => void;
  onContextMenu?: (coords: Coords, d: DataPointMetadata) => void;
  renderTooltip?: (
    d: Datapoint<DataPointMetadata>,
    seriesMetadata: SelectionType[],
    nearestSeriesIndex: number,
  ) => React.ReactNode;
  annotations?: {
    data: Annotations;
    setData: React.Dispatch<React.SetStateAction<Annotations>>;
    // used to associate annotations even when views are filtered
    idFn: (d: Datapoint<DataPointMetadata>) => string;
    renderAnnotation: (
      d: Datapoint<DataPointMetadata>,
      seriesMetadata: SelectionType[],
      a: AnnotationData,
    ) => React.ReactNode;
  };
  onBrush?: (v: [number, number] | null) => void;
  // If provided, the chart will fill in missing data points with 0s
  // x values in 'data' must be in milliseconds for the fill to work
  xTimeRange?: XTimeRange<DataPointMetadata>;

  /** If x represents ordinal instead of continuous data */
  isOrdinalX?: boolean;
};

export type Point = {
  x: number;
  y: number;
  symbolIndex?: number;
  symbolGroup?: SelectionType;
  isSelected?: boolean;
};

export const useMultiLineChart = <T,>({
  height,
  data,
  yAxisBounds,
  xAxisBounds,
  seriesMetadata,
  seriesColorMap,
  highlightState,
  hidePoints = false,
  pointSize = 4,
  onClick,
  onHover,
  renderTooltip,
  annotations,
  onBrush,
  xTimeRange,
  isOrdinalX,
}: MultiLineChartProps<T>) => {
  const [isBrushing, setIsBrushing] = useState<boolean>(false);

  const chartData: Datapoint<T>[] = useMemo(() => {
    if (xTimeRange) {
      return fillEmptyDataPoints({
        data,
        xTimeRange,
        numberOfSeries: seriesMetadata.length,
      });
    }
    return data;
  }, [data, xTimeRange, seriesMetadata.length]);

  const { ref, boundsWidth, leftAxisWidth, onLeftAxisWidth } =
    useChartResponsiveWidth();

  const [xValues, xValuesWithYValue] = useMemo(() => {
    const xValuesWithYValue: { value: number; index: number }[] = [];
    const xValues: number[] = [];
    chartData.forEach((d, i) => {
      if (d.y.some((v) => v != null)) {
        xValuesWithYValue.push({
          value: d.x,
          index: i,
        });
      }
      xValues.push(d.x);
    });
    return [xValues, xValuesWithYValue];
  }, [chartData]);

  const xScale = useMemo(() => {
    const domain = [
      xAxisBounds?.[0] ?? Math.min(...xValues),
      xAxisBounds?.[1] ?? Math.max(...xValues),
    ];
    return d3.scaleLinear().domain(domain).range([0, boundsWidth]);
  }, [boundsWidth, xValues, xAxisBounds]);

  const yScale = useMemo(() => {
    const yValues = chartData.flatMap((d) =>
      (d.y || []).flatMap((v) => (v == null ? [] : [v.value])),
    );

    const minY = Math.min(...yValues);
    const maxY = Math.max(...yValues);

    // include zero in domain
    const autoDomain = [Math.min(0, minY), Math.max(0, maxY)];

    // add 5% padding
    const dy = 0.05 * (autoDomain[1] - autoDomain[0]);
    if (autoDomain[1] > 0) {
      autoDomain[1] += dy;
    } else {
      autoDomain[0] -= dy;
    }

    // in [0, 0] case, default to [0, 1]
    if (autoDomain[0] === 0 && autoDomain[1] === 0) {
      autoDomain[1] = 1;
    }

    const domain = yAxisBounds ?? autoDomain;
    return d3
      .scaleLinear()
      .domain(domain)
      .range([0, height])
      .nice()
      .clamp(true);
  }, [height, chartData, yAxisBounds]);

  const getNearestPoint = useCallback(
    (coords: Coords) => {
      return getNearestChartPoint(
        coords,
        xScale,
        yScale,
        xValuesWithYValue,
        chartData,
        isOrdinalX,
      );
    },
    [xScale, yScale, xValuesWithYValue, chartData, isOrdinalX],
  );

  const onChartHover = useCallback(
    (coords: Coords) => {
      if (!onHover) return;

      const nearestPoint = getNearestPoint(coords);
      onHover(
        nearestPoint != null
          ? chartData[nearestPoint.valueIndex].metadata
          : null,
      );
    },
    [onHover, chartData, getNearestPoint],
  );

  const onXClick = useCallback(
    (coords: Coords, event: React.MouseEvent) => {
      if (!onClick) return;
      // so clicking on the left axis doesn't trigger a click
      if (coords.x == null || coords.x < 0) return;

      const nearestPoint = getNearestPoint(coords);
      if (nearestPoint == null) {
        return null;
      }

      onClick(chartData[nearestPoint.valueIndex].metadata, event);
    },
    [onClick, chartData, getNearestPoint],
  );

  const visibleAnnotations = (annotations?.data ?? [])
    .flatMap(({ id }) => {
      const visibleData = chartData.find((d) => annotations?.idFn(d) === id);
      return visibleData ? [{ id, x: visibleData.x }] : [];
    })
    .toSorted((a, b) => a.x - b.x);

  const onXContextMenu = useCallback(
    (coords: Coords) => {
      if (!annotations) {
        return;
      }
      //if (!onContextMenu) return;
      // so clicking on the left axis doesn't trigger a click
      if (coords.x == null || coords.x < 0) return;

      const nearestPoint = getNearestPoint(coords);
      if (nearestPoint == null) {
        return null;
      }

      const id = annotations.idFn(chartData[nearestPoint.valueIndex]);
      annotations.setData((prev) => {
        if (prev.find((a) => a.id === id)) {
          return prev.filter((a) => a.id !== id);
        }
        return [...prev, { id, text: "" }];
      });
      //onContextMenu(coords, nearestData.metadata);
    },
    [annotations, chartData, getNearestPoint],
  );

  const renderTooltipForData = useCallback(
    (nearestPoint: NearestMultilineItems | null) => {
      if (!renderTooltip || nearestPoint == null) return;
      if (isBrushing) {
        return;
      }

      return renderTooltip(
        chartData[nearestPoint.valueIndex],
        seriesMetadata,
        nearestPoint.seriesIndex,
      );
    },
    [chartData, renderTooltip, seriesMetadata, isBrushing],
  );

  const [lineData, hasData, hasSelectedPoints] = useMemo(() => {
    const dataByLine: {
      [key: string]: {
        [key in SelectionTypesEnum]?: (Point | null)[];
      };
    } = {};

    let hasData = false;
    let hasSelectedPoints = false;

    chartData.forEach((d) => {
      if (!hasSelectedPoints && d.isSelected) {
        hasSelectedPoints = true;
      }
      d.y.map((v, j) => {
        if (!hasData && v != null) {
          hasData = true;
        }

        const key = seriesMetadata[j];
        dataByLine[key.value] = dataByLine[key.value] ?? {};
        dataByLine[key.value][key.type] = dataByLine[key.value][key.type] ?? [];

        const points = dataByLine[key.value][key.type]!;
        points.push(
          v == null
            ? null
            : {
                x: d.x,
                y: v.value,
                symbolIndex: v.symbolIndex,
                symbolGroup: v.symbolGroup,
                isSelected: d.isSelected,
              },
        );
      });
    });

    const lineData = Object.entries(dataByLine).flatMap(([seriesName, v]) =>
      Object.entries(v).map(([seriesType, points]) => ({
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        selectionType: {
          value: seriesName,
          type: seriesType,
        } as SelectionType,
        points,
      })),
    );

    return [lineData, hasData, hasSelectedPoints];
  }, [chartData, seriesMetadata]);

  const renderChartContent = useCallback(
    (
      nearestPoint: NearestMultilineItems | null,
      seriesEnabled: boolean[] | null,
    ) => {
      if (!hasData) {
        return null;
      }

      const lineGenerator = d3
        .line<{ x: number; y: number }>()
        .x((d) => xScale(d.x))
        .y((d) => yScale(d.y))
        .curve(d3.curveMonotoneX);

      const sortedLineDataEntries = lineData.toSorted((a, b) => {
        if (!highlightState || isHighlightStateEmpty(highlightState)) {
          return 0;
        }

        const aIndex = [
          highlightState.highlighted,
          ...highlightState.selected,
        ].findIndex(
          (highlighted) =>
            highlighted &&
            isEqualSelectionType(highlighted.groupVal, a.selectionType),
        );

        const bIndex = [
          highlightState.highlighted,
          ...highlightState.selected,
        ].findIndex(
          (highlighted) =>
            highlighted &&
            isEqualSelectionType(highlighted.groupVal, b.selectionType),
        );

        return (aIndex ?? Infinity) - (bIndex ?? Infinity);
      });

      const chart = sortedLineDataEntries.map(
        ({ selectionType: line, points }, i) => {
          if (seriesEnabled && !seriesEnabled[i]) {
            return null;
          }
          const colorClassName = cn({
            [COLOR_CLASSNAMES[i]]: seriesColorMap == null,
            [COLOR_CLASSNAMES[seriesColorMap?.[line.value]?.[line.type] ?? -1]]:
              seriesColorMap != null,
          });

          const lineClassName = calculateLineClassName({
            colorClassName,
            isDimmed: !isLineHighlighted(line, highlightState),
            isHidden: !isLineSelected(line, highlightState),
          });

          return (
            <g key={`path-${i}`} className="pointer-events-none">
              <path
                className={cn(lineClassName, "stroke-2 fill-none")}
                d={
                  lineGenerator(
                    points.filter(<T,>(v: T | null): v is T => v != null),
                  ) || ""
                }
                strokeLinejoin="round"
              />
              {!hidePoints &&
                points.map((point, j) => {
                  if (point == null) {
                    return;
                  }
                  const isFocused = Boolean(
                    point.isSelected ||
                      (nearestPoint?.valueIndex === j &&
                        nearestPoint?.seriesIndex === i) ||
                      isPointHighlighted(point, line, highlightState),
                  );

                  const pointClassName = calculatePointClassName({
                    colorClassName,
                    isDimmed:
                      !isHighlightStateEmpty(highlightState) &&
                      !isPointHighlighted(point, line, highlightState),
                    isHighlighting: nearestPoint != null || hasSelectedPoints,
                    isFocused: !!isFocused,
                    isHidden: !isLineSelected(line, highlightState),
                  });
                  const x = xScale(point.x);
                  const y = yScale(point.y);
                  return (
                    <ChartSymbol
                      key={j}
                      className={pointClassName}
                      index={point.symbolIndex ?? 0}
                      size={pointSize}
                      isFocused={isFocused}
                      x={x}
                      y={y}
                    />
                  );
                })}
            </g>
          );
        },
      );

      return chart;
    },
    [
      hasData,
      hasSelectedPoints,
      lineData,
      xScale,
      yScale,
      pointSize,
      hidePoints,
      seriesColorMap,
      highlightState,
    ],
  );

  const handleOnBrushStart = useCallback(() => {
    setIsBrushing(true);
  }, [setIsBrushing]);

  const handleOnBrushEnd = useCallback(() => {
    setIsBrushing(false);
  }, [setIsBrushing]);

  const { brushRef, removeBrush } = useBrush({
    xScale,
    yScale,
    onBrushStart: handleOnBrushStart,
    onBrushEnd: handleOnBrushEnd,
    onBrush,
    ignoreBrushClick: true,
  });

  return {
    chartProps: {
      chartRef: ref,
      brushRef: onBrush ? brushRef : null,
      height,
      leftAxisWidth,
      renderChartContent,
      xScale,
      yScale,
      shouldClipArea: true,
      onClick: onXClick,
      onMouseMove: onChartHover,
      onContextMenu: onXContextMenu,
      annotations: annotations
        ? {
            render: (point: { id: string }) => {
              const dataPoint = chartData.find(
                (d) => annotations.idFn(d) === point.id,
              );
              if (!dataPoint) {
                return null;
              }
              const annotation = annotations.data.find(
                (a) => a.id === point.id,
              );
              if (!annotation) {
                return null;
              }
              return annotations.renderAnnotation(
                dataPoint,
                seriesMetadata,
                annotation,
              );
            },
            points: visibleAnnotations,
          }
        : undefined,
      renderTooltip: renderTooltipForData,
      getNearestItems: getNearestPoint,
    },
    lineData,
    leftAxisProps: {
      onLeftAxisWidth,
      xScale,
      yScale,
    },
    bottomAxisProps: {
      xScale,
      yScale,
    },
    removeBrush,
  };
};

function calculateLineClassName({
  colorClassName,
  isDimmed,
  isHidden,
}: {
  colorClassName: string;
  isDimmed: boolean;
  isHidden: boolean;
}) {
  return cn(
    "transition-opacity",
    DEFAULT_COLOR_CLASSNAME,
    {
      "opacity-30": isDimmed,
      "opacity-0": isHidden,
    },
    colorClassName,
  );
}

function calculatePointClassName({
  colorClassName,
  isDimmed,
  isHighlighting,
  isFocused,
  isHidden,
}: {
  colorClassName: string;
  isDimmed: boolean;
  isHighlighting: boolean;
  isFocused: boolean;
  isHidden: boolean;
}) {
  return cn(
    "transition-opacity",
    DEFAULT_COLOR_CLASSNAME,
    {
      "opacity-30": isDimmed,
      "opacity-0": isHidden,
    },
    (!isHighlighting || isFocused) && colorClassName,
  );
}

export function fillEmptyDataPoints<T>({
  data,
  xTimeRange,
  numberOfSeries,
}: {
  data: Datapoint<T>[];
  xTimeRange: XTimeRange<T>;
  numberOfSeries: number;
}) {
  const { allXValues, getEmptyMetadata } = xTimeRange;

  // Snap data points to the nearest time slot
  const dataMap = new Map<number, Datapoint<T>>();
  data.forEach((d) => {
    const closestTimeSlot = allXValues.reduce((prev, curr) =>
      Math.abs(curr - d.x) < Math.abs(prev - d.x) ? curr : prev,
    );
    dataMap.set(closestTimeSlot, d);
  });

  return allXValues.map((xValue) => {
    const dataForTimeSlot = dataMap.get(xValue);

    if (dataForTimeSlot) {
      return {
        ...dataForTimeSlot,
        y: dataForTimeSlot.y.map((y) => y ?? { value: 0 }),
      };
    }

    return {
      x: xValue,
      y: Array.from({ length: numberOfSeries }, () => ({ value: 0 })),
      metadata: getEmptyMetadata(xValue),
    };
  });
}
