import type { XScale, YScale } from "#/ui/charts/chart.types";
import { useAtom } from "jotai";
import { globalTimeCursorAtom } from "#/ui/charts/store/atoms";
import { isTimestampWithinXScale } from "./is-timestamp-within-scale";
import type { NearestPoint } from "#/ui/charts/timeseries-data/chart-data.types";

interface TimeCursorProps {
  xScale: XScale;
  yScale: YScale;
  vizType?: "lines" | "bars";
  nearestPoint?: NearestPoint | null;
  timeBucketDuration?: number;
  timestamps?: number[];
  padLeft?: number; // only for <Chart />
}

export const TimeCursor = (props: TimeCursorProps) => {
  const {
    xScale,
    yScale,
    padLeft,
    nearestPoint,
    vizType = "lines",
    timeBucketDuration,
    timestamps,
  } = props;

  const [timestamp] = useAtom(globalTimeCursorAtom);
  // do not show if outside of x scale
  if (!isTimestampWithinXScale(timestamp, xScale) || !("ticks" in xScale)) {
    return;
  }

  // todo - assumption that all buckets are same duration is not accurate
  if (vizType === "bars") {
    let timeStart = 0;
    let timeEnd = 0 + (timeBucketDuration ?? 0);

    // use the time bucket of the last bin that starts before timestamp
    if (timestamps) {
      const start = timestamps.findLast((ts) => ts <= timestamp);
      if (start) {
        timeStart = start;
        timeEnd = start + (timeBucketDuration ?? 0);
      }
    }

    // when nearest point exists, use its explicit times instead
    if (nearestPoint) {
      timeStart = nearestPoint.timeStart;
      timeEnd = nearestPoint.timeEnd;
    }

    if (!timeStart) {
      return;
    }

    // for bars we draw a gray rectangle cursor at discrete time bucket
    const x1 = xScale(timeStart);
    const x2 = xScale(timeEnd);
    const height = Math.abs(yScale.range()[1] - yScale.range()[0]);
    return (
      <g>
        <rect
          x={x1}
          width={x2 - x1}
          y={0}
          height={height}
          fill="currentColor"
          opacity={0.2}
        />
      </g>
    );
  }

  // for lines we draw a thin vertical line at exact hover timestamp
  const x = xScale(timestamp) + (padLeft ?? 0);
  const y = 0;
  const height = Math.abs(yScale.range()[1] - yScale.range()[0]);
  return (
    <g transform={`translate(${x}, ${y})`}>
      <line
        x={0}
        y1={0}
        y2={height}
        stroke="currentColor"
        strokeWidth={0.5}
        opacity={1}
      />
    </g>
  );
};
