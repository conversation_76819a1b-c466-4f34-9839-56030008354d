import { cn } from "#/utils/classnames";
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip";

export enum TagColor {
  Red = "red",
  Orange = "orange",
  Amber = "amber",
  Yellow = "yellow",
  Lime = "lime",
  Emerald = "emerald",
  Tea<PERSON> = "teal",
  <PERSON><PERSON> = "cyan",
  Blue = "blue",
  Indigo = "indigo",
  Violet = "violet",
  Purple = "purple",
  Fuchsia = "fuchsia",
}

// We are accepting both name and label here because the api returns names but the component below taeks labels.
// The sorts in the codebase are done at different times, pre and post conversion, so we need to handle both cases.
export function tagSortFn(
  a: {
    name?: string;
    label?: string;
    position?: string | null;
    [key: string]: unknown;
  },
  b: {
    name?: string;
    label?: string;
    position?: string | null;
    [key: string]: unknown;
  },
): number {
  if (a.position && b.position) {
    return a.position.localeCompare(b.position);
  }
  if (a.position) {
    return -1; // a comes first
  }
  if (b.position) {
    return 1; // b comes first
  }
  const aName = a.name || a.label || "";
  const bName = b.name || b.label || "";
  return aName.localeCompare(bName); // fallback to name/label comparison
}

export const Tag = ({
  color,
  label,
  description,
  className,
  onTagClick,
}: {
  color?: string | null;
  label: string;
  description?: string;
  className?: string;
  onTagClick?: (tag: string) => void;
}) => {
  const colorVariants: Record<string, string> = {
    [TagColor.Red]:
      "bg-red-50 text-red-950 border-red-200 dark:bg-red-950 dark:text-red-50 dark:border-red-800",
    [TagColor.Orange]:
      "bg-orange-50 text-orange-950 border-orange-200 dark:bg-orange-950 dark:text-orange-50 dark:border-orange-800",
    [TagColor.Amber]:
      "bg-amber-50 text-amber-950 border-amber-200 dark:bg-amber-950 dark:text-amber-50 dark:border-amber-800",
    [TagColor.Yellow]:
      "bg-yellow-50 text-yellow-950 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-50 dark:border-yellow-800",
    [TagColor.Lime]:
      "bg-lime-50 text-lime-950 border-lime-200 dark:bg-lime-950 dark:text-lime-50 dark:border-lime-800",
    [TagColor.Emerald]:
      "bg-emerald-50 text-emerald-950 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-50 dark:border-emerald-800",
    [TagColor.Teal]:
      "bg-teal-50 text-teal-950 border-teal-200 dark:bg-teal-950 dark:text-teal-50 dark:border-teal-800",
    [TagColor.Cyan]:
      "bg-cyan-50 text-cyan-950 border-cyan-200 dark:bg-cyan-950 dark:text-cyan-50 dark:border-cyan-800",
    [TagColor.Blue]:
      "bg-blue-50 text-blue-950 border-blue-200 dark:bg-blue-950 dark:text-blue-50 dark:border-blue-800",
    [TagColor.Indigo]:
      "bg-indigo-50 text-indigo-950 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-50 dark:border-indigo-800",
    [TagColor.Violet]:
      "bg-violet-50 text-violet-950 border-violet-200 dark:bg-violet-950 dark:text-violet-50 dark:border-violet-800",
    [TagColor.Purple]:
      "bg-purple-50 text-purple-950 border-purple-200 dark:bg-purple-950 dark:text-purple-50 dark:border-purple-800",
    [TagColor.Fuchsia]:
      "bg-fuchsia-50 text-fuchsia-950 border-fuchsia-200 dark:bg-fuchsia-950 dark:text-fuchsia-50 dark:border-fuchsia-800",
  };

  const colorClassName = color
    ? colorVariants[color]
    : // use neutral color if no color is provided
      "bg-primary-50 text-primary-800 border-primary-200";

  const content = (
    <span
      title={label}
      className={cn(
        `rounded-full inline-block px-2 border max-w-full truncate`,
        {
          "cursor-pointer": !!onTagClick,
        },
        colorClassName,
        className,
      )}
      onClick={(e) => {
        if (!onTagClick) {
          return;
        }
        e.stopPropagation();
        onTagClick(label);
      }}
    >
      {label}
    </span>
  );

  if (!description) {
    return content;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>{content}</TooltipTrigger>
      <TooltipContent>{description}</TooltipContent>
    </Tooltip>
  );
};
