import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
} from "#/ui/dropdown-menu";
import { Button } from "#/ui/button";
import { BlueLink } from "#/ui/link";
import { useOrg } from "#/utils/user";
import { type RenderOption, renderOptions } from "#/utils/parse";
import { cn } from "#/utils/classnames";

export const ParseModeSelect = ({
  value,
  options,
  onSelect,
  isFieldReadOnly,
  showSetDefaultMessage,
  buttonClassName,
}: {
  value: RenderOption;
  options: RenderOption[];
  onSelect: (option: RenderOption) => void;
  isFieldReadOnly?: boolean;
  showSetDefaultMessage?: boolean;
  buttonClassName?: string;
}) => {
  const { name: orgName } = useOrg();

  return (
    <div className="flex flex-none items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size="xs"
            variant="ghost"
            className={cn(
              "flex h-7 items-center gap-1 px-0 font-normal transition-all text-primary-500 hover:px-1",
              buttonClassName,
            )}
            isDropdown
          >
            {renderOptions[value].name}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="bottom" align="start">
          <DropdownMenuLabel>Select view type</DropdownMenuLabel>
          {options.map((name) => (
            <DropdownMenuCheckboxItem
              key={name}
              onSelect={() => {
                onSelect(name);
              }}
              checked={name === value}
              className="flex items-baseline justify-between gap-4"
            >
              {renderOptions[name].name}
              {!isFieldReadOnly && renderOptions[name].readOnly && (
                <span className="text-xs text-primary-400">Read only</span>
              )}
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
        {showSetDefaultMessage && (
          <DropdownMenuLabel>
            Prefer a specific view type?{" "}
            <BlueLink href={`/app/${orgName}/settings/user`}>
              Set the default
            </BlueLink>
          </DropdownMenuLabel>
        )}
      </DropdownMenu>
    </div>
  );
};
