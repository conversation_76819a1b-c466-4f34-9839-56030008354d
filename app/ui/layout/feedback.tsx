"use client";

import { <PERSON><PERSON><PERSON><PERSON>, SUPPORT } from "#/utils/links";
import { useRef, useState } from "react";
import { Button } from "#/ui/button";
import { ExternalLink } from "#/ui/link";
import { submitUserFeedback } from "./actions";
import { Dialog, DialogContent, DialogTitle } from "#/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import TextArea from "#/ui/text-area";

export const Feedback = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
}) => {
  const [selectedEmoji, setSelectedEmoji] = useState<string | undefined>(
    undefined,
  );
  const feedbackOpen = open;
  const _setFeedbackOpen = setOpen;

  const [submitting, setSubmitting] = useState<
    "unsubmitted" | "submitting" | "submitted"
  >("unsubmitted");
  const [error, setError] = useState<string | undefined>(undefined);
  const [feedback, setFeedback] = useState<string>("");

  const resetTimeoutCallback = useRef<ReturnType<typeof setTimeout> | null>(
    null,
  );
  const resetFeedback = () => {
    setSelectedEmoji(undefined);
    setFeedback("");
    setSubmitting("unsubmitted");
    resetTimeoutCallback.current = null;
  };

  const setFeedbackOpen = (open: boolean) => {
    _setFeedbackOpen(open);
    if (resetTimeoutCallback.current) {
      clearTimeout(resetTimeoutCallback.current);
      resetFeedback();
    }
  };

  const submitFeedback = async () => {
    if (!feedback) {
      setError("Please enter feedback");
      return;
    }
    setSubmitting("submitting");
    setError(undefined);
    try {
      await submitUserFeedback({
        content: feedback,
        emoji: selectedEmoji,
        page: window.location.pathname,
      });
      setSubmitting("submitted");
    } catch (e) {
      setError(`${e}`);
      resetFeedback();
    }

    resetTimeoutCallback.current = setTimeout(() => {
      setFeedbackOpen(false);
      resetFeedback();
    }, 10000);
  };

  return (
    <Dialog
      open={feedbackOpen}
      onOpenChange={(open) => {
        setFeedbackOpen(open);
      }}
    >
      <VisuallyHidden>
        <DialogTitle>Feedback</DialogTitle>
      </VisuallyHidden>
      <DialogContent className="w-[400px] p-2">
        {submitting === "submitted" ? (
          <div className="px-4 py-8 text-sm">
            <p>
              Thank you 🙏. We are continuously improving the product and read
              every piece of feedback carefully.
            </p>
            <p className="mt-4">
              If you need to get in touch with us in the meantime, join us on{" "}
              <ExternalLink href={DISCORD}>Discord</ExternalLink> or send us{" "}
              <ExternalLink href={SUPPORT}>an email</ExternalLink>.
            </p>
          </div>
        ) : (
          <>
            <TextArea
              autoFocus
              className="flex max-h-96 min-h-36 grow overflow-auto p-2"
              placeholder="Bugs, requests, kudos, complaints..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            />
            <div className="flex flex-row justify-between">
              <div className="flex flex-row">
                <ButtonGroup
                  options={["😊", "😐", "😡", "😵‍💫"]}
                  selectedOption={selectedEmoji}
                  setSelectedOption={setSelectedEmoji}
                />
                {error && (
                  <div className="ml-3 mt-[0.1rem] text-sm text-red-500">
                    {error}
                  </div>
                )}
              </div>
              <Button
                className="-mt-1 h-6 p-4"
                size="sm"
                disabled={!feedback}
                isLoading={submitting === "submitting"}
                onClick={(e) => {
                  e.preventDefault();
                  submitFeedback();
                }}
              >
                Send
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

function ButtonGroup({
  options,
  selectedOption,
  setSelectedOption,
}: {
  options: string[];
  selectedOption?: string;
  setSelectedOption: (value?: string) => void;
}) {
  return (
    <div className="flex space-x-2">
      {options.map((option) => (
        <Button
          key={option}
          variant={option === selectedOption ? "primary" : "default"}
          onClick={() => setSelectedOption(option)}
          size="sm"
          className="h-6 p-1 text-lg"
        >
          {option}
        </Button>
      ))}
    </div>
  );
}
