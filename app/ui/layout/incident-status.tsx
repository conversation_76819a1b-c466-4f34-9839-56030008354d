"use client";

import { AlertTriangle } from "lucide-react";
import Link from "next/link";
import { cn } from "#/utils/classnames";
import { buttonVariants } from "#/ui/button";
import { useIncidentStatus } from "#/utils/incident-status";

export function IncidentStatus() {
  const incidentStatus = useIncidentStatus();
  if (!incidentStatus) return null;

  return (
    <Link
      href="https://statuspage.incident.io/braintrust-status"
      target="_blank"
      className={cn(
        buttonVariants({ variant: "ghost", size: "xs" }),
        "text-bad-800 bg-bad-50",
      )}
    >
      <AlertTriangle className="size-3" />
      {incidentStatus}
    </Link>
  );
}
