import { BreadcrumbsList, DefaultBreadcrumbSection } from "./breadcrumbs";
import { usePathname } from "next/navigation";
import { useIsSidenavDocked } from "#/app/app/[org]/sidenav-state";

import { getOrgLink } from "#/app/app/[org]/getOrgLink";
import {
  isBTQLSandboxPage,
  isLogsPage,
  isMonitorPage,
  isOrgPage,
  isProjectOverviewPage,
  isProjectPage,
  isSettingsPage,
  useActivePage,
} from "#/app/app/[org]/pathname-checker";
import { getProjectLink } from "#/app/app/[org]/p/[project]/getProjectLink";

export const HeaderDisplay = () => {
  const pathname = usePathname();
  const isSidenavDocked = useIsSidenavDocked();

  const activePage = useActivePage();

  const settings = isSettingsPage(pathname ?? "");
  if (settings) {
    return (
      <BreadcrumbsList
        list={[
          {
            label: settings.params.org?.toString() ?? "",
            hidden: isSidenavDocked,
            link: getOrgLink({
              orgName: settings.params.org?.toString() ?? "",
            }),
          },
          {
            label: "Settings",
            className: "font-semibold",
          },
        ]}
        className="text-sm"
      />
    );
  }

  const monitor = isMonitorPage(pathname ?? "");
  if (monitor) {
    return (
      <BreadcrumbsList
        list={[
          {
            label: monitor.params.org?.toString() ?? "",
            hidden: isSidenavDocked,
            link: getOrgLink({ orgName: monitor.params.org?.toString() ?? "" }),
          },
          { label: "Monitor", className: "font-semibold" },
        ]}
        className="text-sm"
      />
    );
  }

  const org = isOrgPage(pathname ?? "");
  if (org) {
    return (
      <BreadcrumbsList
        list={[
          {
            label: org.params.org?.toString() ?? "",
            hidden: isSidenavDocked,
            link: getOrgLink({ orgName: org.params.org?.toString() ?? "" }),
          },
          { label: "Projects", className: "font-semibold" },
        ]}
        className="text-sm"
      />
    );
  }

  const projectOverview = isProjectOverviewPage(pathname ?? "");
  if (projectOverview) {
    return (
      <BreadcrumbsList
        list={[
          {
            label: projectOverview.params.org?.toString() ?? "",
            hidden: isSidenavDocked,
            link: getOrgLink({
              orgName: projectOverview.params.org?.toString() ?? "",
            }),
          },
          {
            label: projectOverview.params.project?.toString() ?? "",
            link: getProjectLink({
              orgName: projectOverview.params.org?.toString() ?? "",
              projectName: projectOverview.params.project?.toString() ?? "",
            }),
          },
        ]}
        className="text-sm"
      />
    );
  }

  const logs = isLogsPage(pathname ?? "");
  const project = isProjectPage(pathname ?? "");
  if (project && !logs) {
    return (
      <>
        <BreadcrumbsList
          list={[
            {
              label: project.params.org?.toString() ?? "",
              hidden: isSidenavDocked,
              link: getOrgLink({
                orgName: project.params.org?.toString() ?? "",
              }),
            },
            {
              label: project.params.project?.toString() ?? "",
              hidden: isSidenavDocked,
              link: getProjectLink({
                orgName: project.params.org?.toString() ?? "",
                projectName: project.params.project?.toString() ?? "",
              }),
            },
            {
              label: activePage ?? "",
              className: "font-semibold capitalize",
              hidden: !isSidenavDocked,
            },
          ]}
          className="flex-nowrap truncate text-sm"
        />
      </>
    );
  }

  const btqlSandbox = isBTQLSandboxPage(pathname ?? "");
  if (btqlSandbox) {
    return (
      <BreadcrumbsList
        list={[
          {
            label: btqlSandbox.params.org?.toString() ?? "",
            hidden: isSidenavDocked,
            link: getOrgLink({
              orgName: btqlSandbox.params.org?.toString() ?? "",
            }),
          },
          { label: "BTQL sandbox", className: "font-semibold" },
        ]}
        className="text-sm"
      />
    );
  }

  return (
    <DefaultBreadcrumbSection
      omitProjects
      omitLeaf
      includeOrg={!isSidenavDocked}
      className="flex-nowrap text-sm text-primary-600"
    />
  );
};
