"use client";

import { cn } from "#/utils/classnames";
import { useParams } from "next/navigation";
import Footer from "#/ui/landing/footer";
import { HEIGHT_WITH_TOP_OFFSET } from "#/app/app/body-wrapper";

export function MainContentWrapper(
  props: React.HtmlHTMLAttributes<HTMLDivElement> & {
    contentTitle?: React.ReactNode;
    contentTitleClassName?: string;
    extraRightUI?: React.ReactNode;
    hideFooter?: boolean;
    hideFooterSpacer?: boolean;
  },
) {
  const {
    className,
    hideFooter,
    children,
    contentTitle,
    contentTitleClassName,
    extraRightUI,
    hideFooterSpacer,
    ...otherProps
  } = props;

  const params = useParams<{ org: string }>();
  return (
    <div
      className={cn(
        "pt-4 px-3 pb-5 flex flex-col",
        HEIGHT_WITH_TOP_OFFSET,
        className,
      )}
      {...otherProps}
    >
      <div className="flex flex-none flex-row justify-between">
        {contentTitle && (
          <h1
            className={cn(
              "mb-4 text-base font-semibold",
              contentTitleClassName,
            )}
          >
            {contentTitle}
          </h1>
        )}
        {extraRightUI}
      </div>
      {children}
      {!hideFooter && (
        <>
          {!hideFooterSpacer && <div className="grow"></div>}
          <Footer
            className="flex-none pb-0 sm:pb-0 lg:pb-0"
            inApp
            orgName={params?.org}
          />
        </>
      )}
    </div>
  );
}
