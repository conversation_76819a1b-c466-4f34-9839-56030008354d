import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "#/ui/dialog";
import { <PERSON><PERSON> } from "#/ui/button";
import { useCallback, useState } from "react";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { Spinner } from "#/ui/icons/spinner";
import { invokeServerAction } from "#/utils/invoke-server-action";
import {
  type OrgLookup,
  type UserLookup,
  type adminFindObject,
  type ObjectLookup,
  type FindObjectResult,
} from "#/app/admin/actions";
import { useAuth } from "@clerk/nextjs";
import { BlueLink } from "#/ui/link";
import { useHotkeys } from "react-hotkeys-hook";

export function AdminObjectFinder() {
  const [open, setOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [objectEntry, setObjectEntry] = useState<FindObjectResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  useHotkeys(
    ["meta+k", "ctrl+k"],
    () => {
      setOpen(true);
    },
    [setOpen],
  );

  const { getToken } = useAuth();
  const findObjectId = useCallback(
    async (objectId: string) => {
      try {
        setIsLoading(true);
        const entry = await invokeServerAction<typeof adminFindObject>({
          fName: "adminFindObject",
          args: {
            objectId,
          },
          getToken,
        });

        setObjectEntry(entry);
        setError(null);
      } catch (error) {
        setError(error instanceof Error ? error.message : "Unknown error");
      } finally {
        setIsLoading(false);
      }
    },
    [getToken],
  );

  const debouncedObjectId = useDebouncedCallback(findObjectId, 500);

  return (
    <>
      <Button size="xs" onClick={() => setOpen(true)}>
        Object finder
      </Button>

      {open && (
        <Dialog open onOpenChange={setOpen}>
          <DialogContent
            className="flex flex-col pb-0 sm:min-h-[600px] sm:min-w-[600px]"
            hideCloseButton
          >
            <DialogHeader>
              <DialogTitle>Object finder</DialogTitle>
              <DialogDescription />
              <div className="flex flex-col gap-2">
                <div className="flex flex-1 flex-row">
                  <PlainInput
                    placeholder="Find object"
                    onChange={(e) => debouncedObjectId(e.target.value)}
                    className="h-7 flex-1 border-0 pl-7 outline-none transition-all bg-transparent hover:bg-primary-100 focus:bg-primary-100"
                  />
                  {isLoading && <Spinner className="size-3" />}
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {objectEntry &&
                  (objectEntry.type === "org" ? (
                    <OrgLookupResult orgLookup={objectEntry.object} />
                  ) : objectEntry.type === "object" ? (
                    <ObjectLookupResult objectLookup={objectEntry.object} />
                  ) : (
                    <UserLookupResult userLookup={objectEntry.user} />
                  ))}
              </div>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

function OrgLookupResult({ orgLookup }: { orgLookup: OrgLookup["object"] }) {
  return (
    <div className="mt-4 flex flex-col gap-2">
      <div className="text-sm">
        <BlueLink href={`/admin/org/${encodeURIComponent(orgLookup.org_name)}`}>
          Org: {orgLookup.org_name} ({orgLookup.org_id})
        </BlueLink>
      </div>
      <div className="text-sm">Created: {orgLookup.created}</div>
    </div>
  );
}

export function ObjectLookupResult({
  objectLookup,
}: {
  objectLookup: ObjectLookup["object"];
}) {
  return (
    <div className="mt-4 flex flex-col gap-2">
      <div className="text-sm">
        <BlueLink
          href={`/admin/org/${encodeURIComponent(objectLookup.org_name)}`}
        >
          Org: {objectLookup.org_name} ({objectLookup.org_id})
        </BlueLink>
      </div>
      <div className="text-sm">
        <BlueLink
          href={`/admin/org/${encodeURIComponent(
            objectLookup.org_name,
          )}/${encodeURIComponent(objectLookup.project_name)}`}
        >
          Project: {objectLookup.project_name} ({objectLookup.project_id})
        </BlueLink>
      </div>
      <div className="text-sm">
        <BlueLink href={makeFullObjectLink(objectLookup)}>
          {objectLookup.object_type}: {objectLookup.name}
          <br />
          {objectLookup.object_id}
        </BlueLink>
      </div>
      <div className="text-sm">Created: {objectLookup.created}</div>
    </div>
  );
}

function UserLookupResult({ userLookup }: { userLookup: UserLookup["user"] }) {
  return (
    <div className="mt-4 flex flex-col gap-2">
      <div className="text-sm">
        <BlueLink href={`/admin/user/${encodeURIComponent(userLookup.email)}`}>
          User: {userLookup.email}
        </BlueLink>
      </div>
      {userLookup.orgs.map((org) => (
        <div className="text-sm" key={org.org_id}>
          <BlueLink href={`/admin/org/${encodeURIComponent(org.org_name)}`}>
            Org: {org.org_name} ({org.org_id})
          </BlueLink>
        </div>
      ))}
    </div>
  );
}

export function makeFullObjectLink(objectEntry: ObjectLookup["object"]) {
  return `/admin/org/${encodeURIComponent(
    objectEntry.org_name,
  )}/${encodeURIComponent(
    objectEntry.project_name,
  )}?search=${encodeURIComponent(objectEntry.object_id)}`;
}
