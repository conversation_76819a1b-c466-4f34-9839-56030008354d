import * as React from "react";
import { Dialog, DialogContent, DialogTitle } from "#/ui/dialog";
import { type DialogProps } from "@radix-ui/react-dialog";
import { Skeleton } from "#/ui/skeleton";

export const ChangelogDialog = (props: DialogProps) => {
  const [isLoading, setIsLoading] = React.useState(true);
  return (
    <Dialog {...props}>
      <DialogContent className="relative h-[calc(100vh-100px)] w-full overflow-hidden p-0 sm:max-w-screen-xl">
        <DialogTitle>Changelog</DialogTitle>
        <iframe
          src="/changelog-content"
          className="absolute inset-0 size-full"
          onLoad={() => setIsLoading(false)}
        />
        {isLoading && (
          <div className="absolute inset-0 z-10 flex flex-col gap-2 p-5 bg-background">
            <div className="mb-5 text-4xl font-semibold tracking-tight">
              Changelog
            </div>
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
