import { ArrowRight } from "lucide-react";

export const ExternalLink = ({
  children,
  href,
}: {
  children: React.ReactNode;
  href: string;
}) => {
  return (
    <a
      href={href}
      className="inline-flex gap-x-2 rounded-lg px-3 py-1 text-sm font-medium no-underline bg-gray-700 text-gray-100 hover:bg-gray-500 hover:text-white"
    >
      <div>{children}</div>

      <ArrowRight className="block w-4" />
    </a>
  );
};
