import { cn } from "#/utils/classnames";
import { Spinner } from "#/ui/icons/spinner";
import { Check, TriangleAlert } from "lucide-react";
import { But<PERSON> } from "./button";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

export type SavingState = "saving" | "saved" | "none" | Error;

export function mergeStates(states: SavingState[]): SavingState {
  let curr: SavingState = "none";
  for (const state of states) {
    if (state instanceof Error) {
      curr = state;
    }

    if (!(curr instanceof Error)) {
      if (state === "saving") {
        curr = "saving";
      }

      if (curr !== "saving") {
        if (state === "saved") {
          curr = "saved";
        }
      }
    }
  }
  return curr;
}

export function SavingStatus({
  state,
  className,
  iconClassName,
}: {
  state: SavingState;
  iconClassName?: string;
  className?: string;
}) {
  if (state instanceof Error) {
    return (
      <Popover defaultOpen={true}>
        <PopoverTrigger asChild>
          <Button
            size="xs"
            variant="ghost"
            Icon={TriangleAlert}
            className="text-bad-600"
          >
            Error
          </Button>
        </PopoverTrigger>
        <PopoverContent className="overflow-auto whitespace-pre-wrap break-words text-xs">{`${state}`}</PopoverContent>
      </Popover>
    );
  }

  return (
    <div
      className={cn(
        "flex items-center gap-1 text-sm text-primary-600 transition-opacity duration-200",
        state === "saving" || state === "saved" ? "opacity-100" : "opacity-0",
        className,
      )}
    >
      {state === "saving" ? (
        <>
          <Spinner className={cn("size-3 flex-none", iconClassName)} />
          Saving
        </>
      ) : (
        <>
          <Check className={cn("size-3 flex-none", iconClassName)} />
          Saved
        </>
      )}
    </div>
  );
}
