@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --max-width: 1100px;
  --border-radius: 12px;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
  --primary-body: #222;
  --background: white;
  --destructive: 235, 66, 0;
  --destructive-foreground: 210 40% 98%;
  --muted: #f5f5f5;
  --muted-foreground: #555;
  --popover: var(--background);
  --popover-foreground: var(--foreground-rgb);

  /* zinc from tailwind */
  --zinc-50: 250 250 250;
  --zinc-100: 244 244 245;
  --zinc-200: 228 228 231;
  --zinc-300: 212 212 216;
  --zinc-400: 161 161 170;
  --zinc-500: 113 113 122;
  --zinc-600: 82 82 91;
  --zinc-700: 63 63 70;
  --zinc-800: 39 39 42;
  --zinc-900: 24 24 27;
  --zinc-950: 9 9 11;

  --blue-50: 239 246 255;
  --blue-100: 219 234 254;
  --blue-200: 191 219 254;
  --blue-300: 147 197 253;
  --blue-400: 96 165 250;
  --blue-500: 59 130 246;
  --blue-600: 37 99 235;
  --blue-700: 29 78 216;
  --blue-800: 30 64 175;
  --blue-900: 30 58 138;
  --blue-950: 23 37 84;

  --violet-50: 245 243 255;
  --violet-100: 237 233 254;
  --violet-200: 221 214 254;
  --violet-300: 196 181 253;
  --violet-400: 167 139 250;
  --violet-500: 139 92 246;
  --violet-600: 124 58 237;
  --violet-700: 109 40 217;
  --violet-800: 91 33 182;
  --violet-900: 76 29 149;
  --violet-950: 46 16 101;

  --green-50: 240 253 244;
  --green-100: 220 252 231;
  --green-200: 187 247 208;
  --green-300: 134 239 172;
  --green-400: 74 222 128;
  --green-500: 34 197 94;
  --green-600: 22 163 74;
  --green-700: 21 128 61;
  --green-800: 22 101 52;
  --green-900: 20 83 45;
  --green-950: 5 46 22;

  --pink-50: 253 242 248;
  --pink-100: 252 231 243;
  --pink-200: 251 207 232;
  --pink-300: 249 168 212;
  --pink-400: 244 114 182;
  --pink-500: 236 72 153;
  --pink-600: 219 39 119;
  --pink-700: 190 24 93;
  --pink-800: 157 23 77;
  --pink-900: 131 24 67;
  --pink-950: 112 26 52;

  --primary-50: var(--zinc-50);
  --primary-100: var(--zinc-100);
  --primary-200: var(--zinc-200);
  --primary-300: var(--zinc-300);
  --primary-400: var(--zinc-400);
  --primary-500: var(--zinc-500);
  --primary-600: var(--zinc-600);
  --primary-700: var(--zinc-700);
  --primary-800: var(--zinc-800);
  --primary-900: var(--zinc-900);
  --primary-950: var(--zinc-950);

  --accent-50: var(--blue-50);
  --accent-100: var(--blue-100);
  --accent-200: var(--blue-200);
  --accent-300: var(--blue-300);
  --accent-400: var(--blue-400);
  --accent-500: var(--blue-500);
  --accent-600: var(--blue-600);
  --accent-700: var(--blue-700);
  --accent-800: var(--blue-800);
  --accent-900: var(--blue-900);
  --accent-950: var(--blue-950);

  --comparison-50: var(--violet-50);
  --comparison-100: var(--violet-100);
  --comparison-200: var(--violet-200);
  --comparison-300: var(--violet-300);
  --comparison-400: var(--violet-400);
  --comparison-500: var(--violet-500);
  --comparison-600: var(--violet-600);
  --comparison-700: var(--violet-700);
  --comparison-800: var(--violet-800);
  --comparison-900: var(--violet-900);
  --comparison-950: var(--violet-950);

  --good-50: var(--green-50);
  --good-100: var(--green-100);
  --good-200: var(--green-200);
  --good-300: var(--green-300);
  --good-400: var(--green-400);
  --good-500: var(--green-500);
  --good-600: var(--green-600);
  --good-700: var(--green-700);
  --good-800: var(--green-800);
  --good-900: var(--green-900);
  --good-950: var(--green-950);

  --bad-50: var(--pink-50);
  --bad-100: var(--pink-100);
  --bad-200: var(--pink-200);
  --bad-300: var(--pink-300);
  --bad-400: var(--pink-400);
  --bad-500: var(--pink-500);
  --bad-600: var(--pink-600);
  --bad-700: var(--pink-700);
  --bad-800: var(--pink-800);
  --bad-900: var(--pink-900);
  --bad-950: var(--pink-950);
}

html.dark {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;

  --primary-glow: radial-gradient(rgba(1, 65, 255, 0.4), rgba(1, 65, 255, 0));
  --secondary-glow: linear-gradient(
    to bottom right,
    rgba(1, 65, 255, 0),
    rgba(1, 65, 255, 0),
    rgba(1, 65, 255, 0.3)
  );

  --tile-start-rgb: 2, 13, 46;
  --tile-end-rgb: 2, 5, 19;
  --tile-border: conic-gradient(
    #ffffff80,
    #ffffff40,
    #ffffff30,
    #ffffff20,
    #ffffff10,
    #ffffff10,
    #ffffff80
  );

  --callout-rgb: 20, 20, 20;
  --callout-border-rgb: 108, 108, 108;
  --card-rgb: 100, 100, 100;
  --card-border-rgb: 200, 200, 200;
  --background: #000;
  --destructive: 235, 66, 0;
  --destructive-foreground: 210 40% 98%;
  --muted: #393939;
  --muted-foreground: #aaa;
  --popover: var(--background);
  --popover-foreground: var(--foreground-rgb);

  --primary-50: var(--zinc-950);
  --primary-100: var(--zinc-900);
  --primary-200: var(--zinc-800);
  --primary-300: var(--zinc-700);
  --primary-400: var(--zinc-600);
  --primary-500: var(--zinc-500);
  --primary-600: var(--zinc-400);
  --primary-700: var(--zinc-300);
  --primary-800: var(--zinc-200);
  --primary-900: var(--zinc-100);
  --primary-950: var(--zinc-50);

  --accent-50: var(--blue-950);
  --accent-100: var(--blue-900);
  --accent-200: var(--blue-800);
  --accent-300: var(--blue-700);
  --accent-400: var(--blue-600);
  --accent-500: var(--blue-500);
  --accent-600: var(--blue-400);
  --accent-700: var(--blue-300);
  --accent-800: var(--blue-200);
  --accent-900: var(--blue-100);
  --accent-950: var(--blue-50);

  --comparison-50: var(--violet-950);
  --comparison-100: var(--violet-900);
  --comparison-200: var(--violet-800);
  --comparison-300: var(--violet-700);
  --comparison-400: var(--violet-600);
  --comparison-500: var(--violet-500);
  --comparison-600: var(--violet-400);
  --comparison-700: var(--violet-300);
  --comparison-800: var(--violet-200);
  --comparison-900: var(--violet-100);
  --comparison-950: var(--violet-50);

  --good-50: var(--green-950);
  --good-100: var(--green-900);
  --good-200: var(--green-800);
  --good-300: var(--green-700);
  --good-400: var(--green-600);
  --good-500: var(--green-500);
  --good-600: var(--green-400);
  --good-700: var(--green-300);
  --good-800: var(--green-200);
  --good-900: var(--green-100);
  --good-950: var(--green-50);

  --bad-50: var(--pink-950);
  --bad-100: var(--pink-900);
  --bad-200: var(--pink-800);
  --bad-300: var(--pink-700);
  --bad-400: var(--pink-600);
  --bad-500: var(--pink-500);
  --bad-600: var(--pink-400);
  --bad-700: var(--pink-300);
  --bad-800: var(--pink-200);
  --bad-900: var(--pink-100);
  --bad-950: var(--pink-50);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  @apply font-inter bg-background;
  overscroll-behavior: none;
}

a {
  color: inherit;
  text-decoration: none;
}

html.dark {
  color-scheme: dark;
  color: #ededed;

  body {
    color-scheme: dark;
    color: #ededed;
  }
}

@layer base {
  *,
  ::before,
  ::after {
    @apply border-primary-200;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* This corresponds to our use of the react-image-crop
 * library, and it prevents a big blue outline from appearing
  * when the crop selection is focused.
  */
.ReactCrop__crop-selection:focus {
  outline: none !important;
}

.prose h1 > a,
.prose h2 > a,
.prose h3 > a,
.prose h4 > a,
.prose h5 > a,
.prose h6 > a {
  text-decoration: none !important;
  font-weight: 500 !important;
}

.prose li::before {
  flex: none;
}

.prose li > p:only-child {
  margin: 0;
  display: inline;
}

.blog-article .prose h1 > a {
  font-weight: 400 !important;
}

.shiki,
.shiki span {
  background-color: transparent !important;
}
pre.shiki {
  white-space: pre-wrap;
}

html.dark .shiki:not(.disable-shiki-dark-override),
html.dark .shiki:not(.disable-shiki-dark-override) span {
  color: var(--shiki-dark) !important;
  background-color: transparent !important;
  /* Optional, if you also want font styles */
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

.dark .customer-logos {
  opacity: 0.8;
}
.dark .customer-logos svg * {
  fill: currentColor !important;
  opacity: 1 !important;
}

.font-planar {
  font-feature-settings: "ss03";
}

.cursor-grabbing-global * {
  cursor: grabbing !important;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(3px);
  }
  75% {
    transform: translateX(-3px);
  }
}
