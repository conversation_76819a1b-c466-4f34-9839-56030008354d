@keyframes delay-overflow {
  to {
    position: absolute;

    white-space: pre-line;
    overflow: visible;
    text-overflow: unset;
    width: 400px;
    min-width: 100%;
    max-width: max-content;

    background: inherit;

    outline-width: 1px;
    outline-style: solid;
  }
}

.table {
  font-size: 14px;
  width: 100%;
  position: relative;
}

.draggable:hover > * {
  opacity: 1;
}
.droppable:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  height: 100%;
  @apply bg-accent-500;
  @apply rounded-md;
}

.table input:focus {
  border-color: #4e6172;
  box-shadow: none;
}
.table select {
  font-size: 11px;
}
.table select:focus {
  border-color: #4e6172;
  box-shadow: none;
}

.expanding:hover > * {
  animation: delay-overflow 0ms 500ms forwards;
  top: 0;
  left: 0;
}
.search-wrapper:focus {
  display: none;
}

.dropdown {
  z-index: 100;
}

.editablecell {
  all: inherit;
  margin: 0;
  width: 100%;
}
