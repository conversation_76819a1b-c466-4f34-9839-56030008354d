#!/usr/bin/env python3
"""
This script creates a `.env.local` file to be compiled into the NextJS webapp.
Make sure anybody running this has installed the `dotenv` package in docker
using `apt-get install python3-dotenv`.
"""

import shutil

import dotenv

# Keep this in sync with the identical dict in other docker scripts.
#
# These env vars are compiled into the production webapp. Because they are
# affected by the user's env settings, we need to update them dynamically
# each time we start the app. So we insert placeholder strings in place,
# which we then search-and-replace for when starting the app.
APP_COMPILED_ENV = dict(
    NEXT_PUBLIC_SUPABASE_URL="3b205f33-0144-49fe-a899-5ebc7751ebf1",
    NEXT_PUBLIC_SUPABASE_ANON_KEY="1437d66d-a7aa-45fb-84c2-e1a924dc5d26",
    NEXT_PUBLIC_MULTI_TENANT_API_URL="445812f5-1dba-4f41-9267-c37db18f6063",
    NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL="1b2d1ede-5494-4dbf-8bdd-a2c948f85b2f",
)

if __name__ == "__main__":
    FULLPATH = "/braintrust/app/.env.local"
    shutil.copy2("/braintrust/app/.env.development", FULLPATH)
    for key, val in APP_COMPILED_ENV.items():
        dotenv.set_key(FULLPATH, key, val)
    dotenv.set_key(FULLPATH, "NEXT_PUBLIC_API_KEY_AUTH", "1")
