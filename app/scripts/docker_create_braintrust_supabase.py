#!/usr/bin/env python3
"""
This script creates a supabase NPM project for running migrations in the app
Docker image. Make sure anybody running this has installed the `toml` package in
docker using `apt-get install python3-toml`.
"""

import json
import os
import shutil

import toml

if __name__ == "__main__":
    APP_DIR = "/braintrust/app"
    SUPABASE_DIRNAME = "/braintrust-supabase"
    os.mkdir(SUPABASE_DIRNAME)

    shutil.copytree(
        os.path.join(APP_DIR, "supabase"),
        os.path.join(SUPABASE_DIRNAME, "supabase"),
    )
    config_fullpath = os.path.join(SUPABASE_DIRNAME, "supabase", "config.toml")
    with open(config_fullpath) as f:
        config_data = toml.load(f)
    config_data["project_id"] = "braintrust-standalone"
    with open(config_fullpath, "w") as f:
        toml.dump(config_data, f)

    with open(os.path.join(APP_DIR, "package.json")) as f:
        obj = json.load(f)
        supabase_cli_version = obj["devDependencies"]["supabase"]

    with open(os.path.join(SUPABASE_DIRNAME, "package.json"), "w") as f:
        obj = dict(
            name="braintrust-supabase",
            version="0.0.0",
            devDependencies=dict(supabase=supabase_cli_version),
        )
        json.dump(obj, f)
