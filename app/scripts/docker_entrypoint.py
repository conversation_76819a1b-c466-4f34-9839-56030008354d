import os
import signal
import subprocess
import sys

SED_DELIMITER = "|"
SINGLE_QUOTE = "'"

# Keep this in sync with the identical dict in other docker scripts.
#
# These env vars are compiled into the production webapp. Because they are
# affected by the user's env settings, we need to update them dynamically
# each time we start the app. So we insert placeholder strings in place,
# which we then search-and-replace for when starting the app.
APP_COMPILED_ENV = dict(
    NEXT_PUBLIC_SUPABASE_URL="3b205f33-0144-49fe-a899-5ebc7751ebf1",
    NEXT_PUBLIC_SUPABASE_ANON_KEY="1437d66d-a7aa-45fb-84c2-e1a924dc5d26",
    NEXT_PUBLIC_MULTI_TENANT_API_URL="445812f5-1dba-4f41-9267-c37db18f6063",
    NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL="1b2d1ede-5494-4dbf-8bdd-a2c948f85b2f",
)

APP_COMPILED_ENV_TO_OS_ENV = dict(
    NEXT_PUBLIC_SUPABASE_URL="SUPABASE_PUBLIC_URL",
    NEXT_PUBLIC_SUPABASE_ANON_KEY="SUPABASE_ANON_KEY",
    NEXT_PUBLIC_MULTI_TENANT_API_URL="API_PUBLIC_URL",
    NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL="REALTIME_PUBLIC_URL",
)


def resolve_compiled_env(compiled_env_var):
    os_env_var = APP_COMPILED_ENV_TO_OS_ENV[compiled_env_var]
    env_value = os.environ.get(os_env_var)
    if env_value is None:
        raise Exception(f"Must provide environment variable {os_env_var} to service")
    return env_value


# Before launching the webapp, we need to search-and-replace some of the
# NEXT_PUBLIC URL environment variables with a value constructed from the
# APP_PUBLIC_BASE_DOMAIN env combined with the config info.
def set_app_compiled_env():
    for compiled_env_var, placeholder in APP_COMPILED_ENV.items():
        replacement = resolve_compiled_env(compiled_env_var)
        print(f"Replacing app env var {compiled_env_var} with {replacement}", file=sys.stderr)
        for item in [placeholder, replacement]:
            for char in [SED_DELIMITER, SINGLE_QUOTE]:
                assert char not in item, (item, char)
        command = f"rg --hidden -l {SINGLE_QUOTE}{placeholder}{SINGLE_QUOTE} | xargs -I {{}} sed -i {SINGLE_QUOTE}s{SED_DELIMITER}{placeholder}{SED_DELIMITER}{replacement}{SED_DELIMITER}g{SINGLE_QUOTE} {{}}"
        print(f"Running shell command: {command}", file=sys.stderr)
        subprocess.run(command, cwd="/braintrust/app", shell=True, check=True)


def sigterm_handler(signum, frame):
    del signum, frame
    print(f"Captured sigterm. Exiting container.", file=sys.stderr)
    sys.exit(0)


if __name__ == "__main__":
    signal.signal(signal.SIGTERM, sigterm_handler)

    # Run supabase migrations.
    subprocess.run(
        ["npx", "supabase", "migration", "up", "--db-url", os.environ["SUPABASE_PG_URL"]],
        cwd="/braintrust-supabase",
        check=True,
    )

    bt_env = os.environ.copy()
    bt_env["HOSTNAME"] = "0.0.0.0"
    bt_env["NEXTAUTH_URL"] = os.environ["BRAINTRUST_APP_PUBLIC_URL"] + "/api/auth"

    set_app_compiled_env()
    # Launch app server.
    subprocess.run(
        ["node", "server.js"],
        cwd="/braintrust/app",
        env=bt_env,
    )
