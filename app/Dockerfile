# The build context of this Dockerfile is assumed to be the root of the
# repository.

FROM node:22-bookworm-slim AS base

# Install dependencies only when needed
FROM base AS builder
RUN corepack enable
RUN corepack prepare pnpm@8.15.9 --activate

# Install dependencies
RUN apt-get update && apt-get upgrade -y && apt-get install -y python3-toml python3-dotenv

WORKDIR /braintrust

COPY app app
COPY autoevals autoevals
COPY btql btql
COPY forked/codemirror-copilot forked/codemirror-copilot
COPY local/js local/js
COPY openapi openapi
COPY proxy/packages/proxy proxy/packages/proxy
COPY realtime realtime
COPY sdk/core/js sdk/core/js
COPY sdk/js sdk/js
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml turbo.json .npmrc .eslintrc.cjs .

COPY scripts/docker_update_root_package_json.py app/scripts/docker_update_next_config_options.py app/scripts/docker_create_env_local.py app/scripts/docker_create_braintrust_supabase.py .
RUN python3 docker_update_root_package_json.py
RUN python3 docker_update_next_config_options.py
RUN python3 docker_create_env_local.py
RUN python3 docker_create_braintrust_supabase.py

RUN pnpm install
RUN pnpm build --filter 'braintrustdata'

# -----
# Production image: copy necessary files.
FROM base AS runner

# install runtime and debugging tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    curl vim dstat procps net-tools ripgrep

WORKDIR /braintrust

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

COPY --from=builder /braintrust/app/public app/public

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder /braintrust/node_modules node_modules
COPY --from=builder /braintrust/app/.next/standalone .
COPY --from=builder /braintrust/app/.next/static app/.next/static
COPY --from=builder /braintrust/app/.env.local app/

# Copy over the supabase directory.
COPY --from=builder /braintrust-supabase /braintrust-supabase

COPY app/scripts/docker_entrypoint.py .

EXPOSE 3000
ENTRYPOINT ["python3", "docker_entrypoint.py"]

HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1
