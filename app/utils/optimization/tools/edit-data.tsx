import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "#/ui/dialog";
import { Button } from "#/ui/button";
import { DataTextEditor } from "#/ui/data-text-editor";
import { useMemo } from "react";
import { type EditDataToolParameters } from "@braintrust/local/optimization/tools";

export interface EditDataDialogProps {
  edits: EditDataToolParameters["edits"];
  existing: Record<
    string,
    {
      id: string;
      input: unknown;
      expected: unknown;
      metadata: unknown;
    }
  >;
  onCancel: (error: string) => void;
  onSave: () => void;
}

export function EditDataDialog({
  edits,
  existing,
  onCancel,
  onSave,
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
} & EditDataDialogProps) {
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          onCancel("user rejected");
        }
        onOpenChange(open);
      }}
    >
      <DialogContent className="block h-[80vh] w-full sm:max-w-screen-sm">
        <DialogHeader>
          <DialogTitle>Edit dataset</DialogTitle>
          <DialogDescription>Edit dataset</DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          {edits.map((edit, index) => (
            <EditDataRow
              key={index}
              edit={edit}
              rowId={index.toString()}
              existing={existing}
            />
          ))}
        </div>

        <DialogFooter>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              onCancel("user rejected");
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            size="sm"
            variant="primary"
            onClick={() => {
              onSave();
              onOpenChange(false);
            }}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function EditDataRow({
  edit,
  rowId,
  existing,
}: {
  edit: EditDataToolParameters["edits"][number];
  rowId: string;
  existing: Record<
    string,
    {
      id: string;
      input: unknown;
      expected: unknown;
      metadata: unknown;
    }
  >;
}) {
  const existingRow = useMemo(() => {
    if (!edit.id || !existing[edit.id]) {
      return undefined;
    }
    const { id: _, ...rest } = existing[edit.id];
    return rest;
  }, [existing, edit.id]);
  const updatedValue = useMemo(() => {
    if (edit.delete) {
      return null;
    }
    return {
      input: edit.input ?? existingRow?.input,
      expected: edit.expected ?? existingRow?.expected,
      metadata: edit.metadata ?? existingRow?.metadata,
    };
  }, [edit, existingRow]);
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-2">
        <div className="text-sm font-medium">{edit.id ?? "New row"}</div>
        <div className="text-sm text-muted-foreground">{edit.purpose}</div>
        <DataTextEditor
          allowedRenderOptions={["json", "yaml"]}
          value={edit.delete ? null : updatedValue}
          diffValue={existingRow}
          rowId={rowId}
          readOnly
        />
      </div>
    </div>
  );
}
