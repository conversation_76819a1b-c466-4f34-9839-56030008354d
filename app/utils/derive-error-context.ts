import type { ErrorContext } from "@braintrust/local";
import { EMPTY_ERROR_CONTEXT, errorContextSchema } from "@braintrust/local";
import type { AuthLookup } from "#/pages/api/_login_to_auth_id";
import { canUseCache } from "#/utils/cache";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { kv } from "@vercel/kv";
import { sha1 } from "#/utils/hash";
import { isAdminEmail } from "./auth/admins";

// Note: the ErrorContext caching done here is analogous to the caching done in
// api-ts/src/error_context_cache.ts. In fact the data plane invokes this
// control plane logic, so we end up hitting caches on both fronts.
export async function deriveErrorContext(
  authLookup: AuthLookup | undefined,
): Promise<ErrorContext> {
  if (!authLookup) {
    return EMPTY_ERROR_CONTEXT;
  }
  const authLookupHash = `${sha1(authLookup.auth_id)}_${authLookup.org_id || ""}`;

  if (canUseCache()) {
    const cached = await kv.get<ErrorContext>(
      `error_context_${authLookupHash}`,
    );
    if (cached) {
      return cached;
    }
  }

  const conn = getServiceRoleSupabase();
  const errorContext = await (async (): Promise<ErrorContext> => {
    try {
      const { rows } = await conn.query(
        `
          with
          user_id_email as (
            select id, email from users where auth_id = $1
          ),
          org_name as (
            select name from organizations where id = $2
          )
          select user_id_email.id "userId", user_id_email.email "userEmail", org_name.name "orgName"
          from user_id_email left join org_name on true
      `,
        [authLookup.auth_id, authLookup.org_id],
      );
      if (rows.length !== 1) {
        return EMPTY_ERROR_CONTEXT;
      }
      return errorContextSchema.parse(rows[0]);
    } catch (e) {
      console.error("Failed to fetch error context\n", e);
      return EMPTY_ERROR_CONTEXT;
    }
  })();

  if (canUseCache()) {
    await kv.set(
      `error_context_${authLookupHash}`,
      errorContext,
      // Cache for 60s
      { ex: 60 },
    );
  }

  return errorContext;
}

export async function isAllowedSysadmin(
  authLookup: AuthLookup | undefined,
): Promise<boolean> {
  if (!authLookup) {
    return false;
  }
  const userEmail = (await deriveErrorContext(authLookup)).userEmail;
  if (!userEmail) {
    return false;
  }
  return isAdminEmail(userEmail);
}
