"use server";

import { type Group, groupSchema } from "@braintrust/core/typespecs";
import { helper } from "#/pages/api/group/get";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";

export type FetchOrgGroupsOutput = Record<string, Group>;

export async function fetchOrgGroups(
  {
    orgName,
  }: {
    orgName: string;
  },
  authLookupRaw?: AuthLookup,
): Promise<FetchOrgGroupsOutput> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const groups = groupSchema
    .array()
    .parse(await helper({ org_name: [orgName] }, authLookup));
  return Object.fromEntries(groups.map((group) => [group.id, group]));
}
