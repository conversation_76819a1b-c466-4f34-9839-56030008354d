import { type Virtualizer } from "@tanstack/react-virtual";
import { useEffect } from "react";

// https://tanstack.com/virtual/latest/docs/framework/react/examples/infinite-scroll
export function useInfiniteFetch({
  virtualizer,
  hasNextPage,
  fetchNextPage,
  totalRowCount,
  isFetching,
}: {
  virtualizer: Virtualizer<HTMLDivElement, Element>;
  hasNextPage: boolean;
  fetchNextPage: () => void;
  totalRowCount: number;
  isFetching: boolean;
}) {
  const lastItemIndex = virtualizer.getVirtualItems().at(-1)?.index;
  useEffect(() => {
    if (lastItemIndex == null) {
      return;
    }

    const hasReachedEnd = lastItemIndex >= totalRowCount - 1;
    if (hasReachedEnd && hasNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, fetchNextPage, totalRowCount, isFetching, lastItemIndex]);
}
