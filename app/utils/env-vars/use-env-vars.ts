import { useQuery, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";
import { _urljoin } from "@braintrust/core";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import {
  apiDelete,
  apiFetchGetCors,
  apiPatch,
  apiPostCors,
} from "#/utils/btapi/fetch";
import {
  type EnvVar,
  envVarSchema,
  type envVarObjectTypeEnum,
} from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import { useCallback } from "react";

export const secretFormSchema = z.object({
  envVars: z.array(
    z.object({
      key: z.string(),
      value: z.string().optional(),
    }),
  ),
});

export type SecretForm = z.infer<typeof secretFormSchema>;

async function fetchEnvironmentVariables({
  apiUrl,
  sessionToken,
  objectType,
  objectId,
  setVarsEnabled,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  objectType: z.infer<typeof envVarObjectTypeEnum>;
  objectId?: string;
  setVarsEnabled: (enabled: boolean) => void;
}): Promise<EnvVar[]> {
  if (
    sessionToken === "loading" ||
    sessionToken === "unauthenticated" ||
    !objectId
  ) {
    return [];
  }

  const params = new URLSearchParams({
    object_type: objectType,
    object_id: objectId,
  });
  const resp = await apiFetchGetCors(
    `${apiUrl}/v1/env_var?${params.toString()}`,
    sessionToken,
  );
  if (!resp.ok) {
    if (resp.status === 404 || resp.status === 405) {
      setVarsEnabled(false);
      return [];
    }
    throw new Error(await resp.text());
  }

  const { objects } = await resp.json();
  return z.array(envVarSchema).parse(objects);
}

export function useEnvVars({
  objectType,
  objectId,
  setVarsEnabled,
}: {
  objectType: z.infer<typeof envVarObjectTypeEnum>;
  objectId?: string;
  setVarsEnabled: (enabled: boolean) => void;
}) {
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const queryClient = useQueryClient();

  const { data, refetch, isPending } = useQuery(
    {
      queryKey: ["useEnvVars", objectType, objectId],
      queryFn: async () => {
        const sessionToken = await getOrRefreshToken();
        return fetchEnvironmentVariables({
          apiUrl,
          sessionToken,
          objectType,
          objectId,
          setVarsEnabled,
        });
      },
      enabled: !!apiUrl && !!objectType && !!objectId,
    },
    queryClient,
  );

  const createVariables = useCallback(
    async ({ envVars }: SecretForm) => {
      const sessionToken = await getOrRefreshToken();

      const resp = await Promise.allSettled(
        envVars.map(async (envVar) => {
          const resp = await apiPostCors({
            url: `${apiUrl}/v1/env_var`,
            sessionToken,
            payload: {
              name: envVar.key,
              value: envVar.value ?? "",
              object_type: objectType,
              object_id: objectId,
            },
            alreadySerialized: false,
          });

          if (!resp.ok) {
            throw new Error(`${envVar.key}: ${await resp.text()}`);
          }
          return resp.json();
        }),
      );
      const errors = resp
        .filter(
          (result): result is PromiseRejectedResult =>
            result.status === "rejected",
        )
        .map((result) => result.reason.message);

      if (errors.length > 0) {
        throw new Error(
          `Failed to set environment variables:\n${errors.join("\n")}`,
        );
      }
      refetch();
    },
    [apiUrl, objectId, objectType, refetch, getOrRefreshToken],
  );

  const deleteVariable = useCallback(
    async (envVarId: string) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiDelete({
        url: `${apiUrl}/v1/env_var/${envVarId}`,
        sessionToken,
        payload: {},
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  const updateVariable = useCallback(
    async (envVarId: string, value: string) => {
      const sessionToken = await getOrRefreshToken();
      const resp = await apiPatch({
        url: `${apiUrl}/v1/env_var/${envVarId}`,
        sessionToken,
        payload: {
          value,
        },
        alreadySerialized: false,
      });
      if (!resp.ok) {
        throw new Error(await resp.text());
      }
      refetch();
    },
    [apiUrl, refetch, getOrRefreshToken],
  );

  return {
    data,
    isPending,
    createVariables,
    deleteVariable,
    updateVariable,
  };
}
