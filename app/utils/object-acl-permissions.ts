import { endpointSchemas } from "@braintrust/local/app-schema";
import type { z } from "zod";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { helper as getObjectInfo } from "#/pages/api/self/get_object_info";
import { type Permission } from "@braintrust/core/typespecs";
import { otelWrapTraced } from "./tracing";

const { input: _inputSchema, output: outputSchema } =
  endpointSchemas.self_get_object_info;
type InputSchema = z.infer<typeof _inputSchema>;

type GetObjectAclPermissionsInput = {
  objectType: InputSchema["object_type"];
  objectId: InputSchema["object_ids"][number];
  overrideRestrictObjectType?: InputSchema["override_restrict_object_type"];
};

const getPermissions = otelWrapTraced(
  "getPermissions",
  async (
    input: GetObjectAclPermissionsInput,
    authLookupRaw?: AuthLookup,
  ): Promise<Permission[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
    const fullOutput = outputSchema.parse(
      await getObjectInfo(
        {
          object_type: input.objectType,
          override_restrict_object_type: input.overrideRestrictObjectType,
          object_ids: [input.objectId],
          accept_arbitrary_acl_object_types: true,
        },
        authLookup,
      ),
    );
    if (fullOutput.length !== 1) {
      throw new Error("Expected exactly one object info");
    }
    return fullOutput[0].permissions;
  },
);

export const getObjectAclPermissions = otelWrapTraced(
  "getObjectAclPermissions",
  async (
    input: Omit<GetObjectAclPermissionsInput, "objectId"> &
      Partial<Pick<GetObjectAclPermissionsInput, "objectId">>,
    authLookup?: AuthLookup,
  ): Promise<Permission[] | null> => {
    if (!input.objectId) {
      return null;
    }
    return await getPermissions(
      { ...input, objectId: input.objectId },
      authLookup,
    );
  },
);
