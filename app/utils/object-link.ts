import { objectReferenceSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

export const objectLinkParamsSchema = objectReferenceSchema
  .omit({ id: true })
  .extend({
    id: z.string().nullish(),
  });

export type ObjectLinkParams = z.infer<typeof objectLinkParamsSchema>;

export const objectRedirectLinkParamsSchema = objectLinkParamsSchema.extend({
  orgName: z.string(),
});

export type ObjectRedirectLinkParams = z.infer<
  typeof objectRedirectLinkParamsSchema
>;
