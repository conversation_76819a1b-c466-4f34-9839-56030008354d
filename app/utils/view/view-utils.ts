import { toast } from "sonner";
import * as <PERSON><PERSON> from "@sentry/nextjs";

export function toastAndLogError(
  action: string,
  error: unknown,
  log?: boolean,
) {
  toast.error(`Failed to ${action} view`, { description: `${error}` });
  if (log) {
    console.warn(`Failed to ${action} view`, error);
  }
  Sentry.captureException(error, {
    tags: {
      feature: "views",
      action,
    },
  });
}
