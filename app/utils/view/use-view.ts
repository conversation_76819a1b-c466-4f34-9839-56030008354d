import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  type ColumnOrderState,
  type ColumnSizingState,
  type VisibilityState,
} from "@tanstack/react-table";
import {
  type ChartAggregationType,
  type Entity,
  type EntityStorageSchema,
  MIN_CHART_HEIGHT,
  useEntityStorage,
  type Value,
} from "#/lib/clientDataStorage";
import { useOrg } from "#/utils/user";
import {
  parseAsArrayOf,
  parseAsBoolean,
  parseAsInteger,
  parseAsJson,
  parseAsString,
  parseAsStringLiteral,
  type ParserWithOptionalDefault,
  useQueryState,
} from "nuqs";
import {
  type AnyClauseSpec,
  type CheckResult,
  type ClauseChecker,
  type ClauseSpec,
  type ClauseType,
  type Search,
  addClause,
  checkTag,
  clausesToUrlValue,
  makeBubble,
  normalizeUrlSearch,
  normalizeUrlSearchClauses,
  urlSearchSchema,
  urlValueToClauses,
} from "#/utils/search/search";
import { apiFetchGetCors, apiPatch } from "#/utils/btapi/fetch";
import {
  type AclObjectType,
  type ViewType,
  type View as DBView,
  type ViewOptions,
  type ViewData,
  type MonitorViewOptions,
  type TableViewOptions,
} from "@braintrust/core/typespecs";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { _urljoin, getObjValueByPath } from "@braintrust/core";
import {
  noopParser,
  parseAsJsonEncoded,
  parseSelectionTypeState,
  useLayoutTypeOverrideState,
  useSearchState,
} from "#/ui/query-parameters";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { toastAndLogError } from "./view-utils";
import useEvent from "react-use-event-hook";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  TABLE_ROW_HEIGHTS,
  type TableRowHeight,
} from "#/ui/table-row-height-toggle";
import { doubleQuote } from "@braintrust/btql/planner";
import {
  tableLayoutTypes,
  type TableLayoutType,
} from "#/ui/table/layout-type-control";
import {
  GROUP_BY_NONE,
  GROUP_BY_NONE_VALUE,
  X_AXIS_EXPERIMENT,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import {
  type AggregationType,
  aggregationTypeEnumSchema,
} from "#/utils/queries/aggregations";
import { type Annotations, annotationsSchema } from "#/ui/charts/annotations";
import { useNavigationType } from "#/utils/navigation-detection";

// Time range can be either a string (like "3d", "7d", "30d") or an object with from/to dates
export type TimeRangeFilter = string | { from: string; to: string };

export type View = Omit<Partial<DBView>, "id"> & {
  id: string | null;
} & {
  builtin?: boolean;
};

export type ViewProps<ExperimentsCharting extends boolean = false> =
  ExperimentsCharting extends true
    ? BaseViewProps<ExperimentsCharting> & ExperimentsChartingProps
    : BaseViewProps<ExperimentsCharting>;

type BaseViewProps<ExperimentsCharting extends boolean = false> = {
  search: Search;
  columnVisibility: Record<string, boolean>;
  columnOrder: ColumnOrderState;
  columnSizing: Record<string, number>;
  grouping: ExperimentsCharting extends true ? SelectionType : string;
  rowHeight: TableRowHeight | null;
  tallGroupRows: boolean;
  layout: string | null;
  chartHeight: number;
  timeRangeFilter: TimeRangeFilter;
  setSearch: Dispatch<SetStateAction<Search>>;
  setColumnVisibility: Dispatch<SetStateAction<VisibilityState>>;
  setColumnOrder: Dispatch<SetStateAction<ColumnOrderState>>;
  setColumnSizing: Dispatch<SetStateAction<ColumnSizingState>>;
  setGrouping: Dispatch<
    SetStateAction<ExperimentsCharting extends true ? SelectionType : string>
  >;
  setRowHeight: Dispatch<SetStateAction<TableRowHeight | null>>;
  setTallGroupRows: Dispatch<SetStateAction<boolean>>;
  setLayout: (value: "list" | "grid" | "summary" | null) => void;
  setChartHeight: Dispatch<SetStateAction<number>>;
  setTimeRangeFilter: Dispatch<SetStateAction<TimeRangeFilter>>;
  isPending: boolean;
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  currentView: View | undefined;
  clauseChecker?: ClauseChecker | null;
  resetState: (view: View | null, initialize?: boolean) => void;
};

type ExperimentsChartingProps = ExperimentsChartingValues & {
  setExcludedMeasures: Dispatch<SetStateAction<SelectionType[]>>;
  setYMetric: Dispatch<SetStateAction<SelectionType | null>>;
  setXAxis: Dispatch<SetStateAction<SelectionType | null>>;
  setSymbolGrouping: Dispatch<SetStateAction<SelectionType | null>>;
  setXAxisAggregation: Dispatch<SetStateAction<AggregationType | "all" | null>>;
  setChartAnnotations: Dispatch<SetStateAction<Annotations>>;
};

export type ExperimentsChartingValues = {
  excludedMeasures: SelectionType[];
  yMetric: SelectionType | null;
  xAxis: SelectionType;
  symbolGrouping: SelectionType;
  xAxisAggregation: ChartAggregationType;
  chartAnnotations: Annotations;
};

type SaveViewOpts = {
  viewId: string | undefined | null;
  viewOptionsToSave?: ViewOptions;
  viewDataToSave?: ViewData;
};

const IGNORED_URL_KEYS = new Set([
  "columnVisibility",
  "columnOrder",
  "columnSizing",
  // since localstorage keys are different from the view keys
  "columnOrderConfiguration",
  "columnWidthConfiguration",
]);

const enabledViewFields: Partial<{
  [key in
    | keyof Partial<TableViewOptions>
    | keyof Partial<MonitorViewOptions>]: string[];
}> = {
  timeRangeFilter: ["logs"],
};

function isViewFieldEnabled(
  fieldName:
    | keyof Partial<TableViewOptions>
    | keyof Partial<MonitorViewOptions>,
  viewType: ViewType | undefined,
): boolean {
  const enabledTypes = enabledViewFields[fieldName];
  return enabledTypes ? enabledTypes.includes(viewType ?? "") : false;
}

export function useViewState<
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>({
  viewParams,
  pageIdentifier,
  entityStorageKey,
  viewField,
  queryParamState,
  onUpdateValue,
}: {
  viewParams: ViewParams | undefined;
  pageIdentifier: string;
  entityStorageKey: {
    entityType: E;
    identifier: string;
    key: K;
    defaultValue?: Value<E, K>;
  };
  queryParamState: {
    queryKey: string;
    parser: ParserWithOptionalDefault<NonNullable<Value<E, K>>>;
  };
  viewField: keyof TableViewOptions | keyof MonitorViewOptions;
  onUpdateValue?: (viewOpts: ViewOptions) => void;
}) {
  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const currentView = useMemo(() => {
    return data?.find((v) => v.name === viewName);
  }, [data, viewName]);
  const hasView = currentView && !currentView.builtin;

  const [rawUrlState] = useQueryState(queryParamState.queryKey);
  const [urlState, _setUrlState] = useQueryState(
    queryParamState.queryKey,
    queryParamState.parser,
  );

  const setUrlState = useCallback(
    (...args: Parameters<typeof _setUrlState>) => {
      // a little bit hacky here:
      // the nuqs setter only calculates if the setter argument itself is the default value,
      // not if the post-parsed value is a default value, so exit early here
      // https://github.com/47ng/nuqs/blob/35933596ec401e795a86f1db351ea9f16bf9b84e/packages/nuqs/src/useQueryState.ts#L274
      if (queryParamState.parser === noopParser) {
        _setUrlState(null);
        return;
      }
      _setUrlState(...args);
    },
    [queryParamState.parser, _setUrlState],
  );

  // local storage allows us to sync state from any call site
  const [state, setState, resetLocalState] = useEntityStorage({
    entityType: entityStorageKey.entityType,
    entityIdentifier: entityStorageKey.identifier,
    key: entityStorageKey.key,
    defaultValue: entityStorageKey.defaultValue,
  });

  const updateState = useEvent((value: SetStateAction<Value<E, K>>) => {
    // when no view is selected:
    //   - use url as state
    //   - set local state to state

    // when a view is selected:
    //   - clear url
    //   - set local state to state

    const nextValue = value instanceof Function ? value(state) : value;
    if (!hasView && !IGNORED_URL_KEYS.has(viewField)) {
      setUrlState(nextValue ?? null);
    } else {
      setUrlState(null);
    }
    setState(value);
    onUpdateValue?.({
      [viewField]: nextValue,
    });
  });

  const navigationType = useNavigationType();
  const resetState = useEvent((view: View | null, initialize?: boolean) => {
    if (isPending) {
      return;
    }

    const hasView = view && !view.builtin;
    if (hasView) {
      setUrlState(null);
      const viewType = getObjValueByPath(view ?? {}, ["options", "viewType"]);
      const savedValue = // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        getObjValueByPath(view ?? {}, [
          "options",
          ...(viewType != null ? ["options"] : []),
          viewField,
        ]) as Value<E, K>;
      if (savedValue) {
        setState(savedValue);
        return;
      }
      resetLocalState();
      return;
    }

    if (initialize) {
      // check for rawUrlState because urlState may have a default value
      if (rawUrlState && urlState) {
        // If there is an initial url state, that means the user clicked on a link with
        // some url params. In this case we want to use the url state as truth
        setState(urlState);
        return;
      }

      // If there is no initial url state, then initialize the url state to whatever localstorage value we have

      if (!IGNORED_URL_KEYS.has(viewField)) {
        // Weird edge case where some people remove the url params and then hard reload the page
        // If we were to restore the search value from localstorage,
        // then removing the url params will never reset the state.
        // So only use localstorage as a seed if the user is navigating
        if (navigationType === "hard") {
          resetLocalState();
          return;
        }
        try {
          setUrlState(state ?? null);
        } catch (e) {
          // In some legacy cases we have invalid stored view values, then setting the url could fail in the url parser.
          // This can happen if we change the underlying data type for the view field
          // In that case, just throw out the stored state
          console.warn(
            `Unable to set url state for view field ${viewField}:`,
            e,
          );
          resetLocalState();
        }
      }
      return;
    }

    resetLocalState();
    setUrlState(null);
  });

  return [state, updateState, resetState] as const;
}

function useViewSearchState({
  viewParams,
  pageIdentifier,
  clauseChecker,
  onUpdateSearch,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  onUpdateSearch: (val: Search) => void;
  bypassClauseChecker?: boolean;
}) {
  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const navigationType = useNavigationType();

  // Local storage for search
  const [localSearch, setLocalSearch] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "search",
  });

  // Keep URL state for sharing/bookmarking
  const [urlSearch, setUrlSearch] = useSearchState();

  // for backwards compatibility reasons since we changed the encoding scheme
  // can remove probably after a couple of months
  const hasResetUrlSearch = useRef(false);
  useEffect(() => {
    if (!urlSearch || hasResetUrlSearch.current) {
      return;
    }
    setUrlSearch(urlSearch);
    hasResetUrlSearch.current = true;
  }, [urlSearch, setUrlSearch]);

  // Load legacy URL query params
  const [urlTags, setUrlTags] = useQueryState<string[]>(
    "tags",
    parseAsArrayOf(parseAsString),
  );
  const [urlClauses, setUrlClauses] = useQueryState<string[]>(
    "sb",
    parseAsArrayOf(parseAsString),
  );

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  const [search, _setSearch] = useState<Search>({});
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    if (!hasEdited.current) return;

    onUpdateSearch(search);
  }, [onUpdateSearch, search]);

  // Update both storage methods when search changes
  const updateBothStorages = useCallback(
    (newSearch: Search, currentView: View | null) => {
      // Update URL params
      const newUrlSearch =
        normalizeUrlSearch({
          filter: clausesToUrlValue(newSearch.filter),
          tag: clausesToUrlValue(newSearch.tag),
          match: clausesToUrlValue(newSearch.match),
          sort: clausesToUrlValue(newSearch.sort),
        }) ?? {};
      setUrlSearch(newUrlSearch);

      // Only update local storage for builtin views
      if (currentView?.builtin || !currentView) {
        setLocalSearch(newUrlSearch);
      }
    },
    [setUrlSearch, setLocalSearch],
  );

  // Initialize from either URL or local storage
  useEffect(() => {
    (async () => {
      if (
        (areViewsEnabled && isPending) ||
        hasInitialized ||
        (!bypassClauseChecker && !clauseChecker)
      ) {
        return;
      }

      // Only use local storage for builtin views
      const currentView = data?.find((v) => v.name === viewName);
      const urlOrLocalSearch =
        urlSearch ??
        // Weird edge case where some people remove the url params and then hard reload the page
        // If we were to restore the search value from localstorage,
        // then removing the url params will never reset the state.
        // So only use localstorage as a seed if the user is navigating
        ((currentView?.builtin || !currentView) && navigationType === "soft"
          ? localSearch
          : undefined);
      const storedSearch = urlOrLocalSearch ?? currentView?.view_data?.search;
      const storedSearchParsed = urlSearchSchema
        .nullish()
        .safeParse(storedSearch);
      if (!storedSearchParsed.success) {
        console.error(JSON.stringify(storedSearchParsed.error.errors, null, 2));
      }
      const storedSearchTyped = storedSearchParsed.success
        ? storedSearchParsed.data
        : undefined;

      const clauseSpecs: AnyClauseSpec[] = [
        urlValueToClauses("filter", storedSearchTyped?.filter),
        urlValueToClauses("tag", storedSearchTyped?.tag),
        urlValueToClauses("match", storedSearchTyped?.match),
        urlValueToClauses("sort", storedSearchTyped?.sort),
        (urlTags ?? []).map(
          (text): ClauseSpec<"tag"> => ({ type: "tag", text: `+${text}` }),
        ),
        (urlClauses ?? [])
          .map((text): AnyClauseSpec | undefined => {
            const types: ClauseType[] = ["filter", "match", "sort"];
            for (const type of types) {
              const prefix = `${type}:`;
              if (text.startsWith(prefix)) {
                return { type, text: text.slice(prefix.length) };
              }
            }
          })
          .filter((v): v is AnyClauseSpec => !!v),
      ].flat();

      const checkResults = bypassClauseChecker
        ? clauseSpecs.reduce<CheckResult<ClauseType>[]>((acc, clause) => {
            switch (clause.type) {
              case "filter":
              case "sort":
                acc.push({
                  type: "checked",
                  extraFields: { btql: { parsed: { btql: clause.text } } },
                });
                break;
              case "tag":
                acc.push(checkTag({ text: clause.text }));
                break;
              case "match":
                acc.push({ type: "checked", extraFields: {} });
                break;
            }
            return acc;
          }, [])
        : await Promise.all(
            clauseSpecs.map((clause) => clauseChecker?.(clause)),
          );

      const newSearch = checkResults.reduce((acc, checkResult, i) => {
        const clause = clauseSpecs[i];
        if (checkResult?.type !== "checked") {
          console.error(
            "Error processing URL search clause",
            clause,
            checkResult,
          );
          return acc;
        }

        return addClause(acc, {
          ...clause,
          ...checkResult.extraFields,
          bubble: makeBubble({
            clause,
            setSearch: (v) => {
              hasEdited.current = true;
              _setSearch(v);
            },
          }),
        });
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      }, {} as Search);
      _setSearch(newSearch);

      // Clear legacy URL query params
      setUrlTags(null);
      setUrlClauses(null);

      if (currentView?.builtin || !currentView) {
        const newUrlSearch =
          urlOrLocalSearch && Object.keys(urlOrLocalSearch).length > 0
            ? urlOrLocalSearch
            : null;
        setLocalSearch(newUrlSearch ?? {});
        if (navigationType === "soft") {
          setUrlSearch(newUrlSearch);
        }
      }

      setHasInitialized(true);
    })();
  }, [
    areViewsEnabled,
    isPending,
    hasInitialized,
    setHasInitialized,
    viewName,
    data,
    _setSearch,
    urlSearch,
    setUrlSearch,
    bypassClauseChecker,
    clauseChecker,
    urlTags,
    setUrlTags,
    urlClauses,
    setUrlClauses,
    localSearch,
    setLocalSearch,
    navigationType,
  ]);

  const hasEdited = useRef(false);
  const setSearch: Dispatch<SetStateAction<Search>> = useCallback(
    (valueOrUpdater) => {
      if (!hasInitialized) {
        return;
      }

      hasEdited.current = true;
      _setSearch(valueOrUpdater);
    },
    [hasInitialized, _setSearch],
  );

  useEffect(() => {
    const currentView = data?.find((v) => v.name === viewName);
    if (
      !hasInitialized ||
      !hasEdited.current ||
      (currentView && !currentView.builtin)
    ) {
      return;
    }

    updateBothStorages(search, currentView ?? null);
  }, [hasInitialized, data, viewName, search, updateBothStorages]);

  const resetSearch = useCallback(
    (currentView: View | null) => {
      setUrlSearch(null);
      if (!currentView || currentView.builtin) {
        const storedSearchParsed = urlSearchSchema
          .nullish()
          .safeParse(currentView?.view_data?.search);
        const storedSearch =
          (storedSearchParsed.success ? storedSearchParsed.data : null) ?? {};
        setLocalSearch(storedSearch);
      } else {
        setLocalSearch({});
      }
      setHasInitialized(false);
      hasEdited.current = false;
      return undefined;
    },
    [setUrlSearch, setLocalSearch, setHasInitialized],
  );

  return {
    search,
    setSearch,
    resetSearch,
    hasSearchInitialized: hasInitialized,
  };
}

export const selectionTypeParser = parseSelectionTypeState();
const defaultChartAnnotations: Annotations = [];

export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  bypassClauseChecker?: boolean;
}): ViewProps<false>;
export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  experimentsChartingProps,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  experimentsChartingProps: {
    groupingDefaultSelectionType: SelectionType;
  };
}): ViewProps<true>;
export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  experimentsChartingProps,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  bypassClauseChecker?: boolean;
  experimentsChartingProps?: {
    groupingDefaultSelectionType: SelectionType;
  };
}): ViewProps<true> | ViewProps<false> {
  const { getOrRefreshToken } = useSessionToken();
  const { api_url: apiUrl } = useOrg();
  const queryClient = useQueryClient();
  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });
  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const view = data?.find((v) => v.name === viewName);

  const saveViewMutationFn = async ({
    viewId,
    viewOptionsToSave,
    viewDataToSave,
  }: SaveViewOpts) => {
    const sessionToken = await getOrRefreshToken();
    if (!viewId) {
      throw new Error("Invalid view");
    }
    const payload = {
      ...makeRequiredPatchParams(viewParams),
      options: viewOptionsToSave,
      view_data: viewDataToSave,
    };
    const resp = await apiPatch({
      url: `${apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    return await resp.json();
  };

  const { mutate: saveViewMutation } = useMutation({
    mutationKey: queryKey,
    mutationFn: (opts: SaveViewOpts) => saveViewMutationFn(opts),
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("save", error),
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === view.id ? view : v)),
      );
      resetSearch(view);
    },
  });

  const debouncedSaveView = useDebouncedCallback(saveViewMutation, 1200);

  const onUpdateView = useEvent(
    ({
      viewId,
      viewOptionsToSave,
      viewDataToSave,
    }: {
      viewId: string | undefined | null;
      viewOptionsToSave: ViewOptions;
      viewDataToSave:
        | { type: "searchState"; search: Search }
        | { type: "viewSearch"; urlSearch: ViewData["search"] | undefined };
    }) => {
      if (!view || view.builtin || isPending) return;
      const searchToSave =
        viewDataToSave.type === "viewSearch"
          ? (viewDataToSave.urlSearch ?? null)
          : normalizeUrlSearchClauses({
              filter: clausesToUrlValue(
                viewDataToSave.search.filter ?? undefined,
              ),
              tag: clausesToUrlValue(viewDataToSave.search.tag ?? undefined),
              match: clausesToUrlValue(
                viewDataToSave.search.match ?? undefined,
              ),
              sort: clausesToUrlValue(viewDataToSave.search.sort ?? undefined),
            });
      debouncedSaveView({
        viewId,
        viewOptionsToSave,
        viewDataToSave: {
          search: searchToSave,
        },
      });
    },
  );

  const onUpdateState = useEvent((newOpts: ViewOptions) => {
    onUpdateView({
      viewId: view?.id,
      viewDataToSave: hasSearchInitialized
        ? { type: "searchState", search }
        : { type: "viewSearch", urlSearch: view?.view_data?.search },
      viewOptionsToSave: {
        columnOrder,
        columnSizing,
        columnVisibility,
        grouping: groupingStr,
        rowHeight,
        tallGroupRows,
        layout,
        ...(experimentsChartingProps
          ? {
              chartHeight,
              excludedMeasures,
              yMetric,
              xAxis,
              symbolGrouping,
              xAxisAggregation,
              chartAnnotations,
            }
          : {}),
        ...newOpts,
      },
    });
  });

  const [columnVisibility, setColumnVisibility, resetColumnVisibility] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "tables",
        identifier: pageIdentifier,
        key: "columnVisibility",
      },
      queryParamState: {
        queryKey: "_cv",
        parser: noopParser,
      },
      viewField: "columnVisibility",
      onUpdateValue: onUpdateState,
    });
  const [columnOrder, setColumnOrder, resetColumnOrder] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "columnOrderConfiguration",
    },
    queryParamState: {
      queryKey: "_co",
      parser: noopParser,
    },
    viewField: "columnOrder",
    onUpdateValue: onUpdateState,
  });
  const [columnSizing, setColumnSizing, resetColumnSizing] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "columnWidthConfiguration",
    },
    queryParamState: {
      queryKey: "_cw",
      parser: noopParser,
    },
    viewField: "columnSizing",
    onUpdateValue: onUpdateState,
  });

  const groupingDefaultSelectionType =
    experimentsChartingProps?.groupingDefaultSelectionType;
  const [groupingStr, setGroupingStr, resetGrouping] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "grouping",
    },
    queryParamState: {
      queryKey: "g",
      parser: groupingDefaultSelectionType
        ? parseAsString.withDefault(
            selectionTypeParser.serialize(groupingDefaultSelectionType),
          )
        : parseAsString.withDefault(GROUP_BY_NONE_VALUE),
    },
    viewField: "grouping",
    onUpdateValue: onUpdateState,
  });

  // since we use strings to store grouping types for all pages but the experiments list page,
  // we will continue to store strings values in localstorage but parse them when we come back
  const grouping = useMemo(() => {
    if (groupingDefaultSelectionType) {
      return groupingStr
        ? selectionTypeParser.parse(groupingStr)
        : groupingDefaultSelectionType;
    }

    return groupingStr;
  }, [groupingStr, groupingDefaultSelectionType]);
  const setGrouping = useMemo(() => {
    if (groupingDefaultSelectionType) {
      return (v: SetStateAction<SelectionType>) => {
        const next =
          typeof v === "function"
            ? v(
                (groupingStr ? selectionTypeParser.parse(groupingStr) : null) ??
                  groupingDefaultSelectionType,
              )
            : v;
        setGroupingStr(selectionTypeParser.serialize(next));
        return;
      };
    }

    return setGroupingStr;
  }, [groupingStr, groupingDefaultSelectionType, setGroupingStr]);

  const [rowHeight, setRowHeight, resetRowHeight] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "rowHeight",
    },
    queryParamState: {
      queryKey: "rh",
      parser: parseAsStringLiteral(TABLE_ROW_HEIGHTS).withDefault("compact"),
    },
    viewField: "rowHeight",
    onUpdateValue: onUpdateState,
  });

  const [tallGroupRows, setTallGroupRows, resetTallGroupRows] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "tallGroupRows",
    },
    queryParamState: {
      queryKey: "tg",
      parser: parseAsBoolean.withDefault(true),
    },
    viewField: "tallGroupRows",
    onUpdateValue: onUpdateState,
  });

  const [layoutOverride, setLayoutOverride] = useLayoutTypeOverrideState();
  const defaultLayoutType =
    viewParams?.viewType === "playground" ? "grid" : null;
  const [layout, _setLayout, resetLayout] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "layout",
      defaultValue: defaultLayoutType,
    },
    queryParamState: {
      queryKey: "tl",
      parser: parseAsStringLiteral(tableLayoutTypes),
    },
    viewField: "layout",
    onUpdateValue: onUpdateState,
  });

  // reset the layout override when the layout is explicitly set
  const setLayout = useCallback(
    (value: TableLayoutType | null) => {
      _setLayout(value);
      setLayoutOverride(null);
    },
    [setLayoutOverride, _setLayout],
  );

  const [chartHeight, setChartHeight, resetChartHeight] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "height",
    },
    queryParamState: {
      queryKey: "ch",
      parser: parseAsInteger.withDefault(MIN_CHART_HEIGHT),
    },
    viewField: "chartHeight",
    onUpdateValue: onUpdateState,
  });

  function parseTimeRangeFilter(value: unknown): TimeRangeFilter {
    if (typeof value === "string") {
      return value;
    }

    if (
      value &&
      typeof value === "object" &&
      "from" in value &&
      "to" in value &&
      typeof value.from === "string" &&
      typeof value.to === "string"
    ) {
      return { from: value.from, to: value.to };
    }

    return "";
  }

  const [timeRangeFilter, setTimeRangeFilter, resetTimeRangeFilter] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "tables",
        identifier: pageIdentifier,
        key: "timeRangeFilter",
        defaultValue: isViewFieldEnabled(
          "timeRangeFilter",
          viewParams?.viewType,
        )
          ? "3d"
          : undefined,
      },
      queryParamState: {
        queryKey: "range",
        parser: parseAsJsonEncoded(parseTimeRangeFilter),
      },
      viewField: "timeRangeFilter",
      onUpdateValue: onUpdateState,
    });

  // START - experiments page charting controls
  const updateExperimentsChartState = useCallback(
    (newOpts: ViewOptions) => {
      if (!experimentsChartingProps) {
        return;
      }
      onUpdateState(newOpts);
    },
    [onUpdateState, experimentsChartingProps],
  );
  const [excludedMeasures, setExcludedMeasures, resetExcludedMeasures] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "charts",
        identifier: pageIdentifier,
        key: "excludedMeasures",
      },
      queryParamState: {
        queryKey: "ye",
        parser: experimentsChartingProps
          ? parseAsArrayOf(parseSelectionTypeState())
          : noopParser,
      },
      viewField: "excludedMeasures",
      onUpdateValue: updateExperimentsChartState,
    });
  const [yMetric, setYMetric, resetYMetric] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "yMetric",
    },
    queryParamState: {
      queryKey: "y",
      parser: experimentsChartingProps ? parseSelectionTypeState() : noopParser,
    },
    viewField: "yMetric",
    onUpdateValue: updateExperimentsChartState,
  });

  const [_xAxis, setXAxis, resetXAxis] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "xAxis",
    },
    queryParamState: {
      queryKey: "x",
      parser: experimentsChartingProps
        ? parseSelectionTypeState().withDefault(X_AXIS_EXPERIMENT)
        : noopParser,
    },
    viewField: "xAxis",
    onUpdateValue: updateExperimentsChartState,
  });
  const xAxis = _xAxis ?? X_AXIS_EXPERIMENT;

  const [_symbolGrouping, setSymbolGrouping, resetSymbolGrouping] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "charts",
        identifier: pageIdentifier,
        key: "symbolGrouping",
      },
      queryParamState: {
        queryKey: "sg",
        parser: experimentsChartingProps
          ? parseSelectionTypeState().withDefault(GROUP_BY_NONE)
          : noopParser,
      },
      viewField: "symbolGrouping",
      onUpdateValue: updateExperimentsChartState,
    });
  const symbolGrouping = _symbolGrouping ?? GROUP_BY_NONE;

  const [_xAxisAggregation, setXAxisAggregation, resetXAxisAggregation] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "charts",
        identifier: pageIdentifier,
        key: "xAxisAggregation",
      },
      queryParamState: {
        queryKey: "xa",
        parser: experimentsChartingProps
          ? parseAsStringLiteral([
              ...aggregationTypeEnumSchema.options,
              "all",
            ]).withDefault("avg")
          : noopParser,
      },
      viewField: "xAxisAggregation",
      onUpdateValue: updateExperimentsChartState,
    });
  const xAxisAggregation = _xAxisAggregation ?? "avg";

  const [_chartAnnotations, setChartAnnotations, resetChartAnnotations] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "charts",
        identifier: pageIdentifier,
        key: "annotations",
      },
      queryParamState: {
        queryKey: "ca",
        parser: experimentsChartingProps
          ? parseAsJson(annotationsSchema.parse).withDefault(
              defaultChartAnnotations,
            )
          : noopParser,
      },
      viewField: "chartAnnotations",
      onUpdateValue: updateExperimentsChartState,
    });
  const chartAnnotations = _chartAnnotations ?? defaultChartAnnotations;

  // END - experiments page charting controls

  const onUpdateSearch = useEvent((newSearch: Search) =>
    onUpdateView({
      viewId: view?.id,
      viewDataToSave: { type: "searchState", search: newSearch },
      viewOptionsToSave: {
        columnOrder,
        columnSizing,
        columnVisibility,
        grouping: groupingStr,
        chartHeight,
        rowHeight,
        tallGroupRows,
        layout,
        ...(experimentsChartingProps
          ? {
              excludedMeasures,
              yMetric,
              xAxis,
              symbolGrouping,
              xAxisAggregation,
              chartAnnotations,
            }
          : {}),
      },
    }),
  );

  const { search, setSearch, resetSearch, hasSearchInitialized } =
    useViewSearchState({
      pageIdentifier,
      viewParams,
      clauseChecker,
      onUpdateSearch,
      bypassClauseChecker,
    });

  const resetState = useCallback(
    (view: View | null, initialize?: boolean) => {
      resetColumnVisibility(view, initialize);
      resetColumnOrder(view, initialize);
      resetColumnSizing(view, initialize);
      resetGrouping(view, initialize);
      resetRowHeight(view, initialize);
      resetTallGroupRows(view, initialize);
      resetLayout(view, initialize);
      resetChartHeight(view, initialize);
      resetTimeRangeFilter(view, initialize);
      if (experimentsChartingProps) {
        resetExcludedMeasures(view, initialize);
        resetYMetric(view, initialize);
        resetXAxis(view, initialize);
        resetSymbolGrouping(view, initialize);
        resetXAxisAggregation(view, initialize);
        resetChartAnnotations(view, initialize);
      }
      if (!initialize) {
        resetSearch(view);
      }
    },
    [
      resetColumnVisibility,
      resetColumnOrder,
      resetColumnSizing,
      resetGrouping,
      resetRowHeight,
      resetTallGroupRows,
      resetLayout,
      resetChartHeight,
      resetTimeRangeFilter,
      resetSearch,
      experimentsChartingProps,
      resetExcludedMeasures,
      resetYMetric,
      resetXAxis,
      resetSymbolGrouping,
      resetXAxisAggregation,
      resetChartAnnotations,
    ],
  );

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- not sure how to get this typing to play nice, so ended up using some hacky assertions
  return useMemo(
    () => ({
      search,
      columnVisibility,
      columnOrder,
      columnSizing,
      grouping,
      rowHeight,
      tallGroupRows,
      chartHeight,
      timeRangeFilter,
      layout: layoutOverride ?? layout,
      setColumnVisibility,
      setColumnOrder,
      setColumnSizing,
      setGrouping,
      setRowHeight,
      setTallGroupRows,
      setLayout,
      setSearch,
      setChartHeight,
      setTimeRangeFilter,
      isPending,
      pageIdentifier,
      viewParams,
      currentView: view,
      resetState,
      clauseChecker,
      ...(experimentsChartingProps
        ? {
            excludedMeasures,
            setExcludedMeasures,
            yMetric,
            setYMetric,
            xAxis,
            setXAxis,
            symbolGrouping,
            setSymbolGrouping,
            xAxisAggregation,
            setXAxisAggregation,
            chartAnnotations,
            setChartAnnotations,
          }
        : {}),
    }),
    [
      clauseChecker,
      columnOrder,
      columnSizing,
      columnVisibility,
      grouping,
      isPending,
      layout,
      layoutOverride,
      pageIdentifier,
      resetState,
      rowHeight,
      tallGroupRows,
      chartHeight,
      timeRangeFilter,
      search,
      setColumnOrder,
      setColumnSizing,
      setColumnVisibility,
      setGrouping,
      setLayout,
      setRowHeight,
      setTallGroupRows,
      setChartHeight,
      setTimeRangeFilter,
      setSearch,
      view,
      viewParams,
      experimentsChartingProps,
      excludedMeasures,
      setExcludedMeasures,
      yMetric,
      setYMetric,
      xAxis,
      setXAxis,
      symbolGrouping,
      setSymbolGrouping,
      xAxisAggregation,
      setXAxisAggregation,
      chartAnnotations,
      setChartAnnotations,
    ],
  ) as typeof experimentsChartingProps extends never
    ? ViewProps<false>
    : ViewProps<true>;
}

export type ViewParams = {
  objectType: AclObjectType;
  objectId: string;
  viewType: ViewType;
};

export type GetViewsArgs = {
  apiUrl: string;
  getOrRefreshToken: () => Promise<BtSessionToken>;
  viewParams: ViewParams;
};

async function getViews({
  apiUrl,
  getOrRefreshToken,
  viewParams,
}: GetViewsArgs): Promise<View[] | null> {
  const sessionToken = await getOrRefreshToken();
  if (sessionToken === "loading") {
    throw new Error("Session token is still loading");
  }
  const { objectType, objectId, viewType } = viewParams;
  const params = new URLSearchParams({
    object_type: objectType,
    object_id: objectId,
    view_type: viewType,
  });
  const url = _urljoin(apiUrl, "/v1/view?") + new URLSearchParams(params);
  // NOTE: We use the legacy fetch here because the REST API doesn't support
  // CORS-optimized GET requests that use `"Content-Type": "text/plain"`.
  const resp = await apiFetchGetCors(url, sessionToken);
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
  return (await resp.json()).objects;
}

export function useViewsQueryKey({
  pageIdentifier,
  getViewArgs,
}: {
  pageIdentifier: string;
  getViewArgs: Partial<GetViewsArgs>;
}) {
  const { getOrRefreshToken } = useSessionToken();
  return ["getViews", pageIdentifier, getViewArgs, getOrRefreshToken];
}

const NON_ERROR_VIEW = {
  id: "non_errors",
  builtin: true,
  name: "Non-errors",
  view_data: {
    search: {
      filter: ["error IS NULL and is_root"],
    },
  },
};

const ERROR_VIEW = {
  id: "errors",
  builtin: true,
  name: "Errors",
  view_data: {
    search: {
      filter: ["error IS NOT NULL and is_root"],
    },
  },
};

const UNREVIEWED_VIEW = {
  id: "unreviewed",
  builtin: true,
  name: "Unreviewed",
};

const BUILTIN_VIEWS: Partial<Record<ViewType, View[]>> = {
  logs: [NON_ERROR_VIEW, ERROR_VIEW, UNREVIEWED_VIEW],
  experiment: [NON_ERROR_VIEW, ERROR_VIEW, UNREVIEWED_VIEW],
};

export function useViewQuery({
  viewParams,
  pageIdentifier,
}: {
  viewParams: ViewParams | undefined;
  pageIdentifier: string;
}) {
  const { config } = useContext(ProjectContext);
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });

  const humanReviewScores = useMemo(
    () =>
      config.scores?.filter(
        (s) =>
          s.score_type === "categorical" ||
          s.score_type === "free-form" ||
          s.score_type === "slider",
      ) ?? [],
    [config.scores],
  );

  const builtInViews = useMemo(() => {
    if (!viewParams?.viewType) return [];

    return (BUILTIN_VIEWS[viewParams.viewType] ?? [])
      .filter((v) =>
        v.id === "unreviewed" ? humanReviewScores.length !== 0 : true,
      )
      .map((v) => {
        if (v.id !== "unreviewed") return v;
        return {
          ...v,
          view_data: {
            search: {
              filter: [
                humanReviewScores
                  .map((s) => `scores.${doubleQuote(s.name)} IS NULL`)
                  .join(" AND "),
              ],
            },
          },
        };
      });
  }, [viewParams?.viewType, humanReviewScores]);

  const { data, isPending } = useQuery({
    queryKey,
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();
      // in public pages we want to fake successfully loading views so we can set up the clause checker
      // and support filtering UI
      return sessionToken === "unauthenticated"
        ? []
        : await getViews({
            apiUrl,
            getOrRefreshToken,
            viewParams: viewParams!,
          });
    },
    enabled: areViewsEnabled && !!apiUrl && !!viewParams?.objectId,
    meta: {
      disableGlobalErrorToast: true,
      globalErrorSentryContext: {
        tags: {
          feature: "views",
          action: "get",
        },
      },
    },
  });

  const views = useMemo(() => {
    return builtInViews.concat(data ?? []);
  }, [builtInViews, data]);

  return { data: views, isPending };
}

export const makeRequiredPatchParams = (viewParams: ViewParams | undefined) => {
  if (!viewParams) {
    throw new Error("Cannot update view without viewParams");
  }

  return {
    object_type: viewParams.objectType,
    object_id: viewParams.objectId,
  };
};
