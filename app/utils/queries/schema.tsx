import { DataType, type Schema, type TypeMap } from "apache-arrow";
export { extractIdentifiableRow } from "@braintrust/local";
export type { IdentifiableRow } from "@braintrust/local";
import {
  dbQuery,
  MetadataField,
  RootSpanIdField,
  SpanIdField,
  useDBQuery,
} from "#/utils/duckdb";
import type { DiscriminatedProjectScore } from "#/utils/score-config";
import { useCallback, useMemo, useState } from "react";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import {
  experimentGroupStructureQuery,
  flattenNestedFields,
  isOnlineScore,
  MetricsField,
  parseScoresAndMetrics,
  ScoresField,
  sortScoreFields,
} from "@braintrust/local/query";
import { parseJSONInfo } from "./metadata";

function hasField(schema: Schema<TypeMap> | null, name: string) {
  return schema?.fields.find((n) => n.name === name);
}

export const fetchStructure = async (
  conn: AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  objectScanRaw: string | null,
  objectSchema: Schema<TypeMap> | null,
  fields: string[],
  unfoldArrays: boolean = false,
) => {
  let structure;
  try {
    // Due to https://github.com/duckdb/duckdb/issues/11886, users' metadata may not
    // be inferable.
    const joins = fields.reduce((acc: string[], f) => {
      if (hasField(objectSchema, f)) {
        acc.push(`json_group_structure(
          CASE WHEN ${SpanIdField} = ${RootSpanIdField} THEN json(${f}) ELSE json('{}') END
        ) as ${f}`);
      }
      return acc;
    }, []);
    const query =
      objectScanRaw && objectSchema
        ? `
        SELECT
          1,
          ${joins.join(", ")}
        FROM (
          SELECT * FROM ((${objectScanRaw}))
          -- We only allow breaking down metadata in the root span.
          WHERE ${SpanIdField} = ${RootSpanIdField}
          -- Metadata can be large, so we limit the number of rows we look at.
          LIMIT 100
        ) "t"`
        : null;
    structure = await dbQuery(conn, abortSignal, query);
  } catch (e) {
    console.warn(
      "Failed to infer metadata structure. You can likely ignore this, but certain filtering features may be disabled.",
      e,
    );
  }
  const row = structure?.toArray()?.[0] ?? {};
  return fields.map((f) => parseJSONInfo(row, f, unfoldArrays) ?? []);
};

async function fetchScoresAndMetrics(
  conn: AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  objectScanRaw: string | null,
  objectSchema: Schema<TypeMap> | null,
) {
  const scoreField = objectSchema?.fields.find((f) => f.name === "scores");
  const metricField = objectSchema?.fields.find((f) => f.name === "metrics");
  if (
    DataType.isStruct(scoreField?.type) &&
    DataType.isStruct(metricField?.type)
  ) {
    const scores = scoreField.type.children.map((f) => f.name);
    const metrics = metricField.type.children.map((f) => f.name);
    return {
      scores,
      metrics,
    };
  }
  const scoresMetricsStructure = await dbQuery(
    conn,
    abortSignal,
    objectScanRaw && objectSchema
      ? experimentGroupStructureQuery({
          hasScores: !!hasField(objectSchema, ScoresField),
          hasMetrics: !!hasField(objectSchema, MetricsField),
          experimentScan: objectScanRaw,
        })
      : null,
  );
  const [dataScoreFields, metricFields] = parseScoresAndMetrics(
    scoresMetricsStructure?.toArray()?.[0] ?? {},
  );
  return {
    scores: flattenNestedFields(dataScoreFields ?? []),
    metrics: flattenNestedFields(metricFields ?? []),
  };
}

export async function objectSchemaFieldsQuery(
  conn: AsyncDuckDBConnection,
  abortSignal: AbortSignal,
  objectScanRaw: string | null,
  objectSchema: Schema<TypeMap> | null,
  scoreConfig: DiscriminatedProjectScore[],
) {
  const [
    metadataFields,
    structs,
    { scores: dataFields, metrics: metricFields },
  ] = await Promise.all([
    fetchStructure(conn, abortSignal, objectScanRaw, objectSchema, [
      MetadataField,
    ]),
    fetchStructure(
      conn,
      abortSignal,
      objectScanRaw,
      objectSchema,
      [MetadataField, "input", "output", "expected"],
      true,
    ),
    fetchScoresAndMetrics(conn, abortSignal, objectScanRaw, objectSchema),
  ]);

  const scoreFieldsAll = dataFields.concat(
    scoreConfig
      .filter(
        (s) =>
          !s.config?.destination &&
          !dataFields.includes(s.name) &&
          !isOnlineScore(s.score_type),
      )
      .map((f) => f.name),
  );

  // Deduplicate by case-insensitive comparison.
  const scoreFieldsSet = Object.fromEntries(
    scoreFieldsAll.map((f) => [f.toLowerCase(), f]),
  );

  const rawScoreFields = Object.values(scoreFieldsSet);

  return {
    scoreFields: sortScoreFields(rawScoreFields, scoreConfig),
    metricFields,
    metadataRootFields: metadataFields[0] || [],
    metadataFields: structs[0] || [],
    inputFields: structs[1] || [],
    outputFields: structs[2] || [],
    expectedFields: structs[3] || [],
  };
}

export function useObjectSchemaFields(
  objectScanRaw: string | null,
  objectSchema: Schema<TypeMap> | null,
  objectReady: number[],
  scoreConfig: DiscriminatedProjectScore[],
) {
  const [data, setData] = useState<
    Awaited<ReturnType<typeof objectSchemaFieldsQuery>> & {
      hasLoaded: boolean;
    }
  >({
    scoreFields: [],
    metricFields: [],
    metadataRootFields: [],
    metadataFields: [],
    inputFields: [],
    outputFields: [],
    expectedFields: [],
    hasLoaded: false,
  });
  const queryFn = useMemo(() => {
    if (!objectScanRaw || !objectSchema) {
      return null;
    }
    return async (conn: AsyncDuckDBConnection, abortSignal: AbortSignal) => {
      const data = await objectSchemaFieldsQuery(
        conn,
        abortSignal,
        objectScanRaw,
        objectSchema,
        scoreConfig,
      );
      setData({ ...data, hasLoaded: true });
      return null;
    };
  }, [objectScanRaw, objectSchema, scoreConfig]);

  const setError = useCallback(() => undefined, []);
  useDBQuery(queryFn, objectReady, {
    // swallow the error, it will be console logged in the query function
    setError,
  });

  return data;
}
