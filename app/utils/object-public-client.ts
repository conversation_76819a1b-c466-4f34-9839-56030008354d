import { HTTPError } from "#/utils/http_error";
import { ANON_USER_ID } from "#/utils/constants";
import {
  type LoadedBtSessionToken,
  sessionFetchProps,
} from "#/utils/auth/session-token";
import { type AclItem } from "@braintrust/core/typespecs";

// We restrict the set of object types which we can set to public because
// generally this abstraction only makes sense for leaf-level objects (like
// experiments). It makes less sense for object types higher up the hierarchy
// like projects.
export type PublicObjectType = "experiment" | "dataset" | "prompt_session";

export async function setIsPublic({
  objectType,
  objectId,
  isPublic,
  apiUrl,
  getOrRefreshToken,
}: {
  objectType: PublicObjectType;
  objectId: string;
  isPublic: boolean;
  apiUrl: string;
  getOrRefreshToken: () => Promise<LoadedBtSessionToken>;
}): Promise<void> {
  const aclItem: AclItem = {
    object_type: objectType,
    object_id: objectId,
    user_id: ANON_USER_ID,
    permission: "read",
    restrict_object_type: objectType,
  };
  const { sessionHeaders, sessionExtraFetchProps } = sessionFetchProps(
    await getOrRefreshToken(),
  );
  const resp = await fetch(`${apiUrl}/api/acl/batch_update`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      ...sessionHeaders,
    },
    body: JSON.stringify(
      isPublic ? { add_acls: [aclItem] } : { remove_acls: [aclItem] },
    ),
    ...sessionExtraFetchProps,
  });
  if (!resp.ok) {
    throw new HTTPError(resp.status, await resp.text());
  }
}
