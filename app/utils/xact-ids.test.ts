import { loadPrettyXact, prettifyXact } from "@braintrust/core";
import { expect, test } from "vitest";

test("roundtrip sanity", async () => {
  const cases = [
    ["1000192671193355184", "7a670c06c4787a30"],
    ["1000192656880881099", "81cd05ee665fdfb3"],
    ["1000192649085046089", "39a12b7b05fb91c1"],
    ["1000192689925586944", "0c05c446e60d0000"],
  ];

  for (const [original, pretty] of cases) {
    const encoded = prettifyXact(original);
    expect(encoded).toEqual(pretty);
    expect(loadPrettyXact(encoded)).toEqual(original);
  }
});
