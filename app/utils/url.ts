import { _urljoin } from "@braintrust/core";

export const urlMatcherRegexp = /(https?:\/\/[^\s"]+)/g;
export const urlMatcherRegexpStrict = /^"?https?:\/\/[^\s"]+"?$/;

export function proxyV1Url(proxyUrl: string, ...pieces: string[]) {
  proxyUrl = proxyUrl.replace(/\/$/, "");
  return _urljoin(
    proxyUrl.endsWith("/v1") || proxyUrl.endsWith("/v1/proxy")
      ? proxyUrl
      : proxyUrl + "/v1",
    ...pieces,
  );
}

export function decodeURIComponentPatched(component: string) {
  // Vercel had a bug where %2F in a URL gets turned into %252F (i.e.
  // literally "%2F") before hitting the page. But %2f was decoded correctly. So
  // replace any instances of %252F with %2f before decoding.
  //
  // Note that this means anybody who actually includes the literal string "%2F"
  // their entity name will run into problems, but as of 2024/09/16, that is not
  // the case in prod.
  // return decodeURIComponent(component.replace(/%252F/g, "%2f"));

  // Vercel fixed their bug, but we'll leave this function in place for now in case they regress.
  // This will allow us to fix it quickly.
  return decodeURIComponent(component);
}
