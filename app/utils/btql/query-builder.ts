import {
  type ParsedQuery,
  type Ident,
  type Expr,
  type Shape,
} from "@braintrust/btql/parser";
import { isEmpty } from "#/utils/object";

export function from(
  objectType: string,
  objectIds: string[],
  shape?: Shape,
): ParsedQuery["from"] {
  return {
    op: "function",
    name: { op: "ident", name: [objectType] },
    args:
      objectIds?.map((id) => ({
        op: "literal",
        value: id,
      })) ?? [],
    shape,
  };
}

export function ident(...name: string[]): Ident {
  return {
    op: "ident",
    name,
  };
}

export function binOp(
  op: "and" | "or",
  flattened: boolean,
  ...allClauses: (Expr | null | undefined)[]
): Expr {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const clauses = allClauses.filter((c) => !isEmpty(c)) as Expr[];
  if (clauses.length === 0) {
    return { op: "literal", value: true };
  } else if (clauses.length === 1) {
    return clauses[0];
  }

  if (flattened) {
    return {
      op,
      children: clauses,
    };
  }

  let result = clauses[0];
  for (let i = 1; i < clauses.length; i++) {
    result = {
      op,
      left: result,
      right: clauses[i],
    };
  }
  return result;
}

export function and(...clauses: (Expr | null | undefined)[]): Expr {
  return binOp("and", false, ...clauses);
}

export function or(...clauses: (Expr | null | undefined)[]): Expr {
  return binOp("or", false, ...clauses);
}
