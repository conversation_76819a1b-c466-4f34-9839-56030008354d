import {
  type ParsedQuery,
  type Ident,
  type Expr,
  type Shape,
} from "@braintrust/btql/parser";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useMemo } from "react";
import { binOp, from, ident } from "./query-builder";

export interface BtqlQueryBuilder {
  and: (...clauses: (Expr | null | undefined)[]) => Expr;
  or: (...clauses: (Expr | null | undefined)[]) => Expr;
  from: (
    objectType: string,
    objectIds: string[],
    shape?: Shape,
  ) => ParsedQuery["from"];
  ident: (...name: string[]) => Ident;
}
export function useBtqlQueryBuilder({}: {}): BtqlQueryBuilder {
  const {
    flags: { flattenedBoolOps },
  } = useFeatureFlags();

  const builder = useMemo(
    () => ({
      and: (...clauses: (Expr | null | undefined)[]) =>
        binOp("and", flattenedBoolOps, ...clauses),
      or: (...clauses: (Expr | null | undefined)[]) => {
        return binOp("or", flattenedBoolOps, ...clauses);
      },
      from,
      ident,
    }),
    [flattenedBoolOps],
  );

  return builder;
}
