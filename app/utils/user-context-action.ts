"use server";

import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { type UserContextT, orgContextSchema } from "./user-types";
import { userSchema } from "@braintrust/core/typespecs";
import { sanitizeUserInfo } from "#/pages/api/user/_util";
import { z } from "zod";
import { getServerAuthSession } from "./auth/server-session";
import { ANON_AUTH_ID } from "#/utils/constants";

// RBAC_DISCLAIMER: A user can only fetch their own context.
export async function getUserContext(
  {}: {},
  authLookupRaw?: AuthLookup,
): Promise<UserContextT> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  const session = await getServerAuthSession();

  const noUserContext: UserContextT = {
    session,
    user: undefined,
    orgs: {},
    status: "unauthenticated",
    isAdmin: false,
  };

  if (authLookup.auth_id === ANON_AUTH_ID) {
    return noUserContext;
  }

  const query = `
    with
    user_info as (
    select *
    from users
    where auth_id = $1
    ),
    member_org_infos as (
    select
        jsonb_object_agg(
            organizations.name,
            jsonb_build_object(
                'id', organizations.id,
                'name', organizations.name,
                'api_url', organizations.api_url,
                'is_universal_api', organizations.is_universal_api,
                'proxy_url', organizations.proxy_url,
                'realtime_url', organizations.realtime_url,
                'resources', (to_jsonb(resources) - 'org_id'),
                'plan_id', org_billing.plan_id
            )
        ) orgs
    from
        organizations
        join user_info on true
        join members on members.org_id = organizations.id and members.user_id = user_info.id
        left join resources on resources.org_id = organizations.id
        left join org_billing on org_billing.org_id = organizations.id
    )
    select
        to_jsonb(user_info) as user,
        coalesce(member_org_infos.orgs, '{}'::jsonb) orgs
    from
        user_info left join member_org_infos on true
  `;

  const supabase = getServiceRoleSupabase();
  const { rows } = await supabase.query(query, [authLookup.auth_id]);
  if (rows.length === 0) {
    return noUserContext;
  }
  const row = rows[0];
  const user = userSchema.parse(sanitizeUserInfo(row["user"]));
  const orgs = z.record(orgContextSchema).parse(row["orgs"]);

  return {
    session,
    user,
    orgs,
    status: "authenticated",
    isAdmin: session.isAdmin,
  };
}
