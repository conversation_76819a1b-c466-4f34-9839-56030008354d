import { type BubbleType } from "#/ui/table/bubbles";
import { type Dispatch, type SetStateAction } from "react";
import {
  type Clause,
  type ClauseType,
  type Search,
  isComparisonSortClause,
  makeBubble,
} from "./search";
import { ident } from "@braintrust/local/query";

export function getBubbles({
  search,
  setSearch,
  comparisonExperiments,
  onClear,
}: {
  search: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
  comparisonExperiments?: {
    id: string;
    name: string;
  }[];
  onClear?: (clause: Clause<ClauseType>) => void;
}): BubbleType[] {
  return Object.values(search)
    .map((clauses) => clauses ?? [])
    .flat()
    .filter((c) => !c.bubble.hidden)
    .map((c) => {
      if (isComparisonSortClause(c) && comparisonExperiments) {
        const validComparison = comparisonExperiments.some(
          ({ id }) => id === c.comparison?.experimentId,
        );
        if (validComparison) {
          return makeBubble({
            clause: c,
            customLabel: (
              <span>
                {ident(c.spec?.path.path ?? [])}{" "}
                {c.comparison?.type === "regression"
                  ? "regressions"
                  : c.comparison?.type === "value"
                    ? ""
                    : "difference"}{" "}
                {c.spec?.path.desc ? "DESC" : "ASC"}
              </span>
            ),
            setSearch,
          });
        }
      }
      return {
        ...c.bubble,
        comparisonId: c?.comparison?.experimentId,
        clear: () => {
          c.bubble.clear();
          onClear?.(c);
        },
      };
    });
}
