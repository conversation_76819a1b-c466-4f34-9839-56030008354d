import { z } from "zod";
import { type Dispatch, type SetStateAction, useEffect } from "react";

import { Bubble } from "#/ui/table/bubble";
import { CreatedField, IdField } from "#/utils/duckdb";
import { type Filter } from "#/utils/search/simple-tree";
import { singleQuote } from "#/utils/sql-utils";
import {
  type Expr as ParsedExpr,
  type SortExpr as ParsedSortExpr,
} from "@braintrust/btql/parser";
import { type BoundExpr, type BoundSortItem } from "@braintrust/btql/binder";
import { type ToSQL } from "@braintrust/btql/planner";
import { type TopLevelField } from "@braintrust/local/api-schema";
import {
  makeTagFilter,
  makeTypedTags,
  ComputedMetricFields,
} from "@braintrust/local/query";
import { type CustomColumn } from "@braintrust/core/typespecs";
import { type BtqlQueryBuilder } from "#/utils/btql/use-query-builder";

export const ClauseTypes = ["filter", "tag", "match", "sort"] as const;
export type ClauseType = (typeof ClauseTypes)[number];

const columnSortSchema = z.object({
  id: z.string(),
  desc: z.boolean().optional(),
});
const sortPathSchema = z.object({
  path: z.array(z.string()),
  desc: z.boolean().optional(),
});
export type SortPath = z.infer<typeof sortPathSchema>;

const comparisonSchema = z.object({
  experimentId: z.string(),
  // for backwards compatibility reasons, "score" means "sort by difference in value"
  type: z.enum(["score", "value", "regression"]).default("score"),
});

export type SortComparison = z.infer<typeof comparisonSchema>;

const defaultSpecSchema = z.object({
  label: z.string().optional(),
  text: z.string(),
  originType: z.enum(["form", "btql"]).optional(),
  comparison: z
    .object({
      experimentId: z.string(),
    })
    .nullish(),
});
const clauseSpecSchemaMap = {
  filter: defaultSpecSchema,
  tag: defaultSpecSchema,
  match: defaultSpecSchema,
  sort: defaultSpecSchema.extend({
    spec: z
      .object({
        col: columnSortSchema,
        path: sortPathSchema,
      })
      .optional(),
    comparison: comparisonSchema.nullish(),
  }),
};

export function isComparisonSortClause(
  clause: AnyClauseSpec,
): clause is ClauseSpec<"sort"> {
  return clauseSpecSchemaMap.sort
    .extend({
      type: z.literal("sort"),
      comparison: comparisonSchema,
    })
    .safeParse(clause).success;
}

export function isClauseType<T extends ClauseType>(
  clause: Clause<ClauseType>,
  type: T,
): clause is Clause<T> {
  return clause.type === type;
}

type ClauseSpecMap = {
  [T in ClauseType]: z.infer<(typeof clauseSpecSchemaMap)[T]>;
};
export type ClauseSpec<T extends ClauseType> = {
  type: T;
  originType?: "btql" | "form";
  label?: string;
} & ClauseSpecMap[T];
export type AnyClauseSpec = ClauseSpec<ClauseType>;

// The following types are convenient in-JS representations of search clauses.
// The extra fields (and search bubble) are not included in the URL query string.
type ExtraFieldsMap = {
  filter: {
    tree?: Filter[];
    btql?: {
      parsed: ParsedExpr;
      bound: BoundExpr;
      sql: ToSQL;
    };
  };
  tag: object;
  match: object;
  sort: {
    btql?: {
      parsed: ParsedSortExpr;
      bound: BoundSortItem;
      sql: ToSQL;
    };
  };
};
export type Clause<T extends ClauseType> = ClauseSpec<T> & {
  bubble: Bubble;
} & ExtraFieldsMap[T];

export type SearchSpec = {
  [T in ClauseType]?: ClauseSpec<T>[];
};

export type Search = {
  [T in ClauseType]?: Clause<T>[];
};

const urlSchemaMap = {
  filter: z.union([z.string(), clauseSpecSchemaMap.filter]),
  tag: z.union([z.string(), clauseSpecSchemaMap.tag]),
  match: z.union([z.string(), clauseSpecSchemaMap.match]),
  sort: z.union([z.string(), clauseSpecSchemaMap.sort]),
};
export type UrlClause<T extends ClauseType> = z.infer<(typeof urlSchemaMap)[T]>;

export const urlSearchSchema = z.object({
  filter: z.array(urlSchemaMap.filter).nullish(),
  tag: z.array(urlSchemaMap.tag).nullish(),
  match: z.array(urlSchemaMap.match).nullish(),
  sort: z.array(urlSchemaMap.sort).nullish(),
});
export type UrlSearch = z.infer<typeof urlSearchSchema>;

// Convert "false" boolean values to "undefined" to keep the URL as short as possible.
function pruneSortSpec(
  spec: NonNullable<ClauseSpec<"sort">["spec"]>,
): NonNullable<ClauseSpec<"sort">["spec"]> {
  return {
    col: {
      id: spec.col.id,
      ...(spec.col.desc ? { desc: spec.col.desc } : {}),
    },
    path: {
      path: spec.path.path,
      ...(spec.path.desc ? { desc: spec.path.desc } : {}),
    },
  };
}

export function clausesToUrlValue<T extends ClauseType>(
  clauses?: Clause<T>[],
): UrlClause<T>[] | undefined {
  const values = clauses?.map((c: Clause<T>): UrlClause<T> => {
    if ("spec" in c && c.spec) {
      return {
        text: c.text,
        spec: c.type === "sort" ? pruneSortSpec(c.spec) : c.spec,
        label: c.label,
        originType: c.originType,
        ...("comparison" in c && c.comparison
          ? {
              comparison: c.comparison,
            }
          : {}),
      };
    } else if (c.label) {
      return {
        text: c.text,
        label: c.label,
        originType: c.originType,
        ...("comparison" in c && c.comparison
          ? {
              comparison: c.comparison,
            }
          : {}),
      };
    } else {
      return c.text;
    }
  });
  return values?.length ? values : undefined;
}

export function urlValueToClauses<T extends ClauseType>(
  type: T,
  urlValue: UrlSearch[T] | undefined,
): ClauseSpec<T>[] {
  return (
    urlValue?.flatMap((urlC: UrlClause<T>) => {
      if (typeof urlC === "object") {
        return [
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          {
            type,
            originType: "btql" as const,
            label: urlC.text,
            ...urlC,
            comparison: urlC.comparison,
          } as ClauseSpec<T>,
        ];
      } else if (typeof urlC === "string") {
        return [
          {
            type,
            originType: "btql" as const,
            label: urlC,
            text: urlC,
            comparison: undefined,
          },
        ];
      }
      return [];
    }) ?? []
  );
}

export function normalizeUrlSearch(
  urlSearch: UrlSearch | null,
): UrlSearch | null {
  return Object.values(urlSearch ?? {}).some((v) => v?.length)
    ? urlSearch
    : null;
}

export function normalizeUrlSearchClauses(
  urlSearch?: UrlSearch,
): UrlSearch | null {
  return normalizeUrlSearch({
    ...(urlSearch?.filter ? { filter: urlSearch.filter } : {}),
    ...(urlSearch?.tag ? { tag: urlSearch.tag } : {}),
    ...(urlSearch?.match ? { match: urlSearch.match } : {}),
    ...(urlSearch?.sort ? { sort: urlSearch.sort } : {}),
  });
}

export type CheckResult<T extends ClauseType> =
  | { type: "unchecked" }
  | { type: "checked"; extraFields: ExtraFieldsMap[T] }
  | { type: "error"; error: string };

export type ClauseChecker = (
  clause: ClauseSpec<ClauseType>,
) => Promise<CheckResult<ClauseType>>;

export function addClause<T extends ClauseType>(
  s: Search,
  c: Clause<T>,
): Search {
  return {
    ...s,
    [c.type]: [
      ...(s[c.type] ?? []).filter(
        (v) =>
          v.text !== c.text ||
          c.comparison?.experimentId !== v.comparison?.experimentId ||
          getComparisonType(c) !== getComparisonType(v),
      ),
      c,
    ],
  };
}

export function removeClause<T extends ClauseType>(
  s: Search,
  c: ClauseSpec<T>,
): Search {
  const newValue = (s[c.type] ?? []).filter(
    (v) =>
      v.text !== c.text ||
      c.comparison?.experimentId !== v.comparison?.experimentId ||
      getComparisonType(c) !== getComparisonType(v),
  );
  const result = {
    ...s,
    [c.type]: newValue,
  };
  if (newValue.length === 0) {
    delete result[c.type];
  }
  return result;
}

function getComparisonType<T extends ClauseType>(c: ClauseSpec<T>) {
  if (c.comparison && "type" in c.comparison) {
    return c.comparison.type;
  }

  return undefined;
}

export type TestSearch = (s: SearchSpec) => string | null;

// This is a bit strange, in that we create a hook that accepts a setter, rather than
// compute and return these values directly, but it's important to avoid cyclical dependencies
// between the clause checker (which is initialized before we perform searches in the paginated
// object viewer) and the top level fields which rely on analyzing the schema that is returned
// from the search.
export function useScoreMetricsTopLevelFields({
  scoreFields,
  setTopLevelFields,
  include,
}: {
  scoreFields: string[];
  setTopLevelFields: Dispatch<SetStateAction<TopLevelField[]>>;
  include: boolean;
}) {
  useEffect(
    () =>
      setTopLevelFields(
        include
          ? [
              { prefix: ["scores"], fields: scoreFields },
              {
                prefix: ["metrics"],
                fields: include ? [...ComputedMetricFields] : [],
              },
            ]
          : [],
      ),
    [include, scoreFields, setTopLevelFields],
  );
}

export function checkTag({ text }: { text: string }): CheckResult<"tag"> {
  if (!text.startsWith("+") && !text.startsWith("-")) {
    return { type: "error", error: "Tags must start with + or -" };
  }
  return { type: "checked", extraFields: {} };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export async function noopChecker(): Promise<CheckResult<any>> {
  return { type: "checked", extraFields: {} };
}

export function makeBubble({
  clause,
  customLabel,
  setSearch,
}: {
  clause: AnyClauseSpec;
  customLabel?: React.ReactNode;
  setSearch: Dispatch<SetStateAction<Search>>;
}): Bubble {
  const { type, text, label, originType } = clause;
  return new Bubble({
    type,
    label: customLabel ?? label,
    text,
    comparisonId: clause.comparison?.experimentId,
    originType,
    isReadonly: !!label && !originType,
    clear: () => {
      setSearch((s) =>
        removeClause(s, { type, text, comparison: clause.comparison }),
      );
    },
  });
}

export function makeTagsFilterTree(s: SearchSpec): Filter[] {
  const positiveTags =
    s.tag?.filter((t) => t.text.startsWith("+")).map((t) => t.text.slice(1)) ??
    [];
  return positiveTags.length
    ? [
        {
          type: "path_lookup",
          path: ["tags"],
          value: positiveTags,
        },
      ]
    : [];
}

// Fallback filter to be used when no FTS index is available.
export function makeSimpleMatchFilters(
  s: SearchSpec,
  projectedPaths: string[],
): string[] {
  return projectedPaths.length > 0
    ? (s.match?.map((m) =>
        projectedPaths
          .map((p) => `(${p})::text ILIKE ${singleQuote("%" + m.text + "%")}`)
          .join(" OR "),
      ) ?? [])
    : [];
}

export function makeBtqlFullTextFilter(
  builder: BtqlQueryBuilder,
  s: SearchSpec,
  projectedPaths: string[],
): ParsedExpr[] {
  return (
    s.match?.map((m) =>
      builder.or(
        ...projectedPaths.map((p) => ({
          op: "ilike" as const,
          left: {
            op: "ident" as const,
            name: [p],
          },
          right: {
            op: "literal" as const,
            value: `%${m.text}%`,
          },
        })),
      ),
    ) ?? []
  );
}

export function makeBrainstoreFullTextFilter(
  builder: BtqlQueryBuilder,
  s: SearchSpec,
  projectedPaths: string[],
): ParsedExpr[] {
  return (
    s.match?.map((m) =>
      builder.or(
        ...projectedPaths.map((p) => ({
          op: "match" as const,
          left: {
            op: "ident" as const,
            name: [p],
          },
          right: {
            op: "literal" as const,
            value: m.text,
          },
        })),
      ),
    ) ?? []
  );
}

// Only used for datasets; does not handle any/all filtering across spans.
export function makeSimpleTagFilters(s: SearchSpec): string[] {
  const typedTags = makeTypedTags(s.tag?.map((t) => t.text) ?? []);
  return typedTags.map(makeTagFilter);
}

export function buildDefaultOrderBy(
  s: SearchSpec,
  defaultSort?: string,
  relation?: string,
  customColumns?: {
    columns?: CustomColumn[];
    loaded: boolean;
  },
): string {
  const colRegexMap = new Map(
    customColumns?.columns?.map(({ name }) => [
      name,
      new RegExp(`^\`?${name}\`?`),
    ]),
  );
  const relationStr = relation ? `${relation}.` : "";
  return s.sort?.length
    ? `${s.sort
        .map((s) => s.text)
        .filter((s) => {
          const col = customColumns?.columns?.find(({ name }) =>
            colRegexMap.get(name)?.test(s),
          );
          if (col) {
            return customColumns?.loaded;
          }
          return true;
        })
        .join(", ")}`
    : (defaultSort ??
        `${relationStr}${CreatedField} DESC, ${relationStr}${IdField} DESC`);
}
