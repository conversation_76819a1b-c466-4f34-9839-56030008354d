import { useQuery, useQueryClient } from "@tanstack/react-query";
import { type z } from "zod";
import { _urljoin } from "@braintrust/core";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { apiDelete, apiFetchGet, apiPatch, apiPost } from "#/utils/btapi/fetch";
import {
  type Environment,
  environmentSchema,
  type createEnvironmentSchema,
  type patchEnvironmentSchema,
} from "@braintrust/core/typespecs";
import { useOrg } from "#/utils/user";
import { useCallback } from "react";
import { useSetAtom } from "jotai/react";
import { environmentsAtom } from "./atoms";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

async function fetchEnvironments({
  apiUrl,
  sessionToken,
  orgName,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  orgName: string;
}): Promise<Environment[]> {
  if (sessionToken === "loading" || sessionToken === "unauthenticated") {
    return [];
  }

  try {
    const response = await apiFetchGet(
      _urljoin(apiUrl, "environment") +
        `?org_name=${encodeURIComponent(orgName)}`,
      sessionToken,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch environments: ${response.statusText}`);
    }

    const data = await response.json();
    return environmentSchema.array().parse(data.objects || []);
  } catch (error) {
    console.error("Error fetching environments:", error);
    throw error;
  }
}

export function useEnvironments() {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const queryClient = useQueryClient();
  const setEnvironments = useSetAtom(environmentsAtom);

  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");

  const { data, isPending, error } = useQuery({
    queryKey: ["environments", org.name],
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();
      const environments = await fetchEnvironments({
        apiUrl,
        sessionToken,
        orgName: org.name,
      });
      // Sync to atom
      setEnvironments(environments);
      return environments;
    },
    enabled: !!apiUrl && !!org.name && isEnvironmentsEnabled,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const createEnvironment = useCallback(
    async (environmentData: z.infer<typeof createEnvironmentSchema>) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiPost({
          url: _urljoin(apiUrl, "environment"),
          sessionToken,
          payload: {
            ...environmentData,
            org_name: org.name,
          },
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        const newEnvironment = environmentSchema.parse(await response.json());

        // Invalidate and refetch environments
        queryClient.invalidateQueries({ queryKey: ["environments", org.name] });

        return newEnvironment;
      } catch (error) {
        console.error("Error creating environment:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient],
  );

  const updateEnvironment = useCallback(
    async (
      environmentId: string,
      updates: z.infer<typeof patchEnvironmentSchema>,
    ) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiPatch({
          url: _urljoin(apiUrl, "environment", environmentId),
          sessionToken,
          payload: updates,
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        const updatedEnvironment = environmentSchema.parse(
          await response.json(),
        );

        // Invalidate and refetch environments
        queryClient.invalidateQueries({ queryKey: ["environments", org.name] });

        return updatedEnvironment;
      } catch (error) {
        console.error("Error updating environment:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient],
  );

  const deleteEnvironment = useCallback(
    async (environmentId: string) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiDelete({
          url: _urljoin(apiUrl, "environment", environmentId),
          sessionToken,
          payload: {},
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        // Invalidate and refetch environments
        queryClient.invalidateQueries({ queryKey: ["environments", org.name] });

        return true;
      } catch (error) {
        console.error("Error deleting environment:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient],
  );

  return {
    data: data || [],
    isPending,
    error,
    createEnvironment,
    updateEnvironment,
    deleteEnvironment,
  };
}
