import { useQuery, useQ<PERSON>yClient, useMutation } from "@tanstack/react-query";
import { _urljoin } from "@braintrust/core";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import {
  apiDelete,
  apiFetchGetCors,
  apiPost,
  apiPut,
} from "#/utils/btapi/fetch";
import { useOrg } from "#/utils/user";
import { useCallback } from "react";
import { z } from "zod";
import { useSetAtom } from "jotai/react";
import { environmentAssociationsAtom } from "./atoms";
import type { EnvironmentObject } from "@braintrust/core/typespecs";
import { environmentObjectSchema } from "@braintrust/core/typespecs";
import { useIsFeatureEnabled } from "#/lib/feature-flags";

// Use the core schema for environment object associations
type EnvironmentObjectAssociation = EnvironmentObject;

// Schema for creating associations
const _createEnvironmentObjectSchema = z.object({
  object_version: z.string(),
  environment_slug: z.string(),
  org_name: z.string().optional(),
});

type CreateEnvironmentObjectAssociation = z.infer<
  typeof _createEnvironmentObjectSchema
>;

const _upsertEnvironmentObjectSchema = z.object({
  object_version: z.string(),
  org_name: z.string().optional(),
});

type UpsertEnvironmentObjectAssociation = z.infer<
  typeof _upsertEnvironmentObjectSchema
>;

async function fetchEnvironmentObjectAssociations({
  apiUrl,
  sessionToken,
  orgName,
  objectType,
  objectId,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  orgName: string;
  objectType: string;
  objectId: string;
}): Promise<EnvironmentObjectAssociation[]> {
  if (sessionToken === "loading" || sessionToken === "unauthenticated") {
    return [];
  }

  try {
    const response = await apiFetchGetCors(
      _urljoin(apiUrl, "environment-object", objectType, objectId) +
        `?org_name=${encodeURIComponent(orgName)}`,
      sessionToken,
    );

    if (!response.ok) {
      if (response.status === 404) {
        return [];
      }
      throw new Error(
        `Failed to fetch environment associations: ${response.statusText}`,
      );
    }

    const data = await response.json();
    return environmentObjectSchema.array().parse(data.objects || []);
  } catch (error) {
    console.error("Error fetching environment associations:", error);
    return [];
  }
}

async function fetchSpecificEnvironmentObjectAssociation({
  apiUrl,
  sessionToken,
  orgName,
  objectType,
  objectId,
  environmentSlug,
}: {
  apiUrl: string;
  sessionToken: BtSessionToken;
  orgName: string;
  objectType: string;
  objectId: string;
  environmentSlug: string;
}): Promise<EnvironmentObjectAssociation | null> {
  if (sessionToken === "loading" || sessionToken === "unauthenticated") {
    return null;
  }

  try {
    const response = await apiFetchGetCors(
      _urljoin(
        apiUrl,
        "environment-object",
        objectType,
        objectId,
        environmentSlug,
      ) + `?org_name=${encodeURIComponent(orgName)}`,
      sessionToken,
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(
        `Failed to fetch environment association: ${response.statusText}`,
      );
    }

    const data = await response.json();
    return environmentObjectSchema.parse(data);
  } catch (error) {
    console.error("Error fetching specific environment association:", error);
    return null;
  }
}

export function useEnvironmentObjectAssociations({
  objectType,
  objectId,
}: {
  objectType: string;
  objectId: string;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const queryClient = useQueryClient();
  const setEnvironmentAssociations = useSetAtom(environmentAssociationsAtom);

  const isEnvironmentsEnabled = useIsFeatureEnabled("environments");

  const { data, isPending, error } = useQuery({
    queryKey: [
      "environment-object-associations",
      objectType,
      objectId,
      org.name,
    ],
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();
      const associations = await fetchEnvironmentObjectAssociations({
        apiUrl,
        sessionToken,
        orgName: org.name,
        objectType,
        objectId,
      });
      // Sync to atom
      setEnvironmentAssociations(associations);
      return associations;
    },
    enabled:
      !!apiUrl &&
      !!org.name &&
      !!objectType &&
      !!objectId &&
      isEnvironmentsEnabled,
  });

  const createAssociation = useCallback(
    async (associationData: CreateEnvironmentObjectAssociation) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiPost({
          url: _urljoin(apiUrl, "environment-object", objectType, objectId),
          sessionToken,
          payload: {
            ...associationData,
            org_name: org.name,
          },
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        const newAssociation = environmentObjectSchema.parse(
          await response.json(),
        );

        // Invalidate and refetch associations
        queryClient.invalidateQueries({
          queryKey: [
            "environment-object-associations",
            objectType,
            objectId,
            org.name,
          ],
        });

        return newAssociation;
      } catch (error) {
        console.error("Error creating environment association:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient, objectType, objectId],
  );

  const upsertAssociation = useCallback(
    async (
      environmentSlug: string,
      associationData: UpsertEnvironmentObjectAssociation,
    ) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiPut({
          url: _urljoin(
            apiUrl,
            "environment-object",
            objectType,
            objectId,
            environmentSlug,
          ),
          sessionToken,
          payload: {
            ...associationData,
            org_name: org.name,
          },
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        const updatedAssociation = environmentObjectSchema.parse(
          await response.json(),
        );

        // Invalidate and refetch associations
        queryClient.invalidateQueries({
          queryKey: [
            "environment-object-associations",
            objectType,
            objectId,
            org.name,
          ],
        });

        return updatedAssociation;
      } catch (error) {
        console.error("Error upserting environment association:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient, objectType, objectId],
  );

  const deleteAssociation = useCallback(
    async (environmentSlug: string) => {
      const sessionToken = await getOrRefreshToken();

      try {
        const response = await apiDelete({
          url:
            _urljoin(
              apiUrl,
              "environment-object",
              objectType,
              objectId,
              environmentSlug,
            ) + `?org_name=${encodeURIComponent(org.name)}`,
          sessionToken,
          payload: {},
          alreadySerialized: false,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(errorText);
        }

        // Invalidate and refetch associations
        queryClient.invalidateQueries({
          queryKey: [
            "environment-object-associations",
            objectType,
            objectId,
            org.name,
          ],
        });

        return true;
      } catch (error) {
        console.error("Error deleting environment association:", error);
        throw error;
      }
    },
    [apiUrl, getOrRefreshToken, org.name, queryClient, objectType, objectId],
  );

  return {
    data: data || [],
    isPending,
    error,
    createAssociation,
    upsertAssociation,
    deleteAssociation,
  };
}

export function useSpecificEnvironmentObjectAssociation({
  objectType,
  objectId,
  environmentSlug,
}: {
  objectType: string;
  objectId: string;
  environmentSlug: string;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;

  const { data, isPending, error } = useQuery({
    queryKey: [
      "environment-object-association",
      objectType,
      objectId,
      environmentSlug,
      org.name,
    ],
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();
      return fetchSpecificEnvironmentObjectAssociation({
        apiUrl,
        sessionToken,
        orgName: org.name,
        objectType,
        objectId,
        environmentSlug,
      });
    },
    enabled:
      !!apiUrl && !!org.name && !!objectType && !!objectId && !!environmentSlug,
  });

  return {
    data,
    isPending,
    error,
  };
}

// Export mutation hooks for easier usage with forms
export function useCreateEnvironmentAssociationMutation({
  objectType,
  objectId,
}: {
  objectType: string;
  objectId: string;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (associationData: CreateEnvironmentObjectAssociation) => {
      const sessionToken = await getOrRefreshToken();
      const response = await apiPost({
        url: _urljoin(apiUrl, "environment-object", objectType, objectId),
        sessionToken,
        payload: {
          ...associationData,
          org_name: org.name,
        },
        alreadySerialized: false,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      return environmentObjectSchema.parse(await response.json());
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          "environment-object-associations",
          objectType,
          objectId,
          org.name,
        ],
      });
    },
  });
}

export function useUpsertEnvironmentAssociationMutation({
  objectType,
  objectId,
}: {
  objectType: string;
  objectId: string;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      environmentSlug,
      ...associationData
    }: {
      environmentSlug: string;
    } & UpsertEnvironmentObjectAssociation) => {
      const sessionToken = await getOrRefreshToken();
      const response = await apiPut({
        url: _urljoin(
          apiUrl,
          "environment-object",
          objectType,
          objectId,
          environmentSlug,
        ),
        sessionToken,
        payload: {
          ...associationData,
          org_name: org.name,
        },
        alreadySerialized: false,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      return environmentObjectSchema.parse(await response.json());
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          "environment-object-associations",
          objectType,
          objectId,
          org.name,
        ],
      });
    },
  });
}

export function useDeleteEnvironmentAssociationMutation({
  objectType,
  objectId,
}: {
  objectType: string;
  objectId: string;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const apiUrl = org.api_url;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (environmentSlug: string) => {
      const sessionToken = await getOrRefreshToken();
      const response = await apiDelete({
        url:
          _urljoin(
            apiUrl,
            "environment-object",
            objectType,
            objectId,
            environmentSlug,
          ) + `?org_name=${encodeURIComponent(org.name)}`,
        sessionToken,
        payload: {},
        alreadySerialized: false,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          "environment-object-associations",
          objectType,
          objectId,
          org.name,
        ],
      });
    },
  });
}
