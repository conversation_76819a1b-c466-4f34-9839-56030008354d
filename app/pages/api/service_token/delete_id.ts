import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  deleteObjects,
  extractSingularRow,
} from "../_object_crud_util";
import {
  makeOwnerApiKeysFullResultSetQuery,
  sanitizeApi<PERSON>ey,
} from "../apikey/_util";
import { apiKeySchema } from "@braintrust/core/typespecs";
import { z } from "zod";
import { HTTPError } from "#/utils/server-util";

const paramsSchema = idParamSchema.extend({
  org_id: z.string(),
});

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { query, queryParams, notFoundErrorMessage } =
        makeOwnerApiKeysFullResultSetQuery({
          authLookup,
          org_id: params.org_id,
          user_type: "service_account",
          id: params.id,
        });

      let rows;
      try {
        rows = await deleteObjects({
          fullResultsQueryOverride: query,
          baseTableOverride: "api_key",
          startingParams: queryParams,
          fullResultsSize: 1,
          notFoundErrorMessage,
        });
      } catch (error) {
        if (error instanceof Error && error.message?.includes("Expected 1")) {
          throw new HTTPError(404, "Service token not found");
        }
        throw error;
      }

      const deletedToken = sanitizeApiKey(
        extractSingularRow({
          rows,
          notFoundErrorMessage: undefined,
        }),
      );
      return { service_token: deletedToken };
    },
    {
      paramsSchema,
      outputSchema: z.object({ service_token: apiKeySchema }),
    },
  );
}
