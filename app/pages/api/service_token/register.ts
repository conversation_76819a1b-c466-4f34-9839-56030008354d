import type { NextApiRequest, NextApiResponse } from "next";
import { createApiKeyOutputSchema } from "@braintrust/core/typespecs";
import { z } from "zod";
import { extractSingularRow } from "../_object_crud_util";
import { runJsonRequest } from "../_request_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { sanitizeApiKey } from "../apikey/_util";
import { makeIsOrgOwnerCTE } from "../_special_queries";

const paramsSchema = z.object({
  name: z.string().min(1, "Name is required"),
  org_id: z.string(),
  account_id: z.string(),
});

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { query: isOrgOwnerCTE, queryParams } = makeIsOrgOwnerCTE({
        userId: authLookup.user_id,
        orgId: params.org_id,
      });
      const userIdParam = queryParams.add(params.account_id);
      const orgIdParam = queryParams.add(params.org_id);
      const nameParam = queryParams.add(params.name);
      const query = `
        ${isOrgOwnerCTE},
        user_auth_id as (
          select auth_id from users where id = ${userIdParam}::uuid
          and user_type = 'service_account'::user_type_enum
        )
        select create_api_key_full(
          user_auth_id.auth_id,
          ${orgIdParam},
          ${nameParam},
          true
        )->'api_key' as api_key
        from user_auth_id
        join is_org_owner on is_org_owner.exists
      `;

      const supabase = getServiceRoleSupabase();
      const row = extractSingularRow({
        rows: (await supabase.query(query, queryParams.params)).rows,
        notFoundErrorMessage: {
          permission: "create service_token",
          objectType: "service_account",
          objectIds: [params.account_id],
        },
      });

      return { service_token: sanitizeApiKey(row.api_key) };
    },
    {
      paramsSchema,
      outputSchema: z.object({ service_token: createApiKeyOutputSchema }),
    },
  );
}
