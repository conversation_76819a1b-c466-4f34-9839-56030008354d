import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  makeOwnerApiKeysFullResultSetQuery,
  sanitizeApiKey,
} from "../apikey/_util";
import {
  commonFilterParamsSchema,
  getObjects,
  paginationParamsSchema,
} from "../_object_crud_util";
import { apiKeySchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema
    .pick({
      name: true,
      id: true,
    })
    .extend({
      org_id: z.string(),
    }),
);

const outputSchema = z.object({ service_tokens: apiKeySchema.array() });

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { name, id, org_id, ...paramsRest } = params;
      const { query: fullResultSetQuery, queryParams } =
        makeOwnerApiKeysFullResultSetQuery({
          authLookup,
          org_id,
          user_type: "service_account",
          name,
          id,
        });

      const results = await getObjects({
        fullResultsQueryOverride: fullResultSetQuery,
        startingParams: queryParams,
        ...paramsRest,
        fullResultsSize: undefined,
      });
      return { service_tokens: results.map(sanitizeApiKey) };
    },
    {
      paramsSchema,
      postprocessOutput: (results: unknown) => outputSchema.parse(results),
      outputSchema,
    },
  );
}
