import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_login_to_auth_id";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { meQuery, meSchema } from "#/app-db-views/me";
import { z } from "zod";
import { canUseCache } from "#/utils/cache";
import { kv } from "@vercel/kv";
import { sha1 } from "#/utils/hash";
import { isEmpty } from "#/utils/object";
import { waitUntil } from "@vercel/functions";
import { isAllowedSysadmin } from "#/utils/derive-error-context";

const paramsSchema = z
  .object({
    check_sysadmin: z.boolean().optional(),
  })
  .nullish();

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const authIdHash = `${sha1(authLookup.auth_id)}`;

  const key = `me_query_${authIdHash}`;
  let result: unknown[] | null = null;
  if (canUseCache()) {
    const cached = await kv.get<unknown[]>(key);
    if (!isEmpty(cached)) {
      result = cached;
    }
  }

  if (result === null) {
    const { query, queryParams } = meQuery({ auth_id: authLookup.auth_id });
    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(query, queryParams.params);
    result = rows;
    if (canUseCache()) {
      waitUntil(
        kv.set(
          key,
          result,
          // Cache for 60s
          { ex: 60 },
        ),
      );
    }
  }

  if (!result || result.length === 0) {
    console.log("me request failed");
    throw new HTTPError(400, "User does not exist");
  }
  const ret: Record<string, unknown> = z.record(z.unknown()).parse(result[0]);
  if (params?.check_sysadmin) {
    const isSysadmin = await isAllowedSysadmin(authLookup);
    ret.is_sysadmin = isSysadmin;
  }
  console.log("me request succeeded", ret);
  return ret;
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: meSchema,
  });
}
