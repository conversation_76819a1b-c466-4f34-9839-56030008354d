import type { NextApiRequest, NextApiResponse } from "next";
import { HTTPError } from "#/utils/http_error";
import { type AuthLookup } from "../_login_to_auth_id";
import {
  idParamSchema,
  getObjects,
  extractSingularRow,
} from "../_object_crud_util";
import {
  getRequestParams,
  getSessionAuthInfo,
  httpHandleError,
} from "../_request_util";
import { projectSchema } from "@braintrust/core/typespecs";
import { parseNoStrip } from "@braintrust/core";

async function helper(req: NextApiRequest, authLookup: AuthLookup) {
  if (req.method !== "GET") {
    throw new HTTPError(405, "Only GET is supported");
  }
  const params = getRequestParams(req.query, idParamSchema);
  return parseNoStrip(
    projectSchema,
    extractSingularRow({
      rows: await getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "project",
        },
        filters: {
          id: [params.id],
        },
        fullResultsSize: 1,
      }),
      notFoundErrorMessage: undefined,
    }),
  );
}

// TODO(manu): move this to use runJsonRequest.
export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  let authLookup: AuthLookup | undefined = undefined;
  try {
    authLookup = await getSessionAuthInfo(req, res);
    const out = await helper(req, authLookup);
    res.json(out);
  } catch (error) {
    await httpHandleError({ error, authLookup, req, res });
  }
  res.end();
}
