import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import { sanitizeAISecret } from "./_util";
import {
  aiSecretSchema,
  patchAISecretSchema,
} from "@braintrust/core/typespecs";
import { SqlQueryParams } from "#/utils/sql-query-params";

const paramsSchema = idParamSchema.merge(patchAISecretSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, metadata, ...paramsRest }, authLookup) => {
      const queryParams = new SqlQueryParams();
      return sanitizeAISecret(
        extractSingularRow({
          rows: await patchObjects({
            authLookup,
            priorObjectTables: ["ai_secret"],
            permissionInfo: {
              aclObjectType: "organization",
              aclPermission: "update",
            },
            filters: {
              id,
            },
            // Leave out the columns we are not actually setting. Altering any
            // columns unnecessarily is risky due to the transparent column
            // encryption.
            patchValueParams: Object.fromEntries(
              Object.entries(paramsRest).filter(([_, v]) => !!v),
            ),
            patchJsonValueParams: {
              ...(metadata ? { metadata } : {}),
            },
            patchExpressions: {
              // If we are not setting the secret, but we are setting any of the
              // "associated" columns from the schema definition, we need to
              // explicitly update the secret column with the existing value of
              // the decrypted secret, otherwise we will mess up the encryption.
              ...(!paramsRest.secret && paramsRest.name
                ? {
                    secret: `(select decrypted_secret from secrets.decrypted_org_secrets where id = ${queryParams.add(id)}::uuid)`,
                  }
                : {}),
            },
            fullResultsSize: 1,
            startingParams: queryParams,
          }),
          notFoundErrorMessage: undefined,
        }),
      );
    },
    {
      paramsSchema,
      outputSchema: aiSecretSchema,
    },
  );
}
