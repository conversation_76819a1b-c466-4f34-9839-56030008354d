import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { deleteObjects, extractSingularRow } from "../_object_crud_util";
import { sanitizeAISecret } from "./_util";
import {
  aiSecretSchema,
  deleteAISecretSchema,
} from "@braintrust/core/typespecs";
import { HTTPError } from "#/utils/http_error";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      if (!authLookup.org_id) {
        throw new HTTPError(
          400,
          "No organization is specified. You may specify one explicitly with the org_name param.",
        );
      }
      const res = extractSingularRow({
        rows: await deleteObjects({
          authLookup,
          priorObjectTables: ["ai_secret"],
          permissionInfo: {
            aclObjectType: "organization",
            aclPermission: "update",
          },
          filters: {
            name: params.name,
          },
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      });
      return sanitizeAISecret(res);
    },
    {
      paramsSchema: deleteAISecretSchema,
      outputSchema: aiSecretSchema,
    },
  );
}
