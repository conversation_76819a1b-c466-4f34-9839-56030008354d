import { type AISecret, aiSecretSchema } from "@braintrust/core/typespecs";
import { isEmpty } from "@braintrust/core";
import { z } from "zod";

export function sanitizeAISecret(x: unknown): AISecret {
  const { secret, ...aiSecretRest } = aiSecretSchema
    .extend({ secret: z.string().nullish() })
    .parse(x);
  return {
    ...aiSecretRest,
    preview_secret: isEmpty(secret) ? null : "********",
  };
}
