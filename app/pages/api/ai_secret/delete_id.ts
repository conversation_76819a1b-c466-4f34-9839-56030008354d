import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  deleteObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { sanitizeAISecret } from "./_util";
import { aiSecretSchema } from "@braintrust/core/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) =>
      sanitizeAISecret(
        extractSingularRow({
          rows: await deleteObjects({
            authLookup,
            priorObjectTables: ["ai_secret"],
            permissionInfo: {
              aclObjectType: "organization",
              aclPermission: "update",
            },
            filters: {
              id: [params.id],
            },
            fullResultsSize: 1,
          }),
          notFoundErrorMessage: undefined,
        }),
      ),
    {
      paramsSchema: idParamSchema,
      outputSchema: aiSecretSchema,
    },
  );
}
