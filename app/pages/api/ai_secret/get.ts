import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
  nullishSingleOrArraySchema,
} from "../_object_crud_util";
import { aiSecretSchema } from "@braintrust/core/typespecs";
import { z } from "zod";
import { sanitizeAISecret } from "./_util";

const paramsSchema = paginationParamsSchema
  .merge(
    commonFilterParamsSchema.pick({
      name: true,
      org_name: true,
      org_id: true,
      id: true,
    }),
  )
  .merge(
    nullishSingleOrArraySchema(
      z.object({
        type: aiSecretSchema.shape.type.unwrap().unwrap(),
      }),
    ),
  );

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        priorObjectTables: ["ai_secret", "organization"],
        permissionInfo: "org-membership",
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      postprocessOutput: (x) =>
        z.array(z.unknown()).parse(x).map(sanitizeAISecret),
      outputSchema: aiSecretSchema.array(),
      renameFields: {
        ai_secret_name: "name",
        ai_secret_type: "type",
      },
    },
  );
}
