import type { NextApiRequest, NextApiResponse } from "next";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { httpHandleError } from "../_request_util";
import { type AuthLookup, loginToAuthId } from "../_login_to_auth_id";

type Data = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  org_info: any[];
};

async function getAuthLookup(req: NextApiRequest) {
  let token = null;
  try {
    token = req.body["token"];
  } catch {
    throw new HTTPError(400, "Malformed request");
  }

  if (!token) {
    token = req.headers.authorization?.split(/\s+/)[1];
  }

  if (!token) {
    throw new HTTPError(401, "No authentication token");
  }
  return await loginToAuthId({ token });
}

// RBAC_DISCLAIMER: logging in and obtaining basic org info is permitted to all users within the org.
async function helper(res: NextApiResponse<Data>, authLookup: AuthLookup) {
  const supabase = getServiceRoleSupabase();
  const orgInfoResult = await supabase.query(
    `
    select
        organizations.id,
        organizations.name,
        organizations.api_url,
        org_settings.git_metadata,
        organizations.is_universal_api,
        organizations.proxy_url,
        organizations.realtime_url
    from
        organizations
        join members on organizations.id = members.org_id
        join users on members.user_id = users.id
        left join org_settings on org_settings.org_id = organizations.id
    where
      users.auth_id = $1
      ${authLookup.org_id ? "and organizations.id = $2" : ""}
  `,
    [authLookup.auth_id].concat(authLookup.org_id ? [authLookup.org_id] : []),
  );
  const org_info = orgInfoResult.rows.map(fixOrgUrls);
  res.status(200).json({
    org_info: org_info || [],
  });
}

// TODO(manu): move this to use runJsonRequest.
export async function action(req: NextApiRequest, res: NextApiResponse<Data>) {
  let authLookup: AuthLookup | undefined = undefined;
  try {
    authLookup = await getAuthLookup(req);
    await helper(res, authLookup);
  } catch (error) {
    await httpHandleError({ error, authLookup, req, res });
  }
  res.end();
}

// From https://vercel.com/guides/how-to-enable-cors
const allowCors =
  (fn: (req: NextApiRequest, res: NextApiResponse) => Promise<void>) =>
  async (req: NextApiRequest, res: NextApiResponse) => {
    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader("Access-Control-Allow-Origin", "*");
    // another common pattern
    // res.setHeader('Access-Control-Allow-Origin', req.headers.origin);
    res.setHeader(
      "Access-Control-Allow-Methods",
      "GET,OPTIONS,PATCH,DELETE,POST,PUT",
    );
    res.setHeader(
      "Access-Control-Allow-Headers",
      "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version",
    );
    if (req.method === "OPTIONS") {
      res.status(200).end();
      return;
    }
    return await fn(req, res);
  };

export const corsFn = allowCors(action);
export default corsFn;

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function fixOrgUrls(org: any) {
  const api_url = org.api_url || process.env.NEXT_PUBLIC_MULTI_TENANT_API_URL;
  return {
    ...org,
    api_url,
    // DEPRECATION_NOTICE: We technically do not need to send this, but the client
    // SDK versions before 0.0.147 crashed if the proxy URL is unset.
    proxy_url: org.proxy_url || api_url,
    realtime_url:
      org.realtime_url || process.env.NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL,
  };
}
