import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import { roleSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const memberPermissionsSchema = roleSchema.shape.member_permissions
  .unwrap()
  .unwrap().element;

const paramsSchema = z
  .object({
    role_name: roleSchema.shape.name.min(1),
    description: roleSchema.shape.description,
    member_permissions: memberPermissionsSchema.shape.permission
      .array()
      .nullish(),
    member_restrict_object_types:
      memberPermissionsSchema.shape.restrict_object_type.array().nullish(),
    member_roles: roleSchema.shape.member_roles,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  // Split up the "member_permissions" objects array into separate
  // "member_permissions" and "member_restrict_object_types" arrays.
  if (req.body.member_permissions) {
    const member_permissions = req.body.member_permissions.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Will be validated afterwards
      (x: any) => x.permission ?? null,
    );
    const member_restrict_object_types = req.body.member_permissions.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Will be validated afterwards
      (x: any) => x.restrict_object_type ?? null,
    );
    req.body.member_permissions = member_permissions;
    req.body.member_restrict_object_types = member_restrict_object_types;
  }

  await invokeServiceRoleSupabaseUdf(req, res, "register_role", {
    paramsSchema,
    outputSchema: z.object({ role: roleSchema }),
    argnames: [
      "auth_id",
      "org_id",
      "role_name",
      "description",
      "member_permissions",
      "member_restrict_object_types",
      "member_roles",
      "update",
    ],
  });
}
