// Always include the system roles in the results.
export const aclCheckAdditionalRowsQuery = `select 1 where roles.org_id is null`;

export const additionalProjections = (baseTableName: string) => [
  `(select coalesce(array_agg(jsonb_build_object('permission', permission::text, 'restrict_object_type', restrict_object_type::text))::jsonb[], '{}') member_permissions from role_permissions where role_permissions.role_id = ${baseTableName}.id)`,
  `(select coalesce(array_agg(role_members.member_role_id)::uuid[], '{}') member_roles from role_members join roles "_joined_roles" on role_members.member_role_id = "_joined_roles".id where "_joined_roles".deleted_at isnull and role_members.role_id = ${baseTableName}.id)`,
];
