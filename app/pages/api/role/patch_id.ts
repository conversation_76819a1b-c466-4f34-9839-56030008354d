import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_login_to_auth_id";
import {
  idParamSchema,
  extractSingularRow,
  getObjects,
  patchObjects,
} from "../_object_crud_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { additionalProjections } from "./_constants";
import { roleSchema, patchRoleSchema } from "@braintrust/core/typespecs";
import { type z } from "zod";

const paramsSchema = idParamSchema.merge(patchRoleSchema);

async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  // Because patching occurs across multiple tables, we run everything in a
  // transaction within our own pool client.
  const supabasePool = getServiceRoleSupabase();
  const client = await supabasePool.connect();
  await client.query("begin");
  try {
    const {
      id,
      add_member_permissions,
      remove_member_permissions,
      add_member_roles,
      remove_member_roles,
      ...paramsRest
    } = params;
    extractSingularRow({
      rows: await patchObjects(
        {
          authLookup,
          permissionInfo: {
            aclObjectType: "role",
          },
          filters: {
            id,
          },
          patchValueParams: paramsRest,
          fullResultsSize: 1,
        },
        client,
      ),
      notFoundErrorMessage: undefined,
    });
    // Update the permissions and inheritors. Since the previous query
    // succeeded, we know we have the appropriate permissions.
    if (add_member_permissions?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const permissionParams = add_member_permissions
        .map((p) => queryParams.add(p.permission))
        .join(", ");
      const restrictObjectTypeParams = add_member_permissions
        .map((p) => queryParams.add(p.restrict_object_type))
        .join(", ");
      await client.query(
        `
        insert into role_permissions(role_id, permission, restrict_object_type)
        select ${idParam}, permission, restrict_object_type from
        unnest(array [${permissionParams}]::permission_type[],
               array [${restrictObjectTypeParams}]::acl_object_type[]) t(permission, restrict_object_type)
        on conflict do nothing
      `,
        queryParams.params,
      );
    }
    if (remove_member_permissions?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const permissionsWhereClause = remove_member_permissions
        .map(
          (p) => `
            (
                permission = ${queryParams.add(p.permission)}
                and ${
                  p.restrict_object_type
                    ? `restrict_object_type = ${queryParams.add(
                        p.restrict_object_type,
                      )}`
                    : `restrict_object_type is null`
                }
            )
        `,
        )
        .join(" or ");
      await client.query(
        `
        delete from role_permissions
        where role_id = ${idParam} and ${permissionsWhereClause}
      `,
        queryParams.params,
      );
    }
    if (add_member_roles?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const roleParams = add_member_roles
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
        insert into role_members(role_id, member_role_id)
        select ${idParam}, member_role_id from unnest(array [${roleParams}]::uuid[]) member_role_id
        on conflict do nothing
      `,
        queryParams.params,
      );
    }
    if (remove_member_roles?.length) {
      const queryParams = new SqlQueryParams();
      const idParam = queryParams.add(id);
      const roleParams = remove_member_roles
        .map((p) => queryParams.add(p))
        .join(", ");
      await client.query(
        `
        delete from role_members
        where role_id = ${idParam} and member_role_id in (${roleParams})
      `,
        queryParams.params,
      );
    }
    const result = extractSingularRow({
      rows: await getObjects(
        {
          authLookup,
          permissionInfo: {
            aclObjectType: "role",
          },
          filters: {
            id: [id],
          },
          finalResultSetAdditionalProjections: additionalProjections,
          fullResultsSize: 1,
        },
        client,
      ),
      notFoundErrorMessage: undefined,
    });
    await client.query("commit");
    return result;
  } catch (e) {
    await client.query("rollback");
    throw e;
  } finally {
    await client.end();
  }
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: roleSchema,
  });
}
