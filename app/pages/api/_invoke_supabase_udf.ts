import type { NextApiRequest, NextApiResponse } from "next";
import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { doubleQuote } from "#/utils/sql-utils";
import { isArray } from "#/utils/object";
import {
  type GetRequestParamsOpts,
  getRequestParams,
  getSessionAuthInfo,
  httpHandleError,
} from "./_request_util";
import { z } from "zod";
import { parseNoStrip } from "@braintrust/core";
import {
  formatNotFoundErrorMessage,
  formatNotFoundErrorMessageInputSchema,
} from "@braintrust/local";
import { type AuthLookup } from "./_login_to_auth_id";
import { getCurrentSpan, otelWrapTraced } from "#/utils/tracing";
import { DebugError } from "#/utils/error";

// Commonly, API endpoints that invoke a UDF accept both an org_name and an
// org_id and coalesce them into an org_id for the UDF.
export const udfOrgIdSchema = z.object({
  org_id: z.string().nullish(),
  org_name: z.string().nullish(),
});

// Object registration UDF endpoints often accept an update parameter.
export const registerUdfUpdateSchema = z.object({
  update: z.boolean().nullish(),
});

const sqlUdfNotFoundErrorMessageSchema = z.union([
  formatNotFoundErrorMessageInputSchema.extend({
    kind: z.literal("not-found"),
  }),
  z.object({
    kind: z.literal("http-error"),
    code: z.number(),
    message: z.string(),
  }),
]);

export function processUdfError(error: unknown): unknown {
  const customErrorMessage = (() => {
    if (
      !(
        error instanceof Object &&
        "message" in error &&
        typeof error.message === "string"
      )
    ) {
      return null;
    }
    try {
      return sqlUdfNotFoundErrorMessageSchema.parse(JSON.parse(error.message));
    } catch (e) {
      return null;
    }
  })();
  if (customErrorMessage) {
    if (customErrorMessage.kind === "not-found") {
      throw new HTTPError(400, formatNotFoundErrorMessage(customErrorMessage));
    } else if (customErrorMessage.kind === "http-error") {
      throw new HTTPError(customErrorMessage.code, customErrorMessage.message);
    } else {
      const x: never = customErrorMessage;
      console.error("Unknown custom error message", x);
    }
  }
  throw error;
}

// Executes the given SQL user-defined function using the service role postgres
// client. Requires the call to be authenticated, and provides parameters
// 'auth_id' and 'org_id' if specified in argnames and available.
export const invokeServiceRoleSupabaseUdf = otelWrapTraced(
  "invokeServiceRoleSupabaseUdf",
  async function invokeServiceRoleSupabaseUdf<
    T extends z.ZodType,
    O extends z.ZodType,
  >(
    req: NextApiRequest,
    res: NextApiResponse,
    udf_name: string,
    opts: {
      argnames: string[];
      paramsSchema: T;
      postprocessOutput?: (x: unknown) => unknown;
      outputSchema: O;
      processError?: ({
        error,
        authLookup,
        req,
        res,
      }: {
        error: unknown;
        authLookup: AuthLookup | undefined;
        req: NextApiRequest;
        res: NextApiResponse;
      }) => Promise<void>;
    } & GetRequestParamsOpts,
  ): Promise<void | {
    authLookup: AuthLookup;
    out: z.infer<O>;
  }> {
    const {
      argnames,
      paramsSchema,
      outputSchema,
      postprocessOutput,
      processError = httpHandleError,
      ...getRequestParamOpts
    } = opts;

    const span = getCurrentSpan();

    let authLookupRaw: AuthLookup | undefined = undefined;
    try {
      authLookupRaw = await getSessionAuthInfo(req, res);
      const authLookup = authLookupRaw;

      const isSingle = !isArray(req.body);
      const bodyItemsRaw: unknown[] = isSingle ? [req.body] : req.body;

      if (req.headers["x-bt-debug-udf-input-error"]) {
        throw new DebugError("udf-input-error");
      }

      const bodyItems = bodyItemsRaw.map((item) =>
        getRequestParams(item, paramsSchema, getRequestParamOpts),
      );

      const udfArgSets = bodyItems.map((bodyItem) => {
        const udfArgs = { ...bodyItem };
        udfArgs["auth_id"] = udfArgs["auth_id"] ?? authLookup.auth_id;
        udfArgs["org_id"] = udfArgs["org_id"] ?? authLookup.org_id;
        return udfArgs;
      });
      span?.setAttribute("udf_name", udf_name);
      span?.setAttribute("user_id", authLookup.user_id);
      if (udfArgSets.length > 1) {
        span?.setAttribute("udf_arg_sets_json", JSON.stringify(udfArgSets));
      } else if (udfArgSets.length === 1) {
        span?.setAttributes(udfArgSets[0]);
      } else {
        throw new DebugError("udf-arg-sets-empty");
      }

      const supabase = getServiceRoleSupabase();

      const argVals: unknown[] = [];
      const udfCalls: string[] = [];
      for (const udfArgs of udfArgSets) {
        const singleArgVals = argnames.map((argname) => udfArgs[argname]);
        const argStr = Array.from({ length: singleArgVals.length })
          .map(
            (_, idx) =>
              `${doubleQuote((argnames ?? [])[idx])} => $${
                argVals.length + idx + 1
              }`,
          )
          .join(",");
        argVals.push(...singleArgVals);
        udfCalls.push(`SELECT ${udf_name}(${argStr}) result`);
      }

      if (req.headers["x-bt-debug-udf-runtime-error"]) {
        throw new DebugError("udf-runtime-error");
      }

      const rowsRaw = await (async () => {
        try {
          const { rows } = await supabase.query(
            udfCalls.join(" UNION ALL "),
            argVals,
          );
          return rows;
        } catch (error) {
          throw processUdfError(error);
        }
      })();
      const rows = rowsRaw.map((rowRaw) => {
        if (req.headers["x-bt-debug-udf-output-error"]) {
          throw new DebugError("udf-output-error");
        }

        const row = (postprocessOutput ?? ((x: unknown) => x))(rowRaw.result);
        return parseNoStrip(outputSchema, row);
      });
      const out = isSingle ? rows[0] : rows;
      res.json(out);
      return {
        authLookup,
        out,
      };
    } catch (e) {
      await processError({
        error: e,
        authLookup: authLookupRaw,
        req,
        res,
      });
    }
    res.end();
  },
);
