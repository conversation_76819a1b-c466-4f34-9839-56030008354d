import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { makeApiKeyFullResultSetQuery, sanitizeApiKey } from "../apikey/_util";
import {
  commonFilterParamsSchema,
  getObjects,
  paginationParamsSchema,
} from "../_object_crud_util";
import { apiKeySchema } from "@braintrust/core/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { name, org_name, org_id, id, ...paramsRest } = params;
      const { query: fullResultSetQuery, queryParams } =
        makeApiKeyFullResultSetQuery({
          authLookup,
          name,
          org_name,
          org_id,
          id,
        });
      const results = await getObjects({
        fullResultsQueryOverride: fullResultSetQuery,
        startingParams: queryParams,
        ...paramsRest,
        fullResultsSize: undefined,
      });
      return results.map(sanitizeApiKey);
    },
    {
      paramsSchema,
      outputSchema: apiKeySchema.array(),
      renameFields: { api_key_name: "name" },
    },
  );
}
