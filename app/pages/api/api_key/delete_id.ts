import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  deleteObjects,
  extractSingularRow,
} from "../_object_crud_util";
import { makeApiKeyFullResultSetQuery, sanitizeApiKey } from "../apikey/_util";
import { apiKeySchema } from "@braintrust/core/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const { query, queryParams, notFoundErrorMessage } =
        makeApiKeyFullResultSetQuery({
          authLookup,
          id: [params.id],
        });
      return sanitizeApiKey(
        extractSingularRow({
          rows: await deleteObjects({
            fullResultsQueryOverride: query,
            baseTableOverride: "api_key",
            startingParams: queryParams,
            fullResultsSize: 1,
            notFoundErrorMessage,
          }),
          notFoundErrorMessage: undefined,
        }),
      );
    },
    {
      paramsSchema: idParamSchema,
      outputSchema: apiKeySchema,
    },
  );
}
