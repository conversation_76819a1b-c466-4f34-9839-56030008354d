import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  jsonbPathFilterSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { additionalProjections } from "./_constants";
import { experimentSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const paramsSchema = paginationParamsSchema
  .merge(
    commonFilterParamsSchema.pick({
      name: true,
      project_name: true,
      project_id: true,
      org_name: true,
      org_id: true,
      id: true,
    }),
  )
  .extend({
    consider_deleted: z.boolean().nullish(),
    metadata: jsonbPathFilterSchema.nullish(),
  });

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    ({ consider_deleted, ...params }, authLookup) =>
      getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "experiment",
        },
        consider_deleted,
        ...splitPaginationParams(params),
        finalResultSetAdditionalProjections: additionalProjections,
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: experimentSchema.array(),
      renameFields: { experiment_name: "name" },
    },
  );
}
