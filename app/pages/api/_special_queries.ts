import { SqlQueryParams } from "#/utils/sql-query-params";

// Checks if the user has ownership privileges in the given org, either through
// a direct ACL grant or through membership in a group that has an owner grant.
export function makeIsOrgOwnerCTE({
  userId,
  orgId,
}: {
  userId: string;
  orgId: string;
}): { query: string; queryParams: SqlQueryParams } {
  const queryParams = new SqlQueryParams();

  const userIdParam = queryParams.add(userId);
  const orgIdParam = queryParams.add(orgId);

  const query = `
    with
        owner_role_id as (
            select get_owner_role_id() id
        ),
        is_org_owner as (
          select exists(
              select 1
              from
                  acls
                  join owner_role_id on true
                  left join _expanded_group_members on (
                      acls.group_id = _expanded_group_members.group_id
                      and _expanded_group_members.user_object_type = 'user'
                      and _expanded_group_members.user_group_id = ${userIdParam}::uuid
                  )
              where
                  object_type = 'organization'
                  and object_id = ${orgIdParam}::uuid
                  and grant_object_type = 'role'
                  and role_id = owner_role_id.id
                  and (
                      user_id = ${userIdParam}::uuid
                      or _expanded_group_members.group_id is not null
                  )
          )
        )
    `;
  return { query, queryParams };
}

export function checkIsOrgOwnerQuery({
  userId,
  orgId,
}: {
  userId: string;
  orgId: string;
}) {
  const { query: isOrgOwnerCTE, queryParams } = makeIsOrgOwnerCTE({
    userId,
    orgId,
  });

  return {
    query: `${isOrgOwnerCTE}
    select exists from is_org_owner`,
    queryParams,
  };
}
