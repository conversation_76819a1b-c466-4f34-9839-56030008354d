import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { datasetSchema } from "@braintrust/core/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    project_name: true,
    project_id: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        permissionInfo: {
          aclObjectType: "dataset",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: datasetSchema.array(),
      renameFields: { dataset_name: "name" },
    },
  );
}
