import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import {
  projectTagSchema,
  patchProjectTagSchema,
} from "@braintrust/core/typespecs";

const paramsSchema = idParamSchema.merge(patchProjectTagSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, ...paramsRest }, authLookup) =>
      extractSingularRow({
        rows: await patchObjects({
          authLookup,
          priorObjectTables: ["project_tag"],
          permissionInfo: {
            aclObjectType: "project",
            aclPermission: "update",
          },
          filters: {
            id,
          },
          patchValueParams: paramsRest,
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema,
      outputSchema: projectTagSchema,
    },
  );
}
