import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { extractSingularRow } from "../_object_crud_util";
import { batchUpdateHelper } from "./batch_update";
import { aclSchema, aclItemSchema } from "@braintrust/core/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const res = await batchUpdateHelper(
        { remove_acls: [params] },
        authLookup,
      );
      const row = extractSingularRow({
        rows: res ? res.removed_acls : [],
        notFoundErrorMessage: {
          permission: "delete_acls",
          objectType: params.object_type,
          objectId: params.object_id,
        },
      });
      return row;
    },
    {
      paramsSchema: aclItemSchema,
      outputSchema: aclSchema,
    },
  );
}
