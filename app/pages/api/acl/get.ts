import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_login_to_auth_id";
import {
  commonFilterParamsSchema,
  genQueryFilters,
  nullishSingleOrArraySchema,
  paginationParamsSchema,
  makeFullResultSetQuery,
  getObjects,
} from "../_object_crud_util";
import { aclObjectParamsSchema, cleanupAclJson } from "./_util";
import { z } from "zod";
import { aclSchema } from "@braintrust/core/typespecs";

const paramsSchema = commonFilterParamsSchema
  .pick({ id: true })
  .merge(paginationParamsSchema)
  .merge(aclObjectParamsSchema)
  .merge(
    nullishSingleOrArraySchema(
      z.object({
        user_id: aclSchema.shape.user_id.unwrap().unwrap(),
        group_id: aclSchema.shape.group_id.unwrap().unwrap(),
        permission: aclSchema.shape.permission.unwrap().unwrap(),
        restrict_object_type: aclSchema.shape.restrict_object_type
          .unwrap()
          .unwrap(),
        role_id: aclSchema.shape.role_id.unwrap().unwrap(),
      }),
    ),
  );

export async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  const {
    object_type,
    object_id,
    user_id,
    group_id,
    permission,
    restrict_object_type,
    role_id,
    id,
    ...paginationParams
  } = params;

  const { query: fullResultsSubquery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: object_type,
      aclPermission: "read_acls",
    },
    filters: {
      id: [object_id],
    },
  });

  const fullResultsQueryOverride = `
    select * from acls
    where
        object_type = ${queryParams.add(object_type)}
        and object_id in (select id from (${fullResultsSubquery}) "t")
        and ${genQueryFilters({ tableName: "acls" }, queryParams, {
          id,
          user_id,
          group_id,
          permission,
          restrict_object_type,
          role_id,
        })}
  `;

  const rows = await getObjects({
    fullResultsQueryOverride,
    startingParams: queryParams,
    paginationParams,
    fullResultsSize: undefined,
  });
  return rows.map(cleanupAclJson);
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: aclSchema.array(),
  });
}
