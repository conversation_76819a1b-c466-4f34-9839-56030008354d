import { type AuthLookup } from "../_login_to_auth_id";
import {
  type IdParam,
  makeFullResultSetQuery,
  extractSingularRow,
} from "../_object_crud_util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { aclObjectParamsSchema, cleanupAclJson } from "./_util";

const actionPermissions = {
  get: "read_acls",
  delete: "delete_acls",
} as const;

const finalActionQueries = {
  get: (where: string) => `select * from acls ${where}`,
  delete: (where: string) => `delete from acls ${where} returning *`,
};

export async function runActionOnId(
  action: "get" | "delete",
  params: IdParam,
  authLookup: AuthLookup,
) {
  const supabasePool = getServiceRoleSupabase();
  const client = await supabasePool.connect();
  await client.query("begin");
  try {
    const row = extractSingularRow({
      rows: (
        await client.query(
          "select object_type, object_id from acls where id = $1",
          [params.id],
        )
      ).rows,
      notFoundErrorMessage: {
        permission: "read",
        objectType: "acl",
        objectIds: [params.id],
      },
    });

    // Before we return the row, we must check if we have permissions to
    // perform the acl action on the object, and then perform it.
    const { object_type, object_id } = aclObjectParamsSchema.parse(row);

    const {
      query: fullResultsSubquery,
      queryParams,
      notFoundErrorMessage,
    } = makeFullResultSetQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: object_type,
        aclPermission: actionPermissions[action],
      },
      filters: {
        id: object_id,
      },
    });
    const query = finalActionQueries[action](`
        where
          acls.id = ${queryParams.add(params.id)}
          and exists (${fullResultsSubquery})
      `);
    const result = cleanupAclJson(
      extractSingularRow({
        rows: (await client.query(query, queryParams.params)).rows,
        notFoundErrorMessage,
      }),
    );
    await client.query("commit");
    return result;
  } catch (e) {
    await client.query("rollback");
    throw e;
  } finally {
    await client.end();
  }
}
