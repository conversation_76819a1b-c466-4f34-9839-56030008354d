import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { runActionOnId } from "./_action_on_id";
import { idParamSchema } from "../_object_crud_util";
import { aclSchema } from "@braintrust/core/typespecs";

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) => runActionOnId("get", params, authLookup),
    {
      paramsSchema: idParamSchema,
      outputSchema: aclSchema,
    },
  );
}
