import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import { createGroupSchema, groupSchema } from "@braintrust/core/typespecs";
import { z } from "zod";

const paramsSchema = createGroupSchema
  .omit({ name: true })
  .extend({
    group_name: createGroupSchema.shape.name,
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await invokeServiceRoleSupabaseUdf(req, res, "register_group", {
    paramsSchema,
    outputSchema: z.object({ group: groupSchema }),
    argnames: [
      "auth_id",
      "org_id",
      "group_name",
      "description",
      "member_users",
      "member_groups",
      "update",
    ],
  });
}
