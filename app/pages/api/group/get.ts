import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_login_to_auth_id";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { additionalProjections } from "./_constants";
import { groupSchema } from "@braintrust/core/typespecs";
import { type z } from "zod";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

export async function helper(
  params: z.infer<typeof paramsSchema>,
  authLookup: AuthLookup,
) {
  return getObjects({
    authLookup,
    permissionInfo: {
      aclObjectType: "group",
    },
    ...splitPaginationParams(params),
    finalResultSetAdditionalProjections: additionalProjections,
    fullResultsSize: undefined,
  });
}

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, {
    paramsSchema,
    outputSchema: groupSchema.array(),
    renameFields: { group_name: "name" },
  });
}
