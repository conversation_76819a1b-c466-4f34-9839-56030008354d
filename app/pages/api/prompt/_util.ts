import {
  promptSchema,
  promptBaseSchema,
  patchPromptSchema,
} from "@braintrust/core/typespecs";

export const promptSchemaApp = promptSchema
  .pick({
    id: true,
    project_id: true,
    slug: true,
    created: true,
  })
  .extend({
    deleted_at: promptBaseSchema.shape.deleted_at,
    user_id: promptBaseSchema.shape.user_id,
  });

export const patchPromptSchemaApp = patchPromptSchema
  .pick({
    name: true,
    slug: true,
    description: true,
    prompt_data: true,
    tags: true,
  })
  .extend({
    deleted_at: promptBaseSchema.shape.deleted_at,
    user_id: promptBaseSchema.shape.user_id,
  });
