import type { NextApiRequest, NextApiResponse } from "next";
import {
  invokeServiceRoleSupabaseUdf,
  udfOrgIdSchema,
  registerUdfUpdateSchema,
} from "../_invoke_supabase_udf";
import { createPromptSchema, projectSchema } from "@braintrust/core/typespecs";
import { promptSchemaApp } from "./_util";
import { z } from "zod";

const paramsSchema = z
  .object({
    project_id: createPromptSchema.shape.project_id.nullish(),
    project_name: z.string().nullish(),
    slug: createPromptSchema.shape.slug.nullish(),
    prompt_id: z.string().uuid().nullish(),
  })
  .merge(udfOrgIdSchema)
  .merge(registerUdfUpdateSchema);

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  console.log("registering prompt");
  await invokeServiceRoleSupabaseUdf(req, res, "register_prompt", {
    paramsSchema,
    outputSchema: z.object({
      prompt: promptSchemaApp,
      project: projectSchema,
      found_existing: z.boolean(),
    }),
    argnames: [
      "auth_id",
      "org_id",
      "project_id",
      "project_name",
      "slug",
      "update",
      "prompt_id",
    ],
  });
}
