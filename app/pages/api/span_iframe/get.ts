import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { spanIframeSchema } from "@braintrust/core/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    project_name: true,
    project_id: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    (params, authLookup) =>
      getObjects({
        authLookup,
        priorObjectTables: ["span_iframe"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
      }),
    {
      paramsSchema,
      outputSchema: spanIframeSchema.array(),
      renameFields: { span_iframe_name: "name" },
    },
  );
}
