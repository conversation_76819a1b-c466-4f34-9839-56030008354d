INSERT INTO users (id, auth_id, email) VALUES ('fac36c53-c882-458b-bf80-60d06c3e8a0d', '0ef17051-c1f8-422b-9ee8-85f7ae3afb21', '<EMAIL>');

-- Primary org on the 'unlimited' resource tier.
INSERT INTO organizations (id, name) VALUES ('abb9f3e4-7fdd-4ccc-af40-f7e894fd4125', 'braintrustdata.com') ON CONFLICT DO NOTHING;
select add_member_to_org_unchecked('fac36c53-c882-458b-bf80-60d06c3e8a0d', 'abb9f3e4-7fdd-4ccc-af40-f7e894fd4125', array [get_group_id('abb9f3e4-7fdd-4ccc-af40-f7e894fd4125', 'Owners')]);
select insert_resource_definition('abb9f3e4-7fdd-4ccc-af40-f7e894fd4125', 'unlimited');
