# Custom unittest runner which parses user flags separately from the unittest
# module. Mostly copied from
# https://stackoverflow.com/questions/35270177/passing-arguments-for-argparse-with-unittest-discover.
#
# If you need to update an expect test, here's how:
#
# python -m bt_unittest --expect-test-filter=tests/bt_services/expect_tests/responses_api.py tests/bt_services/test_expect.py --update

import argparse
import os
import sys

import pytest
from braintrust_local.app_db_util import update_foreign_keys_enable_cascade_delete
from braintrust_local.bt_unittest_util import remove_args_for_pid, store_args_for_pid

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Parses arguments used by the braintrust unit test wrapper")
    parser.add_argument(
        "--verbose", action="store_true", help="If specified, print full detail about any errors in the test"
    )
    parser.add_argument(
        "--update",
        action="store_true",
        help='If specified, run the expect tests in "update" mode, which updates the `.expected.json` files instead of checking that the new output matches the saved output',
    )
    parser.add_argument(
        "--expect-test-resanitize",
        action="store_true",
        help='If specified, each expect test will just read the current expected value, re-sanitize the data, and write it back to the `.expected.json` file. This is useful for updating the `.expected.json` files when the data sanitization logic has changed.',
    )
    parser.add_argument(
        "--expect-test-filter",
        nargs="+",
        help="A list of expect test files to filter down to. If not provided, runs the full set of expect tests",
    )
    parser.add_argument(
        "--serial",
        action="store_true",
        help="If specified, use the serial pytest runner, which runs everything in the main process. This can be useful for debugging, setting breakpoints, etc. By default, will use the pytest-xdist to run tests in parallel",
    )
    parser.add_argument(
        "--pdb",
        action="store_true",
        help="If specified, drop into debugger on test failures",
    )
    parser.add_argument(
        "--no-bt-services",
        action="store_true",
        help="If specified, skip any test-related setup that requires the braintrust services to be running. This is useful for running tests in CI, where in some jobs services are not available",
    )
    parser.add_argument(
        "remainder",
        nargs="*",
        help='Arguments after "--" are passed to the test runner',
    )
    bt_args = vars(parser.parse_args())
    remainder_args = bt_args.pop("remainder", [])

    pid = os.getpid()

    pytest_args = []
    if bt_args.get("pdb"):
        try:
            import ipdb
            pytest_args.extend(["--pdb", "--pdbcls=IPython.terminal.debugger:TerminalPdb"])
        except ImportError:
            pytest_args.extend(["--pdb"])
        bt_args["serial"] = True

    if bt_args.get("verbose"):
        pytest_args.append("-vv")

    store_args_for_pid(pid, bt_args)
    try:
        if not bt_args.get("no_bt_services"):
            update_foreign_keys_enable_cascade_delete()

        if not bt_args.get("serial"):
            pytest_args.extend(["-n", "logical", "--dist=load"])

        args = pytest_args + remainder_args
        print(f"Running pytest {' '.join(args)}")
        exit_code = pytest.main(args)
        sys.exit(exit_code)
    finally:
        remove_args_for_pid(pid)
