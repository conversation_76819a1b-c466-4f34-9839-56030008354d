#!/usr/bin/env tsx

/**
 * Simple script to insert a log into a Braintrust project using the SDK with a custom root span ID.
 * 
 * This example shows how to:
 * 1. Initialize a logger for a project
 * 2. Create a span with a custom root span ID
 * 3. Insert a log entry into that span
 * 4. Flush the log to ensure it's sent to the server
 * 
 * Usage:
 *     npx tsx simple_log_insert_custom_root_span.ts
 * 
 * Make sure to set your BRAINTRUST_API_KEY environment variable.
 */

import { initLogger, traced } from "braintrust";

async function main() {
  // Initialize logger for a project (creates project if it doesn't exist)
  const logger = initLogger({ projectName: "pedro-repro4667" });
  
  // Define our custom root span ID
  const customRootSpanId = "my-custom-trace-2024-01-15";
  
  console.log(`Creating span with custom root span ID: ${customRootSpanId}`);
  
  // Method 1: Use logger.startSpan with spanId parameter
  // For root spans, spanId becomes the rootSpanId
  const span = logger.startSpan({
    name: "custom_root_span",
    spanId: customRootSpanId, // This becomes both spanId and rootSpanId for root spans
    event: {
      input: "What is the weather like today?",
      output: "I don't have access to real-time weather data.",
      scores: { helpfulness: 0.3, accuracy: 1.0 },
      metadata: {
        model: "example-model",
        timestamp: "2024-01-15T10:30:00Z",
        user_id: "user123",
        custom_root_span: true
      },
      tags: ["weather", "information_request", "custom_root_span"]
    }
  });
  
  console.log(`Successfully created span with:`);
  console.log(`  - Span ID: ${span.spanId}`);
  console.log(`  - Root Span ID: ${span.rootSpanId}`);
  console.log(`  - Row ID: ${span.id}`);
  
  // End the span to finalize it
  span.end();
  
  // Method 2: Use traced function with spanId parameter
  console.log(`\nAlternative approach with traced function:`);
  
  await traced(async (tracedSpan) => {
    tracedSpan.log({
      input: "How do I set a custom root span ID in TypeScript?",
      output: "You can use the spanId parameter in startSpan or traced functions.",
      scores: { helpfulness: 0.9, accuracy: 1.0 },
      metadata: {
        approach: "traced_function",
        custom_root_span: true
      },
      tags: ["tutorial", "custom_root_span", "typescript"]
    });
    
    console.log(`Traced span created with:`);
    console.log(`  - Span ID: ${tracedSpan.spanId}`);
    console.log(`  - Root Span ID: ${tracedSpan.rootSpanId}`);
    console.log(`  - Row ID: ${tracedSpan.id}`);
    
    // Create a child span to demonstrate the root span ID inheritance
    const childSpan = tracedSpan.startSpan({
      name: "child_span",
      event: {
        input: "This is a child span",
        output: "Child spans inherit the root span ID from their parent",
        metadata: { is_child: true }
      }
    });
    
    console.log(`Child span created with:`);
    console.log(`  - Span ID: ${childSpan.spanId}`);
    console.log(`  - Root Span ID: ${childSpan.rootSpanId} (inherited from parent)`);
    console.log(`  - Row ID: ${childSpan.id}`);
    
    childSpan.end();
    
  }, {
    name: "alternative_custom_root",
    spanId: "alternative-root-span-2024-01-15" // Custom root span ID
  });
  
  // Method 3: Using logger.traced for more control
  console.log(`\nThird approach with logger.traced:`);
  
  await logger.traced(async (loggerSpan) => {
    loggerSpan.log({
      input: "What's the difference between the approaches?",
      output: "All approaches work, but logger.traced gives you more control over the logger instance.",
      scores: { helpfulness: 0.8, accuracy: 1.0 },
      metadata: {
        approach: "logger_traced",
        custom_root_span: true
      },
      tags: ["comparison", "custom_root_span"]
    });
    
    console.log(`Logger traced span created with:`);
    console.log(`  - Span ID: ${loggerSpan.spanId}`);
    console.log(`  - Root Span ID: ${loggerSpan.rootSpanId}`);
    console.log(`  - Row ID: ${loggerSpan.id}`);
    
  }, {
    name: "logger_traced_custom_root",
    spanId: "logger-traced-root-2024-01-15" // Another custom root span ID
  });
  
  // Flush to ensure the logs are sent to the server
  await logger.flush();
  console.log(`\nLogs have been sent to Braintrust server`);
}

// Run the main function and handle any errors
main().catch((error) => {
  console.error("Error running script:", error);
  process.exit(1);
});
